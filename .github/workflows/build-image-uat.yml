name: UAT Build and Push to ECR

on:
  push:
    branches:
      - uat

env:
  APP_NAME: "risk-reduce-api"
  APP_ENV: "uat"
  VERSION_NUMBER: "1.0"
  AWS_REGION: eu-west-1                       # set this to your preferred AWS region, e.g. us-west-1
  ECR_REPOSITORY: 	risk-reduce-api-b9af-uat-ecr     # set this to your Amazon ECR repository name

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  build-and-deploy:
    name: "Build docker image and push to ECR"
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login  Atomazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: "${{ env.APP_NAME }}-${{ env.APP_ENV }}-${{ env.VERSION_NUMBER }}.${{ github.run_number }}"
          FASTFWD_GITHUB_USER: ${{ secrets.FASTFWD_GITHUB_USER }}
          FASTFWD_GITHUB_TOKEN: ${{ secrets.FASTFWD_GITHUB_TOKEN }}
        run: |
          echo "start build image from docker file."
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          echo "Push docker image ECR."
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          echo "Successfully pushed docker image to ECR."
          IMAGE_TAG="$IMAGE_TAG"
          IMAGE_VERSION_FILTER="$ECR_REGISTRY\/$ECR_REPOSITORY:$IMAGE_TAG"
          echo "Printing new image for IMAGE_VERSION_FILTER: $IMAGE_VERSION_FILTER"
          echo "checkout application terraform repo CI/CD. "
          git clone https://$FASTFWD_GITHUB_USER:$<EMAIL>/fastfwd/stack-aws-risk-reduce-api-eks-cicd.git
          echo "change directory to application terraform repo CI/CD. "
          cd stack-aws-risk-reduce-api-eks-cicd
          echo "git config for identification."
          git config --global user.email "<EMAIL>"
          git config --global user.name "devops-fastfwd"
          echo "Replace value for app_image_version key in ${{ env.APP_ENV }}.auto.tfvars file."
          sed -i -e '/app_image_version =/ s/= .*/= "XXXXXXXXAPP_IMAGE_VERSIONXXXXXXXX"/' ${{ env.APP_ENV }}/${{ env.APP_ENV }}.auto.tfvars
          sed -i -e "s/XXXXXXXXAPP_IMAGE_VERSIONXXXXXXXX/$IMAGE_VERSION_FILTER/g" ${{ env.APP_ENV }}/${{ env.APP_ENV }}.auto.tfvars
          echo "Review new ${{ env.APP_ENV }}/${{ env.APP_ENV }}.auto.tfvars file."
          cat ${{ env.APP_ENV }}/${{ env.APP_ENV }}.auto.tfvars
          echo "Git add changes for ${{ env.APP_ENV }}.auto.tfvars file."
          git add ${{ env.APP_ENV }}/${{ env.APP_ENV }}.auto.tfvars
          echo "Git commit changes message."
          git commit -m "#minor: new image $IMAGE_TAG and to deploy."
          echo "Push changes to master branch."
          git push origin master
          echo "Successfully push changes and TF cloud should trigger deployment."
