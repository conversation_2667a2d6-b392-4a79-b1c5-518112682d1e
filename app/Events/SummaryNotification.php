<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SummaryNotification
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $summary;

    public $recipients;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($summary, $recipients)
    {
        $this->summary    = $summary;
        $this->recipients = $recipients;
    }
}
