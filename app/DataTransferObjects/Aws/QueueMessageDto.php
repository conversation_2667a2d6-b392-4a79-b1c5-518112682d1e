<?php

namespace App\DataTransferObjects\Aws;

class QueueMessageDto
{
    public function __construct(private array $message, private array $extras = [])
    {
    }

    public function toArray(): array
    {
        return [
            'to_email'           => $this->message['MessageAttributes']['to']['StringValue'] ?? '',
            'to_name'            => $this->message['MessageAttributes']['to_name']['StringValue'] ?? '',
            'from_name'          => $this->message['MessageAttributes']['from_name']['StringValue'] ?? '',
            'subject'            => $this->message['MessageAttributes']['subject']['StringValue'] ?? '',
            'error'              => $this->extras['error'] ?? '',
            'message_attributes' => $this->message['MessageAttributes'] ?? [],
            'message_id'         => $this->message['MessageId'] ?? [],
            'body'               => $this->message['Body'] ?? [],
        ];
    }
}
