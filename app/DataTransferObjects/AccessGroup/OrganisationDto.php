<?php

namespace App\DataTransferObjects\AccessGroup;

use App\Models\User;
use App\Models\Organisation;

class OrganisationDto
{
    public function __construct(private Organisation $organisation, private User $user)
    {
    }

    public function toArray(): array
    {
        return [
            'FirstName'             => $this->user->first_name,
            'LastName'              => $this->user->last_name,
            'PhoneNumber'           => $this->user->phone ?: '000000000',
            'EmailAddress'          => $this->user->email,
            'Username'              => $this->user->email,
            'DateOfBirth'           => $this->getDateOfBirth(),
            'JobTitle'              => $this->user->jobtitle ?: 'employee',
            'OrganisationName'      => $this->organisation->name,
            // 'Password'              => "",
            // 'PasswordConfirm'       => "",
            'PlaceOrBuildingName'   => $this->organisation->address_line_1 ?: "N/A",
            'StreetAddress'         => $this->organisation->address_line_2 ?: "N/A",
            'City'                  => "N/A",
            'PostCode'              => $this->organisation->postcode ?: "N/A",
            'Country'               => $this->getCountry(),
            'Region'                => "",
            'SendEmailToUser'       => 'false'
        ];
    }

    private function getDateOfBirth(): string
    {
        return "1989/12/31";
    }

    private function getCountry(): string
    {
        $country = $this->organisation->country ?: "GB";
        return $country === 'UK' ? "GB" : $country;
    }
}
