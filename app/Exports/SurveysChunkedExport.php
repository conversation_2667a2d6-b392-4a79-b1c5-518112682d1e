<?php

namespace App\Exports;

use App\Models\Branch;
use App\Models\DeletedResurvey;
use App\Models\ExternalSurveyCompany;
use App\Models\ExternalSurveyor;
use App\Models\LibertyUser;
use App\Models\MgaScheme;
use App\Models\Organisation;
use App\Models\RiskImprovementFormySubmissions;
use App\Models\RiskRecommendationFormy;
use App\Models\Survey;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithDefaultStyles;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Style;


class SurveysChunkedExport implements FromQuery, WithChunkReading, WithMapping, ShouldAutoSize, WithDefaultStyles, WithTitle, WithHeadings
{
    private const CHUNK_SIZE = 300;

    protected $request;

    public function __construct($request)
    {
        $this->request = $request;        
    }

    public function query()
    {
        $request = $this->request;

        $order = $request->has('order')
            ? $request->get('order')
            : 'desc';
        $search = (urldecode($request->get('search', '')));
        $column = $request->has('col')
            ? $request->get('col')
            : null;
        $sort = $request->has('sort')
            ? $request->get('sort')
            : null;
        $month = $request->has('month')
            ? $request->get('month')
            : null;
        $year = $request->has('year')
            ? $request->get('year')
            : null;
        $filters = $request->get('filters', []);

        $surveys = Survey::query();

        $organisation_id = $request->has('organisation_id')
            ? $request->get('organisation_id')
            : null;

        if ($organisation_id != null) {

            if ($request->has('client')) {
                $surveys = $surveys->where('organisation_id', '=', $organisation_id);

            } else {
                $surveys = $surveys->where('organisation_id', '=', $organisation_id);
            }
        }

        $branch_id = $request->has('branch_id')
            ? $request->get('branch_id')
            : null;

        if ($branch_id != null) {
            $org_branches = Organisation::select('id')->where('liberty_branch', $branch_id)->pluck('id');
            $surveys = $surveys->whereIn('organisation_id', $org_branches);
        }

        if ($request->has('branch_type') && $request->get('branch_type') == 'aspen-user') {
            $branches = Branch::select('id')->where('is_aspen', 1)->pluck('id');
            $org_branches = Organisation::select('id')->whereIn('liberty_branch', $branches)->pluck('id');
            $surveys = $surveys->whereIn('organisation_id', $org_branches);
        }

        $mga_scheme = $request->has('mga_scheme')
            ? $request->get('mga_scheme')
            : null;

        if ($mga_scheme != null) {
            $mgas = MgaScheme::select('id')->where('id', $mga_scheme)->pluck('id');

            $organisations = Organisation::whereIn('mga_scheme', $mgas);

            if ($request->has('mga_scheme')) {
                $organisations = $organisations->where('mga_scheme', $request->get('mga_scheme'));
            }

            $organisations = $organisations->pluck('id');

            $surveys = $surveys->whereIn('organisation_id', $organisations);
            $surveys = $surveys->with('organisation.mgascheme');
        }


        // auto scheduled surveys check
        if ($request->has('auto_scheduled_surveys')) {
            $excluded_survey_ids = DeletedResurvey::where('id', '>', 0)->pluck('survey_id');
            $surveyIds = RiskImprovementFormySubmissions::whereNotNull('csr_next_survey_due_date')
                ->where('csr_next_survey_due_date', '!=', '')
                ->where('csr_status', 'submitted')->pluck('survey_id');

            $surveys = $surveys->where(
                function ($query) use ($surveyIds) {
                    $query->whereIn('surveys.id', $surveyIds)
                        ->orWhereIn('legacy_srf', $surveyIds);
                }
            )->whereNotIn('id', $excluded_survey_ids);
        }

        $surveys = $surveys->with([
            'organisation:id,mga_scheme,name,broker_id',
            'organisation.mgascheme:id,name',
            'branch:id,name',
            'schedule',
            'scheduleMeta',
            'scheduleMetas',
            'Location',
            'externalSurveyCompany',
            'surveyor',
            'externalSurveyor',
        ]);

        $broker_org = $request->has('broker_org')
            ? $request->get('broker_org')
            : null;

        if ($broker_org != null) {
            $surveys = $surveys->where('broker_id', '=', $broker_org);
        }

        if ($search) {
            $organisationIds = Organisation::select('id')->where('name', 'LIKE', '%' . $search . '%')->pluck('id');

            $externalSurveyCompanyIds = ExternalSurveyCompany::select('id')->where('name', 'LIKE', '%' . $search . '%')->pluck('id');

            $libertyUserIds = LibertyUser::select('id')->where('role', 'risk-engineer')
                ->where('first_name', 'LIKE', '%' . $search . '%')
                ->orWhere('last_name', 'LIKE', '%' . $search . '%')
                ->orWhereRaw('concat(first_name, \' \', last_name) LIKE ?', ['%' . $search . '%'])
                ->pluck('id');

            $externalSurveyorIds = ExternalSurveyor::select('id')->where('first_name', 'LIKE', '%' . $search . '%')
                ->orWhere('last_name', 'LIKE', '%' . $search . '%')
                ->orWhereRaw('concat(first_name, \' \', last_name) LIKE ?', ['%' . $search . '%'])
                ->pluck('id');

            $surveyIds = Survey::select('id')->where('id', preg_replace('/[^0-9]/', '', $search))
                ->orWhere('legacy_srf', preg_replace('/[^0-9]/', '', $search))->pluck('id');

            $branchIds = Branch::select('id')->where('name', 'LIKE', '%' . $search . '%')->pluck('id');

            $surveys = $surveys->where(
                function ($query) use (
                    $organisationIds,
                    $surveyIds,
                    $branchIds,
                    $externalSurveyCompanyIds,
                    $libertyUserIds,
                    $externalSurveyorIds,
                    $search
                ) {
                    $query->whereIn('organisation_id', $organisationIds)
                        ->orWhereIn('id', $surveyIds)
                        ->orWhereIn('branch_id', $branchIds)
                        ->orWhereIn('external_survey_company_id', $externalSurveyCompanyIds)
                        ->orWhereIn('surveyor_id', $libertyUserIds)->whereNull('external_survey_company_id')
                        ->orWhere(
                            function ($q) use ($externalSurveyorIds) {
                                $q->whereIn('surveyor_id', $externalSurveyorIds)->whereNotNull('external_survey_company_id');
                            }
                        );
                }
            );
        }

        // external surveyor check
        if ($request->has('external_survey_company_id')) {
            $surveys = $surveys->where('external_survey_company_id', $request->get('external_survey_company_id'));
        }

        // stuff
        if ($request->has('user_id') && $request->has('user_role') && $request->has('user_type') && $request->has('user_login_type')) {
            $user = $request->only(
                'user_id', 'user_role', 'user_type', 'user_login_type',
                'user_external_survey_company_id'
            );

            if ($user['user_type'] == 'external-surveyor' && $user['user_role'] == 'admin') {
                // ...
            } else {
                if ($user['user_type'] == 'external-surveyor') {
                    $externalSurveyor = ExternalSurveyor::where('id', $user['user_id'])->first();

                    $surveys = $surveys->where(
                        function ($query) use ($externalSurveyor) {
                            $query->where(
                                function ($q) use ($externalSurveyor) {
                                    $q->where('surveyor_id', $externalSurveyor->id)->whereNotNull('external_survey_company_id');
                                }
                            );
                        }
                    );
                } else {
                    $libertyUser = LibertyUser::where('id', $user['user_id'])->where(
                        function ($q) {
                            $q->where('role', 'risk-engineer')->orWhere('role', 'underwriter');
                        }
                    )->first();

                    $surveys = $surveys->where(
                        function ($query) use ($libertyUser) {
                            $query->where(
                                function ($q) use ($libertyUser) {
                                    $q->where('surveyor_id', $libertyUser->id)->orWhere('underwriter_id', $libertyUser->id);
                                }
                            )->whereNull('external_survey_company_id');
                        }
                    );
                }
            }
        }


        if ($request->has('survey_type') && $request->get('survey_type') === 'rereview') {
            $surveys = $surveys->where('survey_type', '=', 'rereview');
        } elseif ($request->get('survey_type') === 'dtr') {
            $surveys = $surveys->where('survey_type', '=', 'dtr');
        } else {
            $surveys = $surveys->where('survey_type', '=', 'survey');
        }

        if ($year != null) {
            $surveys = $surveys->whereYear('next_survey_due_date', '=', $year);
        }

        if ($month != null) {
            $surveys = $surveys->whereMonth('next_survey_due_date', '=', $month);
        }

        if (isset($filters['year']) && !in_array(trim(strtolower($filters['year'])), ['', 'all'])) {
            $surveys = $surveys->whereYear('created_at', '=', $filters['year']);
        }

        //sorting
        if ($column != '') {
            $column = $column == 'as_org'
                ? 'organisation_name'
                : 'next_survey_due_date';

        } else {
            $column = 'id';
            $sort = $order;
        }

        $surveys = $surveys->orderBy($column, $sort);
        
        return $surveys;
    }
    
    public function chunkSize(): int
    {
        return self::CHUNK_SIZE;
    }

    public function map($resource): array
    {
        $resource->submission = RiskImprovementFormySubmissions::where(
            'survey_id', '=',
            (string)$resource->id
        )->whereIn(
            'surveyor_id',
            [(string)$resource->surveyor_id, $resource->surveyor_id]
        )->first();

        $srf = isset($resource->legacy_srf)
            ? $resource->legacy_srf
            : $resource->id;
        $locationName = isset($resource->Location->location_name)
            ? $resource->Location->location_name
            : 'N/A';
        $insured = isset($resource->organisation->name)
            ? $resource->organisation->name
            : 'N/A';
        $risk_recs = 'N/A';
        $type = isset($resource->policyNumber) && isset($resource->policyNumber->type->name)
            ? $resource->policyNumber->type->name
            : 'N/A';
        $surveyor = $this->formatSurveyor(collect($resource));
        $mga_scheme = isset($resource->organisation->mgascheme->name)
            ? $resource->organisation->mgascheme->name
            : 'N/A';
        $created_at = date('d/m/Y', strtotime($resource->created_at));
        $survey_date = isset($resource->scheduleMeta->value)
            ? date('d/m/Y', strtotime($resource->scheduleMeta->value))
            : 'N/A';
        $csr_submission = isset($resource->submission->csr_submission_date)
            ? $resource->submission->csr_submission_date
            : 'N/A';
        $uwr_submission = isset($resource->submission->uwr_submission_date)
            ? $resource->submission->uwr_submission_date
            : 'N/A';
        $next_survey_date = isset($resource->next_survey_due_date)
            ? $resource->next_survey_due_date
            : 'N/A';
        $broker_organisation = isset($resource->broker->name)
            ? $resource->broker->name
            : 'N/A';
        $csr_approved = $resource->csr_uw_status == '0'
            ? 'Not sent to UW'
            : ($resource->csr_uw_status == '2'
                ? 'Approved'
                : 'Sent to UW');
        $uwr_approved = $resource->uwr_uw_status == '0'
            ? 'Not sent to UW'
            : ($resource->uwr_uw_status == '2'
                ? 'Approved'
                : 'Sent to UW');
        $underwriter_name = isset($resource->broker_underwriter)
            ? ($resource->broker_underwriter->first_name . ' ' . $resource->broker_underwriter->last_name)
            : (isset($resource->underwriter)
                ? ($resource->underwriter->first_name . ' ' . $resource->underwriter->last_name)
                : '');
        $underwriter_email = isset($resource->broker_underwriter)
            ? $resource->broker_underwriter->email
            : (isset($resource->underwriter)
                ? $resource->underwriter->email
                : '');

        if (isset($resource->submission->csr_status) && $resource->submission->csr_status == 'submitted') {
            $rr = RiskRecommendationFormy::where('form_id', '=', $resource->submission->form_id)->first();

            if (isset($rr->risk_recommendation_fields)) {
                $risk_recs = '0';

                foreach ($rr->risk_recommendation_fields as $risk_rec_id) {
                    for ($i = 1; $i <= 15; $i++) {
                        if (isset($resource->submission->{$risk_rec_id . '_' . $i . '_classification'})
                            && $resource->submission->{$risk_rec_id . '_' . $i . '_classification'} != ''
                        ) {
                            $risk_recs = (string)(intval($risk_recs) + 1);
                        }
                    }
                }
            }
        }

        if ($this->request->get('survey_type') === 'dtr') {
            return [
                $srf,
                $locationName,
                $insured,
                $risk_recs,
                $type,
                $surveyor,
                $mga_scheme,
                $created_at,
                $survey_date,
                $csr_submission,
                $next_survey_date,
                $broker_organisation,
                $csr_approved,
            ];
        }

        return [
            $srf,
            $locationName,
            $insured,
            $risk_recs,
            $type,
            $surveyor,
            $mga_scheme,
            $created_at,
            $survey_date,
            $csr_submission,
            $uwr_submission,
            $next_survey_date,
            $broker_organisation,
            $csr_approved,
            $uwr_approved,
            $underwriter_name,
            $underwriter_email,
        ];
    }

    public function title(): string
    {
        return 'Surveys';
    }

    public function headings(): array
    {
        // DTR Headers
        if ($this->request->get('survey_type') === 'dtr') {
            return [
                'DTR',
                'Location',
                'Insured',
                'Number of Risk Recs',
                'Report type',
                'Surveyor',
                'MGA scheme',
                'Created at',
                'Survey date',
                'DTR submission date',
                'Next Survey Due Date',
                'Broker Organisation',
                'DTR approved',
            ];
        }

        // Survey headers
        return [
            'SRF',
            'Location',
            'Insured',
            'Number of Risk Recs',
            'Survey type',
            'Surveyor',
            'MGA scheme',
            'Created at',
            'Survey date',
            'CSR submission date',
            'UWR submission date',
            'Next Survey Due Date',
            'Broker Organisation',
            'CSR approved',
            'UWR approved',
            'Underwriter name',
            'Underwriter email',
        ];
    }

    public function defaultStyles(Style $defaultStyle)
    {
        return [
            'font' => [
                'size'   => 15,
                'family' => 'Calibri',
                'bold'   => false,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
            'fill' => [
                'fillType'   => Fill::FILL_SOLID,
            ],
        ];
    }

    private function formatSurveyor(Collection $survey): ?string
    {
        if ($survey->get('external_survey_company')) {
            $surveyor = data_get($survey, 'external_survey_company.name');

            if ($survey->get('external_surveyor')) {
                $surveyor = data_get($survey, 'external_surveyor.first_name')
                    . ' ' . data_get($survey, 'external_surveyor.last_name')
                    . ' of '
                    . $surveyor;
            }
        } else {
            $surveyor = data_get($survey, 'surveyor.first_name') . ' ' . data_get($survey, 'surveyor.last_name');
        }
        return $surveyor;
    }
}
