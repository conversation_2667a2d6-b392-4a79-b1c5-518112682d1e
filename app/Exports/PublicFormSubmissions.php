<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithDefaultStyles;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Style;

class PublicFormSubmissions implements FromCollection, WithTitle, WithDefaultStyles, WithEvents
{
    public function __construct(public Collection $excelData)
    {
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection(): Collection
    {
        return $this->excelData;
    }

    public function title(): string
    {
        return 'Submissions';
    }

    public function defaultStyles(Style $defaultStyle)
    {
        $defaultStyle->getFont()->setName('Calibri');
        $defaultStyle->getFont()->setSize(15);
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $workSheet = $event->sheet->getDelegate();

                // This will freeze the first row and first column
                $workSheet->freezePane('B2');
            },
        ];
    }
}
