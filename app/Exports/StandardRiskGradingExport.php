<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use App\Exports\StandardRiskGradingExportSheet;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class StandardRiskGradingExport implements WithMultipleSheets
{
    use Exportable;

    protected $data;
    protected $headers;
    protected array $excelSheets;

    public function __construct(array $data, array $excelSheets)
    {
        $this->headers = $data[0];
        $this->data = $data[1];
        $this->excelSheets = $excelSheets;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];

        foreach ($this->excelSheets as $sheetName) {
            if (count($this->headers[$sheetName] ?? []) <= 1) {
                $sheets[] = new StandardRiskGradingExportSheet(['No data for this line of business'], $sheetName);
                continue;
            }

            $sheets[] = new StandardRiskGradingExportSheet($this->data[$sheetName] ?? [], $sheetName, $this->headers[$sheetName]);
        }
        return $sheets;
    }
}
