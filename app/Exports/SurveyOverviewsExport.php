<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithDefaultStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Style;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class SurveyOverviewsExport implements FromArray, ShouldAutoSize, WithDefaultStyles, WithTitle
{

    protected $surveyOverview;

    public function __construct(array $overview)
    {
        $this->surveyOverview = $overview;
    }

    /**
    * @return array
    */
    public function array(): array
    {
        return $this->surveyOverview;
    }

    public function title(): string
    {
        return 'Survey Overview';
    }

    public function headings(): array
    {
        return array_shift($this->surveyOverview);
    }

    /**
     * Register events
     * - Freezes first row and column
     *
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->getDelegate()->getActiveSheet()->freezePane('A2');
                $event->getDelegate()->getActiveSheet()->freezePane('B2');
            }
        ];
    }

    public function defaultStyles(Style $defaultStyle)
    {
        return [
            'font' => [
                'size'   => 15,
                'family' => 'Calibri',
                'bold'   => false,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
            'fill' => [
                'fillType'   => Fill::FILL_SOLID,
            ],
        ];
    }
}
