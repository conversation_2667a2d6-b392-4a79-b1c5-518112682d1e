<?php

namespace App\Exports;

use Illuminate\Support\Str;
use App\Models\RiskRecommendationCards;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\PageSetup;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Style;
use PhpOffice\PhpSpreadsheet\Style\Fill;;

class RiskRecExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize
{
    protected $request;

    public function __construct($request)
    {
        $this->request = $request;
    }

    public function title(): string
    {
        return 'Risk Recommendations';
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function query()
    {
        return RiskRecommendationCards::filter($this->request, null);
    }

    public function headings(): array
    {
        return [
            'SRF',
            'Location Name',
            'Postcode',
            'Organisation',
            'Ref',
            'Title',
            'Classification',
            'Required By',
            'Status',
            'Description',
            'Action',
            'Survey Type',
            'Survey Date',
        ];
    }

    /**
    * @var Invoice $invoice
    */
    public function map($card): array
    {
        $required_by=isset($card->properties['required_by'])?$card->properties['required_by']:null;

        $currentDate = isset($required_by) && $required_by != '' && $required_by != '-' && Carbon::createFromFormat('d/m/Y', $required_by) !== false 
            ? Carbon::createFromFormat('d/m/Y', $required_by) : null;

        $status = 'Open';
        if (!empty($card->export_properties['csr_status'])) {
            if ($card->export_properties['closed'] == 1) {
                $status = 'Closed';
            } elseif (isset($currentDate) && !is_null($currentDate) && strtotime($currentDate) <= strtotime("now")) {
                $status = 'Overdue';
            } else {
                $status = 'Open';
            }
        }

        return [
            $card->properties['srf'],
            $card->properties['location']['location_name'] ?? '',
            $card->export_properties['postcode'],
            $card->export_properties['organisation'],
            $card['title'],
            strip_tags(Str::markdown($card->export_properties['title'])),
            $card->export_properties['classification'],
            $card->properties['required_by'],
            $status,
            str_replace('\r\n', ' <br/>', $card->export_properties['description']),
            str_replace('\r\n', ' <br/>', $card->export_properties['action']),
            $card->properties['survey_type'],
            $card->properties['survey_date'],
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $event->sheet
                ->getPageSetup()
                ->setOrientation(PageSetup::ORIENTATION_LANDSCAPE);
            },
        ];
    }

    public function defaultStyles(Style $defaultStyle)
    {
        return [
            'font' => [
                'size'   => 15,
                'family' => 'Calibri',
                'bold'   => false,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
            'fill' => [
                'fillType'   => Fill::FILL_SOLID,
            ],
        ];
    }

}
