<?php

namespace App\Exports;

use Maatwebsite\Excel\Sheet;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\Style;
use Maatwebsite\Excel\Concerns\WithEvents;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithDefaultStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\PageSetup;

class StandardRiskGradingExportSheet implements FromArray, ShouldAutoSize, WithDefaultStyles, WithTitle, WithEvents
{
    protected $data;
    protected $sheetName;
    protected $headings;

    public function __construct(array $data, string $sheetName,  array $headings = [])
    {
        $this->data = array_merge([$headings], $data);
        $this->sheetName = $sheetName;
        $this->headings = $headings;

        Sheet::macro('styleCells', function (Sheet $sheet, string $cellRange, array $style) {
            $sheet->getDelegate()->getStyle($cellRange)->applyFromArray($style);
        });
    }

    public function title(): string
    {
        return $this->sheetName;
    }

    public function array(): array
    {
        return $this->data;
    }

    public function headings(): array
    {
        return $this->headings;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function (AfterSheet $event) {
                $event->sheet
                    ->getPageSetup()
                    ->setOrientation(PageSetup::ORIENTATION_LANDSCAPE);

                $workSheet = $event->sheet->getDelegate();
                // This will freeze the first row and first column
                $workSheet->freezePane('B2');

                foreach ($workSheet->getCoordinates() as $coordinate) {
                    /**
                     * @var Cell $cell
                     */
                    $cell = $workSheet->getCell($coordinate);
                    $cellValue = $cell->getValue();
                    if (strpos($cellValue, '#') !== false) {
                        $event->sheet->styleCells(
                            $coordinate,
                            [
                                'fill' => [
                                    'fillType' => Fill::FILL_SOLID,
                                    'color' => ['rgb' => substr($cellValue, 1)]
                                ]
                            ]
                        );
                        $cell->setValue("");
                    }
                }
            },
        ];
    }

    public function defaultStyles(Style $defaultStyle)
    {
        return [
            'font' => [
                'size'   => 15,
                'family' => 'Calibri',
                'bold'   => false,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
            'fill' => [
                'fillType'   => Fill::FILL_SOLID,
            ],
        ];
    }
}
