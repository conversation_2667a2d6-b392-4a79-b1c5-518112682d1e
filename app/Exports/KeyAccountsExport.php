<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\PageSetup;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Style;
use PhpOffice\PhpSpreadsheet\Style\Fill;;

class KeyAccountsExport implements FromArray, WithMapping, WithHeadings, ShouldAutoSize
{
    protected $keyAccounts;

    public function __construct(array $keyAccounts)
    {
        $this->keyAccounts = $keyAccounts;
    }

    public function title(): string
    {
        return 'Key Accounts';
    }

    /**
    * @return array
    */
    public function array() : array
    {
        return $this->keyAccounts;
    }

    public function headings(): array
    {
        return [
            'ID',
            'Organisation name',
            'Underwriter',
            'Risk Engineer',
            'Inception Date',
            'Expiry Date',
            'Client contact',
            'Broker',
            'Broker contact'
        ];
    }

    public function map($data): array
    {
        return [
            $data['id'],
            $data['organisation_name'],
            $data['underwriter'],
            $data['risk_engineer'],
            $data['inception_expiry_date'],
            $data['expiry_date'],
            $data['client_contact'],
            $data['broker'],
            $data['broker_contact'],
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $event->sheet
                ->getPageSetup()
                ->setOrientation(PageSetup::ORIENTATION_LANDSCAPE);
            },
        ];
    }

    public function defaultStyles(Style $defaultStyle)
    {
        return [
            'font' => [
                'size'   => 15,
                'family' => 'Calibri',
                'bold'   => false,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
            'fill' => [
                'fillType'   => Fill::FILL_SOLID,
            ],
        ];
    }

}
