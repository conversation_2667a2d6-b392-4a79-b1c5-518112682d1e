<?php

namespace App\Models\Postcode;

use Exception;
use Illuminate\Support\Facades\Log;
use PHPCoord\LatLng;
use PHPCoord\RefEll;
use Symfony\Component\HttpKernel\Exception\HttpException;

class Postcode
{
    const POSTCODE_API = 'https://api.postcodes.io/postcodes/';
    const NI_COUNTRY_NAME = 'northern ireland';

    protected $postcode;

    public function __construct()
    {
        $this->postcode = null;
    }

    public function get($postcode)
    {
        $curl = curl_init();
        curl_setopt_array(
            $curl, [
            CURLOPT_RETURNTRANSFER => 1,
            CURLOPT_URL => self::POSTCODE_API . rawurlencode($postcode),
            ]
        );

        // Fetch postcode data
        $response = curl_exec($curl);
        if (!empty(curl_errno($curl))) {
            $error = curl_error($curl);
            Log::debug($error);
        } else {
            $this->postcode = json_decode($response, true);
        }
        curl_close($curl);

        // Error handling
        if (empty($this->postcode)) {
            throw new Exception(
                $error
                ?: 'Error encountered with Postcode API'
            );
        } elseif (intval($this->postcode['status']) != 200) {
            throw new HttpException($this->postcode['status'], $this->postcode['error']);
        } else {
            $this->postcode = $this->postcode['result'];

            // Check if postcode region is Northern Ireland
            // If yes, we need to convert the lat,long to BGS
            // since postcode.io uses Irish Grid Reference for postcodes in Northern Ireland
            if (!empty($this->postcode['country'])
                && strtolower($this->postcode['country']) === self::NI_COUNTRY_NAME
                && isset($this->postcode['latitude'])
                && isset($this->postcode['longitude'])
            ) {
                $latLong = new LatLng(
                    floatval($this->postcode['latitude']),
                    floatval($this->postcode['longitude']),
                    0,
                    RefEll::wgs84() // Postcodes.io uses WGS84 for Latitude and Longitude. See: https://postcodes.io/docs
                );

                $osRef = $latLong->toOSRef();
                $this->postcode['eastings'] = $osRef->getX();
                $this->postcode['northings'] = $osRef->getY();
            }
        }

        return $this;
    }

    public function __get($property)
    {
        if (isset($this->postcode[$property])) {
            return $this->postcode[$property];
        }

        trigger_error("Undefined property {$property}.", E_USER_ERROR);
    }
}
