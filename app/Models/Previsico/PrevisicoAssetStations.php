<?php

namespace App\Models\Previsico;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PrevisicoAssetStations extends Model
{
    use HasFactory;

    protected $table = 'previsico_asset_stations';

    protected $fillable = [
        'station_ref',
        'latest_reading_date',
        'latest_reading_value',
        'stagescale_typical_range_high',
        'stagescale_typical_range_low',
        'type',
    ];
}
