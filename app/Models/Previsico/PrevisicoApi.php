<?php

namespace App\Models\Previsico;

use GuzzleHttp\Client as GuzzleClient;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class PrevisicoApi
{
    public static function get(string $type, ?string $path, array $params = [])
    {
        $client = new GuzzleClient();

        $config = config('app.previsico.flood_map');
        $url = sprintf($config[$type]['endpoint'] . '/%s', $path);

        $cacheKey = Str::slug(Arr::query($params));
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $queryString = !empty($params)
            ? ['query' => $params]
            : [];

        $res = $client->request('GET', $url, $queryString);
        $data = json_decode($res->getBody());

        Cache::put($cacheKey, $data, now()->addMinutes(15));
        return $data;
    }
}