<?php

namespace App\Models\Previsico;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes as SoftDeletingTrait;
use App\Models\OrganisationLocations;
use App\Models\Previsico\PrevisicoAlert;
use Illuminate\Support\Carbon;

class PrevisicoAsset extends Model
{
    use SoftDeletingTrait;

    protected $table = 'previsico_assets';

    protected $fillable = [
        'name',
        'easting',
        'northing',
        'organisation_id',
        'alert_level',
        'last_notification_sent',
        'organisation_location_id',
        'rs_nearest_station_ref',
        'rs_nearest_station_latest_reading',
        'sw_nearest_station_ref',
        'sw_nearest_station_latest_reading',
        'rs_risk_ranking_raw',
        'rs_risk_ranking',
        'sw_risk_ranking_raw',
        'sw_risk_ranking',
        'last_reading_date',
    ];

    public function alert()
    {
        return $this->hasMany(PrevisicoAlert::class, 'id', 'previsico_asset_id');
    }

    public function rsStation()
    {
        return $this->hasOne(PrevisicoAssetStations::class, 'id', 'rs_nearest_station_latest_reading');
    }

    public function swStation()
    {
        return $this->hasOne(PrevisicoAssetStations::class, 'id', 'sw_nearest_station_latest_reading');
    }

    public function organisationLocation()
    {
        return $this->belongsTo(OrganisationLocations::class, 'organisation_location_id', 'id');
    }

    public function assetAccess()
    {
        return $this->belongsTo(PrevisicoAssetAccess::class,  'id', 'previsico_asset_id');
    }

    /**
     * Get latest alert for the asset
     *
     * @return PrevisicoAlert|null
     */
    public function latestAlert()
    {
        // Remove filter by alert level so we can
        // show assets with alerts within
        // the past 48 hrs
        // https://app.clickup.com/t/8694hzxdc
        // if ($this->alert_level < 1) {
        //     return null;
        // }

        $alert = PrevisicoAlert::where('previsico_asset_id', $this->id)
            ->where(function($where) { // and (where is_new = 1 or (date between 48hrs ago and now))
                $where->where('is_new', 1)
                    ->orWhere(function ($dateQuery) {
                        $now = Carbon::now();
                        $dateQuery->whereBetween('date', [$now->clone()->subHours(48), $now]);
                    });

            })
            ->orderBy('id', 'desc')
            ->limit(1)
            ->first();

        if($alert){
            $alert->actioned_by_user=$alert->actionedBy();
        }
        return $alert;
    }
}
