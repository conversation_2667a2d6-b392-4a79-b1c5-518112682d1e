<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes as SoftDeletingTrait;
use Illuminate\Database\Eloquent\Model;
use App\Models\OrganisationPolicy;
use App\Models\RiskGradingLog;

class RiskGrading extends Model
{
    use SoftDeletingTrait;

    public static $policy_types = [
        '1' => '(P)',
        '2' => '(C)',
        '3' => '(CC)',
        '4' => '(E)',
        '5' => '(CAR)',
        '6' => '(DO)',
    ];
    public static $remaped_values = [
        'Requires Improvement' => 'Requires Improvement',
        'Below Average' => 'Below Average',
        'Average' => 'Average',
        'Good' => 'Good',
        'Superior' => 'Superior',
        'Not Applicable / Not Assessed' => 'Not Applicable / Not Assessed',
        'Contact U/W within 24 hours' => 'Requires Improvement',
        'Multiple Requirements identified - monthly updates required' => 'Below Average',
        'Single Requirement - monitor progress' => 'Average',
        'Recommendations Only -generally reasonable controls' => 'Good',
        'Satisfactory' => 'Good',
        'Not Applicable' => 'Not Applicable / Not Assessed',
    ];
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'risk_gradings';
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];
    protected $dates = ['deleted_at'];
    protected $fillable = [
        'organisation_location_id',
        'location_id',
        'policy_id',
        'attribute',
        'value',
        'max_score',
        'score',
    ];

    protected $appends = [
        'attr_lob',
        'policy_types',
    ];

    public static function remapGradingValue($value)
    {
        if (isset($value) && !empty($value) && isset(self::$remaped_values[$value]) && !empty(self::$remaped_values[$value])) {
            $value = trim($value);
            return self::$remaped_values[$value];
        } else {
            return 'Not Applicable / Not Assessed';
        }
    }

    public function policy()
    {
        return $this->hasOne(OrganisationPolicy::class, 'id', 'policy_id');
    }

    public function getAttrLobAttribute()
    {
        return $this->attribute . '-' . $this->policy->policy_type_id;
    }

    public function getPolicyTypesAttribute()
    {
        return self::$policy_types;
    }

    public function riskGradingLogs()
    {
        return $this->hasMany(RiskGradingLog::class, 'risk_gradings_id', 'id');
    }
}
