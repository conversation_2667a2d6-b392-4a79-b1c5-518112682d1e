<?php

namespace App\Console\Commands;

use App\Models\Surveyfiles;
use App\Models\RiskImprovementFormySubmissions;
use Illuminate\Console\Command;

class UpdateSurveySubmissionPhotoGraphKey extends Command
{
    protected $signature = 'survey-submission:update-photo-graph-key 
                          {key? : The photograph key to process. Default: photographs-1656582967}
                          {--survey-id= : The survey ID to process. Default: null}
                          {--dry-run : Show what would be updated without making changes}';

    protected $description = 'Updates survey submissions with missing photograph key';

    public function handle()
    {
        if ($this->option('survey-id')) {
            $this->info('Processing specific survey ID: ' . $this->option('survey-id'));
            $this->handleSpecificSurvey();
        } else {
            $this->handleMultipleSurveys();
        }

        return Command::SUCCESS;
    }

    private function handleSpecificSurvey()
    {
        // Get the photo key from argument or use default
        $surveyId = $this->option('survey-id');
        
        
        if ($this->option('dry-run')) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        // 1. Get distinct survey IDs from survey_files
        $surveyFile = Surveyfiles::where('field_name','like', '%photographs%')
            ->where('survey_id', $surveyId)
            ->first();

        if (!$surveyFile) {
            $this->error('No survey file found for survey ID: ' . $surveyId);
            return;
        }

        $photoKey = $surveyFile->field_name;
        $this->info(sprintf('Starting to update survey submissions for photo key: %s', $photoKey));

        // 2. Process each survey's submissions
        $updated = 0;
        $skipped = 0;

        $this->processSubmissions($surveyId, $photoKey, $updated, $skipped);
        
        $this->info(sprintf(
            'Process completed. Updated survey ID: %s',
            $surveyId
        ));
    }

    private function handleMultipleSurveys()
    {
        // Get the photo key from argument or use default
        $photoKey = $this->argument('key') ?? 'photographs-1656582967';
        
        $this->info(sprintf('Starting to update survey submissions for photo key: %s', $photoKey));
        
        if ($this->option('dry-run')) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        // 1. Get distinct survey IDs from survey_files
        $surveyIds = Surveyfiles::where('field_name', $photoKey)
            ->distinct()
            ->pluck('survey_id');

        $this->info(sprintf('Found %d surveys to process', count($surveyIds)));


        // 2. Process each survey's submissions
        $updated = 0;
        $skipped = 0;

        foreach ($surveyIds as $surveyId) {
            $this->processSubmissions($surveyId, $photoKey, $updated, $skipped);
        }

        $this->info(sprintf(
            'Process completed. Updated: %d submissions, Skipped: %d submissions',
            $updated,
            $skipped
        ));
    }

    private function processSubmissions($surveyId, $photoKey, &$updated, &$skipped)
    {
        // Get submissions for this survey
        $submissions = RiskImprovementFormySubmissions::where('survey_id', (string)$surveyId)->get();

        foreach ($submissions as $submission) {
            try {
                if ($this->needsPhotoGraphKey($submission, $photoKey)) {
                    if (!$this->option('dry-run')) {
                        $this->updateSubmission($submission, $photoKey);
                    }
                    $updated++;
                    
                    $this->line(sprintf(
                        'Updated submission for survey ID: %s with key: %s',
                        $surveyId,
                        $photoKey
                    ));
                } else {
                    $skipped++;
                }
            } catch (\Exception $e) {
                $this->error(sprintf(
                    'Error processing submission for survey ID %s: %s',
                    $surveyId,
                    $e->getMessage()
                ));
            }
        }
    }

    private function needsPhotoGraphKey($submission, $photoKey)
    {
        // Check if the key exists in the submission data
        return !isset($submission->{$photoKey});
    }

    private function updateSubmission($submission, $photoKey)
    {
        // Add the photograph key with null value
        $submission->{$photoKey} = null;
        $submission->save();
        \Log::info('Survey ID: ' . $submission->survey_id . ' photo key updated to: ' . $photoKey);
    }
}