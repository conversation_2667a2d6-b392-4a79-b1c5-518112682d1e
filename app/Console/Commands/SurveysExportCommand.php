<?php

namespace App\Console\Commands;

use App\Exports\SurveysChunkedExport;
use App\Models\FileUpload;
use App\Models\Mailqueue;
use App\Services\QueueService;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;

class SurveysExportCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'surveys:export';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "This command will get the queue messages to process the export surveys.";

    public $queue;
    public $files;

    public function __construct(Mailqueue $mailqueue, FileUpload $fileupload)
    {
        $this->queue = $mailqueue;
        $this->files = $fileupload;
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $queueService = new QueueService(config('app.aws.surveys_export_sqs'));

        $messages = $queueService->fetchMessages();
        foreach ($messages as $message) {
            try {
                Log::info("[SurveysExportCommand]: " . $message['Body']);
                
                $body = json_decode($message['Body']);
                $requestArr = (array) $body->request;
                $requestArr['email'] = str_replace(' ', '+', $body->request->email ?? '');
                $myRequest = Request::create('surveys/all/1/10000', 'GET', $requestArr);
                $this->handleChunkedExport($myRequest);

                $queueService->deleteMessage($message['ReceiptHandle']);

                Log::info("[SurveysExportCommand]: Done");
                $this->info("[SurveysExportCommand]: Done");
            } catch (\Exception $e) {
                $queueService->deleteMessage($message['ReceiptHandle']);
                $queueService->sendRequeue($message);

                Log::error("[SurveysExportCommand:ERROR] " . $e->getMessage());
                Log::error("[SurveysExportCommand:ERROR] " . $e->getTraceAsString());
            }
        }
    }

    private function handleChunkedExport($request)
    {
        $isDtr = $request->get('survey_type') === 'dtr';

        // user info for the email
        $info = [
            'first_name' => $request->get('first_name'),
            'last_name'  => $request->get('last_name'),
            'email'      => $request->get('email'),
        ];

        $fullPath      = Storage::url('excel/exports/' . date('d-m-Y') . '-surveys-' . uniqid() . '.xlsx');
        $excelResponse = Excel::store(new SurveysChunkedExport($request), $fullPath);

        if ($excelResponse) {
            $excelKey      = Str::uuid()->toString();
            $pathForLookup = base_path('storage/app') . $fullPath;
            if ($this->files->upload_excel($pathForLookup, 'excel/exports/' . $excelKey . '.xlsx')) {
                $file = $this->files->link('excel/exports/' . $excelKey . '.xlsx', '2 days');

                // Send the email to the user who requested the surveys excel sheet download
                $this->queue->queue(
                    $info['email'], ($info['first_name'] . ' ' . $info['last_name']),
                    sprintf('Risk Reduce - %s Excel Export Ready', $isDtr ? 'Reports' : 'Surveys'),
                    'emails.surveys.excel-ready', [
                        'first_name'          => $info['first_name'],
                        'last_name'           => $info['last_name'],
                        'excel_download_link' => $file,
                        'isDtr'               => $isDtr,
                    ]
                );
            }
        }
    }
}
