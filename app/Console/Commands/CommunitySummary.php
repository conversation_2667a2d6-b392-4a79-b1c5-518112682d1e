<?php

namespace App\Console\Commands;

use App\Events\SummaryNotification;
use Illuminate\Console\Command;
use Illuminate\Support\Benchmark;

class CommunitySummary extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'riskreduce:community-summary
                            {--summary= : Either hourly or daily}
                            {--recipients= : List of string of recipients that is separated by comma to receive notification}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get all community messages for the last 24hrs';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        SummaryNotification::dispatch($this->option('summary'), $this->option('recipients'));
    }
}
