<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use App\Models\Mailqueue;
use App\Models\RiskRecommendationNotification;
use App\Models\Survey;
use App\Models\Organisation;
use App\Models\OrganisationPolicy;
use App\Models\UnderwriterPolicies;
use App\Models\LibertyUser;

class BrokerChaserCommand extends Command
{

    protected $mail;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notify:broker-chaser-notifications';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command chase brokers to notify recommendation already in deadline';


    public function __construct(Mailqueue $queue)
    {
        parent::__construct();
        $this->mail = $queue;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // hardcode a date to simulate future date (for testing purposes)
        $current_date_time = date("Y-m-d");

        $notifications  = RiskRecommendationNotification::whereNull('closed_at')->get();
        $notifications2 = RiskRecommendationNotification::whereNull('closed_at_tier_2')->get();
        $now = Carbon::parse($current_date_time);

        foreach ($notifications as $notification) {
            if (isset($notification->updated_deadline_at) && 
                !empty($notification->updated_deadline_at)) {

                $date = Carbon::parse($notification->updated_deadline_at);
                $last_chased_at = Carbon::parse($notification->last_chased_at);

                $diff = $date->diffInDays($now, false);
                $diff2 = $last_chased_at->diffInDays($now, false);

                if ($notification->chase_attempts==0 && $diff==1) {
                    $chase_attempts = $notification->chase_attempts + 1;
                    $this->emailChaserBroker($notification->survey_id, $now, $chase_attempts);
                }

                if ($notification->chase_attempts==1 && $diff2==7) {
                    $chase_attempts = $notification->chase_attempts + 1;
                    $this->emailChaserBroker($notification->survey_id, $now, $chase_attempts);
                }

                if ($notification->chase_attempts==2 && $diff2==14) {
                    $chase_attempts = $notification->chase_attempts + 1;
                    $this->emailChaserBroker($notification->survey_id, $now, $chase_attempts);
                }

                if ($notification->chase_attempts==3 && $diff2==7) {
                    $chase_attempts = $notification->chase_attempts + 1;
                    $this->emailChaserUnderwriter($notification->survey_id, $now, $chase_attempts);
                }
            }
        }

        foreach ($notifications2 as $notification2) {

            $schedule_meta = Survey::with('scheduleMeta')->where('id', $notification2->survey_id)->first();

            if(
                isset($schedule_meta->schedule_meta->key) &&
                !empty($schedule_meta->schedule_meta->key) &&
                $schedule_meta->schedule_meta->key=='actual_submission_deadline'
            ) {
                $actual_survey = Carbon::parse($schedule_meta->schedule_meta->value);
                $now = Carbon::parse($current_date_time);
                $diff3 = $actual_survey->diffInDays($now, false);
                if($diff3==90 && $notification2->tier_2_exist==1)
                {
                    $chase_attempts = $notification2->chase_attempts + 1;
                    $this->emailChaserBroker($notification2->survey_id, $now, $chase_attempts);
                }
            }
        }
    }

    private function emailChaserBroker($survey_id, $now, $chase_attempts)
    {
        $broker_set = [
            'organisation',
            'broker',
            'location',
            'contactsBroker'
        ];

        $brokers = Survey::with($broker_set)
            ->where('id', $survey_id)
            ->first();

        $organisation = Organisation::with('assign_brokers')
            ->where('id', $brokers->organisation_id)
            ->first();
            
        $email = '';
        if (!empty($organisation->assign_brokers)) {
            foreach ($organisation->assign_brokers as $assign_broker) {
                if (!empty($assign_broker->email)) {
                    $email                 = $assign_broker->email;
                    $first_name            = $assign_broker->first_name;
                    $last_name             = $assign_broker->last_name;
                    $organisation_name     = (isset($organisation->name) && !empty($organisation->name)) ? $organisation->name : '';
                    $organisation_location = (isset($brokers->location->location_name) && !empty($brokers->location->location_name)) ? $brokers->location->location_name : '';
                    $this->notify($email, $first_name, $last_name, $survey_id, $organisation_name, $organisation_location);
                }
            }
        }

        $survey_broker = $brokers->contactsBroker;
        // Broker SRF Level Notification
        if (
            isset($survey_broker[0]->email) &&
            !empty($survey_broker[0]->email) &&
            count($survey_broker)>0 &&
            ($email != $survey_broker[0]->email))
        {
            $email = $survey_broker[0]->email;
            $first_name = $survey_broker[0]->name;
            $last_name = '';
            $organisation_name = (isset($organisation_broker->name) && !empty($organisation_broker->name)) ? $organisation_broker->name : '';
            $organisation_location = (isset($brokers->location->location_name) && !empty($brokers->location->location_name)) ? $brokers->location->location_name : '';
            $this->notify($email, $first_name, $last_name, $survey_id, $organisation_name, $organisation_location);
        }

        RiskRecommendationNotification::where('survey_id', $survey_id)
            ->update([
                'chase_attempts' => $chase_attempts,
                'last_chased_at' => $now
            ]);
    }

    private function emailChaserUnderwriter($survey_id, $now, $chase_attempts)
    {
        $organisation_emails = [];
        $underwriter_set = ['organisation', 'underwriter'];

        $underwriters = Survey::with($underwriter_set)
            ->where('id', $survey_id)
            ->first();

        $policy = OrganisationPolicy::where('id', $underwriters->policy_id)->first();

        $underwriter_ids = UnderwriterPolicies::where('organisation_id', $underwriters->organisation_id)
            ->where('policy_id', $policy->policy_type_id)
            ->get();

        // Organisation Level Notiication
        foreach ($underwriter_ids as $underwriter_id) {
            $organisation_underwriter = LibertyUser::where('id', $underwriter_id->underwriter_id)->first();
            $email = $organisation_underwriter->email;
            $first_name = $organisation_underwriter->first_name;
            $last_name = $organisation_underwriter->last_name;
            $this->notifyUnderwriter($email, $first_name, $last_name, $survey_id);
            array_push($organisation_emails, $email);
        }

        if (!in_array($underwriters->underwriter->email, $organisation_emails)) {
            $email = $underwriters->underwriter->email;
            $first_name = $underwriters->underwriter->first_name;
            $last_name = $underwriters->underwriter->last_name;
            $this->notifyUnderwriter($email, $first_name, $last_name, $survey_id);
        }

        RiskRecommendationNotification::where('survey_id', $survey_id)
            ->update([
                'chase_attempts' => $chase_attempts,
                'last_chased_at' => $now
            ]);
    }

    private function notify($email, $first_name, $last_name, $survey_id, $organisation_name = '', $organisation_location = '')
    {
        $viewParameters['name'] = trim($first_name.' '.$last_name);
        $viewParameters['survey_id'] = $survey_id;
        $viewParameters['organisation_name'] = $organisation_name;
        $viewParameters['organisation_location'] = $organisation_location;

        $this->mail->queue(
            $email,
            trim($first_name.' '.$last_name),
            'Open Risk Recommendations',
            'emails.surveys.risk-rec-overdue',
            $viewParameters,
            null
        );
    }

    private function notifyUnderwriter($email, $first_name, $last_name, $survey_id)
    {
        $viewParameters['name'] = trim($first_name.' '.$last_name);
        $viewParameters['survey_id'] = $survey_id;

        $this->mail->queue(
            $email,
            trim($first_name.' '.$last_name),
            'Overdue Risk Recommendations',
            'emails.surveys.risk-rec-underwriter-overdue',
            $viewParameters,
            null
        );
    }
}
