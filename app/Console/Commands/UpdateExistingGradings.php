<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Http\Request;
use Exception;
use Illuminate\Support\Facades\DB;
use App\Models\RiskGrading\RgLocationGrading;
use App\Models\RiskGrading\RgLocationGradingLog;
use App\Models\RiskImprovementFormySubmissions;
use App\Http\Controllers\Api\V1\StandardRiskGradingController;


class UpdateExistingGradings extends Command
{
	/**
	 * The name and signature of the console command.
	 *
	 * @var string
	 */
	protected $signature = 'riskreduce:update-existing-gradings {submission_id?}';

	protected $request;

	protected $rgCalculator;

	public function __construct()
	{
		parent::__construct();
		$this->request = new Request();
		$this->rgCalculator = new StandardRiskGradingController($this->request);
	}

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Update main attribute of existing gradings ';

	/**
	 * Execute the console command.
	 *
	 * @return int
	 */
	public function handle()
	{
		$start = microtime(true);
		$submissions = [];
		$submission_id = $this->argument('submission_id');
		if ($submission_id == 'all') {
			$query = 'select * from tbl_temp_grading_calculate order by survey_id';
			$data = DB::select(DB::raw($query));
			$submissions = $data;
		} else {
			$sub = (object)[];
			$sub->submission_id = $submission_id;
			$submissions = [$sub];
		}

		$allToCalculateData = $this->getDataForSubmissionGradingCalculation($submissions);

		$submissions = [];
		$total = count($allToCalculateData);
        $counter = 1;
		$this->info("Total Srfs: {$total}");

		foreach ($allToCalculateData as $survey_id => $allGradings) {
			$this->info("Calculating Grading for survey: {$survey_id}, {$counter} of {$total} surveys");
			\Log::info("Calculating Grading for survey: {$survey_id}, {$counter} of {$total} surveys");
			$data = (object)[];
			$data->survey_id = $survey_id;
			$data->submission_id = $allGradings['submission_id'];
			$data->attributes = [];
			unset($allGradings['submission_id']);

			foreach ($allGradings as $key => $gradings) {
				$attributeData = (object)[];
				$request = new \Illuminate\Http\Request($gradings);
				$response = $this->rgCalculator->calculateRiskGrading($request);
				if (isset($response)) {
					$output = $response->getData();

					$attributeData->attribute_key = str_replace("forced_hidden_value","",$key);
					$attributeData->attribute_name = $output->attribute_name;
					$attributeData->value = $output->banding_output;
					if($attributeData->value != 'Not Applicable / Not Assessed'){
						($data->attributes)[] = $attributeData;
					}
				} else {
					$this->info('Srf using old form:' . PHP_EOL);
					\Log::info('Srf using old form:' . PHP_EOL);
					\Log::info(print_r($data));
				}
			}
			if(isset($data->attributes) && count($data->attributes) > 0){
				$submissions[] = $data;
			}
			
			$counter++;
		}
	
		$counter = 1;
		$total = count($submissions);
		$this->info("Total Submissions to update: {$total}");

		foreach ($submissions as $submission) {
			$data = [];
			foreach ($submission->attributes as $attribute) {
				$data[$attribute->attribute_key] = $attribute->value;
			}

			$this->info("Updating Grading for submission: {$submission->submission_id}, {$counter} of {$total} submissions");
			\Log::info("Updating Grading for submission: {$submission->submission_id}, {$counter} of {$total} submissions");

			$submissionData = RiskImprovementFormySubmissions::find($submission->submission_id);
			$this->updateGradings($submissionData, $data);
			$counter++;
		}

		$end = microtime(true);
        $time = number_format(($end - $start), 2);

		$this->info("Done processing {$total} submissions in {$time} sec");
		\Log::info("Done processing {$total} submissions  in {$time} sec");
	}

	private function updateGradings($submissionData, $data){
		$sub = tap($submissionData)->update($data);
		$sub = (array)(json_decode($sub));
		RgLocationGrading::updateLocationGradings($sub);
		RgLocationGradingLog::createLocationGradingLog($sub);
	}

	private function getDataForSubmissionGradingCalculation($submissions)
	{
		$allCalculatedData = [];
		foreach ($submissions as $sub) {
			$submission = null;

			if (isset($sub->survey_id)) {
				$submission = RiskImprovementFormySubmissions::where('survey_id', "{$sub->survey_id}")->first();
			}
			if (isset($sub->submission_id)) {
				$submission = RiskImprovementFormySubmissions::find($sub->submission_id);
			}

			if (isset($submission)) {
				$survey_id = (int)$submission->survey_id;
				$submission = json_decode($submission);
				$submission = (array)$submission;
				$allGradings = $this->getGradingsFromSubmission($submission);
				$allCalculatedData[$survey_id] = $allGradings;
			}
		}

		return $allCalculatedData;
	}

	private function getGradingsFromSubmission(array $submission){

        $patternAttr = '/(attr)-\w+/';
        $patternSubattr = '/(sub-attribute)-\w+/';

        $all_gradings=[];

        foreach ($submission as $key => $value) {
            if(preg_match($patternAttr, $key)){
                if(!isset($all_gradings[$key])){
                    $all_gradings[$key]=[];
                }
                $parts=explode("-", $key);
                $all_gradings[$key]['attribute_name']=str_replace("_", " ", $parts[1]);
                $all_gradings[$key]['policy_type_id']=end($parts);
            }

            if(preg_match($patternSubattr, $key)){
                $skey='attr-'.substr($key, strpos($key, "_") + 1);
                if(!isset($all_gradings[$skey])){
                    $all_gradings[$skey]=[];
                }
                $key=substr_replace($key, '|', strpos($key, '_'), 1);
                $all_gradings[$skey][$key]=$value;
            }
        }
        $all_gradings['submission_id']=$submission['_id'];
        return $all_gradings;
    }
}
