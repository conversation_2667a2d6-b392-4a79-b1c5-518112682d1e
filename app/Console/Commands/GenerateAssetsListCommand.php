<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use App\Jobs\Previsico\GenerateAssetsList;

class GenerateAssetsListCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $signature = 'previsico:generate_assets {--force}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command checks the asset lists queue and generates the latest assets list for previsico if there is a message in the queue.';

    /**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
        	$this->jobHandler = App::make(GenerateAssetsList::class);
	}

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->jobHandler->run($this->option('force', false));
    }

    /**
     * Get the console command arguments.
     *
     * @return array
     */
    protected function getArguments()
    {
        return [];
    }

    /**
     * Get the console command options.
     *
     * @return array
     */
    protected function getOptions()
    {
        return [];
    }
}
