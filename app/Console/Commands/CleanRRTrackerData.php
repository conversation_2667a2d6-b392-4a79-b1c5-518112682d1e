<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SRGLog;
use Illuminate\Support\Facades\Config;
use Aws\Laravel\AwsFacade as AWS;

class CleanRRTrackerData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'riskreduce:clean-rr-tracker-data-sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync to kanban after Clean RR Tracker Data and Close';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $submissions=SRGLog::where([['is_closed', '=', '0'],['submission_id', '<>', 'na']])->distinct()->get(['submission_id','survey_id']);
        $surveys=[];
        foreach($submissions as $submission){
            $surveys[]=(object)$submission;
        }
        $this->sendMessages($surveys);
    }

    public function sendMessages($surveys)
    {
        foreach ($surveys as $survey) {
            AWS::createClient('Sqs')->sendMessage([
                'QueueUrl'          => Config::get('app.aws.sqs_kanban_sync_queue'),
                'MessageBody'       => json_encode($survey),
                'MessageAttributes' => [],
                'DelaySeconds'      => 2
            ]);
        }
    }
}
