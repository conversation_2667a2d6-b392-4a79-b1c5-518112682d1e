<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Console\Input\InputArgument;
use App\Models\PublicFormySubmissions;
class MapChartableFields extends Command 
{
	/**
	 * The console command name.
	 *
	 * @var string
	 */
    protected $name = 'map-chartable-fields';
    
	/**
	 * The console command description.
	 *
	 * @var string
	 */
    protected $description = 'Map Chartable fields in submissions';
    
	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */

	protected $fields = [
		'5f7d98980e73aa1cfb4d2709'=>[
			'accident-category'=>[
				'injury-to-third-party-1600370651'=>'Injury to third party' , 
				'third-party-property-damage-1600370669'=>'Third party property damage'
			],			
			'name-1621341848'=>'injured-person-name',
			//'time-1600434946'=>'time-period-of-accident',
		],
		'5f7d98260e73aa1cfb4d20c7'=>[
			'accident-category'=>[
				'injury-to-member-1621342188'=>'Injury to member' , 
				'injury-to-volunteer-1621342245'=>'Injury to volunteer',
				'injury-to-third-party-1621342271'=>'Injury to third party',
				'third-party-property-damage-1621342286'=>'Third party property damage',
				'injury-to-animal-1621342308'=>'Injury to animal'
			],			
			'branch-person-is-from-or-connected-to-1636729947'=>'branch',
			'name-1621342028'=>'injured-person-name',
			//time-1601545710->time-period-of-accident
		],
		'60c0c99c0963d07e20bec8ed'=>[
			'accident-category'=>[
				'injury-to-third-party-1623245369'=>'Injury to third party' , 
				'third-party-property-damage-1623245390'=>'Third party property damage'
			],
			'name-1623245736'=>'injured-person-name',
			'date-1623246028'=>'date-of-accident',
			//time-ampm-1629277602->time-period-of-accident,
			'where-did-the-accident-happen'=>[
				'british-eventing-affiliated-event-1623246228'=>'British Eventing Affiliated Event' , 
				'unaffiliated-event-1623246240'=>'Unaffiliated Event',
				'field-1623246251'=>'Field',
				'indoor-arena-1623246263'=>'Indoor Arena',
				'outdoor-arena-1623246274'=>'Outdoor Arena',
				'stable-area-1623246285'=>'Stable area',
				'public-area-1623246297'=>'Public area',
				'parking-area-1623246308'=>'Parking area',
				'other-1623246320'=>'Other',
			],
		],
		'60c0cfd50963d07f20bec8ee'=>[
			'accident-type-1648036361'=>'accident-category',
			'name-1623248276'=>'injured-person-name',
			'date-1623248538'=>'date-of-accident',
			//time-ampm-1629278408->time-period-of-accident,
			'where-did-the-accident-occur--1623248775'=>'where-did-the-accident-happen',
		],		
	];



	public function __construct()
	{
		parent::__construct();		
    }
    
	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{
		$this->ImportMapSubmissions();
    }
    
	/**
	 * Get the console command arguments.
	 *
	 * @return array
	 */
	protected function getArguments()
	{
		return array(
			['form_id', InputArgument::REQUIRED, 'form id for submissions'],
		);
    }
    
	/**
	 * Get the console command options.
	 *
	 * @return array
	 */
	protected function getOptions()
	{
		return array(
			
		);
	}

	private function ImportMapSubmissions()
	{	
		$form_id = $this->argument('form_id');
		$field=$this->fields[$form_id];
		$formSubmissions = PublicFormySubmissions::where('form_id', $form_id)
                ->get();

		
		foreach ($formSubmissions as $submission) {
			$accident_category=[];
			print_r($submission->_id);
			echo "\n";
			echo "\n";
			foreach ($field as $key => $value) {
				if($key=='accident-category'){					
					$cat=$field[$key];
					foreach ($cat as $k => $v) {
						if($submission[$k]==1){
							array_push($accident_category,$v);
						}
					}
					$submission['accident-category']=$accident_category;
				}

				if($key=='where-did-the-accident-happen'){					
					$acc=$field[$key];
					foreach ($acc as $kk => $vv) {
						if($submission[$kk]==1){							
							$submission['where-did-the-accident-happen']=$vv;
						}
					}
					
				}
				
				$val=$submission[$key];
				if($val != ''){
					if (strpos($key, 'name-') !== false) {
						$submission[$value]=$val;
						unset($submission[$key]);
						PublicFormySubmissions::where('form_id', $form_id)->unset($key);												
					}

					if (strpos($key, 'branch-person-is-from-or-connected-to-') !== false) {
						$submission[$value]=$val;
						//unset($submission[$key]);
						//PublicFormySubmissions::where('form_id', $form_id)->unset($key);
					}
					if (strpos($key, 'date-') !== false) {
						$submission[$value]=$val;
						unset($submission[$key]);
						PublicFormySubmissions::where('form_id', $form_id)->unset($key);						
					}
					if (strpos($key, 'accident-type-') !== false) {
						$submission[$value]=$val;
						unset($submission[$key]);
						PublicFormySubmissions::where('form_id', $form_id)->unset($key);						
					}
					if (strpos($key, 'where-did-the-accident-occur-') !== false) {
						$submission[$value]=$val;
						unset($submission[$key]);
						PublicFormySubmissions::where('form_id', $form_id)->unset($key);
					}
				}				
			}
			$submission->save();
		}
		print_r('Process Complete..');
		echo "\n";
	}
}
