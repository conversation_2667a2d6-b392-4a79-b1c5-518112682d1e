<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AccessGroup\ArchiveOrganisationUsersService;
use App\Models\Organisation;

class AccessGroupArchiveUsersCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'access-group:archive-users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "This command will check the users' end_policy_date and archive them. This should run everyday at 12 midnight";

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $handleArchive = new ArchiveOrganisationUsersService(Organisation::class);
        $handleArchive->archiveUsers();
    }
}
