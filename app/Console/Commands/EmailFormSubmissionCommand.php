<?php

namespace App\Console\Commands;

use App\Models\BrokerUser;
use App\Models\FormSubmissionsMapping;
use App\Models\FormySubmissions;
use App\Models\LibertyUser;
use Illuminate\Console\Command;
use App\Models\Mailqueue;
use App\Models\PublicFormySubmissions;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use App\Models\User;
use Illuminate\Support\Facades\Log;
class EmailFormSubmissionCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'riskreduce:email-form-submission-report
                            {formType : Either formyPublic (public forms) or formy( for protected or private forms) value}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will send a daily report for assigned notifications.';

    protected Mailqueue $mail;


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->mail = new Mailqueue();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $fromDate = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d'.' 00:00:00', strtotime('yesterday')));
        $toDate   = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d'.' 23:59:59', strtotime('yesterday')));

        Log::info($fromDate);
        Log::info($toDate);

        $formType               = $this->argument('formType');
        $start                  = $fromDate;
        $stop                   = $toDate;
        $all_submission_mapping = [];

        if ($formType==='formyPublic') {            
            $formSubmissions = PublicFormySubmissions::with($formType, 'organisationUsers', 'organisation', 'organisation.assign_brokers')
            ->whereDate('created_at', '>=', $start->format('Y-m-d H:i:s'))->whereDate('created_at', '<=', $stop->format('Y-m-d H:i:s'))
            ->get();

            // Holds admin side linked emails
            $emailArr               = [];
            // Holds client side linked emails
            $emailArrClient         = [];
            // Holds broker checkbox emails with admin link
            $brokerEmailArr         = [];
            // Holds Org's Client Users email with client link
            $orgClientUserEmails    = [];

            foreach ($formSubmissions as $value) {
                $action = '';
                try{
                    if ($value->formyPublic['notify_liberty_admin']==="1") {
                        $action = 'Notify Liberty Admin';
                        $libertyUsers = LibertyUser::where('activated', '=', 1)
                            ->where('role', '=', 'admin')
                            ->get();
                        
                        foreach ($libertyUsers as $libertyUser) {
                            $emailArr[$libertyUser->email][$value->formyPublic['name']][] = $value->_id;
                            echo $this->info('submissions #:'.$value->_id.' '.$value->form_id.' notifications:'.$libertyUser->email.' form name:'.$value->formyPublic['name']);
                        }

                        echo "Public Notify Liberty Admin --end--";
                        echo "\n\n";
                    }

                    if ($value->formyPublic['notify_group_user']==="1") {
                        $action = 'Notify group user';
                        foreach ($value->organisationUsers as $organisationUser) {
                            if ($organisationUser->manager===1) {
                                $orgClientUserEmails[$organisationUser->email][$value->formyPublic['name']][] = $value->_id;
                                echo $this->info('submissions #:'.$value->_id.' '.$value->form_id.' notifications:'.$organisationUser->email.' form name:'.$value->formyPublic['name']);
                            }
                        }
                        echo "Public Notifications Group User --end--";
                        echo "\n\n";
                    }
    
                    if ($value->formyPublic['notify_broker'] === '1') {
                        $action = 'Notify broker';
                        $contactBrokers = $value->organisation->assign_brokers;
                        if (!empty($contactBrokers->count())) {
                            foreach ($contactBrokers as $contactBroker) {
                                $brokerEmail = $contactBroker->email;
                                if (empty($brokerEmail)) {
                                    $brokerEmail = $contactBroker->brokerUser->email;
                                }
                                $brokerEmailArr[trim($brokerEmail)][$value->formyPublic['name']][] = $value->_id;
                                echo $this->info('submissions #:' . $value->_id . ' ' . $value->form_id . ' notifications:' . $brokerEmail . ' form name:' . $value->formyPublic['name']);
                            }
                        }

                        echo "Public Notifications Broker --end--";
                        echo "\n\n";
                    }

                    if ($value->formyPublic['notifications']) {
                        $action = 'notifications';

                        $emails = explode(',', $value->formyPublic['notifications']);
                        
                        foreach ($emails as $email) {
                            $emailArr[trim($email)][$value->formyPublic['name']][] = $value->_id;                        
                        }
                    }

                    if ($value->formyPublic['notifications_admin_users']) {
                        $action = 'Notifications Admin Users';

                        $emails = explode(',', $value->formyPublic['notifications_admin_users']);
                        
                        foreach ($emails as $email) {
                            $emailArr[trim($email)][$value->formyPublic['name']][] = $value->_id;                        
                        }
                    }
                    
                    if ($value->formyPublic['notifications_client_users']) {
                        $action = 'Notifications Client Users';

                        $emails = explode(',', $value->formyPublic['notifications_client_users']);
                        
                        foreach ($emails as $email) {
                            $emailArrClient[trim($email)][$value->formyPublic['name']][] = $value->_id;                        
                        }
                    }

                    echo "Public Notifications --end--";
                    echo "\n\n";
                }catch(\Exception $e){
                    Log::info("ERROR_EMAIL_FORM_SUBMISSION_NOTIFICATION : " . $value->_id . $e->getMessage());
                    Log::info("ACTION : " . $action);
                    continue;  
                }
            }
        } else {

			$formSubmissions = FormySubmissions::with($formType, 'organisationUsers', 'organisation', 'organisation.assign_brokers')
                ->whereBetween('updated_at', array($start, $stop))
                ->where('submitted', '=', '1')
                ->has($formType)
				->get();
        
            $emailArr = [];
            $emailArrClient = [];

            foreach ($formSubmissions as $value) {
                try{
                    if ($value->formy['notify_liberty_admin']==="1") {
                        $libertyUsers = LibertyUser::where('activated', '=', 1)
                            ->where('role', '=', 'admin')
                            ->get();
                        
                        foreach ($libertyUsers as $libertyUser) {
                            $emailArr[$libertyUser->email][$value->formy['name']][] = $value->_id;
                            echo $this->info('submissions #:'.$value->_id.' '.$value->form_id.' notifications:'.$libertyUser->email.' form name:'.$value->formy['name']);
                        }
                        echo "Notify Liberty Admin --end--";
                        echo "\n\n";
                        
                    }

                    if ($value->formy['notify_group_user']==="1") {
                        foreach ($value->organisationUsers as $organisationUser) {
                            if ($organisationUser->manager===1) {
                                $emailArrClient[$organisationUser->email][$value->formy['name']][] = $value->_id;
                                echo $this->info('submissions #:'.$value->_id.' '.$value->form_id.' notifications:'.$organisationUser->email.' form name:'.$value->formy['name']);
                            }
                        }
                        echo "Notifications Group User --end--";
                        echo "\n\n";
                    }

                    if ($value->formy['notify_broker'] === '1') {
                        $contactBrokers = $value->organisation->assign_brokers;
                        if (!empty($contactBrokers->count())) {
                            foreach ($contactBrokers as $contactBroker) {
                                $brokerEmail = $contactBroker->email;
                                if (empty($brokerEmail)) {
                                    $brokerEmail = $contactBroker->brokerUser->email;
                                }
                                $emailArrClient[trim($brokerEmail)][$value->formy['name']][] = $value->_id;
                                echo $this->info('submissions #:' . $value->_id . ' ' . $value->form_id . ' notifications:' . $brokerEmail . ' form name:' . $value->formy['name']);
                            }
                        }

                        echo "Notifications Broker --end--";
                        echo "\n\n";
                    }

                    if ($value->formy['notifications']) {
                        $emails = explode(',', $value->formy['notifications']);
                    
                        foreach ($emails as $email) {
                            $emailArr[trim($email)][$value->formy['name']][] = $value->_id;
                            echo $this->info('submissions #:'.$value->_id.' '.$value->form_id.' notifications:'.trim($email).' form name:'.$value->formy['name']);
                        }
                    }

                    if ($value->formy['notifications_admin_users']) {
                        $emails = explode(',', $value->formy['notifications_admin_users']);
                        
                        foreach ($emails as $email) {
                            $emailArr[trim($email)][$value->formy['name']][] = $value->_id;                        
                        }
                    }
                    
                    if ($value->formy['notifications_client_users']) {
                        $emails = explode(',', $value->formy['notifications_client_users']);
                        
                        foreach ($emails as $email) {
                            $emailArrClient[trim($email)][$value->formy['name']][] = $value->_id;                        
                        }
                    }
                    echo "Notifications --end--";
                    echo "\n\n";
                }catch(\Exception $e){
                    \Log::info("ERROR_EMAIL_FORM_SUBMISSION_NOTIFICATION : " . $value->_id . $e->getMessage());
                    continue;  
                }
            }
        }

        // Normal notifications and liberty admin notification
        $this->sendAdminNotifications($all_submission_mapping, $emailArr);
        $this->sendAdminNotifications($all_submission_mapping, $brokerEmailArr, true);
        
        // Send client notifications
        $this->sendClientNotification($all_submission_mapping, $emailArrClient);
        $this->sendClientNotification($all_submission_mapping, $orgClientUserEmails);

        if (isset($all_submission_mapping) && !empty($all_submission_mapping)) {
            $result = call_user_func_array('array_merge', $all_submission_mapping);
            FormSubmissionsMapping::insert($result);
        }
    }

    public function sendAdminNotifications(
        array &$all_submission_mapping,
        array $emailArr = [],
        bool $isBroker = false
    ): void {
        if (empty($emailArr)) {
            return;
        }

        $formType = $this->argument('formType');
        $now = Carbon::now('utc')->toDateTimeString();
        foreach ($emailArr as $key => $forms) {
            foreach ($forms as $key2 => $submissions) {
                $adminSubmissions = [];
                foreach ($submissions as $submission) {
                    $adminSubmissions[] = [
                        'submission_id' => $submission,
                        'mapping_id' => substr(Str::uuid()->toString(), 0, 4),
                        'created_at' => $now,
                        'updated_at' => $now,
                    ];
                }
                $all_submission_mapping[] = $adminSubmissions;

                $this->mail->notify(
                    $key,
                    'Risk Reduce - New submissions for today',
                    'Risk Reduce - New submissions for today',
                    'emails.forms.new_form_notification',
                    [
                        'forms' => $key2,
                        'is_client' => false,
                        'submissions' => $adminSubmissions,
                        'app_url' => config('app.admin_frontend'),
                        'broker_user_type' => $isBroker ? '?user-type=broker-user' : '',
                        'form_type' => ($formType == 'formyPublic') ? 'public' : '',
                        'user_name' => $this->getUserDetils($key, 'admin'),
                    ]
                );
            }
        }
    }
    
    public function sendClientNotification(array &$all_submission_mapping, array $emailArr = []): void
    {
        if (empty($emailArr)) {
            return;
        }

        $formType = $this->argument('formType');
        $now = Carbon::now('utc')->toDateTimeString();
        // Client notifications
        foreach ($emailArr as $key => $forms) {
            foreach ($forms as $key2 => $submissions) {
                $clientSubmissions=[];
                foreach ($submissions as $submission) {
                    $clientSubmissions[] = [
                        'submission_id' => $submission, 'mapping_id' => Str::uuid()->toString(),
                        'created_at' => $now, 'updated_at' => $now
                    ];  
                }
                $all_submission_mapping[]=$clientSubmissions;

                $this->mail->notify(
                    $key,
                    'Risk Reduce - New submissions for today',
                    'Risk Reduce - New submissions for today',
                    'emails.forms.new_form_notification',
                    [
                        'forms'       => $key2,
                        'is_client'   => true,
                        'submissions' => $clientSubmissions,
						'app_url'     => config('app.client_frontend'),
                        'form_type'   => ($formType=='formyPublic') ? 'public' : '',
                        'user_name'   => $this->getUserDetils($key,'client'),
                    ]
                );
            }
        }
    }

    public function getUserDetils($email,$type){
        $user     = (object)[];
        $userName = '';
        if($type == 'client') {
            $clientuser = User::where('email',$email)->first();
        } else {
            $clientuser = LibertyUser::where('email',$email)->first();
        }

        if (!$clientuser) {
            $clientuser = BrokerUser::where('email',$email)->first();
        }
        
        if (isset($clientuser)) {
            $user->first_name=$clientuser->first_name;
            $user->last_name=$clientuser->last_name;
            $userName=$user->first_name.' '.$user->last_name;
        }

        return $userName;
    }
}
