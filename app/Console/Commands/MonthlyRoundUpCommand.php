<?php

namespace App\Console\Commands;

use App\Models\Link;
use App\Models\Formy;
use App\Models\Course;
use App\Models\Document;
use App\Models\Mailqueue;
use App\Models\Organisation;
use App\Models\Documentlevels;
use Illuminate\Console\Command;
use App\Models\OrganisationReport;
use App\Models\PolicyDocumentModel;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use Aloha\Twilio\Twilio;
use App\Models\Cms;
use Twilio\Rest\Client;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class MonthlyRoundUpCommand extends Command {

	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'notify:client-monthly-notification';
	private $twilio;
	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Send a latest updates from Risk Reduce';
	const ALERT_RECIPIENTS = ['+447576904915', '+34640684561']; //ERWIN AND MARIA
	const ALERT_MESSAGE = 'ALERT: Monthly Client Notification - there was an error sending out emails to users.';
	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
		$this->mail = new Mailqueue();
		$twilioConfig = Config::get('app.twilio.sms');        
    	$this->twilio = new Twilio($twilioConfig['sid'], $twilioConfig['token'], $twilioConfig['from']);
	}

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{
		try{
		// Previous month first day 00:00:00
		$start = date("Y-m-d H:i:s",mktime(0,0,0,date("m", strtotime(date('M Y')))-1,1,date("Y", strtotime(date('M Y')))));

		// Previous month last day 23:59:59
		$end = date("Y-m-t 23:59:59", strtotime($start));
		$notifications[]  = $this->riskGuidanceLink($start, $end);
		$notifications[]  = $this->libertyReport($start, $end);
		$notifications[]  = $this->riskGuidanceNotification($start, $end);
		$notifications[]  = $this->courseNotification($start, $end);
		$notifications[]  = $this->categorisedFormsNotification($start, $end, 'protected');
		$notifications[]  = $this->lossLessonNotification($start, $end);
		$notifications[]  = $this->policyDocumentNotification($start, $end);
		$notifications[]  = $this->getThoughLeadershipArticles($start, $end);
		$notifications[]  = $this->getNewsArticles($start, $end);
		$this->notify($notifications);
		$this->info('[' . date('Y-m-d H:i:s') . '] > Done');
		}catch(\Exception $e){
			foreach(self::ALERT_RECIPIENTS as $recipient){
				$this->twilio->message(
					$recipient,
					self::ALERT_MESSAGE,
					[],
				);
			}
			report($e);
		}
	}

	private function riskGuidanceLink($start, $end)
	{
		$this->info('[' . date('Y-m-d H:i:s') . '] > Executing Risk Grading Links Notification');

		$data = [];

		$riskGuidanceLinks = Link::with('sectors')
			->where('created_at', '>=', $start)
			->where('created_at', '<=', $end)
			->orWhere(function($query) use( $start, $end) {
				return $query
						->where('updated_at', '>=', $start)
						->where('updated_at', '<=', $end);

			})
			->orderBy('created_at', 'DESC')
			->get();

		foreach ($riskGuidanceLinks as $riskGuidanceLink)
		{
			$title 		= $riskGuidanceLink->name;
			$recipients = [];
			$created_at = $riskGuidanceLink->created_at->copy()->format('Y-m-d');
			$updatedAt = $riskGuidanceLink->updated_at->copy()->format('Y-m-d');
			$sectors = [];

			foreach ($riskGuidanceLink->sectors as $riskGuidanceLinkSector) {
				$sectors[] = $riskGuidanceLinkSector->sector_id;
			}

			$levels = Documentlevels::select(['level_name'])
				->where('level_id', $riskGuidanceLink->level1_type)
				->orWhere('level_id', $riskGuidanceLink->level3_type)
				->orderby('level_id', 'asc')
				->get();

			$link = Config::get('app.client_frontend') . '/risk_guidance';
			foreach ($levels as $level) {
				$link .= '/' . str_replace(' ', '+', $level->level_name);
			}

			$organisations = Organisation::with('users')
				->whereIn('sector', $sectors)
				->get();

			foreach ($organisations as $organisation) {
				if (isset($organisation->users) && !empty($organisation->users) && count($organisation->users) > 0) {
					foreach ($organisation->users as $user) {
						$recipients[] = trim($user->first_name) . ',' . trim($user->last_name) . ',' . $user->email;
					}
				}
			}

			if (count($recipients) > 0) {
				while (true) {
					$uniqueId = '/' . md5(uniqid(rand(), true));
					$timeKey  = strtotime($riskGuidanceLink->created_at->copy()->format('Y-m-d H:i:s')) . $uniqueId;

					$newKey = strtotime($riskGuidanceLink->created_at->copy()->addSeconds(1)->format('Y-m-d H:i:s')) . $uniqueId;
					$newKey2 = strtotime($riskGuidanceLink->updated_at->copy()->addSeconds(1)->format('Y-m-d H:i:s')) . $uniqueId;

					if (strtotime($updatedAt) > strtotime($created_at)) {
						$created_at = $updatedAt;
						$newKey = $newKey2;
					}

					if (!array_key_exists($timeKey, $data)) {
						$data[$timeKey] = [
							'content_type' => 'Risk Guidance:',
							'title' 	   => $title,
							'created_at'   => $created_at,
							'recipients'   => $recipients,
							'link'		   => $link,
						];
						break;
					}

					$data[$newKey] = [
						'content_type' => 'Risk Guidance:',
						'title'		   => $title,
						'link'		   => $link,
						'recipients'   => $recipients,
						'created_at'   => $created_at,
					];
					break;
				}
			}
		}

		return $data;
	}

	private function lossLessonNotification($start, $end)
	{
		$this->info('[' . date('Y-m-d H:i:s') . '] > Executing Loss Lesson Notification');

		$data = [];

		$lossLessons = Document::where('created_at', '>=', $start)
			->where('created_at', '<=', $end)
			->where('category', 'LOSSLESSON')
			->orWhere(function($query) use($start, $end)
			{
				return $query
					->where('updated_at', '>=', $start)
					->where('updated_at', '<=', $end)
					->where('category', 'LOSSLESSON');
			})
			->orderBy('created_at', 'DESC')
			->get();

		foreach ($lossLessons as $lossLesson) {

			$title 		= $lossLesson->name;
			$recipients = [];
			$created_at = $lossLesson->created_at->copy()->format('Y-m-d');
			$updatedAt = $lossLesson->updated_at->copy()->format('Y-m-d');
			$link 		= Config::get('app.client_frontend') . '/loss-lesson';
			$sectors = [];

			foreach (($lossLesson?->document_sectors ?? []) as $documentSector) {
				$sectors[] = $documentSector->sector_id;
			}

			$organisations = Organisation::with('users')
				->whereIn('sector', $sectors)
				->get();

			foreach ($organisations as $organisation) {
				if (isset($organisation->users) && !empty($organisation->users) && count($organisation->users) > 0) {
					foreach ($organisation->users as $user) {
						$recipients[] = trim($user->first_name) . ',' . trim($user->last_name) . ',' . $user->email;
					}
				}
			}

			if (count($recipients) > 0) {
				while (true) {
					$uniqueId = '/' . md5(uniqid(rand(), true));
					$timeKey  = strtotime($lossLesson->created_at->copy()->format('Y-m-d H:i:s')) . $uniqueId;

					$newKey = strtotime($lossLesson->created_at->copy()->addSeconds(1)->format('Y-m-d H:i:s')) . $uniqueId;
					$newKey2 = strtotime($lossLesson->updated_at->copy()->addSeconds(1)->format('Y-m-d H:i:s')) . $uniqueId;

					if (strtotime($updatedAt) > strtotime($created_at)) {
						$created_at = $updatedAt;
						$newKey = $newKey2;
					}

					if (!array_key_exists($timeKey, $data)) {
						$data[$timeKey] = [
							'content_type' => 'Loss Lesson:',
							'title' 	   => $title,
							'created_at'   => $created_at,
							'recipients'   => $recipients,
							'link' 		   => $link
						];
						break;
					}

					$data[$newKey] = [
						'content_type' => 'Loss Lesson:',
						'title'		   => $title,
						'link'		   => $link,
						'recipients'   => $recipients,
						'created_at'   => $created_at,
					];
					break;
				}
			}
		}

		return $data;
	}

	/**
	 * Handles the risk guidance notification
	 *
	 * @param string $start Start Date
	 * @param string $end End Date
	 * @return array
	 */
	private function riskGuidanceNotification($start, $end)
	{
		$this->info('[' . date('Y-m-d H:i:s') . '] > Executing Risk Guidance Notification');

		$documents = Document::where('created_at', '>=', $start)
			->where('created_at', '<=', $end)
			->whereNull('deleted_at')
			->where('category', '')
			->orWhere(function($query) use( $start, $end) {
				return $query
					->where('updated_at', '>=', $start)
					->where('updated_at', '<=', $end)
					->whereNull('deleted_at')
					->where('category', '');
			})
			->orderBy('created_at', 'desc')
			->get();

		$data  = [];
		foreach ($documents as $document) {
			$sectorIds = $document->documentSectors->map(function ($documentSector) {
				return $documentSector->sector_id;
			})->toArray();

			$levels = Documentlevels::select(['level_name'])
						->where('level_id', $document->level1_type)
						->orWhere('level_id', $document->level3_type)
						->orderby('level_id', 'asc')
						->get();

			$link = Config::get('app.client_frontend') . '/risk_guidance';
			foreach ($levels as $level) {
				$link .= '/' . str_replace(' ', '+', $level->level_name);
			}

			$organisations = Organisation::whereIn('sector', $sectorIds)->get();
			foreach ($organisations as $organisation) {
				$orgUsers   = $organisation->users;
				$recipients = [];
				foreach ($orgUsers as $user) {
					$recipients[] = trim($user->first_name) . ',' . trim($user->last_name) . ',' . $user->email;
				}

				if (!empty($recipients)) {
					while (true) {
						$uniqueId = '/' . md5(uniqid(rand(), true));
						$timeKey  = strtotime($document->created_at->format('Y-m-d H:i:s')) . $uniqueId;
						$newKey = strtotime($document->created_at->copy()->addSeconds(1)->format('Y-m-d H:i:s')) . $uniqueId;
						$newKey2 = strtotime($document->updated_at->copy()->addSeconds(1)->format('Y-m-d H:i:s')) . $uniqueId;

						$createdAt =  $document->created_at->format('Y-m-d');
						$updatedAt =  $document->updated_at->format('Y-m-d');

						if (strtotime($updatedAt) > strtotime($createdAt)) {
							$createdAt = $updatedAt;
							$newKey = $newKey2;
						  }

						if (!array_key_exists($timeKey, $data)) {
							$data[$timeKey] = [
								'content_type' => 'Risk Guidance:',
								'title'		   => $document->name,
								'link'		   => $link,
								'recipients'   => $recipients,
								'created_at'   => $createdAt,
							];
							break;
						}

						$data[$newKey] = [
							'content_type' => 'Risk Guidance:',
							'title'		   => $document->name,
							'link'		   => $link,
							'recipients'   => $recipients,
							'created_at'   => $createdAt,
						];

						break;
					}
				}
			}
		}

		return $data;
	}

	/**
	 * Handles the liberty report notification
	 *
	 * @param string $start
	 * @param string $end
	 * @return array
	 */
	private function libertyReport($start, $end)
	{
		$this->info('[' . date('Y-m-d H:i:s') . '] > Liberty Report Notification');
		$reports = OrganisationReport::where('created_at', '>=', $start)
			->where('created_at', '<=', $end)
			->whereNull('deleted_at')
			->where('visibility', 'external')
			->orWhere(function($query) use( $start, $end) {
				return $query
					->where('updated_at', '>=', $start)
					->where('updated_at', '<=', $end)
					->whereNull('deleted_at')
					->where('visibility', 'external');
			})
			->orderBy('created_at', 'desc')
			->get();

		$data = [];
		foreach ($reports as $report) {
			$orgUsers 	= $report->organisation->users;
			$recipients = [];
			foreach ($orgUsers as $user) {
				if(isset($user->activated) && $user->activated){
					$recipients[] = trim($user->first_name) . ',' . trim($user->last_name) . ',' . $user->email;
				}
			}

			if (!empty($recipients)) {
				while (true) {
					$uniqueId = '/' . md5(uniqid(rand(), true));
					$timeKey = strtotime($report->created_at->format('Y-m-d H:i:s')) . $uniqueId;
					$newKey = strtotime($report->created_at->copy()->addSeconds(1)->format('Y-m-d H:i:s')) . $uniqueId;
					$newKey2 = strtotime($report->updated_at->copy()->addSeconds(1)->format('Y-m-d H:i:s')) . $uniqueId;
					$createdAt = $report->created_at->format('Y-m-d');

					if (strtotime($report->updated_at->format('Y-m-d')) > strtotime($report->created_at->format('Y-m-d'))) {
						$createdAt= $report->updated_at->format('Y-m-d');
						$newKey = $newKey2;
					}

					if (!array_key_exists($timeKey, $data)) {
						$data[$timeKey] = [
							'content_type' => 'Liberty Reports:',
							'title'		   => $report->title,
							'link'		   => Config::get('app.client_frontend') . '/client/folder',
							'recipients'   => $recipients,
							'created_at'   => $createdAt,
						];
						break;
					}

					$data[$newKey] = [
						'content_type' => 'Liberty Reports:',
						'title'		   => $report->title,
						'link'		   => Config::get('app.client_frontend') . '/client/folder',
						'recipients'   => $recipients,
						'created_at'   => $createdAt,
					];

					break;
				}
			}
		}

		return $data;
	}

	private function categorisedFormsNotification($start, $end, $type)
	{
		$this->info('[' . date('Y-m-d H:i:s') . '] > Executing Categorised '.$type.' Forms Notification');

		$data = [];

		if ($type==='protected') {
			$protectedForms = Formy::where('created_at', '>=', $start)
				->where('created_at', '<=', $end)
				->where('formType', 'categorised')
				->orWhere(function($query) use( $start, $end) {
					return $query
						->where('updated_at', '>=', $start)
						->where('updated_at', '<=', $end)
						->where('formType', 'categorised');
				  })
				->orderBy('created_at', 'DESC')
				->get();
		}

		foreach ($protectedForms as $protectedForm)
		{
			$title = $protectedForm->name;
			$recipients = [];
			$organisation_id = $protectedForm->organisation;
			$created_at = date('Y-m-d', strtotime($protectedForm->created_at));
			$updatedAt =  date('Y-m-d', strtotime($protectedForm->updated_at));

			if ($type==='protected') {
				$link = Config::get('app.client_frontend') . '/forms/show/' . $protectedForm->_id;
			}

			if ($type==='public') {
				$link = Config::get('app.client_frontend') . '/forms/show/' . $protectedForm->_id.'/public';
			}

			$organisationAttachedEmails = Organisation::with('users')
				->where('id', $organisation_id)
				->first();

			// Check if the form is attached to all organisation
			if ($protectedForm->organisation=='0') {
				$allOrganisationAttachedEmails = Organisation::with('users')->get();
				foreach ($allOrganisationAttachedEmails as $allOrganisationAttachedEmail)
				{
					if (
						isset($allOrganisationAttachedEmail->users) &&
						!empty($allOrganisationAttachedEmail->users) &&
						count($allOrganisationAttachedEmail->users) > 0)
					{
						foreach ($allOrganisationAttachedEmail->users as $user)
						{
							$recipients[] = trim($user->first_name) . ',' . trim($user->last_name) . ',' . $user->email;
						}
					}
				}
			}

			// Check if the form is attached to a particular organisation
			if (
				isset($organisationAttachedEmails->users) &&
				!empty($organisationAttachedEmails->users) &&
				count($organisationAttachedEmails->users) > 0)
			{
				foreach ($organisationAttachedEmails->users as $user)
				{
					$recipients[] = trim($user->first_name) . ',' . trim($user->last_name) . ',' . $user->email;
				}
			}

			// Check if the form is attached to a sector
			if ($protectedForm->attachment_type=='sector') {
				$sectors = $protectedForm->selected_sectors;

				$sectoredOrganisations = Organisation::with('users')
					->whereIn('sector', $sectors)
					->get();

				foreach ($sectoredOrganisations as $sectoredOrganisation)
				{
					if (
						isset($sectoredOrganisation->users) &&
						!empty($sectoredOrganisation->users) &&
						count($sectoredOrganisation->users) > 0)
					{
						foreach ($sectoredOrganisation->users as $user)
						{
							$recipients[] = trim($user->first_name) . ',' . trim($user->last_name) . ',' . $user->email;
						}
					}
				}
			}

			if (count($recipients) > 0) {
				while (true) {
					$uniqueId = '/' . md5(uniqid(rand(), true));
					$timeKey = strtotime($protectedForm->created_at->format('Y-m-d H:i:s')) . $uniqueId;
					$newKey = strtotime($protectedForm->created_at->copy()->addSeconds(1)->format('Y-m-d H:i:s')) . $uniqueId;
					$newKey2 = strtotime($protectedForm->updated_at->copy()->addSeconds(1)->format('Y-m-d H:i:s')) . $uniqueId;

					if (strtotime($updatedAt) > strtotime($created_at)) {
						$created_at = $updatedAt;
						$newKey = $newKey2;
					}

					if (!array_key_exists($timeKey, $data)) {
						$data[$timeKey] = [
							'content_type' => 'Forms:',
							'title' 	   => $title,
							'created_at'   => $created_at,
							'recipients'   => $recipients,
							'link' 		   => $link
						];
						break;
					}

					$data[$newKey] = [
						'content_type' => 'Forms:',
						'title' 	   => $title,
						'created_at'   => $created_at,
						'recipients'   => $recipients,
						'link' 		   => $link
					];

					break;
				}
			}
		}

		return $data;
	}

	/**
	 * Notify organisation(s) user(s) for the latest created course
	 *  - If Course is attached to  All Organisations → then notify Client users of all organisations
	 *  - If Course is attached to a particular Organisation → then notify only Client users of that Organisation
	 *
	 * @param string $start Start Date
	 * @param string $end End Date
	 *
	 * @return array
	 */
	private function courseNotification($start, $end)
	{
		$this->info('[' . date('Y-m-d H:i:s') . '] > Executing Course Notification');

		// Get latest course created base on start and end date provided
		$courses = Course::where('created_at', '>=', $start)
			->where('created_at', '<=', $end)
			->where('published', '=', 1)
			->whereNull('deleted_at')

			->orWhere(function($query) use( $start, $end) {
				return $query
				->where('updated_at', '>=', $start)
				->where('updated_at', '<=', $end)
				->where('published', '=', 1)
				->whereNull('deleted_at');
			})

			->orderBy('created_at', 'desc')
			->with(['courseOrganisations', 'sectorCourses'])
			->get();

		$data = [];
		foreach ($courses as $course) {
			$courseOrgs = [];
			if (isset($course->sectorCourses) && !empty($course->sectorCourses->count())) {
				$sectorIds = $course->sectorCourses->map(function ($sectorCourse) {
					return $sectorCourse->sector_id;
				})->toArray();

				$courseOrgs = Organisation::whereIn('sector', $sectorIds)->get();
			}

			if (isset($course->courseOrganisations) && !empty($course->courseOrganisations->count())) {
				$attachOrgs = $course->courseOrganisations;
				if (!empty($courseOrgs)) {
					$courseOrgIds = $courseOrgs->map(function ($org) {
						return $org->id;
					})->toArray();
					$uniqueOrg = $attachOrgs->map(function ($courseOrganisation) use ($courseOrgIds) {
						return !in_array($courseOrganisation->id, $courseOrgIds) ? $courseOrganisation->organisation : null;
					});

					$uniqueOrg = $uniqueOrg->filter(function ($org) {
						return !is_null($org);
					});

					$courseOrgs = $courseOrgs->merge($uniqueOrg);
				}

				if (!empty($course->organisation_id)) {
					$ownershipOrg = Organisation::where('id', $course->organisation_id)->get();
					$courseOrgs   = !empty($courseOrgs) ? $courseOrgs->merge($ownershipOrg) : $ownershipOrg;
				}
			}

			if (!empty($courseOrgs)) {
				foreach ($courseOrgs as $courseOrg) {
					$recipients = [];
					$orgUsers   = [];
					if ($courseOrg->organisation_id === 0) { // if organisation id is 0 then the course is for all
						$organisations = Organisation::whereNull('deleted_at')->get();
						foreach ($organisations as $org) {
							$orgUsers = !is_null($org->users) ? $org->users : [];
							if (!empty($orgUsers)) {
								foreach ($orgUsers as $user) {
									$recipients[] = trim($user->first_name) . ',' . trim($user->last_name) . ',' . $user->email;
								}
							}
						}
					}

					if (!is_null($courseOrg->organisation)) {
						$orgUsers = $courseOrg->organisation->users;
						if (!empty($orgUsers)) {
							foreach ($orgUsers as $user) {
								$recipients[] = trim($user->first_name) . ',' . trim($user->last_name) . ',' . $user->email;
							}
						}
					}

					if (!is_null($courseOrg->users)) { // sectors
						$orgUsers = $courseOrg->users;
						if (!empty($orgUsers)) {
							foreach ($orgUsers as $user) {
								$recipients[] = trim($user->first_name) . ',' . trim($user->last_name) . ',' . $user->email;
							}
						}
					}

					if (!empty($recipients)) {
						while (true) {
							$uniqueId = '/' . md5(uniqid(rand(), true));
							$timeKey = strtotime($course->created_at->format('Y-m-d H:i:s')) . $uniqueId;

							$createdAt = $course->created_at->copy()->format('Y-m-d');
							$updatedAt = $course->updated_at->copy()->format('Y-m-d');
							$newKey = strtotime($course->created_at->copy()->addSeconds(1)->format('Y-m-d H:i:s')) . $uniqueId;
							$newKey2 = strtotime($course->updated_at->copy()->addSeconds(1)->format('Y-m-d H:i:s')) . $uniqueId;

							if (strtotime($updatedAt) > strtotime($createdAt)) {
								$createdAt = $updatedAt;
								$newKey = $newKey2;
							}

							if (!array_key_exists($timeKey, $data)) {
								$data[$timeKey] = [
									'content_type' => 'Courses:',
									'title'		   => $course->title,
									'link'		   => Config::get('app.client_frontend') . '/learning/course/' . $course->id,
									'recipients'   => $recipients,
									'created_at'   => $createdAt,
								];
								break;
							}

							$data[$newKey] = [
								'content_type' => 'Courses:',
								'title'		   => $course->title,
								'link'		   => Config::get('app.client_frontend') . '/learning/course/' . $course->id,
								'recipients'   => $recipients,
								'created_at'   => $createdAt,
							];

							break;
						}
					}
				}
			}
		}

		return $data;
	}

	private function getNewsArticles($start, $end)
	{
		$env = config('app.env');
		$this->info('[' . date('Y-m-d H:i:s') . '] > Executing News Articles Notification');
		
		$workspaceId = config('app.cms.news.' . $env . '.workspace');
		$contentType = config('app.cms.news.' . $env . '.content_type');

		$newsWorkspace = 'workspaces/' . $workspaceId
                 . '/content-types/' . $contentType
                 . '/content-entries?query={"status":"publish","operator":"=","order":"publish_date_1564320647247"}';

		$news = json_decode(Cms::get($newsWorkspace));
		if (isset($news->data)) {
			$newsArticles = $news->data;
			$articles = $this->extractArticles('news', $newsArticles, $start, $end);
			return $this->matchUsersWithSectors('News', $articles);
		}
		return [];
	}

	private function getThoughLeadershipArticles($start, $end)
	{
		$env = config('app.env');
		$this->info('[' . date('Y-m-d H:i:s') . '] > Executing Thought Leadership Articles Notification');

		$workspaceId = config('app.cms.thought_leadership.' . $env . '.workspace');
		$contentType = config('app.cms.thought_leadership.' . $env . '.content_type');

		$thoughtLeadersWorkspace = 'workspaces/' . $workspaceId
			. '/content-types/' . $contentType
			. '/content-entries?query={"status":"publish","operator":"=","order":"publish_date_1687430785026"}';

		$thoughtLeaders = json_decode(Cms::get($thoughtLeadersWorkspace));
		if (isset($thoughtLeaders->data)) {
			$thoughtLeadersArticles = $thoughtLeaders->data;
			$articles = $this->extractArticles('thought_leadership', $thoughtLeadersArticles, $start, $end);
			return $this->matchUsersWithSectors('Thought Leaderships', $articles);
		}
		return [];
	}

	private function extractArticles(string $articleFor, array $articles, string $start, string $end)
	{
		$env = config('app.env');

		// Get the first day of the previous month
		$firstDayOfPreviousMonth = $start ?? Carbon::now()->subMonth()->startOfMonth();
		
		// Get the last day of the previous month
		$lastDayOfPreviousMonth = $end ?? Carbon::now()->startOfMonth()->subDay();

		$titleKey = $articleFor === 'news' ? config("app.cms.$articleFor.$env.title") : config("app.cms.$articleFor.$env.title");
		$sectorKey = $articleFor === 'news' ? config("app.cms.$articleFor.$env.sector") : config("app.cms.$articleFor.$env.sector");
		$publishDateKey = $articleFor === 'news' ? config("app.cms.$articleFor.$env.publish_date") : config("app.cms.$articleFor.$env.publish_date");

		$filteredArticles = [];
		foreach ($articles as $article) {

			// Parse the updated_at and created_at dates
			$updatedAt = $this->parseDate($article->{'updated_at'} ?? null);
			$createdAt = $this->parseDate($article->{'created_at'} ?? null);
			
			// Check if the article was created or updated in the previous month
			if (($updatedAt && $updatedAt->between($firstDayOfPreviousMonth, $lastDayOfPreviousMonth)) ||
				($createdAt && $createdAt->between($firstDayOfPreviousMonth, $lastDayOfPreviousMonth))) {

				// Extract sectors information
				$sectors = [];
				if (isset($article->{$sectorKey}) && is_array($article->{$sectorKey})) {
					foreach ($article->{$sectorKey} as $sector) {
						$sectors[] = [
							'id' => $sector->{'_id'} ?? null,
							'name' => $sector->{'name'} ?? null
						];
					}
				}
				
				// Add only the required fields to the filtered articles
				$filteredArticles[] = [
					'id' => $article->{'_id'} ?? null,
					'name' => $article->{'name'} ?? null,
					'title' => $article->{$titleKey} ?? null,
					'sectors' => $sectors,
					'publish_date' => $article->{$publishDateKey} ?? null
				];
			}
		}
		
		return $filteredArticles;
	}

	/**
	 * Parse date string into Carbon instance
	 * 
	 * @param string|null $dateString
	 * @return Carbon|null
	 */
	private function parseDate($dateString)
	{
		if (!$dateString) {
			return null;
		}
		
		try {
			// Handle dates in format like "15th April 2025" or standard formats
			return Carbon::parse($dateString);
		} catch (\Exception $e) {
			// Return null if date cannot be parsed
			return null;
		}
	}

	/**
	 * Handles the notification for policy document
	 *
	 * @param string $start Start Date
	 * @param string $end End Date
	 *
	 * @return array
	 */
	private function policyDocumentNotification($start, $end)
	{
		$this->info('[' . date('Y-m-d H:i:s') . '] > Executing Policy Document Notification');

		$policyDocs = PolicyDocumentModel::select(['id', 'name', 'organisation_id', 'created_at', 'updated_at'])
			->where('created_at', '>=', $start)
			->where('created_at', '<=', $end)
			->whereNull('deleted_at')
			->orWhere(function($query) use($start, $end) {
				return $query
					->where('updated_at', '>=', $start)
					->where('updated_at', '<=', $end)
					->whereNull('deleted_at');
			})
			->orderBy('created_at', 'desc')
			->with('organisation')
			->get();

		$data = [];
		foreach ($policyDocs as $policyDoc) {

			$orgUsers 	= !is_null($policyDoc->organisation) ? $policyDoc->organisation->users : [];
			$recipients = [];
			foreach ($orgUsers as $user) {
				$recipients[] = trim($user->first_name) . ',' . trim($user->last_name) . ',' . $user->email;
			}

			if (!empty($recipients)) {
				while (true) {
					$uniqueId = '/' . md5(uniqid(rand(), true));
					$timeKey = strtotime($policyDoc->created_at->format('Y-m-d H:i:s')) . $uniqueId;

					$newKey = strtotime($policyDoc->created_at->copy()->addSeconds(1)->format('Y-m-d H:i:s')) . $uniqueId;
					$newKey2 = strtotime($policyDoc->updated_at->copy()->addSeconds(1)->format('Y-m-d H:i:s')) . $uniqueId;

					$createdAt = $policyDoc->created_at->format('Y-m-d');
					$updatedAt = $policyDoc->updated_at->format('Y-m-d');

					if (strtotime($updatedAt) > strtotime($createdAt)) {
						$createdAt = $updatedAt;
						$newKey = $newKey2;
					}

					if (!array_key_exists($timeKey, $data)) {
						$data[$timeKey] = [
							'content_type' => 'Policy Documents:',
							'title'		   => $policyDoc->name,
							'link'		   => Config::get('app.client_frontend') . '/policy-documents',
							'recipients'   => $recipients,
							'created_at'   => $createdAt,
						];
						break;
					}

					$data[$newKey] = [
						'content_type' => 'Policy Documents:',
						'title'		   => $policyDoc->name,
						'link'		   => Config::get('app.client_frontend') . '/policy-documents',
						'recipients'   => $recipients,
						'created_at'   => $createdAt,
					];

					break;
				}
			}
		}

		return $data;
	}

	/**
	 * Send notifications to all content types that was form
	 *
	 * @param array $notifications List of all notifications for content types
	 * @return void
	 */
	private function notify($notifications)
	{
		$final_recipients = [];
		foreach ($notifications as $notification) {
			ksort($notification);
			foreach ($notification as $key => $data) {
				foreach ($data['recipients'] as $recipient) {
					$final_recipients[$recipient][$key] = [
						'content_type' => $notification[$key]['content_type'],
						'title' 	   => $notification[$key]['title'],
						'link'         => $notification[$key]['link'],
						'created'      => $notification[$key]['created_at'],
					];
				}
			}
		}

		$errorList = [];
		foreach ($final_recipients as $key => $final_recipient) {
			try {
				$keys = array_keys($final_recipient);
				usort($keys, function ($a, $b) {
					$datetimeA = explode('/', $a)[0];
					$datetimeB = explode('/', $b)[0];

					if ($datetimeA === $datetimeB) return 0;
					return $datetimeA > $datetimeB ? -1 : 1;
				});

				$sort = [];
				foreach ($keys as $finalRecipientKey) {
					$sort[$finalRecipientKey] = $final_recipient[$finalRecipientKey];
				}

				$userInfo = explode(',', $key);
				if (count($userInfo) === 3) {
					if (filter_var($userInfo[2], FILTER_VALIDATE_EMAIL)) {
						$fullName  = $userInfo[0] . ' ' . $userInfo[1];
						$email 	   = $userInfo[2];
						$contentTypes = array_slice($sort, 0, 10);

						$this->mail->queue(
							$email,
							$fullName,
							'Recent updates from Risk Reduce',
							'emails.client.monthly-update',
							[
								'full_name'     => $fullName,
								'contentTypes'	=> $contentTypes,
							]
						);
					}
				} else {
					$errorList[] = $key;
				}
			} catch (\Exception $e) {
				$errorList[] = $key;
			}
		}

		if (!empty($errorList)) {
			Log::info('Monthly Round Up: ' . json_encode($errorList));
		}
	}

	/**
	 * Match users with their sectors based on sector names
	 * 
	 * @param string $articleFor The type of article (News or Thought Leaderships)
	 * @param array $articles Articles from the previous month
	 * @return array Grouped articles with recipients
	 */
	private function matchUsersWithSectors(string $articleFor, array $articles)
	{
		// Get all active users with their organization information
		$users = DB::table('users')
			->select('users.id', 'users.first_name', 'users.last_name', 'users.email', 
				'organisations.id as organisation_id', 'sectors.handle as sector_handle')
			->join('organisations', 'users.organisation_id', '=', 'organisations.id')
			->join('sectors', 'organisations.sector', '=', 'sectors.id')
			->whereNull('users.deleted_at')
			->where('users.activated', 1)
			->get();

		// Create a mapping of sector names/handles to article data
		$sectorToArticlesMap = [];
		foreach ($articles as $article) {
			foreach ($article['sectors'] as $sector) {
				$sectorName = $sector['name'];
				// Convert sector name to a handle format for comparison (lowercase, no special chars)
				$sectorHandle = $this->convertToHandle($sectorName);
				if (!isset($sectorToArticlesMap[$sectorHandle])) {
					$sectorToArticlesMap[$sectorHandle] = [
						'name' => $sectorName,
						'articles' => []
					];
				}
				
				$sectorToArticlesMap[$sectorHandle]['articles'][] = [
					'id' => $article['id'],
					'name' => $article['name'],
					'title' => $article['title'],
					'publish_date' => $article['publish_date']
				];
			}
		}

		// First, collect all recipients for each article title
		$articleRecipients = [];
		foreach ($users as $user) {
			$sectorHandle = $this->convertToHandle($user->sector_handle);
			
			// Skip users without a sector handle
			if (!$sectorHandle) {
				continue;
			}
			
			// Check if this sector has any relevant articles
			if (isset($sectorToArticlesMap[$sectorHandle])) {
				$userEmail = $user->email;
				$userFullName = trim($user->first_name) . ',' . trim($user->last_name);
				$recipient = $userFullName . ',' . $userEmail;
				
				// Add this user as a recipient for each article in their sector
				foreach ($sectorToArticlesMap[$sectorHandle]['articles'] as $article) {
					$articleTitle = $article['name'];
					if (!isset($articleRecipients[$articleTitle])) {
						$articleRecipients[$articleTitle] = [
							'id' => $article['id'],
							'publish_date' => $article['publish_date'],
							'recipients' => []
						];
					}
					
					// Add this user as a recipient if not already added
					if (!in_array($recipient, $articleRecipients[$articleTitle]['recipients'])) {
						$articleRecipients[$articleTitle]['recipients'][] = $recipient;
					}
				}
			}
		}

		$link = $articleFor === 'News' ? '/news' : '/thought-leaderships';
		
		// Now create the final structure with unique keys
		$userArticles = [];
		foreach ($articleRecipients as $title => $data) {
			// Generate a unique key for each article
			$uniqueId = '/' . md5(uniqid(rand(), true));
			$key = strtotime(Carbon::now()) . $uniqueId;
			
			$userArticles[$key] = [
				'content_type' => $articleFor . ':',
				'title' => $title,
				'link' => Config::get('app.client_frontend') . $link . '/' . urlencode($title) . '/' . $data['id'],
				'recipients' => $data['recipients'],
				'created_at' => $data['publish_date']
			];
		}

		return $userArticles;
	}

	/**
	 * Convert a string to a handle format (lowercase, no special chars)
	 * 
	 * @param string $string The string to convert
	 * @return string The handle
	 */
	private function convertToHandle($string)
	{
		if (!$string) {
			return '';
		}
		
		// Convert to lowercase
		$handle = strtolower($string);
		
		// Replace HTML entities
		$handle = html_entity_decode($handle);
		
		// Remove special characters and replace spaces with hyphens
		$handle = preg_replace('/[^a-z0-9\s-]/', '', $handle);
		$handle = preg_replace('/\s+/', '-', $handle);
		
		return $handle;
	}
}