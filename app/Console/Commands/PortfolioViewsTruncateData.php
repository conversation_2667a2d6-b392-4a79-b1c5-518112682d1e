<?php

namespace App\Console\Commands;

use App\Models\PortfolioViews\PortfolioViewsInsights;
use App\Models\PortfolioViews\PortfolioViewsInsightsData;
use App\Models\PortfolioViews\PortfolioViewsRawData;
use Illuminate\Console\Command;

class PortfolioViewsTruncateData extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'portfolioviews:truncate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Truncate all Portfolio Views data';

    public function handle()
    {
        if ($this->confirm('Deleting all Portfolio Views data. Are you sure you want to continue?')) {
            $this->line('Deleting insights...');

            PortfolioViewsInsightsData::truncate();
            PortfolioViewsInsights::truncate();

            $this->info('Insights deleted.');

            $this->line('Deleting raw data.');

            PortfolioViewsRawData::truncate();

            $this->info('Raw data deleted.');
        }
    }
}
