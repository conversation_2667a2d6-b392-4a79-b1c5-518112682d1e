<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Organisation;
use App\Models\ReMetricsLog;
use Illuminate\Support\Arr;

class LogReMetrics extends Command
{
    protected $signature = 'log:re-metrics {seed2025?}';
    protected $description = 'Log average risk metrics for organisations';

    const RISK_MAPPING = [
        'A' => 5,
        'B' => 4,
        'C' => 3,
        'D' => 2,
        'E' => 1
    ];

    public function handle()
    {
        if ($this->argument('seed2025')) {
            return $this->seedData2025();
        }

        $organisations = Organisation::select('risk_grading_value')
            ->whereNotNull('risk_grading_value')
            ->where('risk_grading_value', '!=', '0')
            ->get();

        // Initialize counts array
        $gradingCounts = [
            'A' => 0,
            'B' => 0,
            'C' => 0,
            'D' => 0,
            'E' => 0
        ];

        // Count organizations by grade
        foreach ($organisations as $organisation) {
            if (isset($gradingCounts[$organisation->risk_grading_value])) {
                $gradingCounts[$organisation->risk_grading_value]++;
            }
        }

        $numericalGrades = $organisations->map(function ($organisation) {
            if (isset(self::RISK_MAPPING[$organisation->risk_grading_value])) {
                return self::RISK_MAPPING[$organisation->risk_grading_value];
            }
            return 0;
        })->filter(function ($grade) {
            return $grade > 0;
        });

        $average = $numericalGrades->isEmpty() ? 0 : $numericalGrades->average();
        $riskGradingMap = array_flip(self::RISK_MAPPING);
        $grade = Arr::get($riskGradingMap, round($average), "");

        $processedData = [
            'grade' => $grade,
            'average_value' => round($average, 2),
            'total_organisations' => $organisations->count(),
        ];

        // Save to ReMetricsLog
        $date = now()->subDay()->endOfDay();
        ReMetricsLog::create([
            'log_type' => 'risk_grading',
            'processed_data' => $processedData,
            'raw_data' => $gradingCounts,
            'created_at' => $date,
            'updated_at' => $date,
        ]);

        ReMetricsLog::create([
            'log_type' => 'accounts_under_management',
            'processed_data' => $this->getKeyOrgsCount(),
            'raw_data' => [],
            'created_at' => $date,
            'updated_at' => $date,
        ]);

        $this->info('Risk metrics logged successfully.');
    }

    public function getKeyOrgsCount()
    {
        return Organisation::whereHas('contacts', function ($query) {
            $query->where('type', 'risk-engineer');
        })->count();
    }

    // For testing purposes only
     protected function seedData2025()
     {
         for ($month = 1; $month <= 12; $month++) {
             $date = now()->setYear(2024)->setMonth($month)->endOfMonth();
             
             // Generate random grading counts
             $gradingCounts = [
                 'A' => rand(10, 30),
                 'B' => rand(20, 40),
                 'C' => rand(30, 50),
                 'D' => rand(15, 35),
                 'E' => rand(5, 25)
             ];
             
             // Calculate average
             $total = 0;
             $count = 0;
             foreach ($gradingCounts as $grade => $number) {
                 $total += self::RISK_MAPPING[$grade] * $number;
                 $count += $number;
             }
             
             $average = $count > 0 ? $total / $count : 0;
             $riskGradingMap = array_flip(self::RISK_MAPPING);
             $grade = Arr::get($riskGradingMap, round($average), "");
             
             $processedData = [
                 'grade' => $grade,
                 'average_value' => round($average, 2),
                 'total_organisations' => $count,
             ];
             
             // Create risk_grading log
             ReMetricsLog::create([
                 'log_type' => 'risk_grading',
                 'processed_data' => $processedData,
                 'raw_data' => $gradingCounts,
                 'created_at' => $date,
                 'updated_at' => $date,
             ]);
             
             // Create accounts_under_management log with random count
             ReMetricsLog::create([
                 'log_type' => 'accounts_under_management',
                 'processed_data' => rand(50, 150),
                 'raw_data' => [],
                 'created_at' => $date,
                 'updated_at' => $date,
             ]);
         }
         
         $this->info('2025 random data seeded successfully.');
     }
}
