<?php

namespace App\Console\Commands;

use App\Models\Previsico\PrevisicoAsset;
use Illuminate\Console\Command;

class PrevisicoClearFloodMapStationData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'previsico:clear_floodmap_station_data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear Station Data to allow regeneration';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        PrevisicoAsset::whereNotNull('rs_nearest_station_ref')
            ->whereNotNull('sw_nearest_station_ref')
            ->update([
                'rs_nearest_station_ref' => NULL,
                'sw_nearest_station_ref' => NULL
            ]);

        $this->info("Previsico flood map station data has been cleared.");
    }
}
