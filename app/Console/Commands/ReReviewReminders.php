<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Mailqueue;
use App\Models\ScheduleMeta;
use App\Models\RiskImprovementFormySubmissions;
use App\Models\LibertyUser;
use Carbon\Carbon;

class ReReviewReminders extends Command
{
    protected $queue;

    protected $email_subject = 'RE Review Reminder';
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'riskreduce:send-rereview-reminder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Risk Reduce Send RE Review Reminders';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->queue = new Mailqueue();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function fire()
    {
        $this->sendRemindersForDatesOlderThan(1);
    }

    public function handle()
    {
        $this->sendRemindersForDatesOlderThan(1);
    }

    private function sendRemindersForDatesOlderThan($number_of_days)
    {
        $previous_date = Carbon::now()->startOfDay()->addDays($number_of_days * -1);
        $schedule_meta = ScheduleMeta::where('key', '=', 're_review_deadline')
                            ->where('value', '=', $previous_date->toDateTimeString(), 'AND')
                            ->get();

        $schedule_ids = [];
        foreach ($schedule_meta as $schedule) {
            array_push($schedule_ids, $schedule->schedule_id);
        }

        $survey_ids = ScheduleMeta::where('key', '=', 'survey_id')->whereIn('schedule_id', $schedule_ids, 'AND')->get();

        $survey_id_array = [];

        foreach ($survey_ids as $survey_id) {
            array_push($survey_id_array, $survey_id->value);
        }

        foreach ($survey_id_array as $survey_id) {
            $submissions = RiskImprovementFormySubmissions::whereIn('survey_id', [(int)$survey_id, (string)$survey_id])
                            ->first();

            if (isset($submissions->rereview_status) && $submissions->rereview_status == 'submitted') {
                //
            } else {
                $rc = LibertyUser::where('email', 'LIKE', '<EMAIL>')->first();
                $rc->survey_id = $survey_id;
                $this->queue->queue(
                    $rc['email'],
                    $rc->fullName(),
                    'Risk Reduce - RE Review past due',
                    'emails.surveys.re_review_past_due',
                    $rc
                );
            }
        }
    }
}
