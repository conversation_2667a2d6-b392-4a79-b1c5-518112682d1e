<?php

namespace App\Console\Commands;

use App\Services\AccessGroup\FetchAndMapOrganisation;
use Illuminate\Console\Command;

class AccessGroupFetchAndMapOrganisation extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'access-group:fetch-and-map-organisation';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "This command will fetch and map the organisations agains Risk Reduce System. Should run only once.";

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        FetchAndMapOrganisation::execute($this);
    }
}
