<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Console\Input\InputOption;
use Illuminate\Support\Facades\Event;
class CommunitySummary extends Command {

	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'riskreduce:community-summary';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Get all community messages for the last 24hrs';

	

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();		
	}

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function fire()
    {
		$response = Event::fire('summary.notify', [
			$this->option('summary'),
			$this->option('recipients'),
		]);
	}

	/**
	 * Get the console command arguments.
	 *
	 * @return array
	 */
	protected function getArguments()
	{
		return array(
		);
	}

	/**
	 * Get the console command options.
	 *
	 * @return array
	 */
	protected function getOptions()
	{
		return array(
			array('summary', 'summary', InputOption::VALUE_REQUIRED),
			array('recipients', 'recipients', InputOption::VALUE_OPTIONAL),
		);
	}

}
