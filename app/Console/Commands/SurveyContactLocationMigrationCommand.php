<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use App\Models\SurveyContact;
use App\Models\Survey;
use App\Models\OrganisationLocations;
use Illuminate\Support\Facades\DB;
use Exception;
class SurveyContactLocationMigrationCommand extends Command {

	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'riskreduce:migrate-survey-location';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Risk reduce migrate the location of a survey from survey_contacts to organisation_locations table';

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
	}

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{
		try {
			$surveyContacts    = SurveyContact::select(['survey_id', 'city', 'postcode', 'address_1', 'address_2', 'country'])
				->with('survey')
				->where('postcode', '!=', '')
				->whereNotNull('postcode')
				->groupBy('survey_id')
				->get();

			DB::beginTransaction();

			foreach ($surveyContacts as $surveyContactIdx => $surveyContact) {
				if (!is_null($surveyContact->survey)) {
					$organisation 		   = $surveyContact->survey->organisation;
					$organisationLocations = !is_null($organisation) ? OrganisationLocations::where('organisation_id', $organisation->id)->get() : null;
					$locationsCount    	   = !is_null($organisationLocations) ? $organisationLocations->count() : 0;
					$existingOrgLocation   = [];
					$locationId			   = 1;
					$scPostcode 	       = str_replace(' ', '', $surveyContact->postcode); // survey_contacts.postcode

					if ($locationsCount > 0) {
						$locationId = strpos($organisationLocations->last()->location_id, 'LOC') !== false ? (int)filter_var($organisationLocations->last()->location_id, FILTER_SANITIZE_NUMBER_INT) + 1: 1;
						foreach ($organisationLocations as $location) {
							$existingLocPostcode = str_replace(' ', '', $location->postcode);
							if ($existingLocPostcode == $scPostcode && trim($location->address_line_1) === trim($surveyContact->address_1)) {
								$existingOrgLocation['location_id'] = $location->id;
								break;
							}
						}
					}

					if (!empty($existingOrgLocation)) { // Update the current survey with existing location id in organisation_locations
						$surveyContact->survey()->update($existingOrgLocation);
						if ($surveyContacts->count() - 1 !== $surveyContactIdx) {
							continue;
						}
					} else {
						$locationIdKey = 'LOC' . str_pad($locationId, 3, '0', STR_PAD_LEFT);
						$data = [
							'organisation_id' => !empty($surveyContact->survey->organisation_id) ? $surveyContact->survey->organisation_id : 0,
							'location_id'     => $locationIdKey,
							'location_name'	  => $surveyContact->address_1,
							'postcode'        => $surveyContact->postcode,
							'address_line_1'  => trim($surveyContact->address_1),
							'address_line_2'  => $surveyContact->address_2,
							'city'            => $surveyContact->city,
							'country'         => $surveyContact->country,
							'created_at'      => Carbon::now(),
							'updated_at'      => Carbon::now()
						];

						$orgLocationObj = OrganisationLocations::create($data);
						Survey::where('id', $surveyContact->survey_id)
							->update(['location_id' => $orgLocationObj->id]);
						DB::commit();

					}
					$locationId++;
				}
			}

			$this->info('[' . Carbon::now() . '] > Survey locations successfully migrated!');
		} catch (Exception $e) {
			DB::rollBack();
			$this->error('> ' . $e->getMessage());
		}
	}
}