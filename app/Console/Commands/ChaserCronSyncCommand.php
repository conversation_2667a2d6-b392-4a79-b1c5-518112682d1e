<?php

namespace App\Console\Commands;

use App\Models\RiskRecommendationNotification;
use Illuminate\Console\Command;
use App\Models\Survey;

class ChaserCronSyncCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:chaser-notification';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command syncs SRF to CRON table monitor';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $surveys = Survey::get();
        foreach ($surveys as $survey) {
            RiskRecommendationNotification::monitor($survey->id);
        }
    }
}
