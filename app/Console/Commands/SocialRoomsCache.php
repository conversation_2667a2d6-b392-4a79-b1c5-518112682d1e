<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use App\Models\Cms;
use Illuminate\Support\Facades\Config;
class SocialRoomsCache extends Command {

	public static $business = 'lsm';

	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'virtual-rooms:social-rooms-cache';	

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'This command builds cache for social rooms';

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
	}


	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{
        $this->info("Start caching people for Social Rooms");
        $lsmLibReps = $this->fetchFromCms();
        $this->info("Done with LSM");
        $lmreLibReps = $this->fetchFromCms('lmre');
        $this->info("Finished caching people for Social Rooms");

        if($lsmLibReps == null){
            $lsmLibReps = [];
        }
        if($lmreLibReps == null){
            $lmreLibReps = [];
        }
        $merged = array_merge($lmreLibReps, $lsmLibReps);

        Cache::forever('socialRoomRepresentatives', $merged);

        $letsTalkWorkspace = Config::get('app.cms.lsm.lets_talk_workspace');
        $announcementCategoriesContentType = Config::get('app.cms.lsm.announcement_categories_content_type');


        $this->info("Start caching Global categories");

        $globalCategories = json_decode(Cms::get('workspaces/' . $letsTalkWorkspace . '/content-types/' . $announcementCategoriesContentType . '/content-entries'))->data;
        $filteredGlobalCategories = array_filter($globalCategories, function ($category) {
            return isset($category->{Config::get('app.cms.lsm.announcement_category')}[0]);
        });
        $globalCategoryNames = array_map(function ($category) {
            return $category->name;
        }, $filteredGlobalCategories);
        Cache::forever('globalCategoryNames', $globalCategoryNames);

        $this->info("Finish caching Global categories");
        
       
	}	

	public function fetchFromCms($business = 'lsm') {

		self::$business = $business;


        $libertyRepresenativesCms = json_decode(Cms::get('workspaces/'.self::getCmsConfig('lets_talk_workspace').'/content-types/'.self::getCmsConfig('liberty_representatives_content_type').'/content-entries?joined=true&query={"status":"publish","operator":"="}'));
        Cache::put(self::$business . '_socialRoomLibertyRepresenativesCms', $libertyRepresenativesCms, 1440);
    

        $peopleProfileCms = json_decode(Cms::get('workspaces/'.self::getCmsConfig('workspace_id').'/content-types/'.self::getCmsConfig('people_content_type').'/content-entries?joined=true&query={"status":"publish","operator":"="}'));
        Cache::put(self::$business . '_socialRoomPeopleProfileCms', $peopleProfileCms, 1440);
    

        $peopleProfile = [];

        foreach ($peopleProfileCms->data as $profile) {
            $peopleProfile[$profile->_id]['line_of_business'] = [];
            if (isset($profile->{self::getCmsConfig('liberty_representative_line_of_business')})) {
                foreach ($profile->{self::getCmsConfig('liberty_representative_line_of_business')} as $lob) {
                    if (isset($lob->name)) {
                        $peopleProfile[$profile->_id]['line_of_business'][] = $lob->name;
                        $peopleProfile[$profile->_id]['line_of_business_slug'][] = $lob->slug;
                    }
                }
            }
            $timezoneAssignment = isset($profile->{self::getCmsConfig('people_profile_office')}) ? $profile->{self::getCmsConfig('people_profile_office')} : null ;
            $peopleProfile[$profile->_id]['timezone'] = isset($timezoneAssignment[0]) ? $timezoneAssignment[0]->{self::getCmsConfig('people_profile_office_timezone')} : null;
            $peopleProfile[$profile->_id]['office_title'] = isset($timezoneAssignment[0]) ? $timezoneAssignment[0]->{self::getCmsConfig('people_profile_office_title')} : null;
        }

        if (isset($libertyRepresenativesCms->data) && isset($libertyRepresenativesCms->data['0']) && isset($libertyRepresenativesCms->data['0']->{self::getCmsConfig('liberty_representatives')})) {
            $libertyRepresenatives = $libertyRepresenativesCms->data['0']->{self::getCmsConfig('liberty_representatives')};
        } else {
            $libertyRepresenatives = [];
        }

        foreach ($libertyRepresenatives as $key => $libRep) {
            // self::getCmsConfig('liberty_representative_email')
            if (!isset($libRep->status) || $libRep->status != 'publish' || empty($libRep->{self::getCmsConfig('liberty_representative_mobile_number')}) || empty($libRep->{self::getCmsConfig('liberty_representative_email')})) {
                unset($libertyRepresenatives[$key]);
                continue;
            }

            if (isset($libRep->{self::getCmsConfig('liberty_representative_line_of_business')}) && isset($peopleProfile[$libRep->_id]['line_of_business'])) {
                $libRep->{self::getCmsConfig('liberty_representative_line_of_business')} = $peopleProfile[$libRep->_id]['line_of_business'];
            }
            if(isset($peopleProfile[$libRep->_id]['line_of_business_slug'])) {
                $libRep->{self::getCmsConfig('liberty_representative_line_of_business').'_slug'} = $peopleProfile[$libRep->_id]['line_of_business_slug'];
            }
        }
        array_splice($libertyRepresenatives, 0, 0);

        $libertyRepresentatives = $this->compileRepData($libertyRepresenatives, $peopleProfile);

        Cache::put(self::$business . '_socialRoomLibertyRepresentatives', $libertyRepresentatives, 1440);
        Cache::put(self::$business . '_socialRoomLibertyRepresentativesLongLived', $libertyRepresentatives, 1440);
        

        return $libertyRepresentatives;
	}

	private static function getCmsConfig($config)
    {
        return Config::get('app.cms.' . self::$business . '.' . $config);
    }

    private static function compileRepData($representatives, $timezone)
    {
        if (empty($representatives)) {
            return [];
        }
        
        foreach ($representatives as $value) {
            if (isset($value->{self::getCmsConfig('liberty_representative_mobile_number')}) && isset($value->{self::getCmsConfig('liberty_representative_email')})) {
                $mobileNumber = str_replace('(0)', '', $value->{self::getCmsConfig('liberty_representative_mobile_number')});
                if (substr($mobileNumber, 0, 1) === '0') {
                    $mobileNumber = ltrim($mobileNumber, '0');
                } elseif (substr($mobileNumber, 0, 1) !== '+') {
                    $mobileNumber = "+44".$mobileNumber;
                }
                if (is_array($value->{self::getCmsConfig('liberty_representative_profile_picture')}) && isset($value->{self::getCmsConfig('liberty_representative_profile_picture')}[0])) {
                    $parsedUrl = parse_url($value->{self::getCmsConfig('liberty_representative_profile_picture')}[0]->url);
                    $parsedUrl['query'] = urlencode($parsedUrl['query']);
                    $profileImage = $parsedUrl['scheme']."://".$parsedUrl['host'].$parsedUrl['path'].'?'.$parsedUrl['query'];
                } else {
                    $profileImage = "https://asset-management-dev.s3.eu-west-1.amazonaws.com/assets/p2NoaMtcZQmyXDW9hoGy0GVtKpcZOQBi5mQg1nKe.png?name%3Duser-icon-silhouette-ae9ddcaf4a156a47931d5719ecee17b9.png%26size%3D6KB%26mime%3Dimage%2Fpng";
                }

                $officeNumber = !empty($value->{Config::get('app.cms.people_profile_office_number')}) ?  $value->{Config::get('app.cms.people_profile_office_number')} : '';

                $reprsentativeList[$value->_id] = [
                    'person_id' => $value->_id,
                    'name' => $value->name,
                    'user_name' => $value->name,
                    'line_of_business' => isset($value->{self::getCmsConfig('liberty_representative_line_of_business')}) ? $value->{self::getCmsConfig('liberty_representative_line_of_business')} : [],
                    'line_of_business_slug' => isset($value->{self::getCmsConfig('liberty_representative_line_of_business').'_slug'}) ? $value->{self::getCmsConfig('liberty_representative_line_of_business').'_slug'} : [],
                    'bio' => isset($value->{Config::get('app.cms.people_profile_bio')}) ? $value->{Config::get('app.cms.people_profile_bio')} : '',
                    'business_function' => isset($value->{self::getCmsConfig('liberty_representative_business_function')}) ? $value->{self::getCmsConfig('liberty_representative_business_function')} : null,
                    'email' => $value->{self::getCmsConfig('liberty_representative_email')},
                    'mobile' => "+".preg_replace('/[^0-9]/', '', $mobileNumber),
                    'office_number' => $officeNumber,
                    'profile_picture' => $profileImage,
                    'job_title' => $value->{self::getCmsConfig('liberty_representative_job_title')},
                    'office_timezone' => isset($timezone[$value->_id]['timezone']) ? $timezone[$value->_id]['timezone'] : 'Europe/London',
                    'office_title' => isset($timezone[$value->_id]['office_title']) ? $timezone[$value->_id]['office_title'] : 'N/A',
                    'schedule' => isset($reprsentativeSchedule[$value->_id]) ? $reprsentativeSchedule[$value->_id] : [],
                    'business' => self::$business,
                    'trained_as' => isset($value->{Config::get('app.cms.people_profile_trained_as')}) ? $value->{Config::get('app.cms.people_profile_trained_as')} : '',
                ];
            }
        }

        return $reprsentativeList;
    }

}
