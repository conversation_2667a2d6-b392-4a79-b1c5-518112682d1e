<?php

namespace App\Console\Commands;

use Aws\Laravel\AwsFacade as AWS;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RecacheClientClaimsData extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'recache:client-claims-perma-cache';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "This command will recache Client Claims related cache in client side.";

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $query = 'select * from client_claim_ids where liberty_id <> "" AND liberty_id in (select distinct liberty_id from client_claims);';
        $clientClaimIds = DB::select(DB::raw($query));

        foreach ($clientClaimIds as $clientClaim) {
            $this->sendClientSideRecacheQueue($clientClaim);
        }

        Log::info("Organisations Count: " . count($clientClaimIds));
        $this->info("Organisations Count: " . count($clientClaimIds));

        Log::info("Execution Time: " . round(microtime(true) - LARAVEL_START, 2));
        $this->info("Execution Time: " . round(microtime(true) - LARAVEL_START, 2));
    }

    private function sendClientSideRecacheQueue($clientClaim)
    {
        if (empty($clientClaim->organisation_id)) {
            return;
        }

        $messages = [
            [
                'serviceClass' => 'App\Services\CacheContent\GetClientClaimsData', //class is in client side
                'params' => $clientClaim->organisation_id ?? '',
                'source' => 'FROM_COMMAND:recache:client-claims-perma-cache',
                'isClient' => 1
            ]
        ];

        foreach ($messages as $message) {
            AWS::createClient('Sqs')->sendMessage([
                'QueueUrl'          => self::getQueueUrl($message['isClient'] ?? false),
                'MessageBody'       => json_encode($message),
                'MessageAttributes' => [],
                'DelaySeconds'      => 2
            ]);

            Log::info("recache:client-claims-perma-cache Queue Sent: " . json_encode($message));
            $this->info("recache:client-claims-perma-cache Queue Sent: " . json_encode($message));
        }
    }

    private static function getQueueUrl(bool $isClient): string
    {
        return $isClient ? Config::get('app.aws.invalidate_cache_sqs_client_claims') : Config::get('app.aws.invalidate_cache_sqs_admin');
    }
}