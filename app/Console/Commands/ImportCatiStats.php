<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\CatiStat;
use Carbon\Carbon;
use App\Models\Cati;

class ImportCatiStats extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'riskreduce:cati_import';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Gets Cati Login Stats';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $cati     = new Cati();
        $catiStat = $cati->getStatistics();

        if (
            !empty($catiStat)
            && !empty(($catiStat->StatusCode ?? null))
            && $catiStat->StatusCode == 'GCS00'
        ) {
            $statistics = $catiStat->Statistics;
            foreach ($statistics as $stat) {
                if (!$_catiStat = CatiStat::where('client_id', $stat->ClientID)->first()) {
                    $_catiStat = new CatiStat();
                }

                $_catiStat->client_id    = $stat->ClientID;
                $_catiStat->client_name  = $stat->ClientName;
                $_catiStat->total_logins = $stat->TotalLogins;
                $_catiStat->updated_at   = Carbon::now();
                $_catiStat->save();
            }
        }
    }
}
