<?php

namespace App\Console\Commands;
use Illuminate\Console\Command;
use App\Models\Survey;
use App\Models\PolicyType;
use App\Models\SurveyCards;
use Carbon\Carbon;
use Exception;


class SortSurveyCardsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'riskrec:survey_cards {id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rebuild survey tracking mongo document';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $surveyId = $this->argument('id');
        $this->importCards($surveyId);
    }


    private function additionalInfo($card)
    {
        //APPROVED UWR
        $data = [];

        if ($card->uwr_uw_status == '2') {
            if ($card->csr_uw_status == '1') {                    
                array_push($data, 'CSR UNDERWRITER');
            }

            if ($card->csr_uw_status == '2') {                    
                array_push($data, 'CSR APPROVED');
            }
        }

        //APPROVED CSR
        if ($card->csr_uw_status == '2') {
            if ($card->uwr_uw_status == '1') {
                array_push($data, 'UWR UNDERWRITER');
            }
            if ($card->uwr_uw_status == '2') {
                array_push($data, 'UWR APPROVED');
            }
        }
            
        return $data;
    }

    private function importCards($surveyId){

        \Log::info('riskrec:survey_cards '.$surveyId);

        $type = 'surveys';
        $surveys = Survey::where('survey_type', '<>', 'rereview')
        ->with([
            'organisation.mgascheme',
            'branch',
            'underwriter',
            'brokerUnderwriter',
            'broker',
            'schedule',
            'scheduleMeta',
            'scheduleMetas',
            'agendaFile',
            'policyNumber.type',
            'contacts',
            'Location',
            'externalSurveyCompany',
        ])
        ->orderBy('id', 'desc');

        if ($surveyId) {
            $surveys = $surveys->where('id', $surveyId);
        }

        $surveys = $surveys->get();

        $cards = $surveys;

        $columns=[];

        foreach ($cards as $card) { 
            if (!is_null($card->schedule)) {
                $card->schedule->getParsedMeta();
            }

            $card->submission=$card->submission();

            $card=json_decode($card);

            $card->additional_data = array();
            $column=static::getCardColumnByCriteria($card, $type);
            $broker_column=static::getCardColumnByCriteria($card, $type, true);
            $columns[$column]['cards'][] = (object) [
                'underwriter_id' => $card->underwriter_id,
                'title'         => $card->organisation->name ?? 'NA',
                'broker_column' => $broker_column,
                'url'           => '/surveys/'. $card->id,
                'survey_id'     => $card->id,
                'survey_date'   => isset($card->schedule->actual_submission_deadline) ? date('d/m/Y', strtotime($card->schedule->actual_submission_deadline)) : '-',
                'properties'    => [
                    'SRF'                 => isset($card->legacy_srf) && $card->legacy_srf != null ? 'SRF'.$card->legacy_srf : 'SRF'.$card->id,
                    'Survey Type'         => isset($card->policy_number->type->name) && $card->policy_number->type->name != null ? $card->policy_number->type->name : '',
                    'Survey Date'         => isset($card->schedule->actual_submission_deadline) ? date('d/m/Y', strtotime($card->schedule->actual_submission_deadline)) : '-',
                    'Additional Statuses' => $this->additionalInfo($card),
                    'mga_scheme'          => isset($card->organisation->mga_scheme) ? $card->organisation->mga_scheme : 'NA',
                    'Postcode'            => isset($card->location) ? $card->location->postcode : 'N/A',
                    'organisation_id'     => $card->organisation_id,
                    'survey_form_id'         => $card->survey_form,
                    'key_account'         => $card->organisation_id,
                    'branch_id'         => $card->branch_id,
                ],
            ];
        }

        if ($surveyId) {
            $this->info('Dropping cards of ' . $surveyId . '...');
            SurveyCards::where('survey_id', $surveyId)->delete();
        } else {
            $this->info('Dropping all cards...');
            SurveyCards::truncate();
        }

        foreach($columns as $column => $cards) {            
            foreach($cards as $card) {
                foreach($card as $value) {
                    SurveyCards::create([
                        'underwriter_id' => $value->underwriter_id,
                        'survey_id' => explode('SRF', $value->properties['SRF'])[1],
                        'survey_date' => $value->survey_date,
                        'title' => $value->title,
                        'column' => $column,
                        'broker-column' => $value->broker_column,
                        'url' => $value->url,
                        'properties' => $value->properties
                    ]);
                }
            }
        }
    }

    private static function getCardColumnbyCriteria($card, $type, $isBroker = false)
    {
        if($isBroker && isset($card->submission) && isset($card->submission->csr_status) && $card->submission->csr_status == 'submitted') {
            return 'survey-completed';
        }

        //Underwriter deadline set
        try {
            $underwriter_date = isset($card->schedule->underwriter_deadline) ? \Carbon\Carbon::parse($card->schedule->underwriter_deadline) : null;
        } catch (Exception $ex) {
            $underwriter_date = isset($card->schedule->underwriter_deadline) ? \Carbon\Carbon::createFromFormat('d/m/Y', $card->schedule->underwriter_deadline) : null;
        }

        //CSR Deadline set
        try {
            $csr_date = isset($card->schedule->client_survey_report_deadline) ? \Carbon\Carbon::parse($card->schedule->client_survey_report_deadline) : null;
        } catch (Exception $ex) {
            $csr_date = isset($card->schedule->client_survey_report_deadline) ? \Carbon\Carbon::createFromFormat('d/m/Y', $card->schedule->client_survey_report_deadline) : null;
        }


        if ($type == 'surveys') {
            
            switch (true) {
                //APPROVED UWR

            case $card->uwr_uw_status == '2':
                return 'approved-uwr';

                //APPROVED CSR
            case $card->csr_uw_status == '2':
                if (isset($card->schedule->actual_submission_deadline) && isset($underwriter_date) && $underwriter_date != null && $underwriter_date < \Carbon\Carbon::now() && ((isset($card->submission->uwr_status) && $card->submission->uwr_status != 'submitted') || !isset($card->submission->uwr_status))) {
                    if (!$isBroker) {
                        return 'past-due-uwr';
                    }else{
                        if(!isset($card->submission) && isset($card->schedule->actual_submission_deadline) && \Carbon\Carbon::parse($card->schedule->actual_submission_deadline) < \Carbon\Carbon::now()) {
                            if ($isBroker) {
                                return 'survey-completed';
                            }
                        }
                        if(!isset($card->submission) && isset($card->schedule->actual_submission_deadline)) {
                            return 'survey-date-booked';
                        }
                    }
                }
                return 'approved-csr';


                //SENT TO UWR
            case $card->uwr_uw_status == '1':
                return 'underwriter-uwr';

                //SENT TO CSR
            case $card->csr_uw_status == '1':
                return 'underwriter-csr';

                //QA UWR
            case $card->uwr_qa_status == '1':
                if (!$isBroker) {
                    return 'quality-assurance-uwr';
                }

                //QA CSR
            case $card->csr_qa_status == '1':
                if (!$isBroker) {
                    return 'quality-assurance-csr';
                }
                
            case isset($card->submission->uwr_status) && $card->submission->uwr_status === 'submitted':

                return 'submitted-uwr';

            case isset($card->submission->csr_status) && $card->submission->csr_status == 'submitted':
                return 'submitted-csr';

            case isset($card->submission->uwr_status) && $card->submission->uwr_status != 'submitted':
                if (!$isBroker) {
                    return 'draft-report-in-progress-uwr';
                }
                
            case isset($card->submission->csr_status) && $card->submission->csr_status != 'submitted':
                if (!$isBroker) {
                    return 'draft-report-in-progress-csr';
                }
                
                //Past Due CSR
            case isset($card->schedule->actual_submission_deadline) && isset($csr_date) && $csr_date != null && $csr_date < \Carbon\Carbon::now() && ((isset($card->submission->csr_status) && $card->submission->csr_status != 'submitted') || !isset($card->submission->csr_status)):
                if (!$isBroker) {
                    return 'past-due-csr';
                }
                //Past DUE UWR
            case isset($card->schedule->actual_submission_deadline) && isset($underwriter_date) && $underwriter_date != null && $underwriter_date < \Carbon\Carbon::now() && ((isset($card->submission->uwr_status) && $card->submission->uwr_status != 'submitted') || !isset($card->submission->uwr_status)):
                if (!$isBroker) {
                    return 'past-due-uwr';
                }
            case isset($card->schedule->actual_submission_deadline) && \Carbon\Carbon::parse($card->schedule->actual_submission_deadline) < \Carbon\Carbon::now():
                if ($isBroker) {
                    return 'survey-completed';
                }

            case isset($card->schedule->actual_submission_deadline):
                return 'survey-date-booked';

            case $card->external_survey_company || (!$card->external_survey_company && isset($card->surveyor_id)):
                if (!$isBroker) {
                    return 'assigned';
                }

                // no break
            default:
                $ret = 'backlog';
                if ($isBroker) {
                    $ret = 'survey-date-booked';
                }

                return $ret;
            }
        }       
    }
}