<?php

namespace App\Console\Commands;

use App\Http\Controllers\Api\V1\SurveyController;
use App\Models\RiskImprovementFormySubmissions;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AutoApproveCSR extends Command {

	protected $queue;

	protected $email_subject = 'Risk Recommendation Reminder';
	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'riskreduce:auto-approve-csr';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Risk Reduce Auto approve CSR';

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
	}

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{
		//

		$this->approve();

	}



	private function approve()
	{
        $surveyController = app()->make(SurveyController::class);
        $submissions = $this->getSubmissions();
        $approvedSurveyIds = [];

        foreach ($submissions as $submission) {
            $survey = $submission->survey;
            
            if (!empty($survey) && $survey->csr_uw_status !== 2) {
                try {
                    $myRequest = new Request();
                    $myRequest->setMethod('POST');
                    $myRequest->request->add([
                        'csr_uw_status' => 2
                    ]);
                    $surveyController->update($myRequest, $survey->id);

                    $submission->csr_auto_approved_at = now()->toDateTimeString();
                    $submission->save();
                    $approvedSurveyIds[] = $survey->id;
                } catch (\Throwable $th) {
                    Log::error("ERROR][COMMAND][riskreduce:auto-approve-csr]");
                    Log::error($th->__toString());
                }
            }
        }

        $this->info("Approved CSR:");
        $this->info(json_encode($approvedSurveyIds));
	}

    private function getSubmissions()
    {
        $date = now()->subWeekdays(config('app.csr_auto_approve_after_days'))->format('d/m/Y');
        return RiskImprovementFormySubmissions
            ::with('survey')
            ->where('csr_submission_date', $date)
            ->where('csr_status', 'submitted')
            ->select('csr_submission_date', 'csr_status', 'survey_id')
            ->orderByDesc('_id')
            ->get();
    }

    private function getSubmissionsByDate()
    {
        return DB::connection('mongodb')
            ->collection('ri_submissions')->raw(function ($collection) {
                return $collection->aggregate(
                    [
                        ['$match' => [
                            'csr_status' => [
                                '$eq' => 'submitted'
                            ],
                        ]],
                        [
                            '$addFields' => [
                                'csr_date_sub_arr' => [
                                    '$split' => [
                                        '$csr_submission_date', '/'
                                    ]
                                ]
                            ]
                        ], // explode to date parts
                        [
                            '$addFields' => [
                                'csr_date_sub_format' => [
                                    '$reduce' => [
                                        'input' => [
                                            '$reverseArray' => '$csr_date_sub_arr'
                                        ],
                                        'initialValue' => '',
                                        'in' => [
                                            '$concat' => [
                                                '$$value',
                                                [
                                                    '$cond' => [
                                                        [
                                                            '$eq' => [
                                                                '$$value',
                                                                ''
                                                            ]
                                                        ],
                                                        '',
                                                        '-'
                                                    ]
                                                ],
                                                '$$this'
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ], // implode in reverse order
                        [
                            '$match' => [
                                'csr_date_sub_format' => [
                                    '$gte' => now()->subDays(5)->format('Y-m-d'),
                                    '$lte'  => now()->subDays(10)->format('Y-m-d')
                                ]
                            ]
                        ], // date range filter
                        [
                            '$sort' => [
                                'updated_at' => -1
                            ]
                        ], // order by
                        // [
                        //     '$limit' => 10
                        // ] // Limit
                    ],
                    [
                        'cursor' => (object)[],
                        'allowDiskUse' => true 
                    ], // Needed for aggregate to work,
                );
            });
    }

	/**
	 * Get the console command arguments.
	 *
	 * @return array
	 */
	protected function getArguments()
	{
		return array(
		);
	}

	/**
	 * Get the console command options.
	 *
	 * @return array
	 */
	protected function getOptions()
	{
		return array(
		);
	}

}
