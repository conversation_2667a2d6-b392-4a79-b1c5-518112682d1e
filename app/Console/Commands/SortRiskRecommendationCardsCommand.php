<?php

namespace App\Console\Commands;

use App\Models\RiskImprovementFormySubmissions;
use App\Models\Survey;
use App\Models\RiskRecommendationCards;
use Aws\Laravel\AwsFacade as AWS;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

class SortRiskRecommendationCardsCommand extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'riskrec:cards {id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $submissionId = $this->argument('id');

        $submission = RiskImprovementFormySubmissions::with('survey:id,resurvey_id')
            ->select(['_id', 'survey_id', 'csr_status'])
            ->where('_id', $submissionId)
            ->first();

        if ($submission->csr_status === 'submitted' || !empty($submission->survey->resurvey_id)) {
            AWS::createClient('Sqs')->sendMessage([
                'QueueUrl' => Config::get('app.aws.sqs_kanban_sync_queue'),
                'MessageBody' => json_encode(['submission_id' => $submission->_id]),
                'MessageAttributes' => [],
                'DelaySeconds' => 2,
            ]);
        }
    }
}
