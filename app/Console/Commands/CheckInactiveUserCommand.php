<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Notifications\GDPR\SendNotification;
use App\Services\GDPR\GetInactiveUserService;

class CheckInactiveUserCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:check-inactive';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check inactive users and notify them to if they want to delete/anonymise their data.';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(GetInactiveUserService $getInactiveUserService): void
    {
        $users = $getInactiveUserService->get();
        if ($users->isEmpty()) {
            return;
        }

        $users->each(function ($user) {
            if (empty($user)) {
                SendNotification::sendInactiveAccountEmail($user->toArray());
                Log::info("Sending Inactive Notification email to: {$user->email}");
            }
        });

        $this->info($users);
    }
}
