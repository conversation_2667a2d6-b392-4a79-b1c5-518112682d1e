<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Survey;
use App\Models\RiskRecommendationNotification;

class ChaserCronSyncCommand extends Command
{

    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'sync:chaser-notification';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command syncs SRF to CRON table monitor';

        /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function fire()
    {
        $surveys = Survey::get();        
        foreach($surveys as $survey)
        {
            RiskRecommendationNotification::monitor($survey->id);
            //\Log::info($survey->id);
        }
    }

    /**
     * Get the console command arguments.
     *
     * @return array
     */
    protected function getArguments()
    {
        return array(
            //array('example', InputArgument::REQUIRED, 'An example argument.'),
        );
    }

    /**
     * Get the console command options.
     *
     * @return array
     */
    protected function getOptions()
    {
        return array(
            //array('example', InputArgument::REQUIRED, 'An example argument.'),
        );
    }
}