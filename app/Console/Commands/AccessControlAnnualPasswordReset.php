<?php

namespace App\Console\Commands;

use App\Helpers\Helper;
use Illuminate\Console\Command;
use App\Services\AccessControl\AnnualPasswordResetService;
use Carbon\Carbon;

class AccessControlAnnualPasswordReset extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'access-control:annual-password-reset';
    private $annualPasswordResetService;
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "This command will check the users' password_update_change and force reset the user's password if it has been 1 year.";

    public function __construct(AnnualPasswordResetService $annualPasswordResetService)
    {
        parent::__construct();
        $this->annualPasswordResetService = $annualPasswordResetService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $effectivityDate = Carbon::parse(config('app.password_expired_effectivity_date'));
        if (Helper::shouldTakeEffect($effectivityDate)) {
            $this->info($this->annualPasswordResetService->fetchAnnualPasswordChangeUpdate());
        }
    }
}
