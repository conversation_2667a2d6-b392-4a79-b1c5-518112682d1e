<?php

namespace App\Console\Commands;

use App\Enums\KanbanStatus;
use App\Models\Messaging;
use App\Models\RiskImprovementFormySubmissions;
use App\Models\RiskRecommendationCards;
use App\Models\RiskRecommendationLogs;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Config;
use Aws\Laravel\AwsFacade as AWS;
use App\Services\RiskGradingService;

class SyncKanbanBoardForSurvey extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'riskreduce:sync-riskrec-cards-kanban';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */

    public function handle()
    {
        $this->recieveMessages();
    }

    private function recieveMessages()
    {
        $url = (string) Config::get('app.aws.sqs_kanban_sync_queue');
        $messages = [];
        try{
            if (isset($url) && !empty($url)) {
                $messages = AWS::createClient('Sqs')->receiveMessage([
                    'QueueUrl'              => $url,
                    'MaxNumberOfMessages'   => 5,
                    'MessageAttributeNames' => [],
                ]);
            } else {
                \Log::info('Invalid configuration');
            }
        }catch(\Exception $e){
            $this->info($e->getMessage());
        }

        if(!$messages) return;
        
        foreach($messages as $message){
            foreach($message as $msg){
                try{
                    $body = isset($msg['Body']) ? $msg['Body'] : '';
                    if(!$body) continue;
                    $survey=json_decode($body);
                    if(empty($survey->submission_id)) continue;
                    $this->processSubmission($survey->submission_id);
                    $receiptHandle = isset($msg['ReceiptHandle']) ? $msg['ReceiptHandle'] : '';
                    $this->deleteAllQueue($receiptHandle);
                }catch(\Exception $e){
                    \Log::info($e->getMessage());
                    \Log::info($e->getTraceAsString());
                    $this->info($e->getMessage());
                }
            }
        }
    }

    private function deleteAllQueue($message)
    {
        if($message){
            $url = (string) Config::get('app.aws.sqs_kanban_sync_queue');
            AWS::createClient('Sqs')->deleteMessage([
                'QueueUrl'      => $url,
                'ReceiptHandle' => $message
            ]);
        }
    }


    public function processSubmission($submissionId)
    {
        \Log::info('riskreduce:sync-riskrec-cards-kanban:' . $submissionId);
       
        $submissions = RiskImprovementFormySubmissions::with([
                'formy',
                'surveyContacts',
                'riskRecommendationForm',
                'survey' => [
                    'existingPolicyNumber',
                    'schedule' => [
                        'meta',
                    ],
                    'organisation',
                    'location',
                ],
            ])
            ->where('csr_status', '=', 'submitted')
            ->where('_id', $submissionId)
            ->get();

        if(count($submissions) <= 0){
            exit;
        }

        $survey=$submissions[0]['survey'];

        $isDtr = $survey?->isDtr() ?? false;
        if ($isDtr && empty($survey?->policy_id)) {
            exit;
        }

        $survey_id=$survey->id;
        $resurvey_id=$survey->resurvey_id;
       
        $surveyMessages = $this->getSurveyMessages($submissions);

        $fields = [
            '_ref',
            '_message',
            '_classification',
            '_required_by',
            '_issue_closed',
            '_description',
            '_action',
            '_title',
            '_issue_closed',
        ];
        $policies = ['1' => 'Property', '2' => 'Casualty', '3' => 'Commercial Combined', '5' => 'Construction All Risk'];

        RiskRecommendationCards::where('submission_id', $submissionId)->delete();
       
        foreach ($submissions as $submission) {
            if (!$submission->survey) {
                continue;
            }

            $risk_rec_array = [];
            if (isset($submission->riskRecommendationForm->risk_recommendation_fields)) {
                $risk_rec_array = $submission->riskRecommendationForm->risk_recommendation_fields;
            } else {
                $form = $submission->formy;
                if (isset($form->fields)) {
                    foreach ($form->fields as $field) {
                        foreach ($field as $key => $value) {
                            if ($key == 'risk_recommendation') {
                                foreach ($value as $val) {
                                    if ($val['name'] == 'name') {
                                        $risk_rec_array[] = $val['value'];
                                    }
                                }
                            }
                        }
                    }
                }
            }

            $schedule = $submission->formatSchedule($submission->survey->schedule);
            if($schedule){
                $schedule->actual_submission_deadline = $submission->survey?->scheduleMeta?->value;
            }


            foreach ($risk_rec_array as $risk_rec) {
                for ($i = 1; $i <= 15; ++$i) {
                    $card = ['submission_id' => $submission->_id];
                    $risk_rec_prefix = $risk_rec . '_' . $i;
                    $hasMessage = false;
                    $exportProperties = [];

                    foreach ($fields as $field) {
                        if (isset($submission->{$risk_rec_prefix . $field})) {
                            if ($field === '_classification' && $submission->{$risk_rec_prefix . '_classification'} != '') {

                                $srfValue = isset($submission->survey->legacy_srf) && $submission->survey->legacy_srf != null
                                    ? 'SRF' . $submission->survey->legacy_srf
                                    : 'SRF' . $submission->survey->id;

                                $surveyDate = isset($schedule)
                                    ? (isset($schedule->actual_submission_deadline)
                                        ? date('d/m/Y', strtotime($schedule->actual_submission_deadline))
                                        : '-')
                                    : null;

                                // Override values for DTR
                                if ($isDtr ) {
                                    $srfValue = 'DTR' . $submission->survey->id;
                                    $surveyDate = $submission->csr_submission_date ?? '-';
                                }


                                $policyTypeId = $submission->survey->existingPolicyNumber?->policy_type_id;

                                $properties = [
                                    'client' => $submission->survey->organisation != null
                                        ? $submission->survey->organisation->name
                                        : 'N/A',
                                    'srf' => $srfValue,
                                    'survey_type' => in_array($policyTypeId, array_keys($policies))
                                        ? $policies[$policyTypeId]
                                        : '',
                                    'survey_date' => $surveyDate,
                                    'mga_scheme' => $submission->survey->organisation->mga_scheme,
                                    'required_by' => isset($submission->{$risk_rec_prefix . '_required_by'})
                                        ? $submission->{$risk_rec_prefix . '_required_by'}
                                        : '-',
                                    'location' => [
                                        'location_name' => $submission->survey->location?->location_name,
                                        'postcode' => $submission->survey->location?->postcode,
                                    ]
                                ];

                                // region Fields for export
                                $postcode = 'N/A';
                                if (isset($submission->survey->visit_arrangement) && $submission->survey->visit_arrangement != '' && isset($submission->surveyContacts)) {
                                    foreach ($submission->surveyContacts as $contact) {
                                        if ($contact->postcode != '' && !is_null($contact->postcode)) {
                                            $postcode = $contact->postcode;
                                        }
                                    }
                                }

                                $exportProperties['organisation'] = isset($submission->survey->organisation->name) && $submission->survey->organisation->name != null
                                    ? $submission->survey->organisation->name
                                    : '';
                                $exportProperties['description'] = isset($submission->{$risk_rec_prefix . '_description'})
                                    ? $submission->{$risk_rec_prefix . '_description'}
                                    : '-';
                                $exportProperties['action'] = isset($submission->{$risk_rec_prefix . '_action'})
                                    ? $submission->{$risk_rec_prefix . '_action'}
                                    : '-';
                                $exportProperties['title'] = isset($submission->{$risk_rec_prefix . '_title'})
                                    ? $submission->{$risk_rec_prefix . '_title'}
                                    : '-';
                                $exportProperties['closed'] = isset($submission->{$risk_rec_prefix . '_issue_closed'})
                                    ? $submission->{$risk_rec_prefix . '_issue_closed'}
                                    : '0';
                                $exportProperties['csr_status'] = isset($submission->csr_ststus)
                                    ? $submission->csr_ststus
                                    : 'completed';
                                $exportProperties['postcode'] = $postcode;
                                $exportProperties['classification'] = isset($submission->{$risk_rec_prefix . '_classification'})
                                    ? $submission->{$risk_rec_prefix . '_classification'}
                                    : '-';
                                // endregion

                                foreach ($surveyMessages as $surveyMessage) {
                                    if ($surveyMessage->rr_ref === $risk_rec_prefix . '_message') {
                                        if (isset($surveyMessage->sent_at)) {
                                            $hasMessage = true;
                                            break;
                                        }
                                    }
                                }
                                // region Distribute cards to correct columns
                                $current_date = isset($submission->{$risk_rec_prefix . '_required_by'}) && ($submission->{$risk_rec_prefix . '_required_by'} != '')
                                    ? Carbon::createFromFormat(
                                        'd-m-Y',
                                        str_replace('/', '-', $submission->{$risk_rec_prefix . '_required_by'})
                                    )
                                    : null;
                                $column = KanbanStatus::OPEN->value;
                                if (isset($submission->{$risk_rec_prefix . '_issue_closed'}) && $submission->{$risk_rec_prefix . '_issue_closed'} == '1') {
                                    $column = KanbanStatus::CLOSED->value;
                                } else {
                                    if (isset($current_date) && $current_date->lt(Carbon::now())) {
                                        $column = KanbanStatus::OVERDUE->value;
                                    } else {
                                        if ($hasMessage && ((isset($current_date) && $current_date->gte(Carbon::now())) || !isset($current_date))) {
                                            $column = KanbanStatus::FEEDBACK_RECEIVED->value;
                                        }
                                    }
                                }
                                $card['column'] = $column;
                                // endregion

                                $card['survey_id'] = $submission->survey_id;
                                $card['title'] = $submission->{$risk_rec_prefix . '_ref'};
                                $card['url'] = '/surveys/report/' . $submission->survey->id . '/risk-recommendation/' . $risk_rec_prefix . '_';
                                $card['properties'] = $properties;

                                // used for filtering
                                $card['export_properties'] = $exportProperties;
                                $card['organisation_id'] = $submission->survey->organisation_id;
                                $card['branch_id'] = $submission->survey->branch_id;
                                $card['underwriter_id'] = $submission->survey->underwriter_id;

                                // used for sorting
                                $card['survey_date'] = isset($properties['survey_date']) && $properties['survey_date'] !== '-'
                                    ? Carbon::createFromFormat('d/m/Y', $properties['survey_date'])->toString()
                                    : Carbon::parse('Jan 1, 1970')->toString();

                                RiskRecommendationCards::create($card);

                                // Check if the log already exists
                                $existingLogs = RiskRecommendationLogs::where('survey_id', $submission->survey_id)
                                ->where('title', $exportProperties['title'])
                                ->where('status', $column)
                                ->first();

                                if (!$existingLogs) {
                                    RiskRecommendationLogs::create([
                                        'submission_id' => $submission->_id,
                                        'survey_id' => $submission->survey_id,
                                        'surveyor_id' => $submission->survey->surveyor_id,
                                        'risk_rec_title' => $exportProperties['title'],
                                        'status' => $column,
                                    ]);
                                }
                            }
                        }
                    }
                }
            }
        }

        $this->sendAdminSideRecacheQueue($survey->organisation_id ?? '');

        if($survey_id >0 && $resurvey_id > 0){
            //This will remove duplicates from allowed sueveys with resurvey in grading.
            $gradingService = new RiskGradingService();
            $gradingService->processKanbanTracker($survey_id);
        }
    }

    /**
     * Get all messages for all submissions
     *
     * @param  Collection  $submissions
     * @return Collection
     */
    private function getSurveyMessages(Collection $submissions): Collection
    {
        $messageSurveyIds = $submissions
            ->pluck('survey.id')
            ->transform(fn ($value, $key) => (string)$value)
            ->reject(fn ($value) => empty($value))
            ->values();

        return Messaging::whereIn('survey_id', $messageSurveyIds)->get();
    }

    private function sendAdminSideRecacheQueue($orgId)
    {
        if (empty($orgId)) {
            return;
        }

        $messages = [
            [
                'serviceClass' => 'App\Services\CacheContent\GetOrgRiskGradingData', //class is in admin side
                'params' => $orgId ?? '',
                'source' => 'FROM_API:SyncKanbanBoardForSurvey',
            ],
            [
                'serviceClass' => 'App\Services\CacheContent\GetOrganisationDetailsForDashboard', //class is in client side
                'params' => $orgId ?? '',
                'source' => 'FROM_API:SyncKanbanBoardForSurvey',
                'isClient' => true
            ],
        ];

        foreach ($messages as $message) {
            AWS::createClient('Sqs')->sendMessage([
                'QueueUrl'          => self::getQueueUrl($message['isClient'] ?? false),
                'MessageBody'       => json_encode($message),
                'MessageAttributes' => [],
                'DelaySeconds'      => 2
            ]);

            \Log::info("SyncKanbanBoardForSurvey Queue:" . json_encode($message));
        }
    }

    private static function getQueueUrl(bool $isClient): string
    {
        return $isClient ? Config::get('app.aws.invalidate_cache_sqs_client') : Config::get('app.aws.invalidate_cache_sqs_admin');
    }
}
