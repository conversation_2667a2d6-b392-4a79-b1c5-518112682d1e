<?php

namespace App\Console\Commands;

use Aws\Laravel\AwsFacade as AWS;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RecachePermanentCachedData extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'recache:permanent-cache';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "This command will recache Org Survey related cache in admin side.";

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $query = "SELECT organisation_id, COUNT(*) as survey_count
                FROM surveys
                GROUP BY organisation_id
                HAVING COUNT(*) < 5
                ORDER BY survey_count desc;
                ";

        $organisations = DB::select(DB::raw($query));

        foreach ($organisations as $organisation) {
            $this->sendAdminSideRecacheQueue($organisation);
        }

        Log::info("Organisations Count: " . count($organisations));
        $this->info("Organisations Count: " . count($organisations));

        Log::info("Execution Time: " . microtime(true) - LARAVEL_START);
        $this->info("Execution Time: " . microtime(true) - LARAVEL_START);
    }

    private function sendAdminSideRecacheQueue($organisation)
    {
        if (empty($organisation->organisation_id)) {
            return;
        }

        $messages = [
            [
                'serviceClass' => 'App\Services\CacheContent\GetOrgRiskGradingData', //class is in admin side
                'params' => $organisation->organisation_id ?? '',
                'surveyCount' => $organisation->survey_count ?? '',
                'source' => 'FROM_COMMAND:recache:permanent-cache',
            ],
            [
                'serviceClass' => 'App\Services\CacheContent\GetOrganisationDetailsForDashboard', //class is in client side
                'isClient' => true,
                'params' => $organisation->organisation_id ?? '',
                'surveyCount' => $organisation->survey_count ?? '',
                'source' => 'FROM_COMMAND:recache:permanent-cache',
            ]
        ];

        foreach ($messages as $message) {
            AWS::createClient('Sqs')->sendMessage([
                'QueueUrl'          => self::getQueueUrl($message['isClient'] ?? false),
                'MessageBody'       => json_encode($message),
                'MessageAttributes' => [],
                'DelaySeconds'      => 2
            ]);

            Log::info("recache:permanent-cache Queue Sent: " . json_encode($message));
            $this->info("recache:permanent-cache Queue Sent: " . json_encode($message));
        }
    }

    private static function getQueueUrl(bool $isClient): string
    {
        return $isClient ? Config::get('app.aws.invalidate_cache_sqs_client_temp') : Config::get('app.aws.invalidate_cache_sqs_admin_temp');
    }
}
