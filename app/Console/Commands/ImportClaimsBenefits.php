<?php

namespace App\Console\Commands;

use App\Models\ClaimType;
use App\Models\Cms;
use App\Models\Sector;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ImportClaimsBenefits extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'riskreduce:import-claims
                            {--truncate : Flag to truncate records}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import claims';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $cto     = null;
        $url     = 'workspaces/5d498d6f08936701996cc232'
                 . '/content-types/5d36dfb9bf3b21131941e7e3'
                 . '/content-entries?query={"order":{"name":"asc"},"status":"publish","operator":"="}';
        $sectors = json_decode(Cms::get($url));

        if ($this->option('truncate')) {
            $cto = $this->handleTruncate();
            DB::statement("SET foreign_key_checks=0");
            ClaimType::truncate();
            DB::statement("SET foreign_key_checks=1");
        }

        $sector_name = null;
        if (count($sectors->data) > 0) {
            foreach ($sectors->data as $sector) {
                $sectorModel = Sector::where('type', $sector->name)
                    ->orWhere('type', html_entity_decode($sector->name))->first();
                $sector_id = null;

                if ($sectorModel) {
                    $sector_id = $sectorModel->id;
                }

                foreach ($sector as $prop => $val) {
                    if (stripos($prop, 'claims_benefits') !== false && count($val) >= 1 && $val !== '') {
                        foreach ($val as $claim) {
                            $icon = null;

                            $claimType = ClaimType::where('name', $claim->name)
                                            ->where('sector_id', $sector_id)
                                            ->first();

                            if (!$claimType) {
                                $claimType = new ClaimType();
                            }

                            $claimType->name = $claim->name;
                            $claimType->url = 'http://google.com';

                            foreach ($claim as $prop => $val) {
                                if (stripos($prop, 'icon') !== false && count($val) >= 1 && $val !== '') {
                                    $claimType->icon = $val[0]->url;
                                }
                            }

                            $claimType->sector_id = $sector_id;

                            $claimType->save();

                            if ($this->option('truncate') && !$cto->isEmpty()) {
                                $toInserts = Arr::where($cto->toArray(), function ($key, $val) use ($claimType) {
                                    return $val->name == $claimType->name && $val->sector_id == $claimType->sector_id;
                                });

                                foreach ($toInserts as $toInsert) {
                                    DB::table('claim_type_organisation')->insert([
                                        'claim_type_id' => $claimType->id,
                                        'organisation_id' => $toInsert->organisation_id,
                                        'created_at' => Carbon::now(),
                                        'updated_at' => Carbon::now(),
                                    ]);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    protected function handleTruncate(): Collection
    {
        $cto = DB::table('claim_type_organisation as cto')
            ->join('claim_types as ct', 'ct.id', '=', 'cto.claim_type_id')
            ->select('ct.*', 'cto.*', 'cto.id as id')
            ->get();

        DB::table('claim_type_organisation')->truncate();
        return $cto;
    }
}
