<?php

namespace App\Console\Commands;

use <PERSON><PERSON>a\Twilio\Twilio;
use Twi<PERSON>\Rest\Client;
use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Models\LetsTalk\VideoCallParticipant;
use App\Models\LetsTalk\VideoCallRoom;
use App\Models\LetsTalk\ResourceBooking;
use App\Models\Mailqueue;
use Illuminate\Support\Facades\Log; 
use Illuminate\Support\Facades\Config;
class VrBeforeMeetingReminder extends Command 
{
	private $twilio;
	private $twilioClient;
	private $twilioConfigUS;
	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'virtual-rooms:before-meeting-reminder';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Remind participants 15 minuites before the actual video conference.';

	 /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
		$this->mail = new Mailqueue();
		$twilioConfig = Config::get('app.twilio.sms');
		$this->twilio = new Twilio($twilioConfig['sid'], $twilioConfig['token'], $twilioConfig['from']);
		$this->twilioConfigUS = new Twilio($twilioConfig['sid'], $twilioConfig['token'], $twilioConfig['from-us']);
        $this->twilioClient = new Client($twilioConfig['sid'], $twilioConfig['token']);
	}

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{
		$this->info('Display this on the screen');
		$bookings = ResourceBooking::where('is_reminded', 0)
			->where('is_call_now', 0)->get();
			
		$now = Carbon::now();
		foreach ($bookings as $booking) {

			$client = "";
			$company = "";
			$email = "";
			$client_phone = "";
			$meeting_subject = "";

			echo "\n";
			$parsed_date = Carbon::parse($booking->from);
			$startDate = $parsed_date->subMinutes(7);
			if ($startDate->lessThanOrEqualTo($now)) {
				ResourceBooking::where('id', $booking->id)
					->update(['is_reminded' => 1]);

				// Collect all data required
				foreach ($booking->participants as $participant) 
				{
					if ($participant->role=='client') 
					{
						$client = $participant->user_name;
						$company = $participant->company;
						$email = $participant->email;
						$client_phone = $participant->mobile_number;
						$meeting_subject = $participant->meeting_subject;
					}

					if ($participant->role=='liberty_representative') 
					{
						// get the main liberty_representative
						$mainLibertyRepresentative = VideoCallParticipant::where('role', 'liberty_representative')
							->where('room_id', $participant->room_id)
							->where('notes', '!=', "invited from either invite page(laravel) or (react)")
							->first();
	
						$liberty_representative = $mainLibertyRepresentative->user_name;
						$liberty_representative_email = $mainLibertyRepresentative->email;
						$liberty_representative_job_title = $mainLibertyRepresentative->representative_job_title;
					}
				}
			
				// Submit to queue
				foreach ($booking->participants as $participant) 
				{
					if ($participant->role!='receptionist') {

						$clientsLink = VideoCallParticipant::generateLink($participant->user_code);

						// $clientsLink = parse_url($clientsLink);
                		// $clientsLink = $clientsLink['scheme']."://".$clientsLink['host']."\r\n".$clientsLink['path'];

						$data = [
							'booking_type' => 'future',
							'client' => $client,
							'company' => $company,
							'email' => $email,
							'client_phone' => $client_phone,
							'schedule' => $booking->from.' to '.$booking->to,
							'time_start' => date("H:i", strtotime($booking->from)),
							'meeting_subject' => $meeting_subject,
							'liberty_representative' => $liberty_representative,
							'liberty_representative_email' => $liberty_representative_email,
							'liberty_representative_job_title' => $liberty_representative_job_title 
						];

						$twilio_data = $clientsLink;
                		$clientsLink = parse_url($clientsLink);
                		$clientsLink = $clientsLink['scheme']."://".$clientsLink['host']."\r\n\t".$clientsLink['path'];

						$viewParameters = [
							'to_name' => $participant->user_name,
							'video_call_details' => $data,
							'twilio_data' => $twilio_data,
							'role' => $participant->role,
							'is_reminder' => false,
							'is_reminder_before_5' => true,
							'is_added_participant' => false,
						];

						$datetime_start = date("Ymd\THis", strtotime($booking->from));
						$datetime_end = date("Ymd\THis", strtotime($booking->to));
					
						if ($participant->role=='client' || $participant->role=='guest_clients') {
							$yourSalutation = true;
							//$message = VideoCallParticipant::generateMessageN11($participant->user_code, $liberty_representative);

							$roomCode = VideoCallRoom::where('_id', $participant->room_id)->first();
							$message = VideoCallParticipant::generateMessageN11($roomCode->name, $liberty_representative);

							// $fullDescription = "DESCRIPTION;LANGUAGE=en-US:".$data['meeting_subject']." Link: ".$clientsLink;
                            // $chunks = str_split($fullDescription, 75);
                            // $description = implode("\r\n\t", $chunks);

							// $attachment = [
							// 	'ics' => [
							// 		'StringValue' =>"BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Virtual Rooms//EN\r\nMETHOD:REQUEST\r\nBEGIN:VTIMEZONE\r\nTZID:Europe/London\r\nBEGIN:DAYLIGHT\r\nTZOFFSETFROM:+0000\r\nTZOFFSETTO:+0100\r\nTZNAME:BST\r\nDTSTART:19700329T010000\r\nRRULE:FREQ=YEARLY;BYMONTH=3;BYDAY=-1SU\r\nEND:DAYLIGHT\r\nBEGIN:STANDARD\r\nTZOFFSETFROM:+0100\r\nTZOFFSETTO:+0000\r\nTZNAME:GMT\r\nDTSTART:19701025T020000\r\nRRULE:FREQ=YEARLY;BYMONTH=10;BYDAY=-1SU\r\nEND:STANDARD\r\nEND:VTIMEZONE\r\nBEGIN:VEVENT\r\nSUMMARY:Liberty Virtual Rooms with ".$data['liberty_representative']."\r\n".$description."\r\nDTSTART;VALUE=DATE-TIME:".$datetime_start."\r\nDTEND;VALUE=DATE-TIME:".$datetime_end."\r\nDTSTAMP;VALUE=DATE-TIME:".$datetime_start."\r\nUID:".str_random(40)."\r\nSEQUENCE:1\r\nATTENDEE:".$data['email']."\r\n"."CREATED;VALUE=DATE-TIME:".$datetime_start."\r\nURL;VALUE=URI:".$clientsLink."\r\nLOCATION:".$clientsLink."\r\nORGANIZER:mailto:".$data['email']."\r\nPRIORITY:5\r\nSTATUS:CONFIRMED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-P15M\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR",
							// 		'DataType' => 'String'
							// 	]
							// ];
							
							// $attachment = null;
						}

						if ($participant->role=='liberty_representative') {
							$yourSalutation = false;
							$roomCode = VideoCallRoom::where('_id', $participant->room_id)->first();
							$message = VideoCallParticipant::generateMessageN12($roomCode->name, $client, $company);

							// $fullDescription = "DESCRIPTION;LANGUAGE=en-US:".$data['meeting_subject']." Link: ".$clientsLink;
                            // $chunks = str_split($fullDescription, 75);
                            // $description = implode("\r\n\t", $chunks);

							// $attachment = [
							// 	'ics' => [
							// 		'StringValue' =>"BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Virtual Rooms//EN\r\nMETHOD:REQUEST\r\nBEGIN:VTIMEZONE\r\nTZID:Europe/London\r\nBEGIN:DAYLIGHT\r\nTZOFFSETFROM:+0000\r\nTZOFFSETTO:+0100\r\nTZNAME:BST\r\nDTSTART:19700329T010000\r\nRRULE:FREQ=YEARLY;BYMONTH=3;BYDAY=-1SU\r\nEND:DAYLIGHT\r\nBEGIN:STANDARD\r\nTZOFFSETFROM:+0100\r\nTZOFFSETTO:+0000\r\nTZNAME:GMT\r\nDTSTART:19701025T020000\r\nRRULE:FREQ=YEARLY;BYMONTH=10;BYDAY=-1SU\r\nEND:STANDARD\r\nEND:VTIMEZONE\r\nBEGIN:VEVENT\r\nSUMMARY:Liberty Virtual Rooms with ".$data['client']." from ".$data['company']."\r\n".$description."\r\nDTSTART;VALUE=DATE-TIME:".$datetime_start."\r\nDTEND;VALUE=DATE-TIME:".$datetime_end."\r\nDTSTAMP;VALUE=DATE-TIME:".$datetime_start."\r\nUID:".str_random(40)."\r\nSEQUENCE:1\r\nATTENDEE:".$data['email']."\r\n"."CREATED;VALUE=DATE-TIME:".$datetime_start."\r\nURL;VALUE=URI:".$clientsLink."\r\nLOCATION:".$clientsLink."\r\nORGANIZER:mailto:".$data['email']."\r\nPRIORITY:5\r\nSTATUS:CONFIRMED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-P15M\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR",
							// 		'DataType' => 'String'
							// 	]
							// ];
							
							//$attachment = null;
						}
						
						// $this->mail->queue(
						// 	$participant->email, $participant->user_name, 
						// 	'Starting - '.(($yourSalutation) ? 'Your' : '').' Liberty Virtual Rooms', 
						// 	'emails.lets-talk.notification', 
						// 	$viewParameters,
						// 	$attachment
						// );

						$statusCallBack = Config::get('app.twilio.statusCallBackUrl');
						$params = !empty($statusCallBack) ? ['statusCallback' => Config::get('app.client_frontend').'/'.$statusCallBack] : [];

                        $logDetails = [
                            'user_name' => $participant->user_name,
                            'mobile_number' => $participant->mobile_number,
                            'room_id' => $participant->room_id
                        ];

                        // skip participants that does not have mobile number
                        if(empty($participant->mobile_number)) {
                            Log::info("Can't Notify: ", $logDetails);
                            $this->info("Can't Notify: " . json_encode($logDetails));
                            continue;
                        } 
                        try {
							//NOTE: REMOVE COMMENT FOR SMS TESTING
							// if($this->isNumberUS($participant->mobile_number)){
							// 	$twilioSms = $this->twilioConfigUS->message(
							// 		$participant->mobile_number,
							// 		$message,
							// 		[],
							// 		$params
							// 	);
							// }else{
							// 	$twilioSms = $this->twilio->message(
							// 		$participant->mobile_number,
							// 		$message,
							// 		[],
							// 		$params
							// 	);
							// }
                            Log::info("Notified: ", $logDetails);
                            $this->info("Notified: " . json_encode($logDetails));
                        } catch (\Exception $e) {
                            $logDetails['error'] = $e->getMessage();
                            Log::info("Can't Notify: ", $logDetails);
                            $this->info("Can't Notify: " . json_encode($logDetails));
                        }
					}
				}

				$client = '';
				$company = '';
				$email = '';
				$client_phone = '';
				$meeting_subject = '';
				$liberty_representative = '';
				$liberty_representative_email = '';
				$liberty_representative_job_title = '';

				print_r("processed");	
			} else {
				print_r("waiting");
			}
		}
		echo "\n";
	}

	private function isNumberUS($stringNum){
        if(substr($stringNum, 0, 1) == '1' || substr($stringNum, 0, 2) == '+1'){
            return true;
        }
        return false;
	}
	
	/**
	 * Get the console command arguments.
	 *
	 * @return array
	 */
	// protected function getArguments()
	// {
	// 	return array(
	// 		array('example', InputArgument::REQUIRED, 'An example argument.'),
	// 	);
	// }

	/**
	 * Get the console command options.
	 *
	 * @return array
	 */
	// protected function getOptions()
	// {
	// 	return array(
	// 		array('example', null, InputOption::VALUE_OPTIONAL, 'An example option.', null),
	// 	);
	// }

}