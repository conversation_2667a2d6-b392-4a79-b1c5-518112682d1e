<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\riskGrading\RgLocationGrading;
use App\Models\riskGrading\RgLocationGradingLog;
use App\Models\RGAttribute;
use App\Models\RiskImprovementFormy;
use App\Models\RiskImprovementFormySubmissions;
class StandardRiskGradingMigrationCommand extends Command {

	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'standard-riskgrading:migrate';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Migrate old risk gradings';

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
	}

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{
		$withLegacy    = false;
		$isSrgMigrated = false;
		$rgAttributes  = RGAttribute::with('policyType', 'subAttributes')
			->get()
			->toArray();

		$allowedForms = RiskImprovementFormy::whereIn('name', [
			'OLD - DO NOT USE Commercial Casualty Survey Report - Contractors',
			'OLD - DO NOT USE Commercial Casualty Executive Report',
			'OLD - DO NOT USE Remote Risk Review - Casualty',
			'OLD - DO NOT USE Commercial Property Survey Report V3 2018',
			'OLD - DO NOT USE Commercial Property Short Form V2',
			'OLD - DO NOT USE Remote Risk Review - Property',
			'OLD - DO NOT USE Commercial Property Survey Report',
			'OLD - DO NOT USE - Commercial Property Survey Report V2 2017',
			'OLD - DO NOT USE Commercial Combined Survey Report ',
			'OLD - DO NOT USE Remote Risk Review - Combined',
			'OLD - DO NOT USE Construction CAR Report Form',
		])->pluck('_id');

		$submissions = RiskImprovementFormySubmissions::join('surveys', function($query) {
			$query->on('surveys.survey_id', '=', 'ri_submissions.survey_id');
			$query->on('surveys.survey_form', '=', 'ri_submissions.form_id');
		})->whereIn('form_id', $allowedForms)->where('csr_status', 'submitted')->with('survey')->get();

		$this->info('> Starting...');
		foreach ($submissions as $submission) {
			if (!is_null($submission->survey)) {
				try {
					$this->info('> Migrating submission #: ' . $submission->_id);
					$surveyLocation 	      = $submission->survey->location_id;
					$form 					  = RiskImprovementFormy::where('_id', $submission->form_id)->select('name')->first();

					$actualSubmissionDeadline = date('Y-m-d H:i:s');
					if (!empty($submission->csr_submission_date)) {
						list($date, $month, $year) = explode('/', $submission->csr_submission_date);
						$actualSubmissionDeadline  = $year . '-' . $month . '-' . $date . '00:00:00';
					}
	
					if (empty($surveyLocation)) {
						continue;
					}
	
					// Setting default values
					$this->setDefaults($form, $surveyLocation, $actualSubmissionDeadline, $submission);
	
					$submissionArray = $submission->toArray();
					foreach ($submissionArray as $attribute => $value) {
						$matchingAttribute = strpos($attribute, 'risk-control') !== false ? $this->hasMatchAttribute($attribute, $rgAttributes) : null;
						if ($matchingAttribute) { // If has risk-control in text
							$withLegacy		= true;
							$isSrgMigrated 	= true;
							$matchAttribute = $matchingAttribute['attribute'];
							$policyType		= $matchingAttribute['rg_policy_type_id'];
	
							// add the attribute to existing submission
							$grading = null;
							if ($value === 'Good') {
								$grading = 'Above Average';
							} elseif ($value === 'Requires Improvement') {
								$grading = 'Poor';
							} else {
								$grading = $value;
							}
	
							$submission->{'attr-' . strtolower(str_replace(' ', '_', $matchAttribute)) . '-' . $policyType} = $grading;
							// Adding sub attributes default values to avoid errors in survey reports
							foreach ($matchingAttribute['sub_attributes'] as $subAttribute) {
								$submission->{'sub-attribute-' . strtolower(str_replace(' ', '-', $subAttribute['sub_attribute'])) . '_' . strtolower(str_replace(' ', '_', $matchAttribute))  . '-' . $policyType} = 'Not Applicable / Not Assessed';
							}
	
							if (!empty($submission->survey) && !empty($submission->survey->location_id)) {
	
								$locationGrading = RgLocationGrading::where('organisation_location_id', $surveyLocation)
									->where('attribute', $matchAttribute)->first();
	
								$locationGradingLog = RgLocationGradingLog::where('organisation_location_id', $surveyLocation)
									->where('attribute', $matchAttribute)->first();
	
								if (!empty($locationGrading)) {
									$locationGrading->value = $value;
									$locationGrading->save();
								}
	
								if (!empty($locationGradingLog)) {
									$locationGradingLog->value = $value;
									$locationGradingLog->save();
								}
							}
						}
						$submission->with_legacy_data = $withLegacy;
						$submission->is_srg_migrated  = $isSrgMigrated;
						$submission->save();
					}
				} catch (\Exception $e) {
					\Log::info($e->getMessage());
				}
			}
		}
	}

	/**
	 * Set the default values of each attributes and sub attributes based on form match
	 *
	 * @param Object $form
	 * @param int $surveyLocation
	 * @param string $actualSubmissionDeadline
	 * @param Object $submission
	 * @return void
	 */
	private function setDefaults($form, $surveyLocation, $actualSubmissionDeadline, $submission)
	{
		$groupAllowedForms = [
			'property' => [
				'OLD - DO NOT USE Commercial Property Survey Report V3 2018',
				'OLD - DO NOT USE Commercial Property Short Form V2',
				'OLD - DO NOT USE Remote Risk Review - Property',
				'OLD - DO NOT USE Commercial Property Survey Report',
				'OLD - DO NOT USE - Commercial Property Survey Report V2 2017',
			],
			'casualty' => [
				'OLD - DO NOT USE Commercial Casualty Survey Report - Contractors',
				'OLD - DO NOT USE Commercial Casualty Executive Report',
				'OLD - DO NOT USE Remote Risk Review - Casualty',
			],
			'commercial_combined' => [
				'OLD - DO NOT USE Commercial Combined Survey Report ',
				'OLD - DO NOT USE Remote Risk Review - Combined',
			],
			'construction_all_risk' => [
				'OLD - DO NOT USE Construction CAR Report Form',
			],
		];

		foreach ($groupAllowedForms as $lineOfBusiness => $supportedForms) {
			if (in_array($form->name, $supportedForms)) {
				if ($lineOfBusiness === 'property') {
					$rgPolicyTypeId = [1];
				} elseif ($lineOfBusiness === 'casualty') {
					$rgPolicyTypeId = [2];
				} elseif ($lineOfBusiness === 'construction_all_risk') {
					$rgPolicyTypeId = [5];
				} else {
					$rgPolicyTypeId = [1, 2];
				}

				$attributes = RGAttribute::whereIn('rg_policy_type_id', $rgPolicyTypeId)->get();
				$isLocationGradingExist = RgLocationGrading::where('organisation_location_id', $surveyLocation)
					->exists();

				if ($isLocationGradingExist) { // delete all the data to reset the value
					RgLocationGrading::where('organisation_location_id', $surveyLocation)->delete();
					RgLocationGradingLog::where('organisation_location_id', $surveyLocation)->where('survey_id', $submission->survey_id)->delete();
				}

				foreach ($attributes as $attr) {
					$rgLocationsDef[] = [
						'policy_type_id' 		   => $attr->rg_policy_type_id,
						'organisation_location_id' => $surveyLocation,
						'attribute'		 		   => $attr->attribute,
						'attribute_type'		   => 'attribute',
						'value'			 		   => 'Not Applicable / Not Assessed',
						'created_at'			   => $actualSubmissionDeadline,
						'updated_at'			   => date('Y-m-d H:i:s'),
					];

					$rgLocationLogsDef[] = [
						'organisation_location_id' => $surveyLocation,
						'policy_type_id' 		   => $attr->rg_policy_type_id,
						'attribute'				   => $attr->attribute,
						'value'			 		   => 'Not Applicable / Not Assessed',
						'survey_id'				   => (int)$submission->survey_id,
						'created_at'			   => $actualSubmissionDeadline,
						'updated_at'			   => date('Y-m-d H:i:s'),
					];

					$submission->{'attr-' . strtolower(str_replace(' ', '_', $attr->attribute)) . '-' . $attr->rg_policy_type_id} = 'Not Applicable / Not Assessed';
					foreach ($attr->sub_attributes as $subAttr) {
						$submission->{'sub-attribute-' . strtolower(str_replace(' ', '-', $subAttr->sub_attribute)) . '_' . strtolower(str_replace(' ', '_', $attr->attribute))  . '-' . $attr->rg_policy_type_id} = 'Not Applicable / Not Assessed';
						$rgLocationsDef[] = [
							'policy_type_id' 		   => $attr->rg_policy_type_id,
							'organisation_location_id' => $surveyLocation,
							'attribute'		 		   => $subAttr->sub_attribute,
							'attribute_type'		   => 'sub-attribute',
							'value'			 		   => 'Not Applicable / Not Assessed',
							'created_at'			   => $actualSubmissionDeadline,
							'updated_at'			   => date('Y-m-d H:i:s'),
						];

						$rgLocationLogsDef[] = [
							'organisation_location_id' => $surveyLocation,
							'policy_type_id' 		   => $attr->rg_policy_type_id,
							'attribute'				   => $subAttr->sub_attribute,
							'value'			 		   => 'Not Applicable / Not Assessed',
							'survey_id'				   => (int)$submission->survey_id,
							'created_at'			   => $actualSubmissionDeadline,
							'updated_at'			   => date('Y-m-d H:i:s'),
						];
					}

					$submission->save();
				}

				// Save for rg_location_gradings
				if (!empty($rgLocationsDef)) {
					for ($i = 0; $i < count($rgLocationsDef) - 1; $i++) {
						RgLocationGrading::create($rgLocationsDef[$i]);
					}
				}

				// Save for rg_location_grading_logs
				if (!empty($rgLocationLogsDef)) {
					for ($x = 0; $x < count($rgLocationLogsDef) - 1; $x++) {
						RgLocationGradingLog::create($rgLocationLogsDef[$x]);
					}
				}

				$rgLocationsDef    = [];
				$rgLocationLogsDef = [];
			}
		}
	}

	/**
	 * Check has matching attribute inside risk grading attribute table
	 *
	 * @param string $attr
	 * @param Collection $standardRiskAttributes
	 * @return string
	 */
	private function hasMatchAttribute($attr, $standardRiskAttributes)
	{
		$attribute = str_replace('-', ' ', substr($attr, 0, strpos($attr, 'risk-control') - 1)); // Minus 1 to avoid extra "-"
		$finalAttr = trim(str_replace('overall', '', $attribute));
		foreach ($standardRiskAttributes as $riskAttribute) {
			if (strtolower($riskAttribute['attribute']) === $finalAttr) {
				return $riskAttribute;
			}
		}
		return '';
	}
}
