<?php

namespace App\Console\Commands;

use App\Models\Cms;
use App\Models\Mailqueue;
use App\Models\LibertyUser;
use Illuminate\Support\Str;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Config;
use Symfony\Component\Console\Input\InputArgument;

class ImportCMSUsers extends Command 
{
	/**
	 * The console command name.
	 *
	 * @var string
	 */
    protected $name = 'riskreduce:cms_users_import';
    
	/**
	 * The console command description.
	 *
	 * @var string
	 */
    protected $description = 'Import Liberty Users From CMS for Virtual Rooms';
    
	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
		$this->mail = new Mailqueue();
    }
    
	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{
		$this->ImportCMSUsers();
		$this->SendInvitation();
    }
    
	/**
	 * Get the console command arguments.
	 *
	 * @return array
	 */
	protected function getArguments()
	{
		return array(
			['LineOfBusinessSlug', InputArgument::REQUIRED, 'The slug of the Line of Business'],
		);
    }
    
	/**
	 * Get the console command options.
	 *
	 * @return array
	 */
	protected function getOptions()
	{
		return array(
			
		);
	}

	private function ImportCMSUsers()
	{	
		$query="SELECT COUNT(*) as 'total' FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'liberty_users' AND COLUMN_NAME = 'imported_from_cms'";		
		$column_exists=DB::select($query);		
		$column_exists=$column_exists[0]->total;		
        if($column_exists==0){
            $query="ALTER TABLE liberty_users ADD COLUMN imported_from_cms TINYINT(1) NULL";
            DB::statement($query);
		}
		$LineOfBusinessSlug = $this->argument('LineOfBusinessSlug');
		$peopleProfileCms = json_decode(Cms::get('workspaces/'.Config::get('app.cms.workspace_id').'/content-types/'.Config::get('app.cms.people_content_type').'/content-entries?joined=true&query={"'.Config::get('app.cms.liberty_representative_line_of_business').'":{"slug":"'.$LineOfBusinessSlug.'"},"operator":"=","status":"publish"}'));	
		
		$profiles=[];
		$importedUsers=[];
		$discardedUsers=[];

		foreach ($peopleProfileCms->data as $profile) {
			$user=[];
			$user['email']=isset($profile->{Config::get('app.cms.user.email')})?$profile->{Config::get('app.cms.user.email')}:'';
			if(!empty($user['email'])){
				$exists = LibertyUser::where('email',$user['email'])->count();
				if ($exists==0) {
					$name=isset($profile->name)?explode(" ", $profile->name):'';
					$user['first_name']=isset($name[0])?$name[0]:'';
					unset($name[0]);
					$user['last_name']=count($name)>0?implode (" ", $name):'';
					$user['phone']=isset($profile->{Config::get('app.cms.user.mobile')})?$profile->{Config::get('app.cms.user.mobile')}:'';
					$user['role']='virtual-rooms';

					$uuid= Str::uuid(4);
					$expiry= time() + (7*24*60*60); //7 days
					$user['activation_code']=$uuid->string;
					$user['activation_code_expires']=$expiry;

					$user['password']=Hash::make(uniqid());
					$user['secret']=strtoupper(uniqid());
					$user['imported_from_cms']=1;
					$user = LibertyUser::updateOrCreate(['email'=>$user['email']], $user);
					$importedUsers[]=$user['email'];

				}
				else{
					$discardedUsers[]=$user['email'];
				}
			} 
		}  
		echo "\n\n-----------------------------------------------------\n\n";          
		
		echo "Imported Users (Total: ".count($importedUsers).")\n\n";
		foreach ($importedUsers as $user) {
			print_r($user);
			echo "\n\n";
		}
		
		echo "\n\n-----------------------------------------------------\n\n";    
		echo "Ignored Users (Total: ".count($discardedUsers).")";
		echo "\n\n";
		foreach ($discardedUsers as $user) {
			print_r($user);
			echo "\n\n";
		}
	}

	private function SendInvitation()
	{
		$emailSend=[];		
		$users = LibertyUser::where('imported_from_cms',1)->where('activated',0)->get();
        foreach ($users as $liberty_user) {
            $liberty_user->type = 'liberty-user';
            $this->mail->queue(
                $liberty_user->email,
                $liberty_user->fullName(),
                'Risk Reduce, Welcome to Risk Reduce',
                'emails.authadmin.welcome',
                $liberty_user
			);
			$emailSend[]=$liberty_user->email;
		}
		echo "\n\n-----------------------------------------------------\n\n";     
		echo "Email Sent (Total: ".count($emailSend).")";
		echo "\n\n";
		foreach ($emailSend as $user) {
			print_r($user);
			echo "\n\n";
		}
    }
}
