<?php

namespace App\Console\Commands;

use App\Jobs\PortfolioViews\Generators\Concerns\RawDataGenerator;
use App\Models\FileUpload;
use App\Models\Organisation;
use App\Models\PortfolioViews\PortfolioViewsRawData;
use Aws\Laravel\AwsFacade as AWS;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

class PortfolioViewsDataImportCommand extends Command
{
    use RawDataGenerator;

    const S3_KEY = "portfolioviews/imports/%s";

    /**
     * The console command name.
     *
     * @var string
     */
    protected $signature = 'portfolioviews:import
                       {filename : Filename of csv file to import. Make sure this is in the correct s3 bucket and folder "portfolioviews/imports/"}
                       {--local : Will get the file locally inside storage/app folder}';

    /**
     * @var \App\Models\FileUpload $uploadService
     */
    protected $uploadService;

    public function __construct()
    {
        parent::__construct();
        $this->uploadService = new FileUpload();
    }
    
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import data from csv file in s3.';

    public function handle()
    {
        $csvFp = null;
        
        try {
            $csvFp = $this->getFile();

            /**
             * Header Format:
             * 
             * Org Name, Org Id, Month Year Field...
             */
            $headers = fgetcsv($csvFp);

            while (!feof($csvFp)) {
                $data = fgetcsv($csvFp);
                if(!is_array($data)) {
                    continue;
                }
                $mappedData = array_combine($headers, $data);

                if (!empty($mappedData)) {
                    try {
                        $org = Organisation::find($mappedData['Organisation ID'] ?? 0);
                    } catch (\Throwable $th) {
                        Log::error("[portfolioviews:import]: " . $th->getMessage());
                        continue;
                    }
                    
                    $orgRawData = $this->fetchOrgsData([$org->id]);
                    $monthlyData = $this->splitImportDataToMonthlyData($mappedData, collect($orgRawData));

                    foreach ($monthlyData as $key => $overrideData) {
                        $orgMonthlyData = $this->transformData(collect($orgRawData), collect([]), $overrideData);

                        // Check if there is an existing raw data for the month given
                        $date    = Carbon::createFromFormat('F d, Y', $key);
                        $month   = $date->format('n');
                        $year    = $date->format('Y');
                        $quarter = (string) (ceil((int) $month / 3));

                        // Note: Not sure if firstOrNew is supported by Mongo Model
                        $rawDataRow = PortfolioViewsRawData::where([
                                ['month', '=', $month],
                                ['year' , '=', $year],
                                ['date' , '=', $date->format('Y-m-d')]
                            ])
                            ->first();

                        if (!empty($rawDataRow)) {
                            $existingData = Collection::make($rawDataRow->data);
                            $existingData->add(...$orgMonthlyData);
                            $rawDataRow->data = [...$existingData->sortBy('id')
                                                    ->toArray()];
                            $rawDataRow->save();
                        } else {
                            PortfolioViewsRawData::create([
                                'date'    => $date->format('Y-m-d'),
                                'month'   => $month,
                                'year'    => $year,
                                'quarter' => $quarter,
                                'data'    => $orgMonthlyData
                            ]);
                        }
                    }
                }
            }
            fclose($csvFp);
        } catch (Exception $e) {
            if (
                !empty($csvFp)
                && (get_resource_type($csvFp) == 'file' || get_resource_type($csvFp) == 'stream')
            ) {
                fclose(($csvFp));
            }

            Log::error("[portfolioviews:import]: " . $e->getMessage());
            // throw $e;
        }
    }

    protected function getFile()
    {
        $filename = $this->argument('filename');

        if (empty($filename)) {
            throw new Exception('Filename not set.');
        }

        if ($this->option('local')) {
            return $this->getFileLocally();
        }

        return $this->getFileFromAws();
    }

    protected function getFileLocally()
    {
        $file  = storage_path('app/' . $this->argument('filename'));
        $csvFp = fopen($file, 'r');

        if ($csvFp === false) {
            throw new Exception('Cannot access CSV file in local.');
        }
        return $csvFp;
    }

    protected function getFileFromAws()
    {
        // Get file from AWS
        $bucket = Config::get('app.aws.bucket');

        if (empty($bucket)) {
            throw new Exception('S3 Bucket not set.');
        }

        $s3Key = sprintf(self::S3_KEY, $this->argument('filename'));

        // Make sure file exists
        if (!$this->uploadService->exists($s3Key)) {
            throw new Exception("File not found in S3 Bucket: {$bucket}.");
        }

        // Initiate s3 client and stream wrapper
        $s3Client = AWS::createClient('s3');
        $s3Client->registerStreamWrapper();

        $file  = "s3://{$bucket}/{$s3Key}";
        $csvFp = fopen($file, 'r');

        if ($csvFp === false) {
            throw new Exception('Cannot access CSV file in S3.');
        }

        return $csvFp;
    }

    protected function splitImportDataToMonthlyData(array $mappedData, Collection $orgData): array
    {
        $monthlyData = [];
        foreach ($mappedData as $key => $data) {
            $key = filter_var(trim($key), FILTER_SANITIZE_SPECIAL_CHARS, FILTER_FLAG_STRIP_HIGH);
            if (in_array($key, ['Account Name', 'Organisation ID'])) {
                continue;
            }

            $keyParts = preg_split('/\s+/', $key);
            $dateString = implode(' ', [$keyParts[0], '01,', $keyParts[1]]);
            $keyPrefix = implode(' ', [$keyParts[0], $keyParts[1]]);

            if (!array_key_exists($dateString, $monthlyData)) {
                $monthlyData[$dateString] = [];
            }

            $fieldKey = str_ireplace($keyPrefix, '', $key);
            $monthlyData[$dateString][$fieldKey] = $data;
        }

        return $monthlyData;
    }

    /**
     * Transform raw data to Risk Grading insight data
     *
     * @param Collection $rawData
     * @param Collection $srgData
     * @param array $override
     * @return array
     */
    protected function transformData(Collection $rawData, Collection $srgData, array $override)
    {
        return $rawData->map(function ($item) use ($srgData, $override) {
            return $this->applyOverrideToOrg(
                array_merge(
                    json_decode(json_encode($item), true),
                    $this->transformSectorData($item),
                    $this->buildLineOfBusinessData($item),
                    ['risk_recommendataion_titles' => $srgData->get($item->id, [])]
                ),
                $override
            );
        })
        ->toArray();
    }

    protected function applyOverrideToOrg(array $orgData, array $override)
    {
        foreach ($override as $key => $overrideValue) {
            $usingLossRatio = false;
            $flagPos = stripos(strtolower($key), 'risk grading');

            // Try loss ratio
            if ($flagPos === false) {
                $flagPos = stripos(strtolower($key), 'loss ratio');
                $usingLossRatio = true;
            }

            if ($flagPos === false || empty($overrideValue)) {
                continue;
            }

            $type = strtolower(trim(substr($key, 0, $flagPos)));
            
            if (!$usingLossRatio && $type == 'account-level') {
                $orgData['risk_grading'] = $overrideValue;
            }

            // Check Lobs
            if (empty($orgData['lobs'])) {
                continue;
            }

            foreach ($orgData['lobs'] as $key => $lob) {
                if (strtolower($lob['name']) == strtolower($type)) {
                    $overrideKey = $usingLossRatio ? 'loss_ratio' : 'risk_grading';
                    $orgData['lobs'][$key][$overrideKey] = $overrideValue;
                }
            }
        }

        return $orgData;
    }
}
