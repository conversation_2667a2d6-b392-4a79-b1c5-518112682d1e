<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Organisation;
use App\Models\OrganisationLocations;
use App\Models\Broker;
use App\Models\Sector;
use App\Models\RiskGrading;
use Carbon\Carbon;
use Exception;

class ImportCsvForDemo extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'insights:import {--type=all : Type of data to import (organisations, locations, risk-gradings, all)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import script for risk insights demo - imports organisation, location, and risk grading data from CSV files';

    /**
     * CSV file paths relative to public directory
     *
     * @var array
     */
    protected $csvFilePaths = [
        'organisations' => 'risk-insights-csv/organisations.csv',
        'locations' => 'risk-insights-csv/organisation_locations.csv',
        'risk-gradings' => 'risk-insights-csv/grading_attributes.csv'
    ];

    /**
     * Statistics for import process
     *
     * @var array
     */
    protected $stats = [
        'organisations' => [
            'total_rows' => 0,
            'imported' => 0,
            'updated' => 0,
            'errors' => 0,
            'error_details' => []
        ],
        'locations' => [
            'total_rows' => 0,
            'imported' => 0,
            'updated' => 0,
            'skipped' => 0,
            'errors' => 0,
            'error_details' => []
        ],
        'risk-gradings' => [
            'total_rows' => 0,
            'imported' => 0,
            'updated' => 0,
            'skipped' => 0,
            'errors' => 0,
            'error_details' => []
        ]
    ];

    /**
     * Track location counts per organisation for generating location_id
     *
     * @var array
     */
    protected $organisationLocationCounts = [];

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $importType = $this->option('type');

        // Validate import type
        if (!in_array($importType, ['organisations', 'locations', 'risk-gradings', 'all'])) {
            $this->error('Invalid import type. Use: organisations, locations, risk-gradings, or all');
            return Command::FAILURE;
        }

        $this->info("Starting {$importType} CSV import...");

        // Show file paths that will be processed
        if ($importType === 'organisations' || $importType === 'all') {
            $this->info('Organisations CSV: ' . public_path($this->csvFilePaths['organisations']));
        }
        if ($importType === 'locations' || $importType === 'all') {
            $this->info('Locations CSV: ' . public_path($this->csvFilePaths['locations']));
        }
        if ($importType === 'risk-gradings' || $importType === 'all') {
            $this->info('Risk Gradings CSV: ' . public_path($this->csvFilePaths['risk-gradings']));
        }

        // Confirm before proceeding
        $confirmMessage = $importType === 'all'
            ? 'Do you want to proceed with the import? This will create/update organisation, location, and risk grading records.'
            : "Do you want to proceed with the {$importType} import? This will create/update {$importType} records.";

        if (!$this->confirm($confirmMessage)) {
            $this->info('Import cancelled by user.');
            return Command::SUCCESS;
        }

        try {
            if ($importType === 'organisations' || $importType === 'all') {
                $this->importOrganisations();
            }

            if ($importType === 'locations' || $importType === 'all') {
                $this->importLocations();
            }

            if ($importType === 'risk-gradings' || $importType === 'all') {
                $this->importRiskGradings();
            }

            $this->displayResults();
        } catch (Exception $e) {
            $this->error('Import failed: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    /**
     * Import organisations from CSV file
     *
     * @return void
     */
    protected function importOrganisations()
    {
        $this->info('Importing organisations...');
        $csvPath = public_path($this->csvFilePaths['organisations']);

        if (!file_exists($csvPath)) {
            throw new Exception("CSV file not found at: {$csvPath}");
        }

        $handle = fopen($csvPath, 'r');

        if ($handle === false) {
            throw new Exception("Unable to open CSV file: {$csvPath}");
        }

        // Read header row
        $headers = fgetcsv($handle);
        if ($headers === false) {
            throw new Exception("Unable to read CSV headers");
        }

        $this->info("CSV headers found: " . implode(', ', $headers));

        // Count total rows for progress bar
        $totalRows = 0;
        while (fgetcsv($handle) !== false) {
            $totalRows++;
        }
        rewind($handle);
        fgetcsv($handle); // Skip header again

        $this->info("Found {$totalRows} data rows to process");

        // Create progress bar
        $progressBar = $this->output->createProgressBar($totalRows);
        $progressBar->setFormat('verbose');
        $progressBar->start();

        // Process each data row
        $rowNumber = 1; // Start from 1 (header is row 0)

        while (($data = fgetcsv($handle)) !== false) {
            $rowNumber++;
            $this->stats['organisations']['total_rows']++;

            if (empty(array_filter($data))) {
                $progressBar->advance();
                continue; // Skip empty rows
            }

            try {
                $mappedData = array_combine($headers, $data);
                $this->processOrganisationRow($mappedData, $rowNumber);

            } catch (Exception $e) {
                $this->stats['organisations']['errors']++;
                $this->stats['organisations']['error_details'][] = "Row {$rowNumber}: " . $e->getMessage();
                // Don't show individual errors during progress to keep output clean
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        fclose($handle);
    }

    /**
     * Process a single organisation row from CSV
     *
     * @param array $data
     * @param int $rowNumber
     * @return void
     */
    protected function processOrganisationRow(array $data, int $rowNumber)
    {
        // Validate required fields
        if (empty($data['client_name'])) {
            throw new Exception("Missing client_name");
        }

        // Map CSV data to Organisation fields
        $organisationData = $this->mapCsvToOrganisation($data);

        // Check if organisation already exists (by name)
        $existingOrg = Organisation::where('name', $organisationData['name'])->first();

        if ($existingOrg) {
            // Update existing organisation
            $existingOrg->update($organisationData);
            $this->stats['organisations']['updated']++;
        } else {
            // Create new organisation
            Organisation::create($organisationData);
            $this->stats['organisations']['imported']++;
        }
    }

    /**
     * Map CSV data to Organisation model fields
     *
     * @param array $csvData
     * @return array
     */
    protected function mapCsvToOrganisation(array $csvData): array
    {
        $data = [
            'name' => trim($csvData['client_name']),
            'product_subsector' => !empty($csvData['subsector']) ? trim($csvData['subsector']) : null,
        ];

        // Handle sector lookup/creation
        if (!empty($csvData['sector'])) {
            $sectorId = $this->findSectorByName(trim($csvData['sector']));
            if ($sectorId) {
                $data['sector'] = $sectorId;
            }
        }

        // Handle broker lookup
        if (!empty($csvData['Broker'])) {
            $brokerId = $this->findBrokerByName(trim($csvData['Broker']));
            if ($brokerId) {
                $data['broker_id'] = $brokerId;
            }
        }

        // Handle renewal date
        if (!empty($csvData['Renewal Date'])) {
            $renewalDate = $this->parseDate($csvData['Renewal Date']);
            if ($renewalDate) {
                // Convert to Unix timestamp as the field expects an integer
                $data['expiry_date_of_cover'] = $renewalDate->getTimestamp();
            }
        }

        // Handle risk score
        if (!empty($csvData['risk_score']) && is_numeric($csvData['risk_score'])) {
            $data['risk_grading'] = (int) $csvData['risk_score'];
        }

        // Set default values for required fields that might be missing
        $data = array_merge([
            'bound' => 0,
            'status' => 0, // Status is an integer field (0 = default)
            'country' => 'GB', // Default to UK
            'address_line_1' => '',
            'postcode' => '',
            'phone' => '',
            'email' => '',
        ], $data);

        return $data;
    }

    /**
     * Import locations from CSV file
     *
     * @return void
     */
    protected function importLocations()
    {
        $this->info('Importing organisation locations...');
        $csvPath = public_path($this->csvFilePaths['locations']);

        if (!file_exists($csvPath)) {
            throw new Exception("CSV file not found at: {$csvPath}");
        }

        $handle = fopen($csvPath, 'r');

        if ($handle === false) {
            throw new Exception("Unable to open CSV file: {$csvPath}");
        }

        // Read header row
        $headers = fgetcsv($handle);
        if ($headers === false) {
            throw new Exception("Unable to read CSV headers");
        }

        $this->info("CSV headers found: " . implode(', ', $headers));

        // Count total rows for progress bar
        $totalRows = 0;
        while (fgetcsv($handle) !== false) {
            $totalRows++;
        }
        rewind($handle);
        fgetcsv($handle); // Skip header again

        $this->info("Found {$totalRows} data rows to process");

        // Create progress bar
        $progressBar = $this->output->createProgressBar($totalRows);
        $progressBar->setFormat('verbose');
        $progressBar->start();

        // Process each data row
        $rowNumber = 1; // Start from 1 (header is row 0)

        while (($data = fgetcsv($handle)) !== false) {
            $rowNumber++;
            $this->stats['locations']['total_rows']++;

            if (empty(array_filter($data))) {
                $progressBar->advance();
                continue; // Skip empty rows
            }

            try {
                $mappedData = array_combine($headers, $data);
                $this->processLocationRow($mappedData, $rowNumber);

            } catch (Exception $e) {
                $this->stats['locations']['errors']++;
                $this->stats['locations']['error_details'][] = "Row {$rowNumber}: " . $e->getMessage();
                // Don't show individual errors during progress to keep output clean
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        fclose($handle);
    }

    /**
     * Process a single location row from CSV
     *
     * @param array $data
     * @param int $rowNumber
     * @return void
     */
    protected function processLocationRow(array $data, int $rowNumber)
    {
        // Skip the demo instruction row
        if (strpos($data['client_name'] ?? '', 'For the demo') !== false) {
            $this->stats['locations']['skipped']++;
            return;
        }

        // Validate required fields
        if (empty($data['client_name'])) {
            throw new Exception("Missing client_name");
        }

        if (empty($data['location_name'])) {
            throw new Exception("Missing location_name");
        }

        // Find the organisation this location belongs to
        $organisation = $this->findOrganisationByName(trim($data['client_name']));

        if (!$organisation) {
            $this->stats['locations']['skipped']++;
            $this->stats['locations']['error_details'][] = "Row {$rowNumber}: Organisation '{$data['client_name']}' not found";
            return;
        }

        // Check if location already exists (by organisation_id and location_name)
        $existingLocation = OrganisationLocations::where('organisation_id', $organisation->id)
                                                 ->where('location_name', trim($data['location_name']))
                                                 ->first();

        if ($existingLocation) {
            // Update existing location - map data without generating new location_id
            $locationData = $this->mapCsvToLocation($data, $organisation->id, $existingLocation->location_id);
            $existingLocation->update($locationData);
            $this->stats['locations']['updated']++;
        } else {
            // Create new location - generate new location_id
            $nextLocationId = $this->getNextLocationId($organisation->id);
            $locationData = $this->mapCsvToLocation($data, $organisation->id, $nextLocationId);
            OrganisationLocations::create($locationData);
            $this->stats['locations']['imported']++;
        }
    }

    /**
     * Find organisation by name
     *
     * @param string $organisationName
     * @return Organisation|null
     */
    protected function findOrganisationByName(string $organisationName): ?Organisation
    {
        return Organisation::where('name', 'LIKE', '%' . trim($organisationName) . '%')->first();
    }

    /**
     * Get the next location_id for an organisation
     *
     * @param int $organisationId
     * @return string
     */
    protected function getNextLocationId(int $organisationId): string
    {
        // Check if we've already calculated the max for this organisation
        if (!isset($this->organisationLocationCounts[$organisationId])) {
            // Get the highest location_id for this organisation from the database
            $maxLocationId = OrganisationLocations::where('organisation_id', $organisationId)
                                                  ->max('location_id');

            // If no locations exist, start at 0, otherwise use the max value
            $this->organisationLocationCounts[$organisationId] = $maxLocationId ? (int)$maxLocationId : 0;
        }

        // Increment and return the next location_id
        $this->organisationLocationCounts[$organisationId]++;
        return (string)$this->organisationLocationCounts[$organisationId];
    }

    /**
     * Map CSV data to OrganisationLocations model fields
     *
     * @param array $csvData
     * @param int $organisationId
     * @param string $locationId
     * @return array
     */
    protected function mapCsvToLocation(array $csvData, int $organisationId, string $locationId): array
    {
        $data = [
            'organisation_id' => $organisationId,
            'location_id' => $locationId,
            'location_name' => trim($csvData['location_name']),
        ];

        // Handle postcode
        if (!empty($csvData['postcode'])) {
            $data['postcode'] = trim($csvData['postcode']);
        }

        // Handle country
        if (!empty($csvData['Country'])) {
            $data['country'] = trim($csvData['Country']);
        }

        // Handle TIV (Total Insured Value) - parse currency format
        if (!empty($csvData['TIV'])) {
            $tivValue = $this->parseCurrencyValue($csvData['TIV']);
            if ($tivValue !== null) {
                $data['tiv'] = $tivValue;
            }
        }

        // Parse address information from location_name if it contains full address
        $addressInfo = $this->parseAddressFromLocationName($csvData['location_name']);
        if ($addressInfo) {
            $data = array_merge($data, $addressInfo);
        }

        // Set default values for required fields that might be missing
        $data = array_merge([
            'address_line_1' => '',
            'address_line_2' => '',
            'city' => '',
            'county' => '',
            'country' => 'UK', // Default to UK
            'postcode' => '',
        ], $data);

        return $data;
    }

    /**
     * Import risk gradings from CSV file
     *
     * @return void
     */
    protected function importRiskGradings()
    {
        $this->info('Importing risk gradings...');
        $csvPath = public_path($this->csvFilePaths['risk-gradings']);

        if (!file_exists($csvPath)) {
            throw new Exception("CSV file not found at: {$csvPath}");
        }

        $handle = fopen($csvPath, 'r');

        if ($handle === false) {
            throw new Exception("Unable to open CSV file: {$csvPath}");
        }

        // Read header row
        $headers = fgetcsv($handle);
        if ($headers === false) {
            throw new Exception("Unable to read CSV headers");
        }

        $this->info("CSV headers found: " . implode(', ', $headers));

        // Count total rows for progress bar
        $totalRows = 0;
        while (fgetcsv($handle) !== false) {
            $totalRows++;
        }
        rewind($handle);
        fgetcsv($handle); // Skip header again

        $this->info("Found {$totalRows} data rows to process");

        // Create progress bar
        $progressBar = $this->output->createProgressBar($totalRows);
        $progressBar->setFormat('verbose');
        $progressBar->start();

        // Process each data row
        $rowNumber = 1; // Start from 1 (header is row 0)

        while (($data = fgetcsv($handle)) !== false) {
            $rowNumber++;
            $this->stats['risk-gradings']['total_rows']++;

            if (empty(array_filter($data))) {
                $progressBar->advance();
                continue; // Skip empty rows
            }

            try {
                $mappedData = array_combine($headers, $data);
                $this->processRiskGradingRow($mappedData, $rowNumber);

            } catch (Exception $e) {
                $this->stats['risk-gradings']['errors']++;
                $this->stats['risk-gradings']['error_details'][] = "Row {$rowNumber}: " . $e->getMessage();
                // Don't show individual errors during progress to keep output clean
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        fclose($handle);
    }

    /**
     * Process a single risk grading row from CSV
     *
     * @param array $data
     * @param int $rowNumber
     * @return void
     */
    protected function processRiskGradingRow(array $data, int $rowNumber)
    {
        // Validate required fields
        if (empty($data['client_name'])) {
            throw new Exception("Missing client_name");
        }

        if (empty($data['attribute_name'])) {
            throw new Exception("Missing attribute_name");
        }

        if (empty($data['risk_grading'])) {
            throw new Exception("Missing risk_grading");
        }

        // Find the organisation location this grading belongs to
        $organisationLocation = $this->findOrganisationLocationByNameAndLocation(
            trim($data['client_name']),
            trim($data['location_name'] ?? ''),
            trim($data['postcode'] ?? '')
        );

        if (!$organisationLocation) {
            $this->stats['risk-gradings']['skipped']++;
            $this->stats['risk-gradings']['error_details'][] = "Row {$rowNumber}: Organisation location not found for '{$data['client_name']}' - '{$data['location_name']}'";
            return;
        }

        // Map CSV data to RiskGrading fields
        $riskGradingData = $this->mapCsvToRiskGrading($data, $organisationLocation->id);

        // Check if risk grading already exists (by organisation_location_id and attribute)
        $existingGrading = RiskGrading::where('organisation_location_id', $organisationLocation->id)
                                    ->where('attribute', $riskGradingData['attribute'])
                                    ->first();

        if ($existingGrading) {
            // Update existing risk grading
            $existingGrading->update($riskGradingData);
            $this->stats['risk-gradings']['updated']++;
        } else {
            // Create new risk grading
            RiskGrading::create($riskGradingData);
            $this->stats['risk-gradings']['imported']++;
        }
    }

    /**
     * Find organisation location by client name, location name, and postcode
     *
     * @param string $clientName
     * @param string $locationName
     * @param string $postcode
     * @return OrganisationLocations|null
     */
    protected function findOrganisationLocationByNameAndLocation(string $clientName, string $locationName, string $postcode): ?OrganisationLocations
    {
        // First, find the organisation
        $organisation = $this->findOrganisationByName($clientName);

        if (!$organisation) {
            return null;
        }

        // Build query for organisation location
        $query = OrganisationLocations::where('organisation_id', $organisation->id);

        // If location name is provided, use it for matching
        if (!empty($locationName)) {
            $query->where('location_name', 'LIKE', '%' . trim($locationName) . '%');
        }

        // If postcode is provided, use it for additional matching
        if (!empty($postcode)) {
            $query->where('postcode', 'LIKE', '%' . trim($postcode) . '%');
        }

        $location = $query->first();

        // If no exact match found and we have a postcode, try postcode-only match
        if (!$location && !empty($postcode)) {
            $location = OrganisationLocations::where('organisation_id', $organisation->id)
                                            ->where('postcode', 'LIKE', '%' . trim($postcode) . '%')
                                            ->first();
        }

        // If still no match and we have a location name, try location name-only match
        if (!$location && !empty($locationName)) {
            $location = OrganisationLocations::where('organisation_id', $organisation->id)
                                            ->where('location_name', 'LIKE', '%' . trim($locationName) . '%')
                                            ->first();
        }

        // If still no match, get the first location for this organisation
        if (!$location) {
            $location = OrganisationLocations::where('organisation_id', $organisation->id)->first();
        }

        return $location;
    }

    /**
     * Map CSV data to RiskGrading model fields
     *
     * @param array $csvData
     * @param int $organisationLocationId
     * @return array
     */
    protected function mapCsvToRiskGrading(array $csvData, int $organisationLocationId): array
    {
        $data = [
            'organisation_location_id' => $organisationLocationId,
            'location_id' => '', // This field exists in the model but may not be used
            'attribute' => trim($csvData['attribute_name']),
            'value' => RiskGrading::remapGradingValue(trim($csvData['risk_grading'])),
        ];

        // Handle max_score field - parse as numeric value with default fallback
        $data['max_score'] = $this->parseNumericValue($csvData['max_score'] ?? '', 0);

        // Handle score field - parse as numeric value with default fallback
        $data['score'] = $this->parseNumericValue($csvData['score'] ?? '', 0);

        // Set policy_id to null as it's not provided in the CSV
        // This field is nullable in the fillable array
        $data['policy_id'] = null;

        return $data;
    }

    /**
     * Parse numeric value from CSV with validation and default fallback
     *
     * @param string $value
     * @param int|float $default
     * @return int|float
     */
    protected function parseNumericValue(string $value, $default = 0)
    {
        // Trim whitespace
        $value = trim($value);

        // Return default if empty
        if (empty($value)) {
            return $default;
        }

        // Check if the value is numeric
        if (is_numeric($value)) {
            // Return as integer if it's a whole number, otherwise as float
            return (strpos($value, '.') !== false) ? (float) $value : (int) $value;
        }

        // If not numeric, return default value
        return $default;
    }

    /**
     * Find sector by name or create if it doesn't exist
     *
     * @param string $sectorName
     * @return int|null
     */
    protected function findSectorByName(string $sectorName): ?int
    {
        // Try to find existing sector by handle or type
        $sector = Sector::where('handle', 'LIKE', '%' . $sectorName . '%')
                       ->orWhere('type', 'LIKE', '%' . $sectorName . '%')
                       ->first();

        if (!$sector) {
            // Create sector if it doesn't exist
            $sector = Sector::create([
                'handle' => $sectorName,
                'type' => $sectorName,
            ]);
        }

        return $sector->id;
    }

    /**
     * Find broker by name
     *
     * @param string $brokerName
     * @return int|null
     */
    protected function findBrokerByName(string $brokerName): ?int
    {
        $broker = Broker::where('name', 'LIKE', '%' . $brokerName . '%')->first();

        if (!$broker) {
            // Create broker if it doesn't exist
            $broker = Broker::create([
                'name' => $brokerName,
                'address_1' => '',
                'email' => '',
                'phone' => '',
            ]);
        }

        return $broker->id;
    }

    /**
     * Parse currency value from CSV format (e.g., "£1,236,906,834.00")
     *
     * @param string $currencyString
     * @return float|null
     */
    protected function parseCurrencyValue(string $currencyString): ?float
    {
        try {
            // Remove currency symbols, commas, and spaces
            $cleanValue = preg_replace('/[£$€,\s]/', '', $currencyString);

            // Check if it's a valid number
            if (is_numeric($cleanValue)) {
                return (float) $cleanValue;
            }

            return null;
        } catch (Exception $e) {
            $this->warn("Unable to parse currency value: {$currencyString}");
            return null;
        }
    }

    /**
     * Parse address information from location name field
     *
     * @param string $locationName
     * @return array|null
     */
    protected function parseAddressFromLocationName(string $locationName): ?array
    {
        // If location name contains commas, it might be a full address
        if (strpos($locationName, ',') !== false) {
            $parts = array_map('trim', explode(',', $locationName));

            $addressInfo = [];

            // Try to extract meaningful address components
            if (count($parts) >= 2) {
                $addressInfo['address_line_1'] = $parts[0];

                // Look for postcode pattern in the parts
                foreach ($parts as $part) {
                    // UK postcode pattern
                    if (preg_match('/^[A-Z]{1,2}[0-9R][0-9A-Z]? [0-9][A-Z]{2}$/i', trim($part))) {
                        $addressInfo['postcode'] = trim($part);
                    }
                    // Look for city/county indicators
                    elseif (stripos($part, 'London') !== false ||
                           stripos($part, 'Birmingham') !== false ||
                           stripos($part, 'Manchester') !== false ||
                           stripos($part, 'Leeds') !== false ||
                           stripos($part, 'Liverpool') !== false) {
                        $addressInfo['city'] = trim($part);
                    }
                    // Look for county indicators
                    elseif (stripos($part, 'shire') !== false ||
                           stripos($part, 'County') !== false ||
                           stripos($part, 'Greater London') !== false ||
                           stripos($part, 'West Midlands') !== false) {
                        $addressInfo['county'] = trim($part);
                    }
                }

                // If we have more than 2 parts and haven't assigned city yet, use the second part
                if (count($parts) >= 3 && !isset($addressInfo['city'])) {
                    $addressInfo['city'] = $parts[1];
                }
            }

            return $addressInfo;
        }

        return null;
    }

    /**
     * Parse date from CSV format (d/m/Y)
     *
     * @param string $dateString
     * @return Carbon|null
     */
    protected function parseDate(string $dateString): ?Carbon
    {
        try {
            // Try parsing d/m/Y format first (e.g., "14/3/2026")
            if (preg_match('/^\d{1,2}\/\d{1,2}\/\d{4}$/', $dateString)) {
                return Carbon::createFromFormat('d/m/Y', $dateString);
            }

            // Fallback to Carbon's general parsing
            return Carbon::parse($dateString);

        } catch (Exception $e) {
            $this->warn("Unable to parse date: {$dateString}");
            return null;
        }
    }

    /**
     * Display import results
     *
     * @return void
     */
    protected function displayResults()
    {
        $this->info("\n" . str_repeat('=', 60));
        $this->info('IMPORT COMPLETED');
        $this->info(str_repeat('=', 60));

        // Display organisation statistics if any were processed
        if ($this->stats['organisations']['total_rows'] > 0) {
            $this->info("\nORGANISATION IMPORT RESULTS:");
            $this->table(
                ['Metric', 'Count'],
                [
                    ['Total Rows Processed', $this->stats['organisations']['total_rows']],
                    ['New Organisations Imported', $this->stats['organisations']['imported']],
                    ['Existing Organisations Updated', $this->stats['organisations']['updated']],
                    ['Errors Encountered', $this->stats['organisations']['errors']],
                ]
            );

            if (!empty($this->stats['organisations']['error_details'])) {
                $this->error("\nOrganisation Import Error Details:");
                foreach ($this->stats['organisations']['error_details'] as $error) {
                    $this->error("- {$error}");
                }
            }
        }

        // Display location statistics if any were processed
        if ($this->stats['locations']['total_rows'] > 0) {
            $this->info("\nLOCATION IMPORT RESULTS:");
            $this->table(
                ['Metric', 'Count'],
                [
                    ['Total Rows Processed', $this->stats['locations']['total_rows']],
                    ['New Locations Imported', $this->stats['locations']['imported']],
                    ['Existing Locations Updated', $this->stats['locations']['updated']],
                    ['Locations Skipped (No Organisation)', $this->stats['locations']['skipped']],
                    ['Errors Encountered', $this->stats['locations']['errors']],
                ]
            );

            if (!empty($this->stats['locations']['error_details'])) {
                $this->error("\nLocation Import Error Details:");
                foreach ($this->stats['locations']['error_details'] as $error) {
                    $this->error("- {$error}");
                }
            }
        }

        // Display risk grading statistics if any were processed
        if ($this->stats['risk-gradings']['total_rows'] > 0) {
            $this->info("\nRISK GRADING IMPORT RESULTS:");
            $this->table(
                ['Metric', 'Count'],
                [
                    ['Total Rows Processed', $this->stats['risk-gradings']['total_rows']],
                    ['New Risk Gradings Imported', $this->stats['risk-gradings']['imported']],
                    ['Existing Risk Gradings Updated', $this->stats['risk-gradings']['updated']],
                    ['Risk Gradings Skipped (No Location)', $this->stats['risk-gradings']['skipped']],
                    ['Errors Encountered', $this->stats['risk-gradings']['errors']],
                ]
            );

            if (!empty($this->stats['risk-gradings']['error_details'])) {
                $this->error("\nRisk Grading Import Error Details:");
                foreach ($this->stats['risk-gradings']['error_details'] as $error) {
                    $this->error("- {$error}");
                }
            }
        }

        // Display summary
        $totalImported = $this->stats['organisations']['imported'] + $this->stats['locations']['imported'] + $this->stats['risk-gradings']['imported'];
        $totalUpdated = $this->stats['organisations']['updated'] + $this->stats['locations']['updated'] + $this->stats['risk-gradings']['updated'];
        $totalErrors = $this->stats['organisations']['errors'] + $this->stats['locations']['errors'] + $this->stats['risk-gradings']['errors'];

        $this->info("\n" . str_repeat('=', 60));
        $this->info("SUMMARY: {$totalImported} new records imported, {$totalUpdated} records updated, {$totalErrors} errors");
        $this->info("Import process completed successfully!");
        $this->info(str_repeat('=', 60));
    }
}
