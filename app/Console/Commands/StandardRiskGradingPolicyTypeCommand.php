<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputArgument;

class StandardRiskGradingPolicyTypeCommand extends Command {

	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'standard-riskgrading:update-policy-type';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Update standard risk grading policy type';

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
	}

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{
		$policyId = $this->argument('id');
		$name	  = $this->argument('name');
		$order	  = $this->argument('order');

		$policyType = RGPolicyType::find($policyId);
		if (isset($policyType)) {
			if (isset($name)) {
				$policyType->name = ucfirst($name);
				$policyType->slug = $name;
			}
			$policyType->order = isset($order) ? $order : $policyType->order;
			$policyType->version = isset($name) || isset($order) ? $policyType->version + 1 : $policyType->version;
			$policyType->save();

			if ($policyType->id) {
				RGPolicyTypeLog::create([
					'rg_policy_type_id' => $policyType->id,
					'name'		 		=> $policyType->name,
					'order' 	 		=> $policyType->order,
					'version' 	 		=> $policyType->version,
					'created_at' 		=> date('Y-m-d H:i:s'),
					'updated_at' 		=> date('Y-m-d H:i:s'),
				]);
			}
		}
	}

	/**
	 * Get the console command arguments.
	 *
	 * @return array
	 */
	protected function getArguments()
	{
		return [
			['id', InputArgument::REQUIRED, 'Policy Type Id'],
			['name', InputArgument::OPTIONAL, 'Policy type name'],
			['order', InputArgument::OPTIONAL, 'Order'],
		];
	}

	/**
	 * Get the console command options.
	 *
	 * @return array
	 */
	protected function getOptions()
	{
		return array(
			array('example', null, InputOption::VALUE_OPTIONAL, 'An example option.', null),
		);
	}

}
