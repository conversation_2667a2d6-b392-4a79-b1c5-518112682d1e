<?php

namespace App\Console\Commands;

use <PERSON><PERSON><PERSON>\Twilio\Twilio;
use Twi<PERSON>\Rest\Client;
use Illuminate\Console\Command;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use App\Models\LetsTalk\UpcomingSchedule;
use App\Models\LetsTalk\SocialRoom as SocialRoom;
use App\Models\LetsTalk\SocialRoomSmsNotification;
use App\Models\LetsTalk\CmsLibertyRepresentative;
use Illuminate\Support\Facades\Config;
class VrSMSBeforeMeeting extends Command 
{
    private $twilio;
    private $twilioClient;
    private $libReps;
    
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'virtual-rooms:sms-before-meeting-reminder';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remind participants 5 minuites before catchup or team meeting.';

     /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
                
        $twilioConfig = Config::get('app.twilio.sms');        
        $this->twilio = new Twilio($twilioConfig['sid'], $twilioConfig['token'], $twilioConfig['from']);
        $this->twilioConfigUS = new Twilio($twilioConfig['sid'], $twilioConfig['token'], $twilioConfig['from-us']);
        $this->twilioClient = new Client($twilioConfig['sid'], $twilioConfig['token']);
        
        $lsmLibReps = CmsLibertyRepresentative::fetchFromCacheOrCms();
        $lmreLibReps = CmsLibertyRepresentative::fetchFromCacheOrCms('lmre');
        $this->libReps = array_merge($lmreLibReps, $lsmLibReps);
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {        
        $day=Carbon::now()->dayOfWeek;
        if($day != 6 && $day !=0){ //Exclude weekend
            $schedules=$this->getInstantScheduleToSendSMS();              
            foreach ($schedules as $key => $schedule) {            
                $socialRoom=$schedule;
                $libRepParticipants=$schedule->socialRoomParticipants;

                foreach ($libRepParticipants as $key => &$value) {
                    if($value->role == 'external-guest'){
                        if($value->external_user_mobile == null || $value->external_user_mobile == ''){
                            continue;
                        }
                        $value->is_external_participant=true;
                        $value->user_name=$value->external_user;
                        $value->mobile=$value->external_user_mobile;
                    }else{
                        $other_details=(object)$this->getRepFurtherDetails($value->person_id); 
                        $value->user_name=$other_details->name;
                        $value->mobile=$other_details->mobile;
                    }
                    
                }
                
                $socialRoomSchedule=$schedule->socialRoomSchedule;
                $sendSms = new SocialRoomSmsNotification;                
                $sendSms->sendSmsTemplate2($socialRoom, $libRepParticipants, $socialRoomSchedule, $schedule->lt_social_room_type_id);
            }
        }
    }

    public function getRepFurtherDetails($person_id){
        $libReps = $this->libReps[$person_id];
        return $libReps;
    }

    public function getInstantScheduleToSendSMS()
    {        
        // For testing only
        // Carbon::setTestNow('2021-03-26 13:55:00');
        $minsBefore=-5; //5 mins before        
        $carbonNow = Carbon::now();    

        //Get All Schedules with valid date before 5 mins
        $socialRooms = SocialRoom::whereIn('lt_social_room_type_id', [4,5])            
            ->with('socialRoomSchedule')
            ->whereHas('socialRoomSchedule', function($query) use($carbonNow) {
                $query->where('valid_until', '>=', $carbonNow)
                      ->where('start_date', '<=', Carbon::now()->addDays(1));
            })
            ->with('socialRoomParticipants')
            ->with('deletedScheduleEntries')
            ->get();
        
        
        $SmsReady=[];       

        //Filter schedules
        
        foreach ($socialRooms as $key => $socialRoom) {
            
            $start_date=Carbon::Parse($socialRoom->socialRoomSchedule->start_date);
            $scheduledDayofWeek = $start_date->dayOfWeek;

            $startdateCompare=Carbon::Parse(date("H:i",strtotime($start_date)));
            //$compareDate=Carbon::Parse(date("H:i",strtotime(Carbon::now())));            
            $compareDate=Carbon::Parse(date("H:i",strtotime($carbonNow)));            

            $differenceInMinute=$startdateCompare->diffInMinutes($compareDate,false);            
          
            if($differenceInMinute==$minsBefore){ 
                $schedule=$socialRoom->socialRoomSchedule;

                $upcomingSched = $this->getSoonestUpcomingSched($socialRoom);
                $isUpcomingSchedToday = false;
                if ($upcomingSched && $upcomingSched->isToday()) {
                    $isUpcomingSchedToday = true;
                    $SmsReady[]=$socialRoom;
                }
              
                // switch ($schedule->frequency_type) {
                //     case 'day': //every day 
                //         if($isUpcomingSchedToday) {
                //             $SmsReady[]=$socialRoom;
                //         }
                //         break;  
                //     case 'week': 
                //         \Log::info('CRON: SMS EXECUTED: '.$carbonNow);
                //         \Log::info('week');
                //         \Log::info($carbonNow->dayOfWeek);
                //         \Log::info($scheduledDayofWeek);
                    
                //         if($carbonNow->dayOfWeek==$scheduledDayofWeek ){
                //             if(($schedule->frequency==1) || ($schedule->frequency==2 && ($carbonNow->weekOfMonth % 2 ==1))){
                //                 \Log::info('weeksmsactivated');
                //                 if($isUpcomingSchedToday) {
                //                     $SmsReady[]=$socialRoom;
                //                 }
                //             }
                //         }                                
                //         break;
                //     case 'month': 
                //         if($schedule->frequency==1 && $carbonNow->weekOfMonth == $start_date->weekOfMonth && $carbonNow->dayOfWeek==$scheduledDayofWeek){
                //             if($isUpcomingSchedToday) {
                //                 $SmsReady[]=$socialRoom;             
                //             }
                //         }                                 
                //         break;
                //     case 'quarter': 
                //         if($schedule->frequency==1 && $carbonNow->weekOfMonth == $start_date->weekOfMonth && $carbonNow->dayOfWeek==$scheduledDayofWeek){
                //             if($isUpcomingSchedToday) {
                //                 $SmsReady[]=$socialRoom;             
                //             }
                //         }
                //         break; 
                //     default:
                //         break;
                // }
            }
        }
        return $SmsReady;
    }   

    public function getSoonestUpcomingSched($socialRoom) {
        try {
            $socialRoomCollection = new Collection([$socialRoom]);
            $tmpData = UpcomingSchedule::generate($socialRoomCollection, ['office_timezone' => 'Europe/London'], 30, 2);
            $upcomingSched = null;
            if(isset($tmpData['upcomingSchedules'][0]['schedule'])) {
                $upcomingSched = Carbon::parse($tmpData['upcomingSchedules'][0]['schedule']);
            }
            return $upcomingSched;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get the console command arguments.
     *
     * @return array
     */
    // protected function getArguments()
    // {
    //  return array(
    //      array('example', InputArgument::REQUIRED, 'An example argument.'),
    //  );
    // }

    /**
     * Get the console command options.
     *
     * @return array
     */
    // protected function getOptions()
    // {
    //  return array(
    //      array('example', null, InputOption::VALUE_OPTIONAL, 'An example option.', null),
    //  );
    // }

}
