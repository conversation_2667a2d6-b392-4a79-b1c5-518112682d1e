<?php

namespace App\Console\Commands;

use App\Http\Controllers\Api\V1\RiskRecommendationCardsController;
use App\Services\QueueService;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;

class RiskRecommendationExportCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'risk-rec:export';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will get the queue messages to process the export for risk recommendations.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $rrController = App::make(RiskRecommendationCardsController::class);
        $queueService = new QueueService(config('app.aws.risk_recommendation_export_sqs'));

        $messages = $queueService->fetchMessages();
        foreach ($messages as $message) {
            try {
                Log::info("[RiskRecommendationExportCommand]: " . $message['Body']);
                $body       = json_decode($message['Body']);
                $requestArr = (array) $body->request;
                $myRequest  = Request::create('risk-recommendations/processExport', 'POST', $requestArr);
                $rrController->queueExport($myRequest);

                $queueService->deleteMessage($message['ReceiptHandle']);

                Log::info("[RiskRecommendationExportCommand]: Done");
                $this->info("[RiskRecommendationExportCommand]: Done");
            } catch (\Exception $e) {
                $queueService->deleteMessage($message['ReceiptHandle']);
                $queueService->sendRequeue($message);

                Log::error("[RiskRecommendationExportCommand:ERROR] " . $e->getMessage());
                Log::error("[RiskRecommendationExportCommand:ERROR] " . $e->getTraceAsString());
            }
        }
    }
}
