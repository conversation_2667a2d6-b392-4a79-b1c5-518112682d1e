<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Console\Input\InputArgument;
use App\Models\FormySubmissions;
use App\Models\FormSubmissionsMapping;
use App\Models\PublicFormySubmissions;
use App\Models\LibertyUser;
use App\Models\User;
use Carbon\Carbon;
use App\Models\Mailqueue;
use Illuminate\Support\Facades\Log;
use MongoDB\BSON\UTCDateTime;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Config;
use App\Models\BrokerUser;
class EmailFormSubmissionCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'riskreduce:email-form-submission-report';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will send a daily report for assigned notifications.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->mail = new Mailqueue();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function fire()
    {
        $this->triggerEmail();
    }

    public function triggerEmail()
    {        
        $fromDate = date('Y-m-d'.' 00:00:00', strtotime('yesterday'));
        $toDate = date('Y-m-d'.' 23:59:59', strtotime('yesterday'));

        Log::info($fromDate);
        Log::info($toDate);

        $start = new UTCDateTime(strtotime($fromDate));
        $stop = new UTCDateTime(strtotime($toDate));
        $formType = $this->argument('formType');

        $all_submission_mapping=[];

        if ($formType==='formyPublic') {            
            $formSubmissions = PublicFormySubmissions::with($formType, 'organisationUsers', 'organisation', 'organisation.assign_brokers')
				->whereBetween('created_at', array($fromDate, $toDate))
                ->get();
            $emailArr = [];
            $emailArrClient = [];

            foreach ($formSubmissions as $value) {
                if ($value->formyPublic['notify_liberty_admin']==="1") {
                    $libertyUsers = LibertyUser::where('activated', '=', 1)
                        ->where('role', '=', 'admin')
                        ->get();
                    
                    foreach ($libertyUsers as $libertyUser) {
                        $emailArr[$libertyUser->email][$value->formyPublic['name']][] = $value->_id;
                        echo $this->info('submissions #:'.$value->_id.' '.$value->form_id.' notifications:'.$libertyUser->email.' form name:'.$value->formy['name']);
                    }

                    echo "Public Notify Liberty Admin --end--";
                    echo "\n\n";
                }

                if ($value->formyPublic['notify_group_user']==="1") {
                    foreach ($value->organisationUsers as $organisationUser) {
                        if ($organisationUser->manager===1) {
                            $emailArrClient[$organisationUser->email][$value->formyPublic['name']][] = $value->_id;
                            echo $this->info('submissions #:'.$value->_id.' '.$value->form_id.' notifications:'.$organisationUser->email.' form name:'.$value->formy['name']);
                        }
                    }
                    echo "Public Notifications Group User --end--";
                    echo "\n\n";
                }

                if ($value->formyPublic['notify_broker'] === '1') {
                    $contactBrokers = $value->organisation->assign_brokers;
                    if (!empty($contactBrokers->count())) {
                        foreach ($contactBrokers as $contactBroker) {
                            $brokerEmail = $contactBroker->email;
                            if (empty($brokerEmail)) {
                                $brokerEmail = $contactBroker->brokerUser->email;
                            }
                            $emailArr[trim($brokerEmail)][$value->formyPublic['name']][] = $value->_id;
                            echo $this->info('submissions #:' . $value->_id . ' ' . $value->form_id . ' notifications:' . $brokerEmail . ' form name:' . $value->formy['name']);
                        }
                    }

                    echo "Public Notifications Broker --end--";
                    echo "\n\n";
                }

                if ($value->formyPublic['notifications']) {
                    $emails = explode(',', $value->formyPublic['notifications']);
                     
                    foreach ($emails as $email) {
                        $emailArr[trim($email)][$value->formyPublic['name']][] = $value->_id;                        
                    }
                }

                if ($value->formyPublic['notifications_admin_users']) {
                    $emails = explode(',', $value->formyPublic['notifications_admin_users']);
                     
                    foreach ($emails as $email) {
                        $emailArr[trim($email)][$value->formyPublic['name']][] = $value->_id;                        
                    }
                }
                
                if ($value->formyPublic['notifications_client_users']) {
                    $emails = explode(',', $value->formyPublic['notifications_client_users']);
                     
                    foreach ($emails as $email) {
                        $emailArrClient[trim($email)][$value->formyPublic['name']][] = $value->_id;                        
                    }
                }

                echo "Public Notifications --end--";
                echo "\n\n";
            }
        } else {

			$formSubmissions = FormySubmissions::with($formType, 'organisationUsers', 'organisation', 'organisation.assign_brokers')
				->whereBetween('updated_at', array($start, $stop))
				->where('submitted', '=', '1')
				->get();
        
            $emailArr = [];
            $emailArrClient = [];

            foreach ($formSubmissions as $value) {

                if ($value->formy['notify_liberty_admin']==="1") {
                    $libertyUsers = LibertyUser::where('activated', '=', 1)
                        ->where('role', '=', 'admin')
                        ->get();
                    
                    foreach ($libertyUsers as $libertyUser) {
                        $emailArr[$libertyUser->email][$value->formy['name']][] = $value->_id;
                        echo $this->info('submissions #:'.$value->_id.' '.$value->form_id.' notifications:'.$libertyUser->email.' form name:'.$value->formy['name']);
                    }
                    echo "Notify Liberty Admin --end--";
                    echo "\n\n";
                    
                }

                if ($value->formy['notify_group_user']==="1") {
                    foreach ($value->organisationUsers as $organisationUser) {
                        if ($organisationUser->manager===1) {
                            $emailArrClient[$organisationUser->email][$value->formy['name']][] = $value->_id;
                            echo $this->info('submissions #:'.$value->_id.' '.$value->form_id.' notifications:'.$organisationUser->email.' form name:'.$value->formy['name']);
                        }
                    }
                    echo "Notifications Group User --end--";
                    echo "\n\n";
                }

                if ($value->formy['notify_broker'] === '1') {
                    $contactBrokers = $value->organisation->assign_brokers;
                    if (!empty($contactBrokers->count())) {
                        foreach ($contactBrokers as $contactBroker) {
                            $brokerEmail = $contactBroker->email;
                            if (empty($brokerEmail)) {
                                $brokerEmail = $contactBroker->brokerUser->email;
                            }
                            $emailArr[trim($brokerEmail)][$value->formy['name']][] = $value->_id;
                            echo $this->info('submissions #:' . $value->_id . ' ' . $value->form_id . ' notifications:' . $brokerEmail . ' form name:' . $value->formy['name']);
                        }
                    }

                    echo "Notifications Broker --end--";
                    echo "\n\n";
                }

                if ($value->formy['notifications']) {
                    $emails = explode(',', $value->formy['notifications']);
                
                    foreach ($emails as $email) {
                        $emailArr[trim($email)][$value->formy['name']][] = $value->_id;
                        echo $this->info('submissions #:'.$value->_id.' '.$value->form_id.' notifications:'.trim($email).' form name:'.$value->formy['name']);
                    }
                }

                if ($value->formyPublic['notifications_admin_users']) {
                    $emails = explode(',', $value->formyPublic['notifications_admin_users']);
                     
                    foreach ($emails as $email) {
                        $emailArr[trim($email)][$value->formyPublic['name']][] = $value->_id;                        
                    }
                }
                
                if ($value->formyPublic['notifications_client_users']) {
                    $emails = explode(',', $value->formyPublic['notifications_client_users']);
                     
                    foreach ($emails as $email) {
                        $emailArrClient[trim($email)][$value->formyPublic['name']][] = $value->_id;                        
                    }
                }
                echo "Notifications --end--";
                echo "\n\n";
            }
        }
        
        // Normal notifications and liberty admin notification
        $now = Carbon::now('utc')->toDateTimeString();
        foreach ($emailArr as $key => $forms) {            
            foreach ($forms as $key2 => $submissions) {
                $adminSubmissions=[];
                foreach ($submissions as $submission) {
                    $adminSubmissions[]=[
                        'submission_id'=>$submission,'mapping_id'=>Uuid::generate(4)->string,
                        'created_at'=> $now,'updated_at'=> $now
                    ];
                }
                $all_submission_mapping[]=$adminSubmissions; 

                Log::info($key);

                $this->mail->notify(
                    $key,
                    'Risk Reduce - New submissions for today',
                    'Risk Reduce - New submissions for today',
                    'emails.forms.new_form_notification',
                    [
                        'forms' => $key2,
                        'is_client' => false,
                        'submissions' => $adminSubmissions,
						'app_url' => Config::get('app.admin_frontend'),
                        'form_type' => ($formType=='formyPublic') ? 'public' : '',
                        'user_name' => $this->getUserDetils($key,'admin'),
                    ]
                ); 
            }
        }

        // Client notifications
        foreach ($emailArrClient as $key => $forms) {
            foreach ($forms as $key2 => $submissions) {
                $clientSubmissions=[];
                foreach ($submissions as $submission) {
                    $clientSubmissions[]=[
                        'submission_id'=>$submission,'mapping_id'=>Str::uuid(4)->string,
                        'created_at'=> $now,'updated_at'=> $now
                    ];  
                }
                $all_submission_mapping[]=$clientSubmissions;

                Log::info($key);

                $this->mail->notify(
                    $key,
                    'Risk Reduce - New submissions for today',
                    'Risk Reduce - New submissions for today',
                    'emails.forms.new_form_notification',
                    [
                        'forms' => $key2,
                        'is_client' => true,
                        'submissions' => $clientSubmissions,
						'app_url' => Config::get('app.client_frontend'),
                        'form_type' => ($formType=='formyPublic') ? 'public' : '',
                        'user_name' => $this->getUserDetils($key,'client'),
                    ]
                );
            }
        }

        if (isset($all_submission_mapping) && !empty($all_submission_mapping)) {
            $result = call_user_func_array('array_merge', $all_submission_mapping);
            FormSubmissionsMapping::insert($result);
        }
    }

    public function getUserDetils($email,$type){
        $user = (object)[];
        $userName = '';
        if ($type == 'client') {
            $clientuser = User::where('email', $email)->first();
        } else {
            $clientuser = LibertyUser::where('email', $email)->first();
            if (empty($clientuser)) {
                $clientuser = BrokerUser::where('email', $email)->first();
            }
        }

        if (!empty($clientuser)) {
            $user->first_name = $clientuser->first_name;
            $user->last_name  = $clientuser->last_name;
            $userName = $user->first_name . ' ' . $user->last_name;
        }

        return $userName;
    }

    /**
     * Get the console command arguments.
     *
     * @return array
     */
    protected function getArguments()
    {
        return array(
            array('formType', InputArgument::REQUIRED, 'A submission type argument.'),
        );
    }

    /**
     * Get the console command options.
     *
     * @return array
     */
    protected function getOptions()
    {
        return array(
            //array('example', null, InputOption::VALUE_OPTIONAL, 'An example option.', null),
        );
    }
}
