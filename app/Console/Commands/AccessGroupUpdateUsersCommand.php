<?php

namespace App\Console\Commands;

use App\Models\Organisation;
use App\Services\AccessGroup\UpdateOrganisationUsersService;
use Illuminate\Console\Command;

class AccessGroupUpdateUsersCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'access-group:update-users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "This command will check the organistaion that has active policy and update their apropriate status in Access LMS";

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        (new UpdateOrganisationUsersService(Organisation::class))->updateUser();
    }
}
