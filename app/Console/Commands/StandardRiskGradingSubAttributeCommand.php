<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Console\Input\InputArgument;
use App\Models\RGSubAttribute;
use App\Models\RGAttribute;
use App\Models\RGSubAttributeLog;
class StandardRiskGradingSubAttributeCommand extends Command {

	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'standard-riskgrading:update-sub-attribute';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Update sub attribute standard risk grading';

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
	}

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{
		$subAttributeId 		= $this->argument('id');
		$attributeId			= $this->argument('attributeId');
		$newSubAttributeName	= $this->argument('subAttribute');
		$newMaxpoint			= $this->argument('maxPoint');
		$order					= $this->argument('order');

		$this->info('Updating sub attribute id: ' . $subAttributeId);
		
		$subAttribute = RGSubAttribute::find($subAttributeId);
		if (isset($subAttribute)) {
			$subAttribute->sub_attribute   = isset($newSubAttributeName) ? $newSubAttributeName : $subAttribute->sub_attribute;
			$subAttribute->max_point	   = isset($newMaxpoint) ? $newMaxpoint : $subAttribute->max_point;
			$subAttribute->order		   = isset($order) ? $order : $subAttribute->order;

			$attribute = RGAttribute::find($attributeId);
			if (isset($attribute)) {
				$subAttribute->rg_attribute_id = isset($attributeId) ? $attributeId : $subAttribute->rg_attribute_id;
			}

			$subAttribute->save();

			RGSubAttributeLog::create([
				'rg_sub_attribute_id' => $subAttribute->id,
				'attribute_id'		  => $subAttribute->attribute_id,
				'sub_attribute' 	  => $subAttribute->name,
				'version' 	 		  => $subAttribute->version,
				'order'				  => $subAttribute->order,
				'created_at' 		  => date('Y-m-d H:i:s'),
				'updated_at' 		  => date('Y-m-d H:i:s'),
			]);
		}

		$this->info('Done');
	}

	/**
	 * Get the console command arguments.
	 *
	 * @return array
	 */
	protected function getArguments()
	{
		return [
			['id', InputArgument::REQUIRED, 'Sub Attribute ID'],
			['attributeId', InputArgument::OPTIONAL, 'Attribute ID'],
			['subAttribute', InputArgument::OPTIONAL, 'Sub Attribute Name'],
			['maxPoint', InputArgument::OPTIONAL, 'Max point'],
			['order', InputArgument::OPTIONAL, 'Order of sub attribute']
		];
	}
}
