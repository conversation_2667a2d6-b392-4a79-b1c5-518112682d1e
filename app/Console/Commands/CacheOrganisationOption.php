<?php

namespace App\Console\Commands;

use App\Models\Organisation;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CacheOrganisationOption extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:org-options';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cache Organisation options';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $start = microtime(true);
        $result = Organisation::select(['id', 'sector', 'name'])
                // ->with('users')
                ->orderBy('name', 'asc')
                ->get();
        $result = $result->map(fn($item) => (object)$item->toArray());
        Cache::forever('organisation-options', $result->all());

        $end = microtime(true);
        $execution_time = ($end - $start);
        Log::info("Successfully cached 'organisation-options'. ($execution_time secs)");
    }
}
