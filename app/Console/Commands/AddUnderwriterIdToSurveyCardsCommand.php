<?php

namespace App\Console\Commands;

use App\Models\SurveyCards;
use Illuminate\Console\Command;


class AddUnderwriterIdToSurveyCardsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'riskrec:add_underwriter_id_to_survey_cards';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update survey cards to add underwriter_id if missing';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $surveyCards = SurveyCards::whereIn('underwriter_id', [null, ''])->with('survey')->get();

        $progress = $this->output->createProgressBar($surveyCards->count());

        $surveyCards->each(function ($surveyCard) use ($progress) {
            if (!empty($surveyCard->survey->underwriter_id)) {
                $surveyCard->underwriter_id = $surveyCard->survey->underwriter_id;
                $surveyCard->save();
            }
            $progress->advance();
        });
    }
}
