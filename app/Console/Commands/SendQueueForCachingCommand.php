<?php

namespace App\Console\Commands;

use App\Http\Controllers\Api\V1\SurveyController;
use App\Models\Survey;
use App\Services\QueueService;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SendQueueForCachingCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'send-queue-for-caching';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "This command will send queue messages to be able to cache data depending on the service class.";

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $queueService = new QueueService(config('app.aws.invalidate_cache_sqs_temp'));

        $surveys = Survey::select('id')->get();

        $messagesSentCount = 0;
        foreach (array_chunk($surveys->toArray(), 100, true) as  $surveysChunk) {
            $messages = [];
            foreach ($surveysChunk as $survey) {
                $messages[] = [
                    'serviceClass' => 'App\Services\CacheContent\GetSurveyService',
                    'params' => $survey['id'] ?? '',
                ];
                $messagesSentCount++;
            }

            $queueService->sendMessages($messages);
            sleep(1);
        }
    }
}
