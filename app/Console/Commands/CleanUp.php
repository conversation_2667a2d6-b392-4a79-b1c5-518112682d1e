<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class CleanUp extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'riskreduce:cleanup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleans up all files that are older than 5 minutes';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // clean up excel exports
        $path  = storage_path('excel/exports');

        if (!File::isDirectory($path)) {
            Log::debug("[CleanUp Comman] {$path} does not exist.");
            return;
        }

        $files = File::files($path);
        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                if (time() - filectime($file) > 300) {
                    File::delete($path . '/' . $file);
                }
            }
        }
    }
}
