<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\RiskImprovementFormySubmissions;
use App\Models\RiskImprovementFormy;
use App\Models\RiskRecommendationFormy;
use App\Models\OrganisationPolicy;
use App\Models\Messaging;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Response;

class RiskRec extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'riskreduce:riskrec';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rebuild riskrec cache';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $submissions = RiskImprovementFormySubmissions::where('csr_status', '=', 'submitted')->get();
        $policies    = ['1' => 'Property', '2' => 'Casualty', '3' => 'Commercial Combined'];
        $org_array   = [];
        $label_array = [];

        $i = 0;
        foreach ($submissions as $ksub => $submission) {
            $risk_rec_data              = [];
            $submission->survey         = $submission->survey;
            $submission->surveyContacts = $submission->surveyContacts;

            if (!isset($submission->survey->organisation_id) || is_null($submission->survey->organisation)) {
                continue;
            }

            $organisation_id = $submission->survey->organisation_id;
            if (array_key_exists($organisation_id, $org_array)) {
                $submission->organisation = $org_array[(string) $organisation_id]['organisation'];
                $submission->organisation->branch = $submission->organisation->libertyBranch();
            } else {
                $submission->organisation = $submission->survey->organisation;
                $submission->organisation->branch = $submission->organisation->libertyBranch();
                $org_array[(string) $organisation_id]['organisation'] = $submission->organisation;
                $org_array[(string) $organisation_id]['branch'] = $submission->organisation->branch;
            }

            $submission->schedule = $submission->schedule();
            $risk_rec_array       = [];
            $formid               = $submission->form_id;
            $form                 = RiskImprovementFormy::find($formid);

            if (isset($form->fields)) {
                $fields = $form->fields;
                $rrstatus = array_map(function ($v) {
                    if (isset($v['risk_recommendation'])) {
                        return array_map(function ($vv) {
                            return array_map(function ($vc) {
                                $arr = [];
                                if ($vc['name'] == 'section' || $vc['name'] == 'prefix') {
                                    array_push($arr, $vc);
                                }
                                return $arr;
                            }, $vv);
                        }, $v);
                    }
                }, $fields);

                $codearray = [];
                foreach ($rrstatus as $k => &$rrstatus) {
                    if (isset($rrstatus['risk_recommendation'])) {
                        $code  = '';
                        $label = '';

                        foreach ($rrstatus['risk_recommendation'] as $rrrec) {
                            if (count($rrrec) > 0) {
                                if ($rrrec[0]['name'] == 'section') {
                                    $label = $rrrec[0]['value'];
                                }

                                if ($rrrec[0]['name'] == 'prefix') {
                                    $code = $rrrec[0]['value'];
                                }
                            }
                            $codearray[] = ['code' => $code,'label' => $label];
                        }
                    }
                }

                $label = array_filter($codearray, function ($v) use (&$codearray) {
                    return ($v['code'] != '' && $v['label'] != '');
                });

                $label_array[]           = array_values($label);
                $submission->label_array = $label_array ;
            }

            $rr_exists = RiskRecommendationFormy::where('form_id', '=', $submission->form_id)->first();

            if (isset($rr_exists->risk_recommendation_fields)) {
                $submission->risk_recommendations = $rr_exists->risk_recommendation_fields;
            }

            if (!isset($submission->risk_recommendations)) {
                $form = RiskImprovementFormy::find($submission->form_id);
                // print_r($form->fields); exit;
                if (isset($form->fields)) {
                    foreach ($form->fields as $field) {
                        foreach ($field as $key => $value) {
                            if ($key == 'risk_recommendation') {
                                //print_r($value); exit;
                                foreach ($value as $val) {
                                    if ($val['name'] == 'name') {
                                        array_push($risk_rec_array, $val['value']);
                                    }
                                }
                            }
                        }
                    }
                }

                $submission->risk_recommendations = $risk_rec_array;

                $risk_rec_data['form_id'] = $submission->form_id;
                $risk_rec_data['risk_recommendation_fields'] = $risk_rec_array;

                $insertRiskRec = RiskRecommendationFormy::insert($risk_rec_data);
            }

            $risk_recs = $submission->risk_recommendations;
            $rrm_array = [];

            foreach ($risk_recs as $risk_rec) {
                for ($i = 1; $i <= 15; ++$i) {
                    if (
                        isset($submission->{$risk_rec . '_' . $i . '_classification'})
                        && $submission->{$risk_rec . '_' . $i . '_classification'} != ''
                    ) {
                        $messages = Messaging::where('survey_id', '=', (string) $submission->survey->id)
                                        ->where('rr_ref', '=', $risk_rec . '_' . $i . '_message', 'AND')
                                        ->get();
                        $rrm_array['' . $risk_rec . '_' . $i . '_message' . ''] = $messages;
                    }
                }
            }

            $submission->rrm = $rrm_array;

            if (isset($submission->survey)) {
                $policy = OrganisationPolicy::find($submission->survey->policy_id);
                if ($policy && in_array($policy->policy_type_id, ['1', '2', '3'])) {
                    $submission->survey->policy_name = $policies[$policy->policy_type_id];
                }
            }
            Cache::forever('rrtrackerdata_' . $submission->_id, $submission);

            ++$i;
        }

        // Command runner expects a response that can be
        // typecasted into an int so we should only
        // return a JSONResponse when command
        // is not ran in console
        if (!app()->runningInConsole()) {
            return Response::json(array('response' => 'success'));
        }

        //if (Input::has('refresh_cache') && !Input::has('admin_call')) {
            // return Response::json(array('response' => 'success'));
        //}
    }
}
