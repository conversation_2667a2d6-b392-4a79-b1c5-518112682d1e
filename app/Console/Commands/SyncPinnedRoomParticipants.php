<?php

namespace App\Console\Commands;

use <PERSON><PERSON>a\Twilio\Twilio;
use Twi<PERSON>\Rest\Client;
use Illuminate\Console\Command;
use App\Models\LetsTalk\SocialRoom;
use App\Models\LetsTalk\CmsLibertyRepresentative;
use App\Models\LetsTalk\SocialRoomParticipant;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Config;
class SyncPinnedRoomParticipants extends Command 
{
    private $twilio;
    private $twilioClient;
    private $libReps;
    
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'virtual-rooms:sync-pinned-room-participants';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create participant data of persons who have pinned the theme or community room but does not have participant data.';

     /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
                
        $twilioConfig = Config::get('app.twilio.sms');        
        $this->twilio = new Twilio($twilioConfig['sid'], $twilioConfig['token'], $twilioConfig['from']);
        $this->twilioConfigUS = new Twilio($twilioConfig['sid'], $twilioConfig['token'], $twilioConfig['from-us']);
        $this->twilioClient = new Client($twilioConfig['sid'], $twilioConfig['token']);
        
        $this->libReps = CmsLibertyRepresentative::fetchAllFromCacheOrCms();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {        
        $socialRooms = SocialRoom::where('lt_social_room_type_id', 3)
        ->where('status','approved')
        ->with('socialRoomParticipants')
        ->with('socialRoomPins')
        ->has('socialRoomPins')
        ->get();

        $libReps = $this->libReps;

        foreach($socialRooms as $key => $room){
            $existingParticipantsPersonId = Arr::pluck($room->socialRoomParticipants, 'person_id');

            foreach ($room->socialRoomPins as $key => $socialRoomPin) {
                $personId = $socialRoomPin->person_id;
                if(!in_array($personId, $existingParticipantsPersonId) && isset($libReps[$personId])) {
                    $personPinCms = $libReps[$personId];
                    SocialRoomParticipant::create([
                        'lt_social_room_id' => $socialRoomPin->lt_social_room_id,
                        'person_id' => $personId,
                        'user_code' => SocialRoomParticipant::generateUserCode(),
                        'role' => 'virtual-rooms',
                        'user_name' => isset($personPinCms['name']) ? $personPinCms['name'] : "n/a",
                        'business' => isset($personPinCms['business']) ? $personPinCms['business'] : "lsm",
                    ]);
                    $this->info('Participant Created: '. $personId .' lt_social_room_id: ' . $socialRoomPin->lt_social_room_id);
                }

            }
        }

        $this->info('Success: Pinned Room participants is now in sync!');
    }

}
