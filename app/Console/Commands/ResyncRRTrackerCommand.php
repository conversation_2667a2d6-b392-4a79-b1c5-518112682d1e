<?php

namespace App\Console\Commands;

use App\Models\RiskImprovementFormySubmissions;
use App\Models\Survey;
use App\Models\RiskRecommendationCards;
use Aws\Laravel\AwsFacade as AWS;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

class ResyncRRTrackerCommand extends Command
{
    //Flag is to pass any additional params for future use

    protected $signature = 're-sync-rrtracker {flag?}';

    protected $description = 'Command to sync rr tracker from existing submissions which did not synced before';

    public function handle()
    {
        $flag = $this->argument('flag');
        switch ($flag) {
            case 'survey':
                $this->processSurvey();
                break;
            default:
                exit;
        }
    }

    private function processSurvey()
    {
        $usedForms = [
            '62de92da30bb8d47830a2096',
            '62de92da30bb8d47830a2097',
            '64df1ff9d03bcf050e0d1812',
            '651d24e2e2a473b943086252',
            '62de92da30bb8d47830a2098',
            '62de92da30bb8d47830a2095'
        ];

        $existingSurveyIds = [];
        $existingCards = RiskRecommendationCards::distinct('survey_id')->get(['survey_id'])->toArray();

        foreach ($existingCards as $submission) {
            $existingSurveyIds[] = $submission[0];
        }

        $surveyIds = [];
        $submissions = RiskImprovementFormySubmissions::where('csr_status', 'submitted')
            ->whereIn('form_id', $usedForms)
            ->distinct('survey_id')->get(['survey_id'])->toArray();

        foreach ($submissions as $submission) {
            $surveyIds[] = $submission[0];
        }

        $requiredSurveyIds = array_diff($surveyIds, $existingSurveyIds);

        $survey_ids = Survey::whereNull('deleted_at')
            ->pluck('id')
            ->all();

        $requiredSurveyIds = array_intersect($requiredSurveyIds, $survey_ids);

        //$total = count($requiredSurveyIds);

        $checkSurveys = [];

        $counter = 1;
        foreach ($requiredSurveyIds as $survey_id) {
            //$this->comment("processing {$counter} of {$total} surveys");
            $requiredSubmissionId = $this->getSurveyRiskRecInfo($survey_id);
            if ($requiredSubmissionId) {
                $checkSurveys[$survey_id] = $requiredSubmissionId;
            }
            $counter++;
        }
        $this->sendSubmissionForSync($checkSurveys);
    }

    private function sendSubmissionForSync($riSubmissions)
    {
        try {
            foreach ($riSubmissions as $survey_id => $submission_id) {
                AWS::createClient('Sqs')->sendMessage([
                    'QueueUrl' => Config::get('app.aws.sqs_kanban_sync_queue'),
                    'MessageBody' => json_encode(['submission_id' => $submission_id]),
                    'MessageAttributes' => [],
                    'DelaySeconds' => 2,
                ]);
            }
        } catch (\Exception $e) {
            Log::info('Error in re-sync-rrtracker:');
            Log::info($e->getMessage());
        }
    }

    private function getSurveyRiskRecInfo($survey_id)
    {
        try {
            $submission = RiskImprovementFormySubmissions::where('survey_id', $survey_id)->first();
            $rr_trackers = $submission->formyRR();

            $fields = [
                '_ref',
                '_message',
                '_classification',
                '_required_by',
                '_issue_closed',
                '_description',
                '_action',
                '_title',
                '_issue_closed',
            ];

            foreach ($rr_trackers as $risk_rec) {
                for ($i = 1; $i <= 15; ++$i) {
                    $risk_rec_prefix = $risk_rec . '_' . $i;
                    foreach ($fields as $field) {
                        if (isset($submission->{$risk_rec_prefix . $field})) {
                            if ($field === '_classification' && $submission->{$risk_rec_prefix . '_classification'} != '') {
                                return $submission->_id;
                            }
                        }
                    }
                }
            }
            return null;
        } catch (Exception $e) {
            return null;
        }
    }
}
