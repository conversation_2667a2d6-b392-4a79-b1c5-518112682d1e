<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Console\Input\InputArgument;
use App\Models\RGAttribute;
use App\Models\RGPolicyType;
class StandardRiskGradingAttributeCommand extends Command {

	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'standard-riskgrading:update-attribute';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Update standard risk grading attribute';

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
	}

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function fire()
	{
		$attributeId 	  = $this->argument('id');
		$newPolicyTypeId  = $this->argument('policyId');
		$newAttributeName = $this->argument('attribute');
		$newOrder		  = $this->argument('order');
		$attribute		  = RGAttribute::find($attributeId);

		$this->info('Updating attribute id: ' . $attributeId);

		if ($attribute) {
			$attribute->attribute  = isset($newAttributeName) ? ucfirst($newAttributeName) : $attribute->attribute;
			$attribute->order	   = isset($newOrder) ? $newOrder : $attribute->order;
			$attribute->version	   = isset($newAttributeName) || isset($newOrder) ? $attribute->version + 1 : $attribute->version;

			$policyType = RGPolicyType::find($newPolicyTypeId);
			if (isset($policyType)) {
				$attribute->rg_policy_type_id = isset($newPolicyTypeId) ? $newPolicyTypeId : $attribute->rg_policy_type_id;
			}

			$attribute->save();

			RGAttributeLog::create([
				'rg_attribute_id' 	  => $attribute->id,
				'rg_policy_type_id'	  => $attribute->rg_policy_type_id,
				'attribute' 	  	  => $attribute->attribute,
				'version' 	 		  => $attribute->version,
				'order'				  => $attribute->order,
				'created_at' 		  => date('Y-m-d H:i:s'),
				'updated_at' 		  => date('Y-m-d H:i:s'),
			]);
		}

		$this->info('Done');
	}

	/**
	 * Get the console command arguments.
	 *
	 * @return array
	 */
	protected function getArguments()
	{
		return [
			['id', InputArgument::REQUIRED, 'Attribute id'],
			['policyId', InputArgument::OPTIONAL, 'Policy Type id'],
			['attribute', InputArgument::OPTIONAL, 'New attribute name'],
			['order', InputArgument::OPTIONAL, 'New order of an attribute']
		];
	}

}