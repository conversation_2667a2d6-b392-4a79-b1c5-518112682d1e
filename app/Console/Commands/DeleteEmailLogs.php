<?php

namespace App\Console\Commands;

use App\Models\EmailLogs;
use Illuminate\Console\Command;

class DeleteEmailLogs extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'riskreduce:delete-email-logs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Risk Reduce Delete Email Logs';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $res = EmailLogs::deleteable()->delete();
        $this->info("Deleted email logs: {$res}");
    }

    /**
     * Get the console command arguments.
     *
     * @return array
     */
    protected function getArguments()
    {
        return array();
    }

    /**
     * Get the console command options.
     *
     * @return array
     */
    protected function getOptions()
    {
        return array();
    }
}
