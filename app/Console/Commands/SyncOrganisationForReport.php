<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Organisation;
use App\Models\LibertyUser;
class SyncOrganisationForReport extends Command {

	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'dashboard:sync-organisation';

	protected $options = ['A' => 'blue', 'B' => 'green', 'C' => 'yellow', 'D' => 'orange', 'E' => 'red'];


	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Sync Organisation For Report';

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
	}


	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{		
		$this->updateReportDataForOrganisation();	
	}

	// View All Organisation Report

    private function updateReportDataForOrganisation()
    {
        $organisations = Organisation::all();
        foreach ($organisations as $org) {
            $reportEntity = $this->formatReportEntity($org);
            $this->updateOrganisation($reportEntity);
        }
    }

    private function updateOrganisation($reportEntity)
    {
        $organisation = Organisation::find($reportEntity->id);
        if (isset($organisation)) {
            $organisation->underwriter = $reportEntity->underwriter;
            $organisation->riskengineer = $reportEntity->riskengineer;
            $organisation->policy_expiring_soon = $reportEntity->policy_expiring_soon;
            $organisation->policy_expiring_description = $reportEntity->policy_expiring_description;
            $organisation->risk_grading_value = $reportEntity->risk_grading_value;
            $organisation->save();
        }
	}

	private function formatReportEntity($org){
		$reportEntity=[];
		$reportEntity['id']=$org->id;
		$reportEntity['underwriter']=null;
		$reportEntity['riskengineer']=null;
		$reportEntity['policy_expiring_soon']=null;
		$reportEntity['policy_expiring_description']=null;
		$reportEntity['risk_grading_value']=array_search ($org->risk_grading, $this->options);			

		//Policy Number
		$org->policy_numbers=$org->policyNumbers();
		$allpolicies='';
		if (isset($org->policy_numbers) && count($org->policy_numbers)>0) {				
			$policyendingSoon=$org->policy_numbers[0];              
			foreach ($org->policy_numbers as $policies) {
				$allpolicies .= $policies->type->name.'___'.$policies->expiry_date_of_cover.',';

				if ($policies->expiry_date_of_cover<$policyendingSoon->expiry_date_of_cover) {
					$policyendingSoon=$policies;
				}
			}
			$reportEntity['policy_expiring_soon']=$policyendingSoon->expiry_date_of_cover;
			$reportEntity['policy_expiring_description']=$allpolicies;
		}

		//Risk Engineer

		foreach ($org->contacts as $contact) {
			if ($contact->type == 'underwriter') {
				$re = LibertyUser::find($contact->user_id);	
				$reportEntity['underwriter']=	(isset($re->first_name) ? $re->first_name : '').' '.(isset($re->last_name) ? $re->last_name : '');			
			}
			if ($contact->type == 'risk-engineer') {
				$re = LibertyUser::find($contact->user_id);
				$reportEntity['riskengineer']=	(isset($re->first_name) ? $re->first_name : '').' '.(isset($re->last_name) ? $re->last_name : '');
			}
		}
		
		return (object)$reportEntity;
	}

	//End of View All Organisation Report 

}