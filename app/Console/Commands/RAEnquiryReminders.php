<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use App\Models\FileUpload;
use App\Models\Mailqueue;
use App\Models\Broker;
use Illuminate\Support\Facades\Config;
use Exception;
use DateTime;
use DateTimeZone;
use App\Models\rrAppetite\FormSubmission;

class RAEnquiryReminders extends Command {

	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'risk-appetite:reminders';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Send enquiry reminders if underwriter has not responded to business enquiries.';

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();

		$this->files = new FileUpload();
		$this->mail = new Mailqueue();
	}

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{
		$business_days = Carbon::now()->subWeekdays(2)->toDateTimeString();

		$submissions = FormSubmission::where('verified_broker', 1)
    		->where('enquiry_status', 0)
    		->where('created_at', '>=', $business_days)
    		->get();
		
		foreach ($submissions as $submission) {
			$admin_link = Config::get('app.admin_frontend');
            $file = $this->getFileData($submission);

			$submission['region'] = $file->region_name;
			$email_domain = substr(strrchr($submission['email'], "@"), 1);

			// primary product
            $selected_product = $file->underwriter_selected_product;
            $underwriter = $file->underwriter_underwriter;

			$ics_file = $this->compileICSFile($submission);
	
			$products = $file->broker_products;
			$product_names = [];
	
			if (is_null($submission['timezone']) || $submission['timezone'] == "+00:00") {
				$submission['timezone'] = 'UTC';
			} else {
				$dateTime = new DateTime(); 
				$dateTime->setTimeZone(new DateTimeZone($submission['timezone'])); 
				$submission['timezone'] = $dateTime->format('T'); 
			}
	
			foreach ($products as $product) {
				$product_names[] = $product->name;
			}
	
			$product_names = implode(", ", $product_names);

	        $download_link = null;

			try {
				$download_link = $this->files->link('pdf/'.$submission['id'].'/liberty-presentation.pdf', '7 days');
			} catch (Exception $e) {
				$download_link = null;
			}

			$links = [
				'dealt_with' => $admin_link.'/rr-appetite/enquiries/'.$submission['id'].'/status?enquiry=1',
				'not_dealt_with' => $admin_link.'/rr-appetite/enquiries/'.$submission['id'].'/status?enquiry=0',
				'download_pdf' => $download_link,
				'expiry_time' => '7',
			];

			$attachments = [
				'ics' => [
					'StringValue' => $ics_file,
					'DataType' => 'String',
				],
			];

			// to set up
            // $cc_email = '<EMAIL>';
			$cc_email = '<EMAIL>';

			$broker = Broker::where('domain', 'LIKE', '%'.$email_domain.'%')->first();
			$subject = 'Have you dealt with the enquiry from ' . $broker->name . '?';

			$email_date = $this->checkSLADate($submission['preferred_datetime'], $submission['created_at']);
        	$submission['preferred_datetime'] = $email_date;

            $tmpl_type = 'lsm';

            if ($file->region_slug == 'us') {
                $tmpl_type = 'lmi';
            }

			$this->mail->queue($underwriter->email_1564407408748, $underwriter->name, $subject, 'emails.risk-appetite.'.$tmpl_type.'.enquiry-reminder', [
				'underwriter' => $underwriter,
				'submission' => $submission,
				'broker_company' => $broker->name,
				'product_names'=> $product_names,
				'primary_product' => $selected_product,
				'verified' => true,
				'links' => $links,
			], $attachments, $cc_email);
		}
	}

	/**
     * Get the console command arguments.
     *
     * @return array
     */
    protected function getArguments()
    {
        return [
            //
        ];
    }

    /**
     * Get the console command options.
     *
     * @return array
     */
    protected function getOptions()
    {
        return [
            //
        ];
    }

    private function getFileData($submission)
    {
        if (! $this->files->exists('json/'.$submission['data_cloudname'])) {
            return;
        }

        $file = $this->files->download('json/'.$submission['data_cloudname'], $submission['data_cloudname']);

        $data = file_get_contents(storage_path() . '/file_to_download/'.$submission['data_cloudname']);

        return json_decode($data);
    }

	private function compileICSFile($submission)
    {
		$admin_link = Config::get('app.admin_frontend');
        $name = 'Business enquiry from: ' . $submission['fname'] . ' ' . $submission['surname'];
        $url = $admin_link . '/rr-appetite/enquiries/' . $submission['id'];
        $description = "Please view business enquiry at this url: " . $url;
        if (is_null($submission['timezone']) || $submission['timezone'] == "+00:00") {
            $timezone = 'GMT';
        } else {
            $dateTime = new DateTime(); 
            $dateTime->setTimeZone(new DateTimeZone($submission['timezone'])); 
			$timezone = 'GMT'; 
        }
        $sla_date = $this->checkSLADate($submission['preferred_datetime'], $submission['created_at']);

        $sla_date_at_9_am = $sla_date->startOfDay()->addHours(9);
        $datetime_start = $sla_date_at_9_am->format("Ymd\THis\Z");

        $datetime_end = $sla_date_at_9_am->addMinutes(30)->format("Ymd\THis\Z");
 
        $datetime_stamp = Carbon::now()->format('Ymd\THis\Z');
        $datetime_start = $datetime_start;
        $datetime_end = $datetime_end;
        $ics_file = "BEGIN:VCALENDAR\nPRODID:-//Liberty Risk Reduce//EN\nVERSION:2.0\nCALSCALE:GREGORIAN\nTZ:+00\nMETHOD:REQUEST\nBEGIN:VEVENT\nDTSTART:".$datetime_start."\nDTEND:$datetime_end\nDTSTAMP:".$datetime_stamp."\nSEQUENCE:0\nSTATUS:CONFIRMED\nURL;VALUE=URI:".$url."\nSUMMARY:".$name."\nDESCRIPTION:".$description."\nTRANSP:OPAQUE\nEND:VEVENT\nEND:VCALENDAR\n";


        return $ics_file;
	}

    private function checkSLADate($rtd_date, $created_date) {
        // to check
        $rtd_date = Carbon::createFromTimestamp(strtotime($rtd_date));
        $created_date = Carbon::createFromTimestamp(strtotime($created_date));

        if ($rtd_date >= $created_date->addWeekdays(3)) {
            return $created_date;
        } else {
            return $rtd_date;
        }
    }

}
