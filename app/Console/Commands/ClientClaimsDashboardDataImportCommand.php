<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Console\Input\InputArgument;
use App\Models\ClientClaimsDashboard\ClientClaimId;
use App\Models\ClientClaimsDashboard\ClientClaimLogs;

class ClientClaimsDashboardDataImportCommand extends Command {

    const HEADER_KEY = 'liberty trading name';

	/**
	 * The console command name.
	 *
	 * @var string
	 */
    protected $name = 'claims-client-dashboard:import-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command import client claims data.';

    protected $headers = [
        'trading_name',
        'liberty_id',
        'trading_name_exact_match',
        'organisation_id'
    ];

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
	}

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{
        $filename   = $this->argument('filename');
        $file       = dirname(__DIR__) . '/../' . $filename;
        $handle     = fopen($file, 'r');

        if ($handle !== FALSE) {
            while (!feof($handle)) {
                $data = fgetcsv($handle, 1000, ",");
                if (!$data) break;

                $mappedData = array_combine($this->headers, $data);

                if (strtolower($mappedData['trading_name']) === self::HEADER_KEY) continue;
                if (empty($mappedData['organisation_id']) || empty($mappedData['trading_name_exact_match'])) {
                    continue;
                }

                $this->store($mappedData, $filename);
            }
        }

        fclose($handle);
	}

	private function store($data, $filename)
    {
        $date = date('Y-m-d H:i:s', time());
        try {
            ClientClaimId::create([
                'name' => $data['trading_name_exact_match'],
                'organisation_id' => $data['organisation_id'],
                'liberty_id' => $data['liberty_id']
            ]);
        } catch (\Exception $e) {
            ClientClaimLogs::create([
                'filename'      => $filename,
                'action'        => ClientClaimLogs::IMPORT_ROW_DATA,
                'message'       => $e->getMessage(),
                'created_at'    => $date
            ]);
        }
    }

	/**
	 * Get the console command arguments.
	 *
	 * @return array
	 */
	protected function getArguments()
	{
		return array(
			array('filename', InputArgument::REQUIRED, 'An import filename.'),
		);
	}
}
