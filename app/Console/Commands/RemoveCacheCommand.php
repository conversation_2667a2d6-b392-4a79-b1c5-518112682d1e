<?php

namespace App\Console\Commands;

use App\Models\Survey;
use Illuminate\Support\Facades\Cache;
use Illuminate\Console\Command;

class RemoveCacheCommand extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'riskreduce:clear-forever-cache {flag?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will remove existing forever caches';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $flag = $this->argument('flag');

        switch ($flag) {
            case "surveys":
                $this->comment("Running command for : {$flag}");
                $this->processSurveysCache();
                break;
            default:
                $this->comment("Valid Argument Required..");
                exit;
        }
    }

    /**
     * Get all survey ids
     */
    private function processSurveysCache()
    {   
        $surveys=Survey::get('id');
        foreach($surveys as $survey){
            $cacheKey='ri_submission_relations_' . $survey->id;
            $this->comment("Removing cache for {$cacheKey}");
            Cache::forget($cacheKey);
        }
        $total=count($surveys);
        $this->comment("Done clearing {$total} cache");
    }
}
