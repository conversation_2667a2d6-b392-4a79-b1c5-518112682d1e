<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\LetsTalk\SocialRoom;
use App\Models\Cms;
use Illuminate\Support\Facades\Config;

class VrSyncAnnouncementCategories extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'virtual-rooms:sync-announcement-categories';
    // {name} {description} {image_link} {status}

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command updates community rooms and '
                           . 'syncs/fetch the updates from CMS announcement categories';


    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $workspace        = Config::get('app.cms.lsm.lets_talk_workspace');
        $communities_type = Config::get('app.cms.lsm.communities_content_type');
        $categorySources  = json_decode(Cms::get(
            'workspaces/'
            . $workspace
            . '/content-types/'
            . $communities_type
            . '/content-entries'
        ));

        $this->syncCommunities($categorySources);
    }

    private function syncCommunities($categorySources)
    {
        $existing_categories     = [];
        $communities_description = Config::get('app.cms.lsm.communities_description');
        $communities_image_link  = Config::get('app.cms.lsm.communities_image_link');

        foreach ($categorySources->data as $categorySource) {
            $category_exist = SocialRoom::where('source_category_id', $categorySource->_id)->first();

            if (!empty($category_exist)) {
                SocialRoom::where([
                    'source_category_id' => $categorySource->_id
                ])->update([
                    'name'        => $categorySource->name,
                    'image_link'  => (
                                        isset($categorySource->{$communities_image_link}[0]->url)
                                        && !empty($categorySource->{$communities_image_link}[0]->url)
                                    ) ? $categorySource->{$communities_image_link}[0]->url : '',
                    'description' => (
                                        isset($categorySource->{$communities_description})
                                        && !empty($categorySource->{$communities_description})
                                    ) ? $categorySource->{$communities_description} : '',
                    'status'      => ($categorySource->status === 'publish') ? 'approved' : 'pending'
                ]);

                $existing_categories[] = $categorySource->_id;
            } else {
                SocialRoom::create([
                    'source_category_id'     => $categorySource->_id,
                    'lt_social_room_type_id' => 3,
                    'is_community'           => 1,
                    'created_by_id'          => 'SYSTEM',
                    'name'                   => $categorySource->name,
                    'image_link'             => (
                                                    isset($categorySource->{$communities_image_link}[0]->url)
                                                    && !empty($categorySource->{$communities_image_link}[0]->url)
                                                ) ? $categorySource->{$communities_image_link}[0]->url : '',
                    'description'            => (
                                                    isset($categorySource->{$communities_description})
                                                    && !empty($categorySource->{$communities_description})
                                                ) ? $categorySource->{$communities_description} : '',
                    'room_code'              => $categorySource->_id,
                    'published_at'           => date("Y-m-d H:i:s"),
                    'published_by'           => 'SYSTEM',
                    'status'                 => ($categorySource->status === 'publish') ? 'approved' : 'pending'
                ]);

                $existing_categories[] = $categorySource->_id;
            }
        }

        // Delete all community not existing on CMS all null
        SocialRoom::where('is_community', 1)
            ->where('lt_social_room_type_id', 3)
            ->whereNull('source_category_id')
            ->delete();

        SocialRoom::where('is_community', 1)
            ->where('lt_social_room_type_id', 3)
            ->whereNotIn('source_category_id', $existing_categories)
            ->delete();
    }
}
