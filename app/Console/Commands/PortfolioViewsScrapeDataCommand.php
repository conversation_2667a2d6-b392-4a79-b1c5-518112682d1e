<?php

namespace App\Console\Commands;

use App\Jobs\PortfolioViews\Scraper;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;

class PortfolioViewsScrapeDataCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'portfolioviews:scrape';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Scrape current data for Portfolio Views.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // Only initialize scraper and its generators
        // when command is ran/needed
        $handler = App::make(Scraper::class);
        $handler->run();
    }

    /**
     * Get the console command arguments.
     *
     * @return array
     */
    protected function getArguments()
    {
        return [];
    }

    /**
     * Get the console command options.
     *
     * @return array
     */
    protected function getOptions()
    {
        return [];
    }
}
