<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use App\Models\Mailqueue;
use App\Models\LibertyUser;
use App\Models\ScheduleMeta;
use App\Models\Survey;
use App\Models\ExternalSurveyor;
use App\Models\RiskImprovementFormySubmissions;
use App\Models\ExternalSurveyCompany;
use Illuminate\Support\Facades\Config;
class ReOverdueReminders extends Command
{
    protected $queue;

    protected $email_subject = 'RE Overdue Reminder';

    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'riskreduce:send-reoverdue-reminder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Risk Reduce Send RE Overdue Reminders';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        $this->queue = new Mailqueue();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $rc = LibertyUser::where('email', 'LIKE', '<EMAIL>')->first();
        // $rc = LibertyUser::where('email', 'LIKE', '<PERSON><PERSON><EMAIL>')->first();

        $overdues = [];

        $schedule_meta = ScheduleMeta::where('key', '=', 'client_survey_report_deadline')
            ->where('value', '<', Carbon::now()->toDateTimeString(), 'AND')
            ->get();

        $schedule_ids = [];

        foreach ($schedule_meta as $schedule) {
            array_push($schedule_ids, $schedule->schedule_id);
        }

        $survey_ids = ScheduleMeta::where('key', '=', 'survey_id')->whereIn('schedule_id', $schedule_ids, 'AND')->get();

        $survey_id_array = [];

        foreach ($survey_ids as $survey_id) {
            array_push($survey_id_array, $survey_id->value);
        }

        foreach ($survey_id_array as $survey_id) {
            $submission = RiskImprovementFormySubmissions::whereIn('survey_id', [(int) $survey_id, (string) $survey_id])->first();

            if (!$submission) {
                continue;
            }

            if (isset($submission->csr_status) && $submission->csr_status == 'submitted') {
                //
            } else {
                $survey = Survey::where('id', $survey_id)->first();

                if (!$survey) {
                    continue;
                }

                $dates = ScheduleMeta::where('id', $survey->schedule_id)->first();

                $surveyor         = ExternalSurveyor::find($survey->surveyor_id);
                $surveyor_company = ExternalSurveyCompany::find($surveyor->external_survey_company_id);

                array_push($overdues, [
                    'survey_id'           => $submission->survey_id,
                    'csr_submission_date' => $submission->csr_submission_date,
                    'dates'               => $dates,
                    'surveyor'            => $surveyor,
                    'surveyor_company'    => $surveyor_company,
                    'link'                => Config::get('app.admin_frontend') . '/surveys/' . $submission->survey_id,
                ]);
            }
        }

        // return View::make('emails.surveys.re_review_overdue', [
        //     'rc'       => $rc,
        //     'overdues' => $overdues,
        // ]);

        $this->queue->queue($rc->email, $rc->fullName(), 'Risk Reduce - Surveys Reminder', 'emails.surveys.surveys_overdue', [
            'rc'   => $rc,
            'overdues' => $overdues,
        ]);
    }

    /**
     * Get the console command arguments.
     *
     * @return array
     */
    protected function getArguments()
    {
        return [
            //
        ];
    }

    /**
     * Get the console command options.
     *
     * @return array
     */
    protected function getOptions()
    {
        return [
            //
        ];
    }
}
