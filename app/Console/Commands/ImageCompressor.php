<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Laravel\Facades\Image;

class ImageCompressor extends Command
{
    const MAX_WIDTH_PX = 1240;
    const MAX_HEIGHT_PX = 1754;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'image:compress {path}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Compress an image base on the path provided';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Track memory usage at the start
        $startMemory = memory_get_usage();

        // Get CPU usage at the start (user and system time)
        $startCpu = getrusage();

        try {
            $path = $this->argument('path');
            if (!storage_path($path)) {
                $this->error('File not found.');
                return Command::FAILURE;
            }

            $file = storage_path($path);
            $size = File::size($file);
            $calcSize = $size / 1024 / 1024; // Convert into MB
            if ($calcSize > 1) { // If greater than 1MB process the image
                $imgInfo   = getimagesize($file);
                $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                $width     = $imgInfo[0];
                $height    = $imgInfo[1];
                $filename  = pathinfo($file, PATHINFO_FILENAME);
    
                $image = Image::read($file);
                if ($width > self::MAX_WIDTH_PX) {
                    $image->scaleDown(width: self::MAX_WIDTH_PX);
                }
    
                if ($height > self::MAX_HEIGHT_PX) {
                    $image->scaleDown(height: self::MAX_WIDTH_PX);
                }
    
                $compressFile = 'compreess-' . $filename;
                if ($extension === 'png') {
                    $image->toPng(70)->save(storage_path('app/' . $compressFile . '.png'));
                    $this->info('DONE PNG COMPRESSION');
                } elseif ($extension === 'jpg' || $extension === 'jpeg') {
                    $image->toJpeg(70)->save(storage_path('app/' . $compressFile . '.jpg'));
                    $this->info('DONE JPG COMPRESSION');
                } else {
                    $this->error('FILE TYPE NOT SUPPORTED!');
                    return Command::FAILURE;
                }
            }

            // Get CPU usage at the end
            $endCpu = getrusage();

            // Track memory usage at the end
            $endMemory = memory_get_usage();

            // Get peak memory usage during execution
            $peakMemory = memory_get_peak_usage();

                    // CPU usage calculation (in microseconds)
            $userCpuTime = ($endCpu["ru_utime.tv_sec"] * 1e6 + $endCpu["ru_utime.tv_usec"]) - 
                ($startCpu["ru_utime.tv_sec"] * 1e6 + $startCpu["ru_utime.tv_usec"]);
        
            $systemCpuTime = ($endCpu["ru_stime.tv_sec"] * 1e6 + $endCpu["ru_stime.tv_usec"]) - 
                ($startCpu["ru_stime.tv_sec"] * 1e6 + $startCpu["ru_stime.tv_usec"]);
            

            $this->info("Memory used: " . ($endMemory - $startMemory) / (1024 * 1024) . " MB");
            $this->info("Peak memory usage: " . $peakMemory / (1024 * 1024) . " MB");

            $this->info("User CPU time: " . ($userCpuTime / 1e6) . " seconds");
            $this->info("System CPU time: " . ($systemCpuTime / 1e6) . " seconds");

            return Command::SUCCESS;
         } catch (\Exception $e) {
            \Log::info('[Image Compress]> Error: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
