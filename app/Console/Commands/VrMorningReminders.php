<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\LetsTalk\ResourceBooking;
use App\Models\LetsTalk\VideoCallParticipant;
use App\Models\Mailqueue;
use DateTime;
use DateTimeZone;
class VrMorningReminders extends Command {
	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'virtual-rooms:morning-reminder';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'CRON that executes every 7:00 am Europe/London time reminding of a todays meeting.';

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
		$this->mail = new Mailqueue();
	}

	public function get_timezone_offset($remote_tz, $origin_tz = null) {
        if($origin_tz === null) {
            if(!is_string($origin_tz = date_default_timezone_get())) {
                return false; // A UTC timestamp was returned -- bail out!
            }
        }
        $origin_dtz = new DateTimeZone($origin_tz);
        $remote_dtz = new DateTimeZone($remote_tz);
        $origin_dt = new DateTime("now", $origin_dtz);
        $remote_dt = new DateTime("now", $remote_dtz);
        $offset = $origin_dtz->getOffset($origin_dt) - $remote_dtz->getOffset($remote_dt);
        
        $hours = floor($offset / 3600);
        $mins = floor($offset / 60 % 60);
        $utcOffset = sprintf('%02d:%02d', $hours, $mins);
        return strpos($utcOffset, '-') !== false ? $utcOffset : "+".$utcOffset; 
    }

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{
		$bookings = ResourceBooking::with('participants')->with('room')
			->where('from', '>', date('Y-m-d 00:00:00'))
			->where('to', '<', date('Y-m-d 23:59:59'))
			->where('is_call_now', 0)
			->get();

		foreach ($bookings as $booking)
		{
			$client = "";
			$company = "";
			$email = "";
			$client_phone = "";
			$meeting_subject = "";

			$business = (isset($booking->room) && isset($booking->room->business)) ? $booking->room->business : 'lsm';
			// Collect all data required
			$guests = [];
			if(isset($liberty_representative)) {
				unset($liberty_representative);
			}
			foreach ($booking->participants as $participant) 
			{	
				if ($participant->role=='client') 
				{
					$client = $participant->user_name;
					$company = $participant->company;
					$email = $participant->email;
					$client_phone = $participant->mobile_number;
					$meeting_subject = $participant->meeting_subject;
                }
                if(empty($meeting_subject) && !empty($participant->meeting_subject))  {
                    $meeting_subject = $participant->meeting_subject;
                }
				if ($participant->role=='liberty_representative') 
				{
					// get the main liberty_representative
					$mainLibertyRepresentative = VideoCallParticipant::where('role', 'liberty_representative')
						->where('room_id', $participant->room_id)
						->where('notes', '!=', "invited from either invite page(laravel) or (react)")
						->first();
					if($mainLibertyRepresentative) {
						$liberty_representative = $mainLibertyRepresentative->user_name;
						$liberty_representative_email = $mainLibertyRepresentative->email;
						$liberty_representative_job_title = $mainLibertyRepresentative->representative_job_title;
					}
				}
				if ($participant->role=='guest_clients') 
				{
					$guests['guest_client'][] = $participant->user_name;
					$guests['guest_emails'][] = $participant->email;
					$guests['guest_clientPhones'][] = $participant->mobile_number;
				}
			}
			// Submit to queue
			foreach ($booking->participants as $participant) 
			{
				if (isset($liberty_representative) && $participant->role!='receptionist') {
					$clientsLink = VideoCallParticipant::generateLink($participant->user_code);
					$twilio_data = $clientsLink;
                    $clientsLink = parse_url($clientsLink);
                	$clientsLink = $clientsLink['scheme']."://".$clientsLink['host']."\r\n\t".$clientsLink['path'];
					$data = [
						'booking_type' => 'future',
						'client' => $client,
						'company' => $company,
						'email' => $email,
						'client_phone' => $client_phone,
						'schedule' => $booking->from.' to '.$booking->to,
						'time_start' => date("H:i", strtotime($booking->from)),
						'meeting_subject' => $meeting_subject,
						'invited_liberty_representative' => (isset($participant->notes) && !empty($participant->notes)) ? $participant->user_name : "",
						'liberty_representative' => $liberty_representative,
						'liberty_representative_email' => $liberty_representative_email,
						'liberty_representative_job_title' => $liberty_representative_job_title 
					];
					$getAttachedFiles = ResourceBooking::with('bookingattachedfiles')->where('room_id', $participant->room_id)->first();
					if($getAttachedFiles && !empty($getAttachedFiles)){
						// ${getAttachedFiles[i].uuid}/downloads3link`);
						//     link.attr("title", getAttachedFiles[i].filename);
						$attached_files = [];
						if(isset($getAttachedFiles['bookingattachedfiles']) && !empty($getAttachedFiles['bookingattachedfiles'])){
							$attached_files = $getAttachedFiles['bookingattachedfiles'];
							$get_proper_object = [];
							foreach($attached_files as $file){
									array_push($get_proper_object, [$file['filename'] => $file['uuid']]);
							}
							$data['attached_files'] = $get_proper_object;
						}
					}

					$otherRepresentatives = VideoCallParticipant::where('room_id', $participant->room_id)
						->where('role', 'liberty_representative')
						->get();

					$viewParameters = [
						'to_name' => $participant->user_name,
						'video_call_details' => $data,
						'twilio_data' => $twilio_data,
						'role' => $participant->role,
						'is_reminder' => true,
						'is_reminder_before_5' => false,
						'is_added_participant' => false,
						'guests' => $guests,
						'user_code' => ($participant->role=='liberty_representative') ? VideoCallParticipant::inviteLink($participant->user_code) : "",
						'booked_timezone' => $participant->booked_timezone,
                        'booked_timezone_offset' => $participant->booked_timezone_offset,
						'booked_timezone_from' => $participant->booked_timezone_from,
						'booked_timezone_to' => $participant->booked_timezone_to,
						'representative_time_zone' => ($participant->role=='liberty_representative') ? $participant->details['office_timezone'] : '',
						'representative_time_zone_offset' => ($participant->role=='liberty_representative') ? $this->get_timezone_offset('UTC', $participant->details['office_timezone']) : '',
						'representative_time_zone_from' => ($participant->role=='liberty_representative') ? \Carbon\Carbon::parse($participant->booked_timezone_from, $participant->booked_timezone)->setTimezone($participant->details['office_timezone']) : '',
						'other_representatives' => (count($otherRepresentatives)>0) ? $otherRepresentatives : '',
					];
					$datetime_start = date("Ymd\THis", strtotime($booking->from));
					$datetime_end = date("Ymd\THis", strtotime($booking->to));
					if ($participant->role=='client') {
						//$this->info('Client: processed');
						$yourSalutation = true;
						$viewParameters['title'] = 'Your meeting is today';
						$fullDescription = "DESCRIPTION;LANGUAGE=en-US:".$data['meeting_subject']." Link: ".$clientsLink;
                        $chunks = str_split($fullDescription, 75);
                        $description = implode("\r\n\t", $chunks);						
						$attachment = null;
					}
					if ($participant->role=='liberty_representative') {
						//$this->info('Liberty Representative: processed');
						$yourSalutation = false;
						$viewParameters['title'] = 'You have a meeting today';
						$message = VideoCallParticipant::generateMessageN12($participant->user_code, $client, $company);
						$fullDescription = "DESCRIPTION;LANGUAGE=en-US:".$data['meeting_subject']." Link: ".$clientsLink;
                        $chunks = str_split($fullDescription, 75);
                        $description = implode("\r\n\t", $chunks);						
						$attachment = null;
					}
					if ($participant->role=='guest_clients') {
						//$this->info('Guest Client: processed');
						$yourSalutation = true;
						$viewParameters['title'] = 'Your meeting is today';
						$viewParameters['yourSalutation'] = true;
						$message = VideoCallParticipant::generateMessageN12($participant->user_code, $client, $company);
						$fullDescription = "DESCRIPTION;LANGUAGE=en-US:".$data['meeting_subject']." Link: ".$clientsLink;
                        $chunks = str_split($fullDescription, 75);
                        $description = implode("\r\n\t", $chunks);						
						$attachment = null;
					}
					$viewParameters['business'] = $business;
					// Check to whom it will be sent
					// If representative use details to check timezone
					$sendFrom = "07:00:00";
					$sendTo = "07:05:00";
					if ($participant->role=='liberty_representative') {					
						$representativeCurrentTime = 
							\Carbon\Carbon::parse(\Carbon\Carbon::now($participant->details['office_timezone']))
							->setTimezone('Europe/London');
						
						$representativeMeetingStart = 
							\Carbon\Carbon::parse($booking->from, 'Europe/London')
							->setTimezone($participant->details['office_timezone']);	

							$this->info($participant->user_name);
							$this->info($representativeCurrentTime);
							$this->info("MEETING START UK TIME AT:". \Carbon\Carbon::parse($booking->from));
							$this->info("MEETING START AT Converted TZ:". $representativeMeetingStart);

						if (
							strtotime($representativeCurrentTime) >= strtotime(date("Y-m-d ".$sendFrom, strtotime($representativeCurrentTime))) &&
							strtotime($representativeCurrentTime) <= strtotime(date("Y-m-d ".$sendTo, strtotime($representativeCurrentTime))) &&
							strtotime($representativeMeetingStart) >= strtotime(date("Y-m-d ".$sendFrom, strtotime($representativeCurrentTime)))
						) {
							$this->info('------------------');
							$this->info($participant->user_name);
							$this->info($representativeCurrentTime);
							$this->info('go ahead an send');
							$this->info('------------------');
							$this->mail->queue(
								$participant->email, $participant->user_name, 
								'Today - '.(($yourSalutation) ? 'Your' : '').' Liberty Virtual Rooms', 
								($business == 'lmre') ? 'emails.lets-talk-lmre.notification' : 'emails.lets-talk.notification',
								$viewParameters,
								$attachment
							);
						}	
					}
					if ($participant->role=='guest_clients' || $participant->role=='client' ) {					
						$representativeCurrentTime = 
							\Carbon\Carbon::parse(\Carbon\Carbon::now($participant->booked_timezone))
							->setTimezone('Europe/London');

						$representativeMeetingStart = 
							\Carbon\Carbon::parse($booking->from, 'Europe/London')
							->setTimezone($participant->booked_timezone);

							$this->info($participant->user_name);
							$this->info($representativeCurrentTime);
							$this->info("MEETING START UK TIME AT:". \Carbon\Carbon::parse($booking->from));
							$this->info("MEETING START AT Converted TZ:". $representativeMeetingStart);

						if (
							strtotime($representativeCurrentTime) >= strtotime(date("Y-m-d ".$sendFrom, strtotime($representativeCurrentTime))) &&
							strtotime($representativeCurrentTime) <= strtotime(date("Y-m-d ".$sendTo, strtotime($representativeCurrentTime))) &&
							strtotime($representativeMeetingStart) >= strtotime(date("Y-m-d ".$sendFrom, strtotime($representativeCurrentTime)))
						) {
							$this->info('------------------');
							$this->info($participant->user_name);
							$this->info($representativeCurrentTime);
							$this->info('go ahead an send');
							$this->info('------------------');
							$this->mail->queue(
								$participant->email, $participant->user_name, 
								'Today - '.(($yourSalutation) ? 'Your' : '').' Liberty Virtual Rooms', 
								($business == 'lmre') ? 'emails.lets-talk-lmre.notification' : 'emails.lets-talk.notification',
								$viewParameters,
								$attachment
							);
						}	
					}
				}
			}
			$client = '';
			$company = '';
			$email = '';
			$client_phone = '';
			$meeting_subject = '';
			$liberty_representative = '';
			$liberty_representative_email = '';
			$liberty_representative_job_title = '';
		}
	}

	/**
	 * Get the console command arguments.
	 *
	 * @return array
	 */
	protected function getArguments()
	{
		return array(
			//array('example', InputArgument::REQUIRED, 'An example argument.'),
		);
	}

	/**
	 * Get the console command options.
	 *
	 * @return array
	 */
	protected function getOptions()
	{
		return array(
			//array('example', null, InputOption::VALUE_OPTIONAL, 'An example option.', null),
		);
	}
}
