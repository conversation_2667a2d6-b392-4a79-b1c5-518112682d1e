<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Survey;
use App\Models\RiskImprovementFormySubmissions;
use App\Models\RiskImprovementFormy;
use App\Models\RiskGrading;
class SyncRiskGradingToLocationsCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'risk-grading:sync-grading-to-locations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command syncs risk gradings to a new risk gradings table';


    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // get all surveys with attached locations
        $surveys = Survey::with('location')->get();
        $this->info("Initializing data...");
        $ctr = 0;
        foreach ($surveys as $survey)
        {
            $survey_m = RiskImprovementFormySubmissions::where('survey_id', (string) $survey->id)->first();

            if (
                (isset($survey_m->_id) && !empty($survey_m->_id))
            ) {
                $form_submissions = RiskImprovementFormySubmissions::find($survey_m->_id);
                $this->info("Importing from FORM: ". $form_submissions->form_id);
                $ctr++;
                // items to be imported
                $data = RiskImprovementFormy::find($form_submissions->form_id);

                // ensures only with data will be imported
                if ($data)
                {
                    $form = json_decode($data, true);

                    $file_data = [];
                    $rr_data = [];
                    $grading_data = [];

                    if (isset($form) && isset($form['fields']))
                    {
                        $file_data_in = [];
                        foreach ($form['fields'] as $field_types)
                        {
                            foreach ($field_types as $field_type => $field_attr)
                            {
                                $file_data_in_name = 'no-name';
                                $file_data_in = [];

                                if ($field_type == 'select_risk_control')
                                {
                                    $arr = [];

                                    foreach ($field_attr as $element)
                                    {
                                        if ($element['name'] == 'name') {
                                            $arr['name'] = $element['value'];
                                        }
                                        if ($element['name'] == 'label') {
                                            $arr['label'] = $element['value'];
                                        }
                                    }
                                    array_push($grading_data, $arr);
                                }
                            }
                        }
                    }

                    if (isset($grading_data) && !empty($grading_data) && $survey->location)
                    {
                        foreach ($grading_data as $grading)
                        {
                            $import_data['location_id'] = $survey->location->location_id;
                            $import_data['attribute'] = $grading['label'];
                            $import_data['value'] = $form_submissions->{$grading['name']};
                            $import_data['organisation_location_id'] = $survey->location->id;
                            $import_data['policy_id'] = $survey->policy_id;

                            $this->store((array) $import_data);
                        }
                    }
                }
            }
        }

        $location_count = RiskGrading::all()->groupBy('organisation_location_id')->count();
        $this->info("Importing done... ");
        $this->info("Processed ".$ctr." items (grades).");
        $this->info("Generated ".$location_count." locations.");
    }

    private function store($import_data)
    {
        $risk_grading = RiskGrading::firstOrCreate([
            'organisation_location_id' => $import_data['organisation_location_id'],
            'location_id' => $import_data['location_id'],
            'attribute' => $import_data['attribute'],
        ]);

        $risk_grading->value = $import_data['value'];
        $risk_grading->policy_id = $import_data['policy_id'];
        $risk_grading->save();
    }

    /**
     * Get the console command arguments.
     *
     * @return array
     */
    protected function getArguments()
    {
        return array(
            //array('example', InputArgument::REQUIRED, 'An example argument.'),
        );
    }

    /**
     * Get the console command options.
     *
     * @return array
     */
    protected function getOptions()
    {
        return array(
            //array('example', InputArgument::REQUIRED, 'An example argument.'),
        );
    }
}
