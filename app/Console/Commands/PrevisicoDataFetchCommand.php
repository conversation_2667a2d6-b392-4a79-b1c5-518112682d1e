<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\Previsico\Previsico;
use App\Models\Previsico\PrevisicoAsset;
use Illuminate\Console\Command;
use App\Models\Previsico\LatestAlertsLog;
use App\Models\Previsico\PrevisicoAlert;
use App\Models\Previsico\PrevisicoUiNotification;
use App\Models\FileUpload;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Aws\Laravel\AwsFacade as AWS;
use Illuminate\Support\Facades\Config;
use Aloha\Twilio\Twilio;
use App\Models\Mailqueue;
use App\Models\Log as AlertLog;
use Exception;

class PrevisicoDataFetchCommand extends Command 
{
    private $twilio;
	const ALERT_RECIPIENTS = ['+447576904915', '+639225199848']; //ERWIN MARIA WARREN , '+34640684561'
    const ALERT_MESSAGE_FAILED = '[UAT] PREVISICO DATA FETCH: FAILED - An issue with data fetch has occurred, and our engineers will be addressing it.';
    const ALERT_MESSAGE_SUCCESS = '[UAT] PREVISICO DATA FETCH: SUCCESS - The data fetch has been successful.';
    const S3_KEY = "previsico/alerts/%s";
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'previsico:data-fetch';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command reads and translates previsico data to readable information.';

    /**
     * @var \App\Models\FileUpload $uploadService
     */
    protected $uploadService;

    /**
     * @var string
     */
    protected $csvFilename;

    /**
     * Mailer class
     *
     * @return Mailqueue
     */
    protected $mailqueue;

    /**
     * @var string
     */
    protected $csvS3Key;

    public function __construct()
    {
        parent::__construct();
        $this->uploadService = new FileUpload();
        $twilioConfig = Config::get('app.twilio.sms');        
    	$this->twilio = new Twilio($twilioConfig['sid'], $twilioConfig['token'], $twilioConfig['from']);
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // $csvLink = $this->fetchData();

        // if (empty($csvLink)) {
        //     $this->error('CSV link not found.');
        //     Log::info('Unable to get CSV link from Previsico.');
        //     return false;
        // }

        // if (
        //     isset($csvLink)
        //     && !empty($csvLink)
        //     && $this->isNewFile($csvLink)
        // ) {
            $this->resetAlertNotifications();
            //$this->processData($this->uploadToS3($csvLink));

            // $this->csvS3Key = sprintf(self::S3_KEY, 'previsico uat asset_jan2024.csv');
            $this->csvS3Key = 'previsico uat asset_jan2024.csv';
            $this->processData('https://liberty-rr-a2be-risk-reduce-stg.s3.eu-west-1.amazonaws.com/previsico%20uat%20asset_jan2024.csv');
        //}
    }

    /**
     * Get the console command arguments.
     *
     * @return array
     */
    protected function getArguments()
    {
        return array(
            //array('example', InputArgument::REQUIRED, 'An example argument.'),
        );
    }

    /**
     * Get the console command options.
     *
     * @return array
     */
    protected function getOptions()
    {
        return array(
            //array('example', InputArgument::REQUIRED, 'An example argument.'),
        );
    }

    /**
     * Init mailqueue class
     *
     * @return void
     */
    private function setMailqueue()
    {
        $this->mailqueue = new Mailqueue();
    }

    /**
     * Checks if given s3 link is new
     * by comparing the filename
     * and save the new file for processing
     *
     * @param string $csvLink
     * @return boolean
     */
    private function isNewFile($csvLink)
    {
        try {
            // Make sure we set what we need for the following ops
            // We do this here so that we don't do
            // unnecessarry processing beforehand
            // if the csvlink is empty.
            $this->setFilename($csvLink);
            $this->setS3Key($csvLink);

            if (!$this->uploadService->exists($this->csvS3Key)) {
                $this->info("New assets alert file received.");
                return true;
            }

            $this->info("Existing asset alert received. Terminating.");
            return false;
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }

        return false;
    }

    /**
     * Get filename from csv s3 link
     *
     * @param string $csvLink
     * @return string
     */
    private function setFilename($csvLink)
    {
        $this->csvFilename = current(explode("?", explode("/", $csvLink)[8]));
    }

    /**
     * Convert csv link to s3 key
     *
     * @param string $csvLink
     * @return string
     */
    private function setS3Key($csvLink)
    {
        // Make sure filename is set before proceeding
        // since filename is a dependency
        if (empty($this->csvFilename)) {
            $this->setFilename($csvLink);
        }

        $this->csvS3Key = sprintf(self::S3_KEY, $this->csvFilename);
    }

    /**
     * Upload csv from previsico to libery s3
     *
     * @param string $csvLink
     * @return string
     */
    private function uploadToS3($csvLink)
    {
        // Make sure s3 key is set before proceeding
        // sinde s3 key is a dependency
        if (empty($this->csvS3Key)) {
            $this->setS3Key($csvLink);
        }

        // We need to make sure the temp directory
        // where we will temporarily store the csv file
        // is available for writing
        if (!file_exists(storage_path(sprintf(self::S3_KEY, '')))) {
            mkdir(storage_path(sprintf(self::S3_KEY, '')), 0755, true);
        }

        // Create csv file
        if ($this->uploadService->copyToS3($csvLink, $this->csvS3Key)) {
            return $this->uploadService->link($this->csvS3Key, '6 hours');
        }

        throw new Exception('Uploading to S3 failed. Retry processing.');
    }

    /**
     * Process CSV
     *
     * @param string $csvLink
     * @return void
     */
    private function processData($csvLink)
    {
        $csvFp = null;
        try {
            $bucket = Config::get('app.aws.bucket');

            if (empty($bucket)) {
                throw new Exception('S3 Bucket not set.');
            }

            if (empty($this->csvS3Key)) {
                throw new Exception('S3 Key for CSV file expected.');
            }

            $s3Client = AWS::createClient('s3');

            if (empty($s3Client)) {
                throw new Exception('Cannot get S3 client.');
            }

            $s3Client->registerStreamWrapper();

            $file  = "s3://{$bucket}/{$this->csvS3Key}";
            $csvFp = fopen($file, 'r');
            if ($csvFp === false) {
                throw new Exception('Cannot access CSV file in S3.');
            }

            $headers = fgetcsv($csvFp);

            // $dateFromFile = Carbon::createFromFormat(
            //     'Y-m-d\TH_i_s\Z',
            //     str_ireplace(['Liberty_all_assets_at_risk-', '.csv'], '', $this->csvFilename)
            // )
            // ->format('Y-m-d H:i:s');

            $dateFromFile = "2024-01-02 12:30:45";

            $previsico = new Previsico($headers, $dateFromFile);
            $processedAssets = [];
            while (!feof($csvFp)) {
                $data = fgetcsv($csvFp);

                if (!empty($data)) {
                    $assetId = $previsico->processAssetAlert($data);

                    if (!empty($assetId)) {
                        $processedAssets[] = $assetId;
                    }
                }
            }
            fclose($csvFp);

            // Update unprocessed assets to have alert level 0
            if (!empty($processedAssets)) {
                PrevisicoAsset::whereNotIn('id', $processedAssets)
                    ->update(['alert_level' => 0]);
            }

            $this->info('Alerts processed successfully.');

            $this->createLog('DATA_FETCH', self::ALERT_MESSAGE_SUCCESS, $processedAssets, 'success');
        } catch (Exception $e) {
            $this->createLog('DATA_FETCH', self::ALERT_MESSAGE_FAILED, ['message' => $e->getMessage()], 'failed');
            $this->sendEmailAlert('failed');

            if (
                !empty($csvFp)
                && (get_resource_type($csvFp) == 'file' || get_resource_type($csvFp) == 'stream')
            ) {
                fclose(($csvFp));
            }

            Log::info($e->getMessage());
            throw $e;
        }
    }

    private function fetchData()
    {
        $headers  = [Config::get('app.previsico.key').':'.Config::get('app.previsico.password')];
        $url      = Config::get('app.previsico.endpoint');
        $csvUrl   = null;
        $response = null;

        try {
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_RETURNTRANSFER  => 1,
                CURLOPT_URL             => $url,
                CURLOPT_HTTPHEADER      => $headers
            ]);
            $response = curl_exec($curl);
            curl_close($curl);

            $csvUrl = json_decode($response)->url;
        } catch (\Exception $e) {
            $response = json_encode([
                'error' => $e->getMessage()
            ]);
            Log::error($e->getMessage());
        }

        LatestAlertsLog::create([
            'request' => [
                'headers' => $headers,
                'url'     => $url
            ],
            'response' => $response
        ]);

        return $csvUrl;
    }

    /**
     * Create log, resend email and sms notification
     * 
     * @param string $action
     * @param string $msg
     * @param array|string $data
     * @param string $status
     * 
     * @return void
     */
    private function createLog($action, $msg, $data, $status)
    {
        AlertLog::create([
            'action'     => $action,
            'message'    => $msg,
            'data'       => $data,
            'email_sent' => 0,
            'status'     => $status,
        ]);
    }

    /**
     * Resets all user notification flags to 0
     * and sets all alerts to viewed
     *
     * @return void
     */
    private function resetAlertNotifications()
    {
        PrevisicoAlert::where('is_new', 1)
            ->update(['is_new' => 0]);

        User::where('has_previsico_alert_ui_notification', 1)
            ->update(['has_previsico_alert_ui_notification' => 0]);

        PrevisicoUiNotification::where('has_notification', 1)
            ->update(['has_notification' => 0]);
    }

    /**
     * WARNING
     * Convenience method for DEVELOPMENT use
     *
     * @param string $csvLink
     * @return void
     */
    private function deleteFromS3($csvLink)
    {
        if (empty($this->csvS3Key)) {
            $this->setS3Key($csvLink);
        }

        $this->uploadService->destroy($this->csvS3Key);
    }

    private function sendSMS($alertType)
    {
        $msg = $alertType === 'success' ? self::ALERT_MESSAGE_SUCCESS : self::ALERT_MESSAGE_FAILED;

        foreach(self::ALERT_RECIPIENTS as $recipient){
            $this->twilio->message(
                $recipient,
                $msg,
                [],
            );
        }
    }

    private function sendEmailAlert($alertType)
    {
        if (empty($this->mailqueue)) {
            $this->setMailqueue();
        }

        $subject = $alertType === 'success' ? 'PREVISICO DATA FETCH: SUCCESS' : 'PREVISICO DATA FETCH: FAILED';
        $body    = $alertType === 'success' ? self::ALERT_MESSAGE_SUCCESS : self::ALERT_MESSAGE_FAILED;
        $forceEmailTo = true;

        $this->mailqueue->sendOnDemand(
            config('app.previsico.alert_email_receipient'),
            $subject,
            $body,
            [],
            [],
            $forceEmailTo
        );
    }

    private function shouldSendEmailAndSMSAlert($alertType) {
        $lastLog = AlertLog::where('status', $alertType)
                         ->where('action', 'DATA_FETCH')
                         ->orderBy('created_at', 'desc')->first();
        if (!$lastLog) {
            return true;
        }
    
        $diffInHours = Carbon::now()->diffInHours($lastLog->created_at);
    
        return $diffInHours >= 4;
    }
}