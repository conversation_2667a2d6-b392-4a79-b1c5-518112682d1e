<?php

namespace App\Console\Commands;

use App\Models\OrganisationLocations;
use App\Models\Survey;
use App\Models\RiskRecommendationCards;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SyncLocationForSurveySubmissionCard extends Command
{

    /**
     * The console command name.
     *
     * @var string
     */
    protected $signature = 'riskreduce:sync-location-for-survey-submission-card {organisation_id?}';

    protected $description = 'Sync Location For Survey Submission Card';

    public function handle()
    {
        $organisationId = $this->argument('organisation_id');
        $this->info('Organisation ID: ' . $organisationId);
        $this->syncLocationForSurveySubmissionCard($organisationId);
    }

    private function syncLocationForSurveySubmissionCard($organisationIds = [])
    {
        $this->info('Sync Location For Survey Submission Card');

        $submissionCards = $this->getSubmissionCards($organisationIds);

        // Start a transaction
        $session = DB::connection('mongodb')->getMongoClient()->startSession();
        $session->startTransaction();
        try {
            foreach ($submissionCards as $submissionCard) {
                $this->info('Submission Card: ' . $submissionCard->_id);

                $survey = Survey::find($submissionCard->survey_id);

                $locationId = $survey?->location_id;

                if ($locationId) {
                    $location = OrganisationLocations::find($locationId);

                    if (!$location) continue; // Skip if there is no location

                    $locationName = $location->location_name;
                    $locationPostcode = $location->postcode;
                    $this->info('Location: ' . $locationName . ': ' . $locationPostcode);

                    $cardLocation = $submissionCard->properties?->location ?? null;
                    $this->info('Card Location: ' . $cardLocation);

                    if ($cardLocation == null) {
                        $this->info('Card Location is null');
                    }

                    if (($cardLocation && ($cardLocation->location_name !== $locationName || $cardLocation->postcode !== $locationPostcode)) || $cardLocation == null) {
                        $this->info('Location name and postcode is not the same as the submission card');

                        // Update the submission card. Need to use the mongodb connection to update the submission card with missing location data.
                        DB::connection('mongodb')->collection('ri_submission_cards')
                            ->where('_id', $submissionCard->_id)
                            ->update([
                                '$set' => [
                                    'properties.location.location_name' => $locationName,
                                    'properties.location.postcode' => $locationPostcode
                                ]
                            ]);
                    }
                }
            }

            // Commit transaction
            $session->commitTransaction();
            $this->info('Successfully updated submission cards');
        } catch (\Exception $e) {
            // Rollback transaction
            $session->abortTransaction();
            // Log the error
            Log::error('Error syncing location for submission card: ' . $e->getMessage());
        }
    }

    private function getSubmissionCards($id = null)
    {
        if ($id !== null) {
            return RiskRecommendationCards::where('organisation_id', (int)$id)->get();
        }

        return RiskRecommendationCards::all();
    }
}
