<?php

namespace App\Console\Commands;

use App\Models\AccessGroup\AccessGroupOrganisation;
use Illuminate\Console\Command;
use App\Models\User;
use App\Services\AccessGroup\CreateUserService;

class AccessGroupCreateUsersCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'access-group:create-users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "This command will get all the users that has safetymedia_access=1 and has not been mapped in ag_persons table.";

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $query = User::where('safetymedia_access', 1)
            ->where('activated', 1)
            ->where('client_dashboard_access', 1)
            ->where('email', 'like', '%_@__%.__%')
            ->doesntHave('accessGroupPerson')
            ->with('organisation')
            ->whereHas('organisation.policies', function ($query) {
                $query->whereNull('deleted_at')
                    ->whereRaw('DATEDIFF(expiry_date_of_cover, CURDATE()-INTERVAL 30 DAY) <=0');
            });

        if(config('app.env') !== 'prod') {
            $ffLmsOrgId = '0fa05e2e-5089-428e-9cfd-afac00d7aed9';
            $accessGroupOrg = AccessGroupOrganisation::where('organisation_id', $ffLmsOrgId)->first();
            $query->where('organisation_id', $accessGroupOrg->rr_organisation_id);
        }

        $users = $query->get();

        foreach ($users as $user) {
            $userService = new CreateUserService($user);
            $userService->create();
        }
    }
}
