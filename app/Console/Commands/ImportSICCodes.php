<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use App\Models\rrAppetite\SicCode;
use App\Models\rrAppetite\Region;
use App\Models\rrAppetite\Product;
use App\Models\rrAppetite\ProductTags;
use App\Models\rrAppetite\Sector;
use App\Models\rrAppetite\Subsector;
use App\Models\rrAppetite\SicCodeSubSectorRelationship;
use App\Models\rrAppetite\SicCodeSectorRelationship;
use App\Models\rrAppetite\SubsectorTags;
use App\Models\rrAppetite\ProductRelationship;
use Illuminate\Support\Str;
use DateTime;

class ImportSICCodes extends Command {

	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'riskreduce:import-sic-codes';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Import SIC Codes and create relation.';

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
	}

	
	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{		
		$this->importSicCodesFromSpreadsheet();		
	}

	function searchForId($name, $array) {
		foreach ($array as $key => $val) {
			if ($val[1] === $name) {
				return $val[0];
			}
		}
		return -1;
	 }

	 function clearLogFile(){
		$date = new DateTime();
		$date=$date->format('Y-m-d H:i:s');
		$myfile = fopen("importlog.txt", "w") or die("Unable to open file!");
		$message='--------------------'.$date.'-----------------';
		fwrite($myfile, $message."\n");
		fclose($myfile);
	 }

	 function LogMessage($invalidMessages) {
		 if(count($invalidMessages)>0) {
			$myfile = fopen("importlog.txt", "a") or die("Unable to open file!");
			foreach($invalidMessages as $message){
				fwrite($myfile, $message."\n");
			}
			fclose($myfile);
        }
	 }

	 private function processRelation($sheetData,$sheetoutput){
		$allIds=[];
		$invalidMessages=[];
		array_push($invalidMessages,'');
		$message="-------------relation logs-----------------";
		array_push($invalidMessages,$message);
		array_push($invalidMessages,'');
		foreach($sheetData as $data){
			$ids=[];
			for($i=1; $i<=4 ; $i++){ 
				$message="";
				//in order-> Region, Sector, Subsector, Product	
				$id=0;					
				if(isset($data[$i])){
					$id=$this->searchForId($data[$i],$sheetoutput[$i-1]);
				}
				if($id>0){
					array_push($ids,$id);
				}

				if ($i == 1) {
					$field = 'Region';
				} elseif ($i === 2) {
					$field = 'Sector';
				} elseif ($i == 3) {
					$field = 'Subsector';
				} else {
					$field = 'Product';
				}

				if($id==-1){
					$message="Error : ".$field." ".$data[$i]." does not exists.";
				}
				if($id==0){
					$message="Error : ".$field." ".$data[$i]." cannot be blank.";
				}
				if(strlen($message)>0){
					array_push($invalidMessages,$message);
				}
			}	

			if(count($ids)===4){
				array_push($allIds,$ids);
			}
		}

		$relationArray=[];
		foreach($allIds as $id){
			$relation=array('region_id'=>$id[0],'sector_id'=>$id[1],'subsector_id'=>$id[2],'product_id'=>$id[3]);
			array_push($relationArray,$relation);
		}				
		ProductRelationship::truncate();
		ProductRelationship::insert($relationArray);
		$this->LogMessage($invalidMessages);
	}

	private function CreateSICSectorsRelation($sheetname,$sheetoutput){		
		$invalidMessages=[];
		$siccodetable=[];
		foreach ($sheetoutput as $codes) {						
			foreach($codes['siccodes'] as $eachcode){
				if ($sheetname==="sector") {
					array_push($siccodetable,array($codes['sector_id'],$eachcode));
				}		
				if ($sheetname==="subsector") {
					array_push($siccodetable,array($codes['sub_sector_id'],$eachcode));
				}
			}
		}	

		$result = array();
		foreach ($siccodetable as $element) {
			$result[$element[1]][] = $element;
		}

		$siccodes=array_keys($result);
		$existing_codes=[];
		
		$siccodetable=SicCode::whereIn('code',$siccodes)->get(['id','code']);
		foreach($siccodetable as $table){	
			array_push($existing_codes,array($table->code,$table->id));
		}

		$siccodes=array_map(function($o) { return $o[0]; }, $existing_codes);
		
		$relation_table=[];	
		array_push($invalidMessages,'');
		$message=$sheetname==="sector"?"-------------sector logs-----------------":"-------------sub sector logs-----------------";
		array_push($invalidMessages,$message);
		array_push($invalidMessages,'');

		foreach($result as $key=>$value)
		{	
			if (in_array($key, $siccodes)) {
				foreach($existing_codes as $k=>$v)
				{
					if($v[0]==$key){
						$siccodeid=$v[1];
						$sector_ids=array_map(function($o) { return $o[0]; }, $value);
						foreach($sector_ids as $id){
							if ($sheetname==="sector") {
								array_push($relation_table,array('sic_code_id'=>$siccodeid,'sector_id'=>$id));
							}
					
							if ($sheetname==="subsector") {
								array_push($relation_table,array('sic_code_id'=>$siccodeid,'subsector_id'=>$id));
							}							
						}
					}
				}
			}	
			else{
				array_push($invalidMessages,'Error : SIC_Code '.$key.' does not exists.');
			}
		}

        if ($sheetname==="sector") {
            SicCodeSectorRelationship::truncate();
            SicCodeSectorRelationship::insert($relation_table);
		}

		if ($sheetname==="subsector") {
            SicCodeSubSectorRelationship::truncate();
            SicCodeSubSectorRelationship::insert($relation_table);
		}	
		$this->LogMessage($invalidMessages);
	}

	private function importSicCodesFromSpreadsheet()
	{
		$this->clearLogFile();
		$reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
		$reader->setReadDataOnly(true);		
		$spreadsheet = $reader->load("data.xlsx");
		$sheets=$spreadsheet->getSheetNames();	
		$counter=0;		
		
		function filterBlank($data){			
			return $data[0]>0 ;//filter id from sheet
		}

		foreach ($sheets as $sheetname)
		{
			$currentsheet = $spreadsheet->getSheet($counter);
			$sheetData = $currentsheet->toArray(null, false, false,false);
			$sheetData=array_filter($sheetData,"filterBlank");

			foreach($sheetData as $data){
				$data=array_map('trim', $data);
			}

			$sheetname=strtolower($sheetname); 
			$sheetoutput=$this->importData($sheetname,$sheetData);

			if(($sheetname=="sector" || $sheetname=="subsector")  && $sheetoutput){
				$this->CreateSICSectorsRelation($sheetname,$sheetoutput);
			}

			if($sheetname=="relation" && $sheetoutput){
				$this->processRelation($sheetData,$sheetoutput);
			}			
			$counter++;
		}				
	}	

	public function getSlug($string)
	{
     return Str::slug($string);
	}

	private function importData($sheetname,$sheetData)
	{	
		switch($sheetname)
		{
			case "sic code":
				SicCode::truncate();
				foreach($sheetData as $data){
					SicCode::firstOrCreate(array('code' => $data[1],'description' => $data[2]));
				}			
				break;
			case "region":			
				Region::truncate();
				foreach($sheetData as $data){					
					Region::firstOrCreate(array('name' => $data[1],'slug' => $this->getSlug($data[1])));
				}
				break;
			case "sector":
				Sector::truncate();
				$sector_codes=[];
				foreach($sheetData as $data){					
					$sector=Sector::firstOrCreate(array('name' => $data[1],'slug' => $this->getSlug($data[1]),'description'=>$data[2]));
					if($data[1]){												
						$siccodes=array_map('trim', explode(',', $data[3]));
						$siccodes=array_unique($siccodes);
						array_push($sector_codes,array('sector_id'=>$sector->id,'siccodes'=>$siccodes));
					}
				}							
				return $sector_codes;
				break;
			case "subsector":				
				Subsector::truncate();
				$subsector_codes=[];
				$subsector_tags=[];
				foreach($sheetData as $data){					
					$subsector=Subsector::firstOrCreate(array('name' => $data[1],'slug' => $this->getSlug($data[1])));
					if($data[1]){												
						$siccodes=array_map('trim', explode(',', $data[3]));
						$siccodes=array_unique($siccodes);
						array_push($subsector_codes,array('sub_sector_id'=>$subsector->id,'siccodes'=>$siccodes));
					}
					if($data[2]){												
						$tags=array_map('trim', explode(',', $data[2]));
						$tags=array_unique($tags);
						foreach($tags as $tag){
							array_push($subsector_tags,array('subsector_id'=>$subsector->id,'tag'=>$tag));
						}						
					}					
				}	
				SubsectorTags::insert($subsector_tags);			
				return $subsector_codes;
				break;
			case "products":			
				Product::truncate();
				$productTags=[];				
				foreach($sheetData as $data){
					$product=Product::firstOrCreate(array('name' => $data[1],'slug' => $this->getSlug($data[1])));									
					if($data[2]){												
						$tags=array_map('trim', explode(',', $data[2]));						
						foreach($tags as $tag){							
							array_push($productTags,array('product_id'=>$product->id,'tag'=>$tag));
						}
					}				
				}				
				ProductTags::truncate();
				ProductTags::insert($productTags);	
				break;
			case "relation":			
				$relation=[];//get unique values
				$relationArray=[];//holds id and name
				for($i=1; $i<=4 ; $i++){
					$relation[$i-1]=array_filter(array_unique(array_column($sheetData, $i)));
				}	
				foreach($relation as $key=>$value){					
					$keyvalueArray=[];
					if($key==0){
						$tablename=Region::whereIn('name',$value)->get(['id','name']);	
					}
					else if($key==1){
						$tablename=Sector::whereIn('name',$value)->get(['id','name']);						
					}
					else if($key==2){
						$tablename=Subsector::whereIn('name',$value)->get(['id','name']);												
					}
					else {
						$tablename=Product::whereIn('name',$value)->get(['id','name']);						
					}					
					foreach($tablename as $table){
						array_push($keyvalueArray,array($table->id,$table->name));
					}
					array_push($relationArray,$keyvalueArray);
				}		
				return($relationArray);
				break;
			default:			
				break;
		}
	}

	/**
	 * Get the console command arguments.
	 *
	 * @return array
	 */
	protected function getArguments()
	{
		return array(

		);
	}

	/**
	 * Get the console command options.
	 *
	 * @return array
	 */
	protected function getOptions()
	{
		return array(
			
		);
	}

}
