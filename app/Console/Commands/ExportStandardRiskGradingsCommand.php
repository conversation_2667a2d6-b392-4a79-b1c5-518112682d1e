<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\Mailqueue;
use App\Models\FileUpload;
use App\Models\LibertyUser;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use App\Models\RGPolicyType;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\StandardRiskGradingExport;
use Illuminate\Support\Facades\File;
use Symfony\Component\Console\Input\InputOption;

class ExportStandardRiskGradingsCommand extends Command
{

    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'standard-riskgrading:export';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Export standard risk gradings';

    /**
     * Color code mapping
     * 
     * @var array
     */
    const COLOR_CODES = [
        'Requires Improvement'                                        => '#fc0d1b',
        'Poor'                                                        => '#fc0d1b',
        'Below Average'                                               => '#fdbf2d',
        'Average'                                                     => '#fffd38',
        'Good'                                                        => '#00b050',
        'Above Average'                                               => '#00b050',
        'Superior'                                                    => '#0070c0',
        'Not Applicable / Not Assessed'                               => '#dddddd',
        'Contact U/W within 24 hours'                                 => '#fc0d1b',
        'Multiple Requirements identified - monthly updates required' => '#fdbf2d',
        'Single Requirement - monitor progress'                       => '#fffd38',
        'Recommendations Only -generally reasonable controls'         => '#00b050',
        'Satisfactory'                                                => '#00b050',
        'Not Applicable'                                              => '#dddddd',
    ];

    /**
     * Mailer class
     *
     * @return \Mailqueue
     */
    protected $mailqueue;
    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $lob              = $this->option('lob');
        $userId           = $this->option('user_id');
        $sheets           = $this->getExcelSheets($lob);
        $gradingData      = $this->getAllGradings($lob);
        $formattedGrading = $this->getFormattedGradings($gradingData, $sheets);
        $this->renderToExcel($formattedGrading, $sheets, $userId);
    }

    private function getAllGradings($lob)
    {
        $policyTypeId = null;
        if ($lob !== 'All') {
            $policyTypeId = RGPolicyType::select('name', 'id')->where('name', $lob)->first();
        }

        $condition = '';
        if ($lob === 'Commercial Combined') {
            $condition = 'WHERE opn.policy_type_id IN (1, 2)';
        }

        if ($lob !== 'All') {
            $condition = 'WHERE opn.policy_type_id = ' . $policyTypeId->id;
        }

        $query = "SELECT
					surveys.id AS survey_id,
					rg_location_gradings.policy_type_id,
					rg_location_gradings.attribute_type,
					rg_location_gradings.attribute,
					rg_location_gradings.value
				FROM
					surveys
					INNER JOIN organisation_policy_numbers AS opn ON opn.id = surveys.policy_id
					INNER JOIN rg_location_gradings ON rg_location_gradings.organisation_location_id = surveys.location_id
						AND rg_location_gradings.policy_type_id = opn.policy_type_id
				$condition
				ORDER BY
					rg_location_gradings.policy_type_id,
					surveys.id";

        return array_map(function ($data) {
            return json_decode(json_encode($data), true);
        }, DB::select($query));
    }

    private function renderToExcel($formattedGrading, $excelSheets, $userId)
    {
        // $formattedGrading[0] is header
        // $formattedGrading[1] is data
        $fileKey   = 'all';
        if (count($excelSheets) === 2 && $this->option('lob') === 'Commercial Combined') {
            $fileKey = 'commercial-combined';
            $excelSheets = [3 => 'Commercial Combined'];
        } elseif (count($excelSheets) === 1) {
            $fileKey = strtolower(array_values($excelSheets)[0]);
        }

        if ($this->option('lob') === 'All') { // Insert Commercial Combined to excelsheets to appear data in All
            $excelSheets[3] = 'Commercial Combined';
        }

        File::ensureDirectoryExists(storage_path('excel'));
        $filename  = 'standard-risk-gradings-' . $fileKey . '-' . Carbon::now()->format('d-m-Y');
        Excel::store(new StandardRiskGradingExport($formattedGrading, $excelSheets), 'excel/' . $filename . '.xlsx', 'storage');

        $path = storage_path('excel/') . $filename . '.xlsx';
        $this->sendMail($userId, $path, $filename);
    }

    private function getExcelSheets($lob)
    {
        if ($lob === 'All') {
            $collection = RGPolicyType::select('name', 'id')
                ->where('slug', '!=', 'commercial combined')
                ->where('slug', '!=', 'do facilities')
                ->get();
            $sheetsArr = $collection->toArray();
            $ids           = Arr::pluck($sheetsArr, 'id');
            $names         = Arr::pluck($sheetsArr, 'name');
            return array_combine($ids, $names);
        } elseif ($lob === 'Commercial Combined') {
            $collection = RGPolicyType::select('name', 'id')
                ->whereIn('id', [1, 2])
                ->get();
            $sheetsArr = $collection->toArray();
            $ids           = Arr::pluck($sheetsArr, 'id');
            $names         = Arr::pluck($sheetsArr, 'name');
            return array_combine($ids, $names);
        } else {
            $collection = RGPolicyType::select('name', 'id')
                ->where('name', $lob)
                ->first()
                ->toArray();
            return [$collection['id'] => $collection['name']];
        }
    }

    private function getFormattedGradings($gradings, $sheets)
    {
        $headers        = [];
        $data           = [];
        $counter        = 1;
        $currentSurvey  = null;
        foreach ($sheets as $id => $sheet) { // Format the headers
            $headers[$sheet] = [''];
            foreach ($gradings as $grading) {
                $srf = 'SRF' . $grading['survey_id'];
                if ($grading['policy_type_id'] === $id) {
                    if (!in_array($srf, $headers[$sheet])) {
                        $headers[$sheet][] = $srf;
                    }
                }
            }
        }

        foreach ($sheets as $id => $sheet) {
            foreach ($gradings as $idx => $grading) {
                $currentSurvey = $grading['survey_id'];
                $gradingName   = $grading['attribute'];

                if ($grading['policy_type_id'] === $id) {
                    if ($grading['attribute_type'] === 'attribute') {
                        $gradingName = strtoupper(str_replace('-', ' ', $gradingName));
                    } else {
                        $gradingName = ucwords(str_replace('-', ' ', $gradingName));
                    }

                    $position = array_search('SRF' . $currentSurvey, $headers[$sheet]);
                    if (isset($data[$sheet]) && count($data[$sheet]) > 0) {
                        foreach ($data[$sheet] as $index => $dataSet) {
                            if ($dataSet[0] === $gradingName) { // 0 -> to target first item of the array which is the name of category
                                $counter = $index;
                                break;
                            }
                        }
                    }

                    if (empty($data[$sheet][$counter])) {
                        $data[$sheet][$counter][] = $gradingName;
                        $data[$sheet][$counter][] = array_key_exists($grading['value'], self::COLOR_CODES) ? self::COLOR_CODES[$grading['value']] : '';

                        // Fill all the way to the last of survey_id listed
                        for ($i = 0; $i < count($headers[$sheet]) - 2; $i++) {
                            $data[$sheet][$counter][] = self::COLOR_CODES['Not Applicable / Not Assessed'];
                        }
                    } else {
                        $data[$sheet][$counter][$position] = array_key_exists($grading['value'], self::COLOR_CODES) ? self::COLOR_CODES[$grading['value']] : '';
                    }

                    $counter++;

                    if (isset($gradings[$idx + 1]) && $gradings[$idx + 1]['survey_id'] !== $currentSurvey) {
                        $counter = 1;
                    }
                }
            }
        }

        if ($this->option('lob') === 'All') {
            $data['Commercial Combined']     = $data['Property'] + $data['Casualty'];
            $headers['Commercial Combined'] = $headers['Property'] + $headers['Casualty'];
        }

        // Commercial Combined
        if ($this->option('lob') === 'Commercial Combined') {
            $temp      = ['Commercial Combined' => []];
            $counter = 3;
            foreach ($data as $dataArr) {
                foreach ($dataArr as $singleData) {
                    $temp['Commercial Combined'][$counter++] = $singleData;
                }
            }

            $data = $temp;
            $headers['Commercial Combined'] = $headers['Property'] + $headers['Casualty'];
        }

        return [$headers, $data];
    }

    private function num2alpha($n)
    {
        for ($r = ''; $n >= 0; $n = intval($n / 26) - 1) {
            $r = chr($n % 26 + 0x41) . $r;
        }

        return $r;
    }

    /**
     * Get the console command arguments.
     *
     * @return array
     */
    protected function getArguments()
    {
        // return array(
        // 	array('example', InputArgument::REQUIRED, 'An example argument.'),
        // );
        return [];
    }

    /**
     * Get the console command options.
     *
     * @return array
     */
    protected function getOptions()
    {
        return [
            ['lob', null, InputOption::VALUE_OPTIONAL, 'Line of Business', 'All'],
            ['user_id', null, InputOption::VALUE_OPTIONAL, 'user', '']
        ];
    }

    private function getUser($userId)
    {
        $this->user = LibertyUser::where('id', $userId)->first();
    }

    private function setMailqueue()
    {
        $this->mailqueue = new Mailqueue;
    }

    private function setFileUpload()
    {
        $this->file = new FileUpload();
    }

    private function sendMail($userId, $path, $filename)
    {
        if (empty($this->mailqueue)) {
            $this->setMailqueue();
        }
        if (empty($this->file)) {
            $this->setFileUpload();
        }

        if (empty($this->user)) {
            $this->getUser($userId);
            if (empty($this->user)) {
                return;
            }
        }

        $excelKey = Str::uuid()->toString();
        if ($this->file->upload_excel($path, 'srg/exports/' . $excelKey . '-' . $filename . '.xlsx')) {
            $messageParameters['file'] = $this->file->link('srg/exports/' . $excelKey . '-' . $filename . '.xlsx', '2 days');
        }

        $attachment = null;

        $messageParameters['email']              = $this->user->email;
        $messageParameters['email_subject']      = 'Risk Reduce - Standardised Risk Gradings Excel Export Ready';
        $messageParameters['email_body']         = 'test';
        $messageParameters['name']               = $this->user->first_name . " " . $this->user->last_name;
        $this->mailqueue->queue(
            $this->user->email,
            $messageParameters['name'],
            'Risk Reduce - Standardised Risk Gradings Excel Export Ready',
            'emails.standard-risk-gradings.export',
            $messageParameters,
            $attachment
        );
    }
}
