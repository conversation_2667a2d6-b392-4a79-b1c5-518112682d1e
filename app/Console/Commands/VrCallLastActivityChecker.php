<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\LetsTalk\SocialRoomParticipant;

class VrCallLastActivity<PERSON>hecker extends Command {

	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'virtual-rooms:last-call-activity-checker';	

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'This command checks the last activity of a VR call participant.';


	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{
        // update all inactive 
        $from = \Carbon\Carbon::parse()->subMinutes(30)->format('Y-m-d H:i:s');
        $to = \Carbon\Carbon::parse()->subMinutes(1)->format('Y-m-d H:i:s');

        $participants = SocialRoomParticipant::where('last_activity_at', '>=' , $from)
            ->where('last_activity_at', '<=' , $to)           
            ->orWhere('last_activity_at', null)          
            ->update([
                'left_room_at' => \Carbon\Carbon::parse()->format('Y-m-d H:i:s')
            ]);

        $this->info("Last Call Activity Heartbeat");
       
	}	

	/**
	 * Get the console command arguments.
	 *
	 * @return array
	 */
	protected function getArguments()
	{
		return array(
			//array('example', InputArgument::REQUIRED, 'An example argument.'),
		);
	}

	/**
	 * Get the console command options.
	 *
	 * @return array
	 */
	protected function getOptions()
	{
		return array(
				
		);
	}
}