<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\RiskImprovementFormySubmissions;
use App\Services\RiskGradingService;

class CleanRRGradingsData extends Command {

	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'riskreduce:clean-rr-gradings-data';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Clean submission by updating correct submission status';

	private $gradingService;

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
		$this->gradingService = new RiskGradingService();
	}

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{
		//These are manual commands which can be modified and run manually as required, not from cron.

		//$this->processCSR();
		//$this->processUWR();
		$this->processKanbanTracker();
	}

	private function processCSR(){
		$submissions = RiskImprovementFormySubmissions::where('csr_status','!=', 'submitted')
		->whereNotNull('csr_submission_date')
		->where('csr_submission_date','!=', '')->get();

		$total=count($submissions);

		$this->info("Total csr: {$total}");

		foreach ($submissions as $submission) {
			\Log::info("processing csr for srf: {$submission->survey_id}");
			$this->info("processing csr for srf: {$submission->survey_id}");
			$submission->csr_status = 'submitted';
			$submission->submitted  = 1;
			$submission->save();
		}

		$this->info("Done processing csr for: {$total} srfs");
	}

	private function processUWR(){
		$submissions = RiskImprovementFormySubmissions::where('uwr_status','!=', 'submitted')
		->whereNotNull('uwr_submission_date')
		->where('uwr_submission_date','!=', '')->get();

		$total=count($submissions);

		$this->info("Total uwr: {$total}");

		foreach ($submissions as $submission) {
			\Log::info("processing uwr for srf: {$submission->survey_id}");
			$this->info("processing uwr for srf: {$submission->survey_id}");
			$submission->uwr_status = 'submitted';
			$submission->submitted  = 1;
			$submission->save();
		}

		$this->info("Done processing uwr for: {$total} srfs");
	}

	public function processKanbanTracker($survey_id=null){
		$this->gradingService->processKanbanTracker($survey_id);
	}
}