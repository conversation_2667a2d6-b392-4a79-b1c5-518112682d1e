<?php

namespace App\Console\Commands;

use App\Jobs\PortfolioViews\GenerateInsightsDataJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;

class PortfolioViewsGenerateInsightsCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'portfolioviews:generate-insights';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate insights for all report.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $handler = App::make(GenerateInsightsDataJob::class);
        $handler->run();
    }

    /**
     * Get the console command arguments.
     *
     * @return array
     */
    protected function getArguments()
    {
        return [];
    }

    /**
     * Get the console command options.
     *
     * @return array
     */
    protected function getOptions()
    {
        return [];
    }
}
