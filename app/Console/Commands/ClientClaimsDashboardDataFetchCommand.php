<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ClientClaimsDashboard\ClientClaimTemp;
use App\Models\ClientClaimsDashboard\ClientClaimsApi;
use App\Models\ClientClaimsDashboard\ClientClaimLogs;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\Community\FileUpload;
class ClientClaimsDashboardDataFetchCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'claims-client-dashboard:fetch-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command reads and claims client data.';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $filename = ClientClaimsApi::getFilename();
        if (!$filename) {
            return;
        }
        $content = ClientClaimsApi::getContent($filename);

        if (!isset($content['message'])) {
            $fileDate = str_replace('Liberty_RiskReduce_Enhanced_Export_', '', $filename);
            $fileDate = str_replace('.json', '', $fileDate);
            $fileDate = date('Y-m-d', strtotime($fileDate));

            if (Carbon::parse($fileDate)->isFuture()) {
                return;
            }

            $fileupload = new FileUpload();
            $s3link = $fileupload->uploadFile($filename . '.txt', 'Risk-Reduce/client-claims-dashboard', $content);

            try {
                ClientClaimTemp::truncate();

                $this->store($content, $filename, $fileDate);

                $clientClaimsTable = 'client_claims';
                $clientClaimsCurrentTempTable = 'client_claims_temp_current';
                $clientClaimsTempTable = 'client_claims_temp';

                $sql = 'RENAME TABLE %s TO %s, %s TO %s, %s TO %s';
                DB::statement(sprintf(
                    $sql,
                    $clientClaimsTable,
                    $clientClaimsCurrentTempTable,
                    $clientClaimsTempTable,
                    $clientClaimsTable,
                    $clientClaimsCurrentTempTable,
                    $clientClaimsTempTable));

            } catch (\PDOException $e) {
                ClientClaimLogs::create([
                    'filename'      => $filename,
                    'action'        => ClientClaimLogs::FETCH_ROW_DATA,
                    'message'       => $e->getMessage(),
                    'created_at'    => date('Y-m-d H:i:s', time())
                ]);
            }
        }
    }

    private function store($claimsData, $filename, $fileDate)
    {
        $claimsDataObj = json_decode($claimsData);
        $success = 0;
        $failed = 0;
        foreach($claimsDataObj as $claim) {
            if (empty($claim->claim_number)) {
                $failed++;
                continue;
            }

            $clientClaimRecord = ClientClaimTemp::where('claim_number', $claim->claim_number)->first();
//            if($clientClaimRecord) {
//                $failed++;
//                continue;
//            }

            try {
                if (!is_numeric($claim->claim_lifecycle_in_days) && $claim->claim_lifecycle_in_days === "") {
                    unset($claim->claim_lifecycle_in_days);
                }

                $claim->file_date = $fileDate;

                ClientClaimTemp::create((array) $claim);

                $success++;
            } catch (\Exception $e) {
                $failed++;

                ClientClaimLogs::create([
                    'filename'      => $filename,
                    'action'        => ClientClaimLogs::FETCH_ROW_DATA,
                    'message'       => $e->getMessage(),
                    'created_at'    => date('Y-m-d H:i:s', time())
                ]);
            }
        }

        // Log the summary
        $message = sprintf('Total Success: %d; Total Failed: %d', $success, $failed);
        ClientClaimLogs::create([
            'filename'      => $filename,
            'action'        => ClientClaimLogs::FETCH_ROW_DATA,
            'message'       => $message,
            'created_at'    => date('Y-m-d H:i:s', time())
        ]);
    }
}