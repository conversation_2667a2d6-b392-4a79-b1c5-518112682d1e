<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Console\Input\InputOption;
use letsTalk\SocialRoom;
use letsTalk\PasswordlessToken;
use letsTalk\SocialRoomRedDotNotification;

class VrCreateReminders extends Command {

	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'virtual-rooms:create-community-room';
	// {name} {description} {image_link} {status}

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'This command creates a new room and broadcasts the notfications to the user.';


	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{
		$name = $this->option('name');
		$meeting_name = $this->option('meeting_name');
		$description = $this->option('description');
		$image_link = $this->option('image_link');
		
		$social_room = SocialRoom::create([
			'lt_social_room_type_id' => 3,
			'is_community' => 1,
			'created_by_id' => '5f1ab9fe3123991b8242baa2',
			'name' => $name,
			'meeting_name' => $meeting_name,
			'description' => $description,
			'imaeg_link' => $image_link,
			'room_code' => SocialRoom::roomCodeGenerator(),
			'status' => 'approved',
			'published_at' => date("Y-m-d H:i:s"),
			'published_by' => '5f1ab9fe3123991b8242baa2',
		]);

		if ($social_room) {

			// Store red dot notification
            // Collect all possible members of VR-Socials
            $persons_to_be_notified = PasswordlessToken::groupBy('person_id')->get();

            foreach ($persons_to_be_notified as $value) {
                SocialRoomRedDotNotification::create([
                    'person_id' => $value->person_id,
                    'room_id' => $social_room->id,
                    'room_type_id' =>  $social_room->lt_social_room_type_id,
					'is_visited' => 0,
					'is_community' => 1,
                ]);
            }

			$this->info("Community Room has been created.");
		} else {
			$this->info("Ooops... Something went wrong");
		}
        
	}	

	/**
	 * Get the console command arguments.
	 *
	 * @return array
	 */
	protected function getArguments()
	{
		return array(
			//array('example', InputArgument::REQUIRED, 'An example argument.'),
		);
	}

	/**
	 * Get the console command options.
	 *
	 * @return array
	 */
	protected function getOptions()
	{
		return array(
			array('name', null, InputOption::VALUE_OPTIONAL, 'Name of the room.', null),
			array('description', null, InputOption::VALUE_OPTIONAL, 'Description of the room.', null),
			array('image_link', null, InputOption::VALUE_OPTIONAL, 'An example option.', null),
			array('status', null, InputOption::VALUE_OPTIONAL, 'An example option.', null),
			array('meeting_name', null, InputOption::VALUE_OPTIONAL, 'An example option.', null),			
		);
	}
}