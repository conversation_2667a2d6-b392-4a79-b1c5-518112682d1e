<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Mailqueue;
use App\Models\User;
use App\Models\RiskImprovementFormySubmissions;
use Carbon\Carbon;
class SurveyReminders extends Command {

	protected $queue;

	protected $email_subject = 'Risk Recommendation Reminder';
	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $name = 'riskreduce:send-survey-reminder';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Risk Reduce Send Survey Reminders';

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
		$this->queue = new Mailqueue();
	}

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 */
	public function handle()
	{
		//

		$this->sendRemindersForDatesOlderThan(30);
		$this->sendRemindersForDatesOlderThan(60);
		$this->sendRemindersForDatesOlderThan(90);

	}



	private function sendRemindersForDatesOlderThan($number_of_days)
	{

		$previous_date = Carbon::now()->addDays($number_of_days * -1);

		$submissions = RiskImprovementFormySubmissions::where('csr_submission_date',  $previous_date->format('d/m/Y'))
			->get();

		if(count($submissions)){


			foreach ($submissions as $key => $submission){

				if($submission->hasOpenRecommendations()) {

					//Send Email to Underwriter
					//Send Email to Underwriter
					if ($submission->survey->broker_underwriter) {
						$underwriter = $submission->survey->broker_underwriter;

						$this->queue->queue($underwriter->email, $underwriter->fullName(), $this->email_subject, 'emails.surveys.reminder', array(
							'first_name' => $underwriter->first_name,
							'last_name' => $underwriter->last_name,
							'policy_no' =>  $submission->survey->policyNumber != null ? $submission->survey->policyNumber->policy_number : null,
							'csr_no' => 'CSR' . $submission->survey->id,
							'survey_id' => $submission->survey->id,
							'days' => $number_of_days,
							'organisation_name' => $submission->survey->organisation->name
						));

						$this->info('Underwriter Emailed: ' . $underwriter->fullName());
					}
					elseif ($submission->survey->underwriter) {
						$underwriter = $submission->survey->underwriter;

						$this->queue->queue($underwriter->email, $underwriter->fullName(), $this->email_subject, 'emails.surveys.reminder', array(
							'first_name' => $underwriter->first_name,
							'last_name' => $underwriter->last_name,
							'policy_no' =>  $submission->survey->policyNumber != null ? $submission->survey->policyNumber->policy_number : null,
							'csr_no' => 'CSR' . $submission->survey->id,
                            'survey_id' => $submission->survey->id,
							'days' => $number_of_days,
							'organisation_name' => $submission->survey->organisation->name
						));

						$this->info('Underwriter Emailed: ' . $underwriter->fullName());
					}
					else {

					}


					//Send Email to Organisation Admins
					if ($submission->survey->organisation) {


						$admin_users = User::where('organisation_id', '=', $submission->survey->organisation->id)
							->where('manager', '=', true)
							->get();


						if (count($admin_users)) {
							foreach ($admin_users as $user) {

								$this->queue->queue($user->email, $user->fullName(), $this->email_subject, 'emails.surveys.reminder-client-admin', array(
									'first_name' => $user->first_name,
									'last_name' => $user->last_name,
									'survey_id' => $submission->survey->id,
								));

								$this->info("Admin Emailed: " . $user->fullName());

							}
						}


					}


				}


			}

		}



	}

	/**
	 * Get the console command arguments.
	 *
	 * @return array
	 */
	protected function getArguments()
	{
		return array(
		);
	}

	/**
	 * Get the console command options.
	 *
	 * @return array
	 */
	protected function getOptions()
	{
		return array(
		);
	}

}
