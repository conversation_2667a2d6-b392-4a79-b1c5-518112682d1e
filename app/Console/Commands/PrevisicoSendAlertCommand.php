<?php

namespace App\Console\Commands;

use App\Jobs\Previsico\NotificationLogger;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;
use App\Jobs\Previsico\Sms;
use App\Models\Previsico\PrevisicoUiNotification;
use App\Models\Mailqueue;
use App\Models\LibertyUser;
use App\Models\User;
use Carbon\Carbon;
use Aws\Laravel\AwsFacade as AWS;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;
use Aloha\Twilio\Twilio;
use App\Models\Log as AlertLog;

class PrevisicoSendAlertCommand extends Command
{
	private $twilio;
    const ALERT_RECIPIENTS = ['+447576904915', '+34640684561']; //ERWIN  MARIA '+639225199848'
    const ALERT_MESSAGE_FAILED = '[UAT] PREVISICO DATA FETCH: FAILED - An issue with email delivery to users has arisen, and our engineers will be handling it.';
    const ALERT_MESSAGE_SUCCESS = '[UAT] PREVISICO DATA FETCH: SUCCESS - The alerts have been sent successfully.';
    const NO_NEW_ALERT = '[UAT] PREVISICO NO NEW ALERT - no new alerts have been reported in the past four hours.';
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'previsico:send-alert';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command sends sms/email alert to client users.';

    /**
     * Mailer class
     *
     * @return Mailqueue
     */
    protected $mailqueue;

    protected $isPrelaunchMode;
    
    /**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
        parent::__construct();
		$twilioConfig = Config::get('app.twilio.sms');        
    	$this->twilio = new Twilio($twilioConfig['sid'], $twilioConfig['token'], $twilioConfig['from']);
	}
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->isPrelaunchMode = Config::get('app.previsico.prelaunch_mode', false);
        $messages              = $this->recieveMessages();
        $this->info('before read alerts');

        $this->readAlerts($messages);
        $this->info('read alerts');
    }

    /**
     * Get the console command arguments.
     *
     * @return array
     */
    protected function getArguments()
    {
        return array(
            //array('example', InputArgument::REQUIRED, 'An example argument.'),
        );
    }

    /**
     * Get the console command options.
     *
     * @return array
     */
    protected function getOptions()
    {
        return array(
            //array('example', InputArgument::REQUIRED, 'An example argument.'),
        );
    }

    private function recieveMessages()
    {
        $url = (string) Config::get('app.previsico.aws_sqs_alert_list_queue');

        if (isset($url) && !empty($url)) {
            $messages = AWS::createClient('Sqs')->receiveMessage([
                'QueueUrl'              => $url,
                'MaxNumberOfMessages'   => 1,
                'MessageAttributeNames' => [],
            ]);

            return $messages;
        } else {
            Log::info('Invalid configuration');
        }
    }

    private function readAlerts($messages)
    {
        $attempts = 0;
        while (!empty($messages['Messages'])) {
            $attempts++;
            $this->sendAlert($messages['Messages'][0]['Body']);
            $this->deleteAllQueue($messages);
            $messages = $this->recieveMessages();

            // Fail safe
            if ($attempts > 1000) {
                break;
            }
        }

        if($this->shouldSendNoNewAlert()){
            $noNewAlert = true;
            $this->sendEmailAlert('success', $noNewAlert);
            $this->createLog('NO_NEW_ALERT', self::NO_NEW_ALERT, ['message' => self::NO_NEW_ALERT], 'success', 1, 1);
        }
    }

    private function sendAlert($alertData)
    {
        try {
            $data                                    = explode("#", $alertData);
            $messageParameters['email']              = $data[0];
            $messageParameters['phone']              = $data[1];
            $messageParameters['sms_header']         = $data[2];
            $messageParameters['sms_body']           = $data[3];
            $messageParameters['email_subject']      = $data[4];
            $messageParameters['email_body']         = $data[5];
            $messageParameters['recipient_name']     = $data[6];
            $messageParameters['client_link']        = $data[7];
            $messageParameters['alert_level']        = (string) $data[8];
            $messageParameters['organisation_id']    = $data[9];
            $messageParameters['is_risk_controller'] = $data[10];
    
            if ($messageParameters['is_risk_controller'] == 0) {
                if ($this->shouldSendSMS(intval($messageParameters['alert_level']))) {
                    $sms = new Sms;
                    $sms->send($messageParameters);
                }
    
                // Always log notifications even when not on prelaunch mode
                NotificationLogger::log(
                    'sms',
                    [
                        'name'    => $messageParameters['recipient_name'],
                        'contact' => $messageParameters['phone']
                    ],
                    [
                        'header' => $messageParameters['sms_header'],
                        'body'   => $messageParameters['sms_body']
                    ]
                );
            }
    
            $riskControllerCCList = Config::get('app.previsico.rc_email_recipients_cc', []);
            $emailRecipientsCC    = (empty($riskControllerCCList) || $messageParameters['is_risk_controller'] == 0)
                                    ? Config::get('app.previsico.email_recipients_cc')
                                    : $riskControllerCCList;
    
            if (empty($this->mailqueue)) {
                $this->setMailqueue();
            }
    
            // Send email notifications to risk controllers
            // Even on prelaunch mode
            if (
                ($messageParameters['is_risk_controller'] == 1)
                || empty($this->isPrelaunchMode)
            ) {
                $this->mailqueue->sendOnDemand(
                    $messageParameters['email'],
                    $messageParameters['email_subject'],
                    $this->buildEmailBody($messageParameters),
                    $emailRecipientsCC
                );
            }
    
            // Always log notifications even when not on prelaunch mode
            NotificationLogger::log(
                'email',
                [
                    'name'    => $messageParameters['recipient_name'],
                    'contact' => $messageParameters['email']
                ],
                [
                    'header' => $messageParameters['email_subject'],
                    'body'   => $messageParameters['email_body']
                ]
            );

            $logData = [
                'email',
                [
                    'name'    => $messageParameters['recipient_name'],
                    'contact' => $messageParameters['email']
                ],
                [
                    'header' => $messageParameters['email_subject'],
                    'body'   => $messageParameters['email_body']
                ]
            ];
    
            $this->generateUiNotification($messageParameters['email'], $messageParameters['organisation_id']);
            $emailSent = 0;
            $smsSent = 0;
            if($this->shouldSendEmailAndSMSAlert('success')){
                $this->sendEmailAlert('success');
                $emailSent = 1;
                $smsSent = 1;
            }
            
            $this->createLog('SEND_ALERT', self::ALERT_MESSAGE_SUCCESS, $logData, 'success', $emailSent, $smsSent);
        } catch (\Exception $e) {
            if($this->shouldSendEmailAndSMSAlert('failed')){
                $this->sendSMS('failed');
                $this->sendEmailAlert('failed');
                $emailSent = 1;
                $smsSent = 1;
            }

            $this->createLog('SEND_ALERT', self::ALERT_MESSAGE_FAILED, ['message' => $e->getMessage()], 'failed', $emailSent, $smsSent);
        }
    }

    /**
     * Create log
     * 
     * @param string $action
     * @param string $msg
     * @param array|string $data
     * @param string $status
     * @return void
     */
    private function createLog($action, $msg, $data, $status, $emailSent, $smsSent)
    {
        AlertLog::create([
            'action'     => $action,
            'message'    => $msg,
            'data'       => $data,
            'email_sent' => $emailSent,
            'sms_sent'   => $smsSent,
            'status'     => $status
        ]);
    }

    /**
     * Generate UI flafs
     *
     * @param string $clientEmail
     * @return void
     */
    private function generateUiNotification($clientEmail, $organisationId)
    {
        // Admin UI Notification
        $libertyUsers = LibertyUser::where('role', 'admin')->get();

        foreach ($libertyUsers as $libertyUser) {
            $previsicoUiNotification = PrevisicoUiNotification::firstOrNew(
                [
                    'liberty_user_id' => $libertyUser->id,
                    'organisation_id' => $organisationId,
                ]
            );

            $previsicoUiNotification->liberty_user_id  = $libertyUser->id;
            $previsicoUiNotification->organisation_id  = $organisationId;
            $previsicoUiNotification->has_notification = 1;
            $previsicoUiNotification->save();
        }

        // Update client UI Notification
        if (isset($clientEmail) && !empty($clientEmail)) {
            User::where('email', $clientEmail)
            ->update([
                'has_previsico_alert_ui_notification' => 1
            ]);
        }
    }

    /**
     * Generate email body for asset email
     *
     * @param string $link
     * @return string
     */
    private function buildEmailBody($messageParameters)
    {
        return View::make('emails.previsico.alert-message', $messageParameters)->render();
    }

    /**
     * Determine if new location has been created
     *
     * @param array $messages
     * @return boolean
     */
    private function deleteAllQueue($messages)
    {
        $url = (string) Config::get('app.previsico.aws_sqs_alert_list_queue');
        if (!empty($messages['Messages']) && (isset($url) && !empty($url))) {
            foreach ($messages['Messages'] as $message) {
                AWS::createClient('Sqs')->deleteMessage([
                    'QueueUrl'      => $url,
                    'ReceiptHandle' => $message['ReceiptHandle']
                ]);
            }
        } else {
            Log::info("Nothing to delete...");
            Log::info("Please check configuration...");
        }
    }

    private function setMailqueue()
    {
        $this->mailqueue = new Mailqueue();
    }

    /**
     * Check if system should send sms
     *
     * @param int $alertLevel
     * @return boolean
     */
    private function shouldSendSMS($alertLevel)
    {
        $allowedTimeFrom    = Config::get('app.previsico.notifications.sms_allowed_time.from');
        $allowedTimeFromDt  = null;
        $allowedTimeTo      = Config::get('app.previsico.notifications.sms_allowed_time.to');
        $allowedTimeToDt    = null;

        if (!empty($allowedTimeFrom)) {
            try {
                $allowedTimeFromDt = Carbon::parse(date("Y-m-d $allowedTimeFrom"));
            } catch (\Exception $e) {
                $allowedTimeFromDt = null;
            }
        }

        if (!empty($allowedTimeFrom)) {
            try {
                $allowedTimeToDt = Carbon::parse(date("Y-m-d $allowedTimeTo"));
            } catch (\Exception $e) {
                $allowedTimeToDt = null;
            }
        }

        // Only send SMS when:
        // - Prelaunch Mode is off
        // - Allowed time for sms is not set
        //   or Allowed time is set,
        //   and current time is between the allowed range
        //   or current time is not between the allowed range
        //   and the alert level is greated than 1 (Prepare)
        return empty($this->isPrelaunchMode)
            && (
                (empty($allowedTimeFromDt) || empty($allowedTimeToDt))
                || (Carbon::now()->between($allowedTimeFromDt, $allowedTimeToDt))
                || (
                    !Carbon::now()->between($allowedTimeFromDt, $allowedTimeToDt)
                    && $alertLevel > 1
                )
            );
    }

    private function sendSMS($alertType, $noNewAlert = false)
    {
        $msg = $alertType === 'success' ? self::ALERT_MESSAGE_SUCCESS : self::ALERT_MESSAGE_FAILED;
        if($noNewAlert){
            $msg = self::NO_NEW_ALERT;
        }
        foreach(self::ALERT_RECIPIENTS as $recipient){
            $this->twilio->message(
                $recipient,
                $msg,
                [],
            );
        }
    }

    private function sendEmailAlert($alertType, $noNewAlert = false)
    {
        if (empty($this->mailqueue)) {
            $this->setMailqueue();
        }

        $subject = $alertType === 'success' ? 'PREVISICO SEND ALERT: SUCCESS' : 'PREVISICO SEND ALERT: FAILED';
        $body    = $alertType === 'success' ? self::ALERT_MESSAGE_SUCCESS : self::ALERT_MESSAGE_FAILED;

        if($noNewAlert){
            $subject = 'No new alert';
            $body    = self::NO_NEW_ALERT;
        }
        
        $forceEmailTo = true;

        $this->mailqueue->sendOnDemand(
            config('app.previsico.alert_email_receipient'),
            $subject,
            $body,
            [],
            [],
            $forceEmailTo
        );
    }

    private function shouldSendEmailAndSMSAlert($alertType) {
        $lastLog = AlertLog::where('status', $alertType)
                         ->where('action', 'SEND_ALERT')
                         ->where('email_sent', 1)
                         ->orderBy('created_at', 'desc')->first();
        if (!$lastLog) {
            return true;
        }
    
        $diffInHours = Carbon::now()->diffInHours($lastLog->created_at);
    
        return $diffInHours >= 4;
    }

    private function shouldSendNoNewAlert() {
        $lastLog = AlertLog::where(function($query){
            $query->where('status', 'success')
                  ->orWhere('status', 'failed');
         })
         ->where('action', 'NO_NEW_ALERT')
         ->where('email_sent', 1)
         ->orderBy('created_at', 'desc')
         ->first();

        if (!$lastLog) {
            return true;
        }
    
        $diffInHours = Carbon::now()->diffInHours($lastLog->created_at);
    
        return $diffInHours >= 4;
    }
}