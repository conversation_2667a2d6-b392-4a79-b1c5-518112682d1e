<?php

namespace App\Console\Commands;

use App\Models\Cati;
use Illuminate\Console\Command;
use App\Models\Organisation;
class ExpiredOrganisation extends Command 
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'riskreduce:suspend_cati_organisation';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Suspend Expired Organisation with 1 policy';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $organisations = Organisation::whereHas('policies', function($query){
            return $query->whereRaw('DATEDIFF(expiry_date_of_cover, CURDATE()-INTERVAL 30 DAY) <=0');
		})->whereNotNull('cati_id')->where('cati_status', 'active')->get();

        $cati = new Cati();
        foreach ($organisations as $organisation) {
            $response = $cati->suspendClient($organisation->cati_id);
            if($response->StatusCode == 'DC00') {
                $organisation->cati_status = 'suspense';
                $organisation->save();
			}
        }
    }
}
