<?php

namespace App\Builders\GPDR;

use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Model;

abstract class LinkToPageBaseBuilder
{
    public Collection $modelData;

    public function __construct(Collection $data)
    {
        $this->modelData = $data;
    }

    abstract public function buildUrl(Model $item): string;

    /**
     * Adds link_to_page attribute.
     *
     * @return Collection
     */
    public function build(): Collection
    {
        $mapped = $this->modelData->map(function ($item) {
            if(empty($item)) {
                return [];
            }
            $return = $this->getFieldsToReturn($item);
            $return['link_to_page'] = $this->buildUrl($item);
            return $return;
        });
        return $mapped;
    }

    /**
     * getFieldsToReturn
     *
     * @param  Model $item
     * @return array
     */
    public function getFieldsToReturn(Model $item): array
    {
        return Arr::only($item->toArray(), [
            'id',
            'first_name',
            'last_name',
            'email',
            'phone'
        ]);
    }

    /**
     * concatUrl
     *
     * @param  string $base
     * @param  string $endpoint
     * @return string
     */
    public function concatUrl(string $base, string $endpoint): string
    {
        return trim($base, "/") . "/" . trim($endpoint, "/");
    }
}
