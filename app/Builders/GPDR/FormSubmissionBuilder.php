<?php

namespace App\Builders\GPDR;

use Illuminate\Database\Eloquent\Model;
use App\Builders\GPDR\LinkToPageBaseBuilder;

class FormSubmissionBuilder extends LinkToPageBaseBuilder
{
    public function buildUrl(Model $item): string
    {
        $endpoint = "forms/submissions/$item->id";
        return $this->concatUrl(config('app.admin_frontend'), $endpoint);
    }

    public function getFieldsToReturn(Model $item): array
    {
        return [
            'id'            => data_get($item, 'id', ''),
            'first_name'    => data_get($item, 'fname', ''),
            'last_name'     => data_get($item, 'surname', ''),
            'email'         => data_get($item, 'email', ''),
            'phone'         => data_get($item, 'phone', ''),
        ];
    }

}
