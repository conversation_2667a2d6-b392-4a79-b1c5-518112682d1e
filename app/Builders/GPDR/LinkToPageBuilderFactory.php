<?php

namespace App\Builders\GPDR;

use Exception;
use App\Models\User;
use App\Models\BrokerUser;
use App\Models\LibertyUser;
use Illuminate\Support\Arr;
use App\Models\ExternalSurveyor;
use Illuminate\Support\Collection;
use App\Models\OrganisationContact;
use Illuminate\Database\Eloquent\Model;
use App\Models\RRAppetite\FormSubmission;
use App\Models\LetsTalk\VideoRecordMessage;
use App\Models\LetsTalk\VideoRecordMessageRecipient;
use App\Models\LibertyAnywhere\LaBookingParticipant;
use App\Models\SurveyContact;
use App\Models\RHS\Customer;
use App\Models\LetsTalk\VideoCallParticipant;
use App\Models\LetsTalk\ResourceExternalContact;

class LinkToPageBuilderFactory
{
    private const BUILDER_CLASS_MAP = [
        LaBookingParticipant::class => LaBookingParticipantBuilder::class,
        LibertyUser::class => LibertyUserBuilder::class,
        User::class => UserBuilder::class,
        FormSubmission::class => FormSubmissionBuilder::class,
        VideoRecordMessage::class => VideoRecordMessageBuilder::class,
        VideoRecordMessageRecipient::class => VideoRecordMessageRecipientBuilder::class,
        OrganisationContact::class => OrganisationContactBuilder::class,
        ExternalSurveyor::class => ExternalSurveyorBuilder::class,
        SurveyContact::class => SurveyContactBuilder::class,
        Customer::class => CustomerBuilder::class,
        VideoCallParticipant::class => VideoCallParticipantBuilder::class,
        ResourceExternalContact::class => ResourceExternalContactBuilder::class,
        BrokerUser::class => BrokerUserBuilder::class,
    ];

    /**
     * make
     *
     * @param  mixed $class
     * @param  mixed $data
     * @return LinkToPageBaseBuilder
     */
    public static function make(string $class, Collection $data): LinkToPageBaseBuilder
    {
        $builderClass = self::BUILDER_CLASS_MAP[$class] ?? null;
        if (!$builderClass) {
            throw new Exception("Builder class not found for $class!");
        }
        return new $builderClass($data);
    }
}
