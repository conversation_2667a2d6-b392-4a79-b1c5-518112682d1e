<?php

namespace App\Builders\GPDR;

use Illuminate\Support\Arr;
use Illuminate\Database\Eloquent\Model;
use App\Builders\GPDR\LinkToPageBaseBuilder;

class ResourceExternalContactBuilder extends LinkToPageBaseBuilder
{
    public function buildUrl(Model $item): string
    {
        $endpoint = "virtual-rooms/external-contacts/$item->id";
        return $this->concatUrl(config('app.admin_frontend'), $endpoint);
    }

    public function getFieldsToReturn(Model $item): array
    {
        return [
            'id'            => data_get($item, 'id', ''),
            'first_name'    => data_get($item, 'name', ''),
            'last_name'     => '',
            'email'         => data_get($item, 'email', ''),
            'phone'         => data_get($item, 'mobile_number', ''),
        ];
    }
}
