<?php

namespace App\Builders\GPDR;

use Illuminate\Support\Arr;
use Illuminate\Database\Eloquent\Model;
use App\Builders\GPDR\LinkToPageBaseBuilder;

class VideoCallParticipantBuilder extends LinkToPageBaseBuilder
{
    public function buildUrl(Model $item): string
    {
        return "";
    }

    public function getFieldsToReturn(Model $item): array
    {
        return [
            'id'            => '',
            'first_name'    => data_get($item, 'user_name', ''),
            'last_name'     => '',
            'email'         => data_get($item, 'email', ''),
            'phone'         => data_get($item, 'mobile_number', ''),
        ];
    }
}
