<?php

namespace App\Builders\GPDR;

use Illuminate\Support\Arr;
use Illuminate\Database\Eloquent\Model;
use App\Builders\GPDR\LinkToPageBaseBuilder;

class CustomerBuilder extends LinkToPageBaseBuilder
{
    public function buildUrl(Model $item): string
    {
        $endpoint =  "/rhs/customers/$item->id";
        return $this->concatUrl(config('app.admin_frontend'), $endpoint);
    }

    public function getFieldsToReturn(Model $item): array
    {
        return [
            'id'            => data_get($item, 'id', ''),
            'first_name'    => data_get($item, 'fullname', ''),
            'last_name'     => '',
            'email'         => data_get($item, 'email', ''),
            'phone'         => '',
        ];
    }
}
