<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\Links;
use App\Models\LinkSector;
use App\Http\Controllers\BaseController;
use App\Models\BrokerUser;
use App\Models\Document;
use App\Models\Documentlevels;
use App\Models\Formy;
use App\Models\Organisation;
use Illuminate\Support\Facades\Response;
use App\Models\SectorDocument;;
use App\Models\User;
use Illuminate\Http\Request;

class SearchController extends BaseController
{

    /**
     * @param Documentlevels $documentLevels
     * @param SectorDocument $sectorDocument
     * @param Document       $document
     */
    public function __construct(
        Documentlevels $documentLevels,
        SectorDocument $sectorDocument,
        Document $document,
        LinkSector $linkSector,
        Links $links
    ) {
        $this->documentLevels = $documentLevels;
        $this->sectorDocument = $sectorDocument;
        $this->documents = $document;
        $this->linkSector = $linkSector;
        $this->links = $links;
    }

    public function search(Request $request)
    {
        $search_string = $request->get('search_string');

        if ($request->has('user_type')) {
            $users = [];

            $organisations = [];

            $pivots = $this->sectorDocument->where('sector_id', '=', $request->get('sector'))->pluck('document_id')->all();
            $documents = $this->documents->where('name', 'LIKE', '%' . $search_string . '%')
                ->where('description', 'LIKE', '%' . $search_string . '%')
                ->Where('deleted_at', null)
                ->where('organisation_id', '=', $request->get('organisation_id'), 'AND')
                ->get();

            // $sectorDocuments = $this->documents->where('deleted_at', NULL)
            //             ->where(function ($query) use ($search_string, $pivots) {
            //                 $query->where('name','LIKE','%'.$search_string.'%')
            //                 ->whereIn('id',array_values($pivots), 'OR')
            //     ->where('description','LIKE','%'.$search_string.'%', 'OR');
            //             })->get();

            $sectorDocuments = $this->documents->whereIn('id', array_values($pivots))
                ->where('name', 'LIKE', '%' . $search_string . '%')
                ->orWhere('description', 'LIKE', '%' . $search_string . '%')
                ->get();
            //echo "<pre/>"; print_r($sectorDocuments); exit;

            foreach ($sectorDocuments as $doc) {

                if (($doc->organisation_id == $request->get('organisation_id') || is_null($doc->organisation_id)) && is_null($doc->deleted_at)) {
                    $documents->add($doc);
                }

            }
            $documents = $documents->unique();


            $pivots_links = $this->linkSector->where('sector_id', '=', $request->get('sector'))->pluck('link_id')->all();
            $links = $this->links->where('name', 'LIKE', '%' . $search_string . '%')->where(
                'organisation_id', '=',
                $request->get('organisation_id')
            )->get();

            $sectorLinks = $this->links->whereIn('id', array_values($pivots_links))->where(
                'name', 'LIKE',
                '%' . $search_string . '%'
            )->get();

            foreach ($sectorLinks as $link) {
                //print_r($link); exit;
                if ($link->organisation_id == $request->get('organisation_id') || is_null($link->organisation_id)) {
                    $links->add($link);
                }
            }
            $links = $links->unique();


            $forms = Formy::where('name', 'LIKE', '%' . $search_string . '%')->whereIn(
                'organisation',
                ['0', $request->get('organisation_id')]
            )->get();
            $organisation_id = $request->get('organisation_id');
            $count = 0;

            if (count($forms) == 0) {
                $forms = Formy::where('name', 'LIKE', '%' . $search_string . '%')
                    ->whereIn('selected_sectors', [$request->get('sector')])
                    ->get();
            }

            if ($forms != null) {

                foreach ($forms as $index => $form) {

                    if (isset($form->exclude_ids)) {
                        $excluded_org_array = explode(',', $form->exclude_ids);
                        if (in_array($organisation_id, $excluded_org_array)) {
                            $count = $count + 1;
                            $forms->offsetUnset($index);
                        }
                    }
                }
            }

            if ($request->get('user_type') == 'Manager') {

                $users = User::where(function($query) use ($search_string, $organisation_id) {
                    $query->where('organisation_id', '=', $organisation_id)
                        ->orWhereRaw('LOWER(TRIM(first_name)) LIKE ?', ["%{$search_string}%"])
                        ->orWhereRaw('LOWER(TRIM(last_name)) LIKE ?', ["%{$search_string}%"])
                        ->orWhereRaw('TRIM(email) LIKE ?', ["%{$search_string}%"])
                        ->orWhereRaw("LOWER(TRIM(CONCAT(TRIM(first_name), ' ', TRIM(last_name)))) LIKE ?", ["%{$search_string}%"]);
                })->get();

            }

        } else {
            $users = User::where(function($query) use ($search_string) {
                $query->whereRaw('LOWER(TRIM(first_name)) LIKE ?', ["%{$search_string}%"])
                      ->orWhereRaw('LOWER(TRIM(last_name)) LIKE ?', ["%{$search_string}%"])
                      ->orWhereRaw('TRIM(email) LIKE ?', ["%{$search_string}%"])
                      ->orWhereRaw("LOWER(TRIM(CONCAT(TRIM(first_name), ' ', TRIM(last_name)))) LIKE ?", ["%{$search_string}%"]);
                })->get();

            $documents = Document::where('name', 'LIKE', '%' . $search_string . '%')
                ->orWhere('description', 'LIKE', '%' . $search_string . '%')
                ->Where('deleted_at', null)
                ->get();

            $forms = Formy::where('name', 'LIKE', '%' . $search_string . '%')->get();

            $organisations = Organisation::where('name', 'LIKE', '%' . $search_string . '%')
                ->where('email', 'LIKE', '%' . $search_string . '%', 'OR')->get();

            $links = Links::where('name', 'LIKE', '%' . $search_string . '%')->get();
        }

        $response = [];
        $brokerUsers = BrokerUser::with('broker:id,name')->where(function($query) use ($search_string) {
            $query->whereRaw('LOWER(TRIM(first_name)) LIKE ?', ["%{$search_string}%"])
                  ->orWhereRaw('LOWER(TRIM(last_name)) LIKE ?', ["%{$search_string}%"])
                  ->orWhereRaw('TRIM(email) LIKE ?', ["%{$search_string}%"])
                  ->orWhereRaw("LOWER(TRIM(CONCAT(TRIM(first_name), ' ', TRIM(last_name)))) LIKE ?", ["%{$search_string}%"]);
            })
            ->get()
            ->toArray();

        $response['broker_users'] = $brokerUsers;
        $response['response'] = 'success';
        if ($users) {
            $response['users'] = $users->toArray();
        }
        foreach ($documents as $doc) {
            $doc->sector = $doc->sectors();
            $doc->cover;
        }
        $response['documents'] = $documents;

        foreach ($links as $link) {
            $link->sector = $link->sectors();
        }
        $response['links'] = $links;

        if ($forms) {
            $response['forms'] = $forms->toArray();
        }
        if ($organisations) {
            $response['organisations'] = $organisations->toArray();
        }

        return Response::json($response);
    }

    public function results()
    {

    }

}
