<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\Document;
use App\Models\Documentlevels;
use App\Models\Link;
use App\Models\Organisation;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;


class DocumentLevelController extends BaseController
{

    /*
    * get all forms
    */
    public function getLevel($org_id, $level, $parent = 0, $sector = 0)
    {
		$level 	= str_replace("*slash*", "/", $level);
		$parent = str_replace("*slash*", "/", $parent);

        if ($level == '99999') {
            if (!is_numeric($parent)) {
                $parent = urldecode($parent);
                $level= Documentlevels::where('level_name', '=', $parent)->first();
                if ($level->level == '3') {
                    $levels = Documentlevels::whereRaw('(level = \'4\' AND child_of = \'0\') OR (level = \'4\' AND child_of = '.addslashes($level->level_id).') AND deleted_at IS NULL')->orderby('level_name', 'asc')->get();
                } else {
                    $levels = Documentlevels::where('child_of','=', $level->level_id)->orderby('level_name', 'asc')->get();
                }

                foreach ($levels as $l) {
                    if ($l->level == 4) {
                        //get documents for this level
                        $l->documents = $l->documents($org_id, $level, $sector);
                    }
                    $l->children = $l->children();

                    if ($level->level === 1) {
                        $level1 = $level->level_id;

                        foreach ($l->children as $index => $child) {
                            $level2 = $child->parent()->level_id;
                            $level3 = $child->level_id;

                            $hasDocument = Document::where('level1_type', $level1)
                                ->where('level2_type', $level2)
                                ->where('level3_type', $level3)
                                ->whereNull('deleted_at')
                                ->exists();

                            $hasLinks = Link::join('link_sector_pivot as lsp', function ($query) use ($sector) {
                                    $query->on('lsp.link_id', '=', 'links.id');
                                    $query->where('lsp.sector_id', '=', $sector);
                                })
                                ->where('level1_type', $level1)
                                ->where('level3_type', $level3)
                                ->exists();

                            if (!$hasDocument && !$hasLinks) {
                                unset($l->children[$index]);
                            }
                        }
                    }
                }
            } else {
                $level 	= Documentlevels::where('level_id','=',$level)->first();
                $levels = Documentlevels::where('child_of', '=', $parent)->orderby('level_name', 'asc')->get();
            }

            return Response::json([
                'response'  =>  'success',
                'data'  =>  array('current_level' => $level,'levels' => $levels),
            ]);

        } else {
            $levels = Documentlevels::where('level', '=', $level);
            if ($level == '4') {
                $docLevels = Documentlevels::select('level_name')->where('level_id', '=', $parent)->first();
                if (isset($docLevels) && array_key_exists($docLevels->level_name, config('client_dashboard.fas_pi_risk_guidances'))) {
                    $levels = $levels->where('child_of', '=', $parent)
                        ->orderBy('level_name', 'asc')
                        ->get();
                } else {
                    $levels = $levels->whereIn('child_of', [0, $parent])
                        ->whereNull('deleted_at')
                        ->orderBy('level_name', 'asc')
                        ->get();
                }
            } else {
                if ($parent == 0) {
                    // For FAS PORTAL Feature
                    $sector = Organisation::select('sector')->find($org_id)?->sector;
                    $fasRiskGuidances = config('client_dashboard.fas_pi_risk_guidances');
                    if (isset($sector) && in_array($sector, $fasRiskGuidances)) {
                        $levelNotInclude = array_keys(array_filter($fasRiskGuidances, function($id) use ($sector) {
                            return $id != $sector;
                        }));
                        $levels->whereNotIn('level_name', $levelNotInclude);
                    }

                    $levels = $levels->orderby('level_name', 'asc')->get();
                } else {
                    $levels = $levels->where('child_of', '=', $parent, 'AND')->orderby('level_name', 'asc')->get();
                }
            } 
        }

		return Response::json([
			'response'  =>  'success',
			'data'      =>  $levels->toArray()
		]);
    }

	public function getSubLevelContentCount($org_id, $sector = 0, $level1, $level2, $level3)
	{
		$documents = Document::select('documents.id')
			->join('sector_document_pivot', function ($query) use($org_id, $sector) {
				$query->on('sector_document_pivot.document_id', '=', 'documents.id');
				$query->whereNull('sector_document_pivot.deleted_at');
				$query->where(DB::raw('organisation_id IS NULL AND sector_document_pivot.sector_id'), '=', $sector);
				$query->orWhere('organisation_id', '=', $org_id);
			})->where('documents.category', '!=', 'LOSSLESSON')
				->where('documents.category', '!=', 'CLIENTFOLDER')
				->where('documents.level1_type', '=', $level1)
				->where('documents.level2_type', '=', $level2)
				->where('documents.level3_type', '=', $level3)
				->get();

		return Response::json([
			'response' => 'success',
			'count'	   => $documents->count(),
		]);
	}

	public function getDocForLevelType1And4($org_id, $sectorId, $level1)
	{
        // Level1 passed is level name not level id
        if (!is_numeric($level1)) {
            $level1 = DocumentLevels::where('level_name', urldecode($level1))->first()?->level_id;
        }

        // Return early if level1 is not recognized
        if (empty($level1)) {
            return Response::json([
                'response' => 'success',
                'data'     => [],
            ]);
        }

        $documents = Document::select('*')
            ->join('sector_document_pivot', function ($query) use($org_id, $sectorId) {
                $query->on('sector_document_pivot.document_id', '=', 'documents.id');
                $query->whereNull('sector_document_pivot.deleted_at');
                $query->where('sector_document_pivot.sector_id', '=', $sectorId);
                $query->orWhere('organisation_id', '=', $org_id);
            })->join('document_levels as dl1',  function ($q) {
                $q->on('dl1.level_id', '=', 'documents.level1_type');
            })->join('document_levels as dl4',  function ($q) {
                $q->on('dl4.level_id', '=', 'documents.level4_type');
            })
            ->where('documents.category', '!=', 'LOSSLESSON')
                ->where('documents.category', '!=', 'CLIENTFOLDER')
                ->where('documents.level1_type', '=', $level1)
                ->whereNull('documents.level2_type')
                ->whereNull('documents.level3_type')
                ->get();

        // Format data
        $finalData = [];
        foreach ($documents as $document) {
            if (empty($finalData[$document->level_name])) {
                $finalData[$document->level_name] = [
                    'level_id'    => $document->level_id,
                    'level_name'  => $document->level_name,
                    'child_of' 	  => $document->child_of,
                    'description' => $document->description,
                    'created_at'  => $document->created_at,
                    'updated'     => $document->updated_at,
                    'deleted_at'  => $document->deleted_at,
                    'documents'   => [],
                    'children'    => [],
                ];
            }

            $finalData[$document->level_name]['documents'][] = [
                'id'                     => $document->id,
                "name"                   => $document->name,
                "category"               => $document->category,
                "sector_id"              => $document->sector_id,
                "organisation_id"        => $document->organisation_id,
                "created_at"             => $document->created_at,
                "updated_at"             => $document->updated_at,
                "document_title"         => $document->document_title,
                "document_store_name"    => $document->document_store_name,
                "level1_type"            => $document->level1_type,
                "level2_type"            => $document->level2_type,
                "level3_type"            => $document->level3_type,
                "level4_type"            => $document->level4_type,
                "featured"               => $document->featured,
                "img_path"               => $document->img_path,
                "cover_id"               => $document->cover_id,
                "release_date"           => $document->release_date,
                "branch_id"              => $document->branch_id,
                "recommended"            => $document->recommended,
                "recommended_image_path" => $document->recommended_image_path,
                "description"            => $document->description,
                "deleted_at"             => $document->deleted_at,
                "document_id"            => $document->document_id
            ];
        }

        if (!empty($finalData)) {
            ksort($finalData);
            $finalData = array_values($finalData);
        }

        return Response::json([
            'response' => 'success',
            'data'     => $finalData,
        ]);
    }

    public function getLevelsAll($level1_type, $level2_type = 0, $level3_type = 0, $level4_type = 0)
    {
        $level1_type = str_replace("*slash*", "/", $level1_type);
        $level2_type = str_replace("*slash*", "/", $level2_type);
        $level3_type = str_replace("*slash*", "/", $level3_type);
        $level4_type = str_replace("*slash*", "/", $level4_type);
        $levels = Documentlevels::where('level_id', '=', $level1_type)
            ->where('level_id', '=', $level2_type, 'OR')
            ->where('level_id', '=', $level3_type, 'OR')
            ->where('level_id', '=', $level4_type, 'OR')
            ->orderby('level_name', 'asc')
            ->get();
        $levels_all = [];
        foreach ($levels as $level) {
            $levels_all['level' . $level->level] = $level;
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => $levels_all,
            ]
        );
    }

}
