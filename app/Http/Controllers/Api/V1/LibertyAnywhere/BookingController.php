<?php

namespace Api\V1\LibertyAnywhere;

use App\Http\Controllers\BaseController;
use App\Models\LibertyAnywhere\LaBooking;
use Illuminate\Support\Facades\Config;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use App\Models\Cms;
use Illuminate\Support\Facades\Cache;

class BookingController extends BaseController
{
    private $business;

    public function __construct(Request $request)
    {
        $this->business = ($request->has('business') && $request->get('business') === 'lmre') ? 'lmre': 'lsm';
    }

    /** 
     * Return all bookings from
     */
    public function getBookings()
    {
        $laBookings = LaBooking::whereNull('deleted_at')
            ->orderBy('created_at', 'desc')
            ->with(['location', 'participants', 'attachments', 'bookingLink'])
            ->get();

        $representatives = [];
        $bookings        = [];
        foreach ($laBookings as $booking) {
            $representatives[$booking->representative_id] = $this->getLibRepById($booking->representative_id);
            if (!$booking->participants->isEmpty()) {
                $bookings[] = $booking;
            }
        }
        return Response::json([
            'data' => [
                'bookings'        => $bookings,
                'representatives' => $representatives
            ],
        ]);
    }

    private function getLibRepById(Request $request, $id)
    {
        $libertyRepresentatives = $this->fetchLibRep($request);

        $foundLibRep = null;
        foreach ($libertyRepresentatives as $key => $librep) {
            if (!isset($librep['email'])) {
                continue;
            }

            if ($id == $key) {
                $foundLibRep = $librep;
                break;
            }
        }

        return $foundLibRep;
    }
    
    /**
     * Fetch Librep from cache, if no cache found, It will be fetched from cms and store to cache
     *
     * @return void
     */
    private function fetchLibRep(Request $request)
    {
        // Forget social room cache libreps on force_recache
        if($request->has('force_recache')) {
            Cache::forget($this->business . '_socialRoomLibertyRepresenativesCms');
            Cache::forget($this->business . '_socialRoomPeopleProfileCms');
            Cache::forget($this->business . '_socialRoomLibertyRepresentatives');
        }

        if (Cache::has($this->business . '_libertyRepresenativesCms') && !$request->has('force_recache')) {
            $libertyRepresenativesCms = Cache::get($this->business . '_libertyRepresenativesCms');
        } else {
            $libertyRepresenativesCms = json_decode(Cms::get('workspaces/'.$this->getCmsConfig('lets_talk_workspace').'/content-types/'.$this->getCmsConfig('liberty_representatives_content_type').'/content-entries?joined=true&query={"status":"publish","operator":"="}'));
            Cache::put($this->business . '_libertyRepresenativesCms', $libertyRepresenativesCms, 1440);
        }

        if (Cache::has($this->business . '_peopleProfileCms') && !$request->has('force_recache')) {
            $peopleProfileCms = Cache::get($this->business . '_peopleProfileCms');
        } else {
            $peopleProfileCms = json_decode(Cms::get('workspaces/'.$this->getCmsConfig('workspace_id').'/content-types/'.$this->getCmsConfig('people_content_type').'/content-entries?joined=true&query={"status":"publish","operator":"="}'));
            Cache::put($this->business . '_peopleProfileCms', $peopleProfileCms, 1440);
        }

        if (Cache::has($this->business . '_libertyRepresentatives') && !$request->has('force_recache')) {
            $libertyRepresentatives = Cache::get($this->business . '_libertyRepresentatives');
        } else {
            $peopleProfile = [];

            foreach ($peopleProfileCms->data as $profile) {
                $peopleProfile[$profile->_id]['line_of_business'] = [];
                if (isset($profile->{$this->getCmsConfig('liberty_representative_line_of_business')})) {
                    foreach ($profile->{$this->getCmsConfig('liberty_representative_line_of_business')} as $lob) {
                        if (isset($lob->name)) {
                            $peopleProfile[$profile->_id]['line_of_business'][] = $lob->name;
                            $peopleProfile[$profile->_id]['line_of_business_slug'][] = $lob->slug;
                        }
                    }
                }
                $timezoneAssignment = isset($profile->{$this->getCmsConfig('people_profile_office')}) ? $profile->{$this->getCmsConfig('people_profile_office')} : null ;
                $peopleProfile[$profile->_id]['timezone'] = isset($timezoneAssignment[0]) ? $timezoneAssignment[0]->{$this->getCmsConfig('people_profile_office_timezone')} : null;
                $peopleProfile[$profile->_id]['office_title'] = isset($timezoneAssignment[0]) ? $timezoneAssignment[0]->{$this->getCmsConfig('people_profile_office_title')} : null;
            }

            if (isset($libertyRepresenativesCms->data) && isset($libertyRepresenativesCms->data['0']) && isset($libertyRepresenativesCms->data['0']->{$this->getCmsConfig('liberty_representatives')})) {
                $libertyRepresenatives = $libertyRepresenativesCms->data['0']->{$this->getCmsConfig('liberty_representatives')};
            } else {
                $libertyRepresenatives = [];
            }

            foreach ($libertyRepresenatives as $key => $libRep) {
                // $this->getCmsConfig('liberty_representative_email')
                if (!isset($libRep->status) || $libRep->status != 'publish' || empty($libRep->{$this->getCmsConfig('liberty_representative_mobile_number')}) || empty($libRep->{$this->getCmsConfig('liberty_representative_email')})) {
                    unset($libertyRepresenatives[$key]);
                    continue;
                }

                if (isset($libRep->{$this->getCmsConfig('liberty_representative_line_of_business')}) && isset($peopleProfile[$libRep->_id]['line_of_business'])) {
                    $libRep->{$this->getCmsConfig('liberty_representative_line_of_business')} = $peopleProfile[$libRep->_id]['line_of_business'];
                }
                if(isset($peopleProfile[$libRep->_id]['line_of_business_slug'])) {
                    $libRep->{$this->getCmsConfig('liberty_representative_line_of_business').'_slug'} = $peopleProfile[$libRep->_id]['line_of_business_slug'];
                }
            }
            array_splice($libertyRepresenatives, 0, 0);

            $libertyRepresentatives = $this->compileRepData($libertyRepresenatives, $peopleProfile);

            Cache::put($this->business . '_libertyRepresentatives', $libertyRepresentatives, 1440);
            Cache::put($this->business . '_libertyRepresentativesLongLived', $libertyRepresentatives, 1440);
        }

        return $libertyRepresentatives;
    }

    /**
     * Compile all the data for representatives
     *
     * @param array $representatives
     * @param string $timezone
     */
    private function compileRepData($representatives, $timezone)
    {
        if (empty($representatives)) {
            return [];
        }

        foreach ($representatives as $value) {
            if (isset($value->{$this->getCmsConfig('liberty_representative_mobile_number')}) && isset($value->{$this->getCmsConfig('liberty_representative_email')})) {
                $mobileNumber = str_replace('(0)', '', $value->{$this->getCmsConfig('liberty_representative_mobile_number')});
                if (substr($mobileNumber, 0, 1) === '0') {
                    $mobileNumber = ltrim($mobileNumber, '0');
                } elseif (substr($mobileNumber, 0, 1) !== '+') {
                    $mobileNumber = "+44".$mobileNumber;
                }
                if (is_array($value->{$this->getCmsConfig('liberty_representative_profile_picture')}) && isset($value->{$this->getCmsConfig('liberty_representative_profile_picture')}[0])) {
                    $parsedUrl = parse_url($value->{$this->getCmsConfig('liberty_representative_profile_picture')}[0]->url);
                    $parsedUrl['query'] = urlencode($parsedUrl['query']);
                    $profileImage = $parsedUrl['scheme']."://".$parsedUrl['host'].$parsedUrl['path'].'?'.$parsedUrl['query'];
                } else {
                    $profileImage = "https://asset-management-dev.s3.eu-west-1.amazonaws.com/assets/p2NoaMtcZQmyXDW9hoGy0GVtKpcZOQBi5mQg1nKe.png?name%3Duser-icon-silhouette-ae9ddcaf4a156a47931d5719ecee17b9.png%26size%3D6KB%26mime%3Dimage%2Fpng";
                }

                $officeNumber = !empty($value->{Config::get('app.cms.people_profile_office_number')}) ?  $value->{Config::get('app.cms.people_profile_office_number')} : '';

                $reprsentativeList[$value->_id] = [
                    'name' => $value->name,
                    'line_of_business' => isset($value->{$this->getCmsConfig('liberty_representative_line_of_business')}) ? $value->{$this->getCmsConfig('liberty_representative_line_of_business')} : [],
                    'line_of_business_slug' => isset($value->{$this->getCmsConfig('liberty_representative_line_of_business').'_slug'}) ? $value->{$this->getCmsConfig('liberty_representative_line_of_business').'_slug'} : [],
                    'bio' => isset($value->{Config::get('app.cms.people_profile_bio')}) ? $value->{Config::get('app.cms.people_profile_bio')} : '',
                    'business_function' => isset($value->{$this->getCmsConfig('liberty_representative_business_function')}) ? $value->{$this->getCmsConfig('liberty_representative_business_function')} : null,
                    'email' => $value->{$this->getCmsConfig('liberty_representative_email')},
                    'mobile' => "+".preg_replace('/[^0-9]/', '', $mobileNumber),
                    'office_number' => $officeNumber,
                    'profile_picture' => $profileImage,
                    'job_title' => $value->{$this->getCmsConfig('liberty_representative_job_title')},
                    'office_timezone' => isset($timezone[$value->_id]['timezone']) ? $timezone[$value->_id]['timezone'] : 'Europe/London',
                    'office_title' => isset($timezone[$value->_id]['office_title']) ? $timezone[$value->_id]['office_title'] : 'N/A',
                    'schedule' => isset($reprsentativeSchedule[$value->_id]) ? $reprsentativeSchedule[$value->_id] : []
                ];
            }
        }

        return $reprsentativeList;
    }

    /**
     * Wrapper function to get config based on business
     *
     * @param string $config
     */
    private function getCmsConfig($config)
    {
        return Config::get('app.cms.' . $this->business . '.' . $config);
    }
}