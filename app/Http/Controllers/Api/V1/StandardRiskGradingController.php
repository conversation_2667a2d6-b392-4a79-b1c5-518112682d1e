<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\Mailqueue;
use App\Models\OrganisationLocations;
use App\Models\RGAttribute;
use App\Models\RGSubAttribute;
use App\Models\RiskGrading\RgLocationGrading;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class StandardRiskGradingController extends BaseController
{
    const NO_GRADE = 'Not Applicable / Not Assessed';
    const GRADINGS = [
        'poor' => 0,
        'below_average' => 0.25,
        'average' => 0.5,
        'good' => 0.75,
        'superior' => 1,
    ];

    const BANDING_OUTPUTS = [
        'Poor' => [
            'min' => 0,
            'max' => 24,
        ],
        'Below Average' => [
            'min' => 25,
            'max' => 49,
        ],
        'Average' => [
            'min' => 50,
            'max' => 69,
        ],
        'Above Average' => [
            'min' => 70,
            'max' => 84,
        ],
        'Superior' => [
            'min' => 85,
            'max' => 100,
        ],
    ];

    /**
     * Get location risk grading overview
     *
     * @return json
     */
    public function getRiskGradingOverview($organisationId)
    {
        $finalAttributes = [];
        $validAttributes = RGAttribute::pluck('attribute')->all();
        $listOfAttr = RGAttribute::select(['id', 'rg_policy_type_id', 'attribute'])
            ->with('subAttributes:rg_attribute_id,sub_attribute,max_point')
            ->get()
            ->toArray();

        $attributes = OrganisationLocations::with('standardLocationsGradingsOverview', 'standardLocationsSubGradingsOverview:organisation_location_id,attribute,value')
            ->whereHas('standardLocationsGradingsOverview')
            ->whereHas('standardLocationsSubGradingsOverview')
            ->where('organisation_id', $organisationId)
            ->get();
        
        foreach ($attributes as $attribute) {
            if (!empty($attribute->standardLocationsGradingsOverview) && !empty($attribute->standardLocationsSubGradingsOverview)) {
                $subListWithValues = $attribute->standardLocationsSubGradingsOverview
                    ->pluck('value', 'attribute')
                    ->toArray();

                foreach ($attribute->standardLocationsGradingsOverview as $rgLocGrading) {
                    $totalPoints = 0;
                    $totalPercentage = 0;
                    $totalSubPoints = 0;
                    $attr = $rgLocGrading->attribute;
                    $index  = array_search($attr, $validAttributes);

                    if (!array_key_exists($attr, $finalAttributes)&& in_array($attr, $validAttributes)) {
                        $finalAttributes[$attr] = $rgLocGrading->policy_type_id;
                    }

                    if ($index !== false) {
                        if (isset($listOfAttr[$index]) && ($rgLocGrading->value === 'not_applicable_not_assessed' || $rgLocGrading->value === 'Not Applicable / Not Assessed')) {
                            $subAttributes = $listOfAttr[$index]['sub_attributes'];
                            foreach ($subAttributes as $subData) {
                                $subName = $subData['sub_attribute'];
                                if (isset($subListWithValues[$subName])) {
                                    $subValue = $subListWithValues[$subName];
                                    $maxPoint = $subData['max_point'];
    
                                    if ($subValue !== 'not_applicable_not_assessed' && $subValue !== 'Not Applicable / Not Assessed') {
                                        $totalPoints += $maxPoint;
                                        $totalSubPoints += $maxPoint * $this->gradingValue($subValue);
                                    }
                                }
                            }

                            if ($totalSubPoints > 0) {
                                $totalPercentage = round(($totalSubPoints / $totalPoints) * 100);
                                $rgLocGrading->value = $this->getBandingOutput($totalPercentage);
                            }
                        }
                    }
                }
            }
        }

        return [
            'overview' => $this->sortOverviewByLatest($attributes),
            'attributes' => $finalAttributes,
        ];
    }

    private function sortOverviewByLatest($overview): array
    {
        $orgLocationIds = $overview->pluck('id');
        $latest = RgLocationGrading::whereIn('organisation_location_id', $orgLocationIds->all())
            ->selectRaw('max(created_at) as created_at, organisation_location_id')
            ->groupBy('organisation_location_id')
            ->orderByDesc('created_at')
            ->get();

        $sortedOverview = [];
        $latest->each(function($latestLocation) use($overview, &$sortedOverview) {
            $sortedOverview[] = $overview->first(function($location) use($latestLocation) {
                return $latestLocation->organisation_location_id === $location->id;
            });
        });

        return $sortedOverview;
    }


    /**
     * Get all the Standard Risk Grading attributes
     *
     * @return json
     */
    public function getAllAttributes()
    {
        return response()
            ->json(RGAttribute::latest()->get());
    }

    /**
     * Return Standard Risk Grading attributes, policy type, and it's sub attributes
     *
     * @param  int $id
     * @return json
     */
    public function getAttribute($attrId)
    {
        return response()->json(
            RGAttribute::with(['policyType', 'subAttributes'])
                ->where('id', (int)$attrId)->first()
        );
    }

    /**
     * Return Standard Risk Grading sub attributes
     *
     * @param  int $subAttrId
     * @return json
     */
    public function getSubAttribute($subAttrId)
    {
        return response()
            ->json(RGSubAttribute::find($subAttrId));
    }

    /**
     * calculate risk grading
     *
     * @return json
     */
    public function calculateRiskGrading(Request $request)
    {
        $data = $request->all();

        $attributeName = $data['attribute_name'] === 'construction and exposure' ? 'construction and exposures' : $data['attribute_name'];
        $attribute = RGAttribute::where('attribute', $attributeName)
            ->where('rg_policy_type_id', (int)$data['policy_type_id'])->first();

        if (isset($attribute)) {

            $result = [];
            $totalPoints = 0;
            $totalSubPoints = 0;
            $subAttributes = $attribute->subAttributes;
            $totalPercentage = 0;
            $validValues = 0;

            $result['attribute_name'] = $attribute->attribute;
            if ($data['attribute_name'] === 'construction and exposure') { // Sometimes the value is `construction and exposure`
                $result['attribute_name'] = $data['attribute_name'];
            }

            foreach ($data as $key => $value) {

                $prefixKey = '|' . str_replace(" ", "_", $result['attribute_name']);

                if (str_contains($key, $prefixKey) && $value != 'Not Applicable / Not Assessed') {
                    $validValues++;
                    $dataSubAttribute = str_replace(
                        '_', ' ',
                        explode(
                            '|' . str_replace(" ", "_", $result['attribute_name']),
                            explode('sub-attribute-', $key)[1]
                        )[0]
                    );

                    foreach ($subAttributes as $subAttribute) {
                        if (strtolower(str_replace('-', ' ', $dataSubAttribute)) === strtolower(
                            str_replace(
                                '-', ' ',
                                $subAttribute->sub_attribute
                            )
                        )
                        ) {
                            $totalPoints += $subAttribute->max_point;
                            $totalSubPoints += $subAttribute->max_point * $this->gradingValue($value);
                        }
                    }
                }
            }

            if ($totalSubPoints > 0) {
                $totalPercentage = round(($totalSubPoints / $totalPoints) * 100);
            }

            $result['percentage'] = $totalPercentage . '%';
            $result['banding_output'] = $this->getBandingOutput($totalPercentage);

            if ($validValues == 0) {
                $result['banding_output'] = 'Not Applicable / Not Assessed';
            }

            return response()->json($result);
        }
    }

    /**
     * Recalcualte the gradings based on given attributes and sub attributes grading data
     *
     * @param Request $request
     * @return array
     */
    public function recalculateGradings(Request $request)
    {
        $gradings = $request->all();
        $attributes = RGAttribute::select('id', 'attribute')->with('subAttributes:id,rg_attribute_id,max_point,sub_attribute')
            ->whereIn('attribute', $gradings['attribute_names'])
            ->where('rg_policy_type_id', (int)$gradings['policy_type_id'])
            ->get();

        $idx    = 0;
        $result = [];
        foreach ($gradings as $indx => $data) {
            $totalPoints     = 0;
            $totalSubPoints  = 0;
            $totalPercentage = 0;
            $validValues     = 0;
            
            if ($idx < count($attributes)) {
                $subAttributes   = $attributes[$idx]->subAttributes;

                if ($indx !== 'attribute_names' && $indx !== 'policy_type_id') {
                    $result[$idx]['attribute_field'] = $data['attribute_field'];
                    foreach ($data as $key => $value) {
                        if ($value != 'Not Applicable / Not Assessed') {
                            $validValues++;
                            foreach ($subAttributes as $subAttribute) {
                                if ($subAttribute->sub_attribute === $key) {
                                    $totalPoints    += $subAttribute->max_point;
                                    $totalSubPoints += $subAttribute->max_point * $this->gradingValue($value);
                                }
                            }
                        }
                    }
        
                    if ($totalSubPoints > 0) {
                        $totalPercentage = round(($totalSubPoints / $totalPoints) * 100);
                    }
        
                    $result[$idx]['percentage']     = $totalPercentage . '%';
                    $result[$idx]['banding_output'] = $this->getBandingOutput($totalPercentage);
        
                    if ($validValues == 0) {
                        $result[$idx]['banding_output'] = 'Not Applicable / Not Assessed';
                    }
                    $idx++;
                }
            }
        }
        return $result;
    }

    private function gradingValue($gradingText)
    {
        $grading = 0;
        switch ($gradingText) {
        case 'Superior':
            $grading = self::GRADINGS['superior'];
            break;
        case 'Average':
            $grading = self::GRADINGS['average'];
            break;
        case 'Above Average':
            $grading = self::GRADINGS['good'];
            break;
        case 'Below Average':
            $grading = self::GRADINGS['below_average'];
            break;
        }

        return $grading;
    }

    public function export(Request $request)
    {
        $lineOfBusiness = $request->get('line_of_business');
        $userId = $request->get('user_id');
        Artisan::call('standard-riskgrading:export', ['--lob' => $lineOfBusiness, '--user_id' => $userId]);
    }

    public function sendErrorFromRiskGrading(Request $request)
    {
        $queue = new Mailqueue();
        $data = $request->all();
        $body = sprintf(
            "User ID: %s<br />User Email: %s<br />Survey ID: %s<br />Form Link: %s<br />",
            $data['user_id'],
            $data['email'],
            $data['survey_id'],
            $data['form_link']
        );
        
        $queue->sendOnDemand(
            '<EMAIL>',
            'Risk Grading Error',
            $body,
            [],
            [],
            true
        );
    }

    private function getBandingOutput($total)
    {
        foreach (self::BANDING_OUTPUTS as $key => $arrayValues) {
            if ($arrayValues['min'] <= $total && $arrayValues['max'] >= $total) {
                return $key;
            }
        }
    }
}
