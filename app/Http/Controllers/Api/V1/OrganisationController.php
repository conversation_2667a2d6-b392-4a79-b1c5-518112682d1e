<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Jobs\Previsico\GenerateAssetsList;
use App\Jobs\UpdatePersonSectorJob;
use App\Jobs\UpdateUserStatusJob;
use Artisaninweb\SoapWrapper\SoapWrapper;
use App\Models\Branch;
use App\Models\BrokerUser;
use Carbon\Carbon;
use App\Models\Cati;
use App\Models\ClientClaimsDashboard\ClientClaimId;
use App\Models\ClientDashboardComponentOrder;
use App\Models\CMS\PeopleProfile;
use App\Models\Community\CommunitySector;
use App\Models\Community\UserType;
use App\Models\Course;
use Exception;
use App\Models\LibertyUser;
use App\Models\MgaScheme;
use App\Models\Organisation;
use App\Models\OrganisationContact;
use App\Models\OrganisationCover;
use App\Models\OrganisationLocations;
use App\Models\OrganisationLog;
use App\Models\OrganisationPolicy;
use App\Models\OrganisationPropDoc;
use App\Models\PolicyDocumentModel;
use App\Models\PolicyType;
use App\Models\Previsico\PrevisicoAsset;
use App\Models\Previsico\PrevisicoAssetAccess;
use Illuminate\Support\Facades\Response;
use App\Models\Sector;
use App\Models\Theme;
use App\Models\UnderwriterPolicies;
use App\Models\User;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Models\RiskGrading;
use App\Models\OrganisationSectionSetting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class OrganisationController extends BaseController
{
    protected $catiErrorMessage = '<b>Operation Failed.</b><br/>The error code is <b>%s</b>:<i>%s.</i><br/>Please contact support with this error information, link of this page and any other details that can help our investigation.';

    protected $options = ['A' => 'blue', 'B' => 'green', 'C' => 'yellow', 'D' => 'orange', 'E' => 'red'];

    protected $soapWrapper;

    public function __construct(SoapWrapper $soapWrapper)
    {
        // $this->soapqueue = $sopaqueue;
        $this->soapWrapper = $soapWrapper;
    }

    //ALL

    public function broker(Request $request, $id, $page = null, $limit = null)
    {
        $search = $request->get('search');
        $mgas = MgaScheme::where('broker_id', $id)->pluck('id')->all();
        $orgids = Organisation::whereIn('mga_scheme', $mgas)->orWhere('broker_id', $id)->pluck('id')->all();
        $organisations = Organisation::whereIn('id', $orgids);

        $type = $request->get('type');
        $user_id = $request->get('user_id');

        if (isset($search) && $search != '') {
            $organisations = Organisation::where('name', 'LIKE', '%' . $search . '%')->whereIn('id', $orgids);
        }

        if ($request->has('mga_scheme')) {
            $organisations = $organisations->where('mga_scheme', $request->get('mga_scheme'));
        }

        if (isset($type) && $type == 'self' && $user_id) {
            $organisations = $organisations->whereHas(
                'contacts',
                function ($query) use ($user_id) {
                    $query->where('user_id', $user_id)
                        ->where('type', 'broker-user-contact');
                }
            );
        }

        $org_total = $organisations->count();

        // if(isset($page) && isset($limit)){
        //     $organisations = $organisations->take($limit)->skip(($page * $limit)-$limit)->orderBy('name', 'asc')->with('branch');
        // }

        $sort = $request->get('sort');
        $col = $request->get('col');

        $column = 'name';
        $sortorder = 'asc';

        if (isset($page) && isset($limit)) {
            if ((isset($sort)) && (isset($col))) {
                $sortorder = $sort;
                switch ($col) {
                    case 'org':
                        $column = 'name';
                        break;
                    case 'un':
                        $column = 'underwriter';
                        break;
                    case 're':
                        $column = 'riskengineer';
                        break;
                    case 'rg':
                        $column = 'risk_grading_value';
                        break;
                    case 'ed':
                        $column = 'policy_expiring_soon';
                        break;
                    case 'lr':
                        $column = 'loss_ratio';
                        break;
                    default:
                        $column = 'name';
                        $sortorder = 'asc';
                }
            }

            $organisations = $organisations->orderBy(
                $column,
                $sortorder
            )->take($limit)->skip(($page * $limit) - $limit)->with('branch');
        }

        $organisations = $organisations->get();

        foreach ($organisations as $org) {
            $org->policy_numbers = $org->policyNumbers();

            //For phase 4

            foreach ($org->contacts as $contact) {
                if ($contact->type == 'underwriter') {
                    $org->underwriter = LibertyUser::find($contact->user_id);
                }
                if ($contact->type == 'risk-engineer') {
                    $org->riskengineer = LibertyUser::find($contact->user_id);
                }
            }

            if (isset($org->policy_numbers) && count($org->policy_numbers) > 0) {
                $policyendingSoon = $org->policy_numbers[0];
                foreach ($org->policy_numbers as $policies) {
                    if ($policies->expiry_date_of_cover < $policyendingSoon->expiry_date_of_cover) {
                        $policyendingSoon = $policies;
                    }
                }
                $org->policy_expiring_soon = $policyendingSoon;
            }

            $org->risk_grading = array_search($org->risk_grading, $this->options);

            //End For phase 4

            $org->user_count = count($org->users);
            if (!isset($org->branch)) {
                $org->branch = $org->libertyBranch();
            }
        }
        $branches = Branch::all();

        return Response::json(
            [
                'response' => 'success',
                'data' => $organisations,
                'branches' => $branches,
                'total' => $org_total,
            ]
        );
    }

    public function orgInfo(Request $request)
    {
        $orgId         = $request->id;
        $userId        = $request->userId ?? false ;
        $previsicoAssetsAccess = false;

        $organisation  = Organisation::with([
            'sector:id,handle',
            'claims:id,name',
            'contacts',
            'products',
            'branch:id,is_aspen',
            'users:id,organisation_id,branch,branch_name',
            // 'locations'
        ])->find($orgId);

        $previsicoAssets = PrevisicoAsset::with('organisationLocation')
            ->where('organisation_id', $orgId)
            ->get();

        if (isset($organisation)) {
            if ($request->has('force_recache') && $request->get('force_recache')) {
                PeopleProfile::recache();
                return response()->json('PeopleProfile successfully recached.');
            }
        }

        if ($userId) {
            $previsicoAssetAccess = PrevisicoAssetAccess::where('user_id', $userId)->get();
            $previsiscoAssetAccessIDs = [];
            foreach ($previsicoAssetAccess as $previsico) {
                $previsiscoAssetAccessIDs[] = $previsico->previsico_asset_id;
            }

            $organisation->previsico_assets_access = $previsiscoAssetAccessIDs;
        }

        $organisation->sector_name = !empty($organisation->sector_id) ? $organisation->sector()->first()->handle : '';

        $uw_id                = [];
        $underwriters         = [];
        $riskEngineers        = [];
        $brokerUserContacts   = [];
        $claimsLaisonContacts = [];
        $keyContact           = [];
        foreach ($organisation->contacts as $contact) {
            if ($contact->type == 'underwriter') {
                $user = LibertyUser::find($contact->user_id);
                $user = $this->addCmsData($user);
                if ($user) {
                    $uw_id[]           = $contact->user_id;
                    $user->policy_id   = $contact->policy_id;
                    $underwriters[]    = $user;
                    $user->policy_type = PolicyType::select('id')->find($contact->policy_id);
                }
            } elseif ($contact->type == 'key-contact') {
                $user = LibertyUser::find($contact->user_id);
                $user = $this->addCmsData($user);
                if ($user) {
                    array_push($keyContact, $user);
                }
            }
        }
        
        $uw_policies = [];
       
        $uw_policies = UnderwriterPolicies::whereIn('underwriter_id', $uw_id)
        ->where('organisation_id', $organisation->id)
        ->select('id', 'policy_id')
        ->get();
        
        $organisation->uw_policies = $uw_policies;
        //end underwriter Policy

        $organisation->uw_policies = $uw_policies;
        
        // $organisation->brokerUserContacts   = $brokerUserContacts;
        $organisation->underwriters         = $underwriters;
        $organisation->riskEngineers        = $riskEngineers;
        $organisation->claimsLaisonContacts = $claimsLaisonContacts;
        $organisation->keyContact           = $keyContact;

        $organisation->user_community_sectors = [];
        $userId = $request->get('user_id');
        $user   = User::find($userId);
        if (!empty($userId) && !empty($user)) {
            $organisation->user_community_sectors = $this->getUserCommunitySubscription($request, $userId);
            $organisation->component_order = ClientDashboardComponentOrder::getOrCreate($userId);
            $organisation->has_unseen_survey = $user->hasUnseenSurvey();
            $organisation->has_unseen_client_info = $user->hasUnseenClientFolderReport();
        }


        // Count number of Previsico alerts in the organisation
        $floodAlertsCount = 0;
        if (!empty($organisation->has_previsico_access)) {
            $locations = PrevisicoAsset::with('assetAccess')
                ->whereHas(
                    'assetAccess', function ($query) use ($userId) {
                        $query->where('user_id', $userId);
                    }
                )->get();

            $allowedLocations = [];
            foreach ($locations as $location) {
                $allowedLocations[] = $location->organisation_location_id;
            }

            PrevisicoAsset::with('organisationLocation')
                ->whereHas(
                    'organisationLocation', function ($query) use ($orgId) {
                        $query->where('organisation_id', $orgId);
                    }
                )
                ->where('alert_level', '>', 0)
                ->orderBy('alert_level', 'desc')
                ->get()
                ->each(
                    function ($asset) use (&$floodAlertsCount, $allowedLocations) {
                        if (in_array($asset->organisation_location_id, $allowedLocations)
                        && !empty($asset->latestAlert())) {
                            $floodAlertsCount++;
                        }
                    }
                );
        }

        $organisation->flood_alerts_count = $floodAlertsCount;
        $libertyId = ClientClaimId::select('liberty_id')
            ->where('organisation_id', $orgId)
            ->first();

        if ($libertyId) {
            $organisation->liberty_id = $libertyId->liberty_id;
        }

        $organisation->section_setting = $this->getOrganisationSectionSetting($orgId);

        $organisation->previsico_assets = $previsicoAssets;

        return Response::json([
            'response' => 'success',
            'data'     => $organisation->toArray(),
        ]);
    }

    public function organisationInfo(Request $request)
    {
        $orgId         = $request->id;
        $organisation  = Organisation::with([
            'sector:id,handle',
            'claims:id,name',
            'contacts',
            'products',
            'branch:id,is_aspen',
            'users:id,organisation_id,branch,branch_name'
        ])->find($orgId);

        $organisation->sector_name = $organisation->sector()->first()->handle;

        return Response::json([
            'response' => 'success',
            'data'     => $organisation->toArray(),
        ]);
    }
    
    public function find(Request $request, $id)
    {
        //client
        
        if ($request->get('request_from')=='client') {
            $organisation = Organisation::find($id);
            $sector=$organisation->Sector; // binding
            return Response::json(
                [
                    'response' => 'success',
                    'data' => $organisation,
                ]
            );
        }

        //admin

        $organisation = Organisation::with('broker_organisations')->find($id);

        if (isset($organisation)) {
            // Clear people profile cache
            if ($request->has('force_recache') && $request->get('force_recache')) {
                PeopleProfile::recache();
                return Response::json("PeopleProfile successfully recached.");
            }

            $organisation->users;
            $organisation->community_organisation = $organisation->community_organisation;
            $organisation->adminUsers = $organisation->adminUsers();

            $organisation->secondaryContacts;
            $organisation->propDoc;
            $organisation->claims;
            $organisation->user_roles = $organisation->userRoles();
            $organisation->brokers = $organisation->brokers();
            $organisation->safetyMediaAccess = $organisation->safetyMediaAccess();
            $stat = $organisation->catiStatistics()->first();

            $organisation->catiStatistics = $stat
                ? $stat->total_logins
                : 0;

            // contacts

            $clientUsers = [];
            $secondaryclientUsers = [];
            $claimsLaisonUser = [];
            $underwriters = [];
            $riskEngineers = [];
            $keyContact = [];
            $clientContacts = [];
            $secondaryclientContacts = [];
            $brokerUserContacts = [];
            $claimsLiaisonManager = (object)[];
            $claimsLaisonContacts = [];
            $broker = (object)[];

            foreach ($organisation->users as $user) {
                $clientUsers[$user->id] = $user->first_name . ' ' . $user->last_name;
            }

            foreach ($organisation->adminUsers as $user) {
                $claimsLaisonUser[$user->id] = $user->first_name . ' ' . $user->last_name;
            }

            foreach ($organisation->secondaryContacts as $user) {
                $secondaryclientUsers[$user->id] = $user->first_name . ' ' . $user->last_name;
            }

            $uw_id = [];
            foreach ($organisation->contacts as $contact) {
                if ($contact->type == 'underwriter') {
                    $user = LibertyUser::find($contact->user_id);
                    $user = $this->addCmsData($user);
                    if ($user) {
                        array_push($uw_id, $contact->user_id);
                        $user->policy_id = $contact->policy_id;
                        $user->policy_type = PolicyType::find($contact->policy_id);
                        array_push($underwriters, $user);
                    }
                } elseif ($contact->type == 'risk-engineer') {
                    $user = LibertyUser::find($contact->user_id);
                    $user = $this->addCmsData($user);
                    if ($user) {
                        array_push($riskEngineers, $user);
                    }
                } elseif ($contact->type == 'key-contact') {
                    $user = LibertyUser::find($contact->user_id);
                    $user = $this->addCmsData($user);
                    if ($user) {
                        array_push($keyContact, $user);
                    }
                } elseif ($contact->type == 'client-contact') {
                    $user = $organisation->users()->find($contact->user_id);
                    $user = $this->addCmsData($user);
                    if ($user) {
                        array_push($clientContacts, $user);
                    }
                } elseif ($contact->type == 'secondary-client-contact') {
                    $user = $organisation->secondaryContacts()->find($contact->user_id);
                    $user = $this->addCmsData($user);
                    if ($user) {
                        array_push($secondaryclientContacts, $user);
                    }
                } elseif ($contact->type == 'claims-liaison-manager') {
                    $user = LibertyUser::find($contact->user_id);
                    $user = $this->addCmsData($user);
                    if ($user) {
                        array_push($claimsLaisonContacts, $user);
                    }
                } elseif ($contact->type == 'broker-user-contact') {
                    if ($contact->user_id !== null) {
                        $user = BrokerUser::find($contact->user_id);
                        $user = $this->addCmsData($user);
                        array_push($brokerUserContacts, $user);
                    }
                    
                    $broker = !empty($user) ? $user : $contact;
                }
            }

            //underwriter policy
            $uw_policies = [];
            foreach ($uw_id as $uid) {
                $uw_policies[$uid] = UnderwriterPolicies::where('underwriter_id', $uid)->where(
                    'organisation_id',
                    $organisation->id
                )->get();
            }
            $organisation->uw_policies = $uw_policies;
            //end underwriter Policy

            //underwriter policy
            $uw_policies = [];
            foreach ($uw_id as $uid) {
                $uw_policies[$uid] = UnderwriterPolicies::where('underwriter_id', $uid)->where(
                    'organisation_id',
                    $organisation->id
                )->get();
            }
            $organisation->uw_policies = $uw_policies;
            //end underwriter Policy

            $organisation->claimsLiaisonManager = $claimsLiaisonManager;
            $organisation->broker = $broker;
            $organisation->brokerUserContacts = $brokerUserContacts;
            $organisation->underwriters = $underwriters;
            $organisation->riskEngineers = $riskEngineers;
            $organisation->clientContacts = $clientContacts;
            $organisation->keyContact = $keyContact;
            $organisation->secondaryclientContacts = $secondaryclientContacts;
            $organisation->clientUsers = $clientUsers;
            $organisation->secondaryclientUsers = $secondaryclientUsers;

            $organisation->claimsLaisonUser = $claimsLaisonUser;
            $organisation->claimsLaisonContacts = $claimsLaisonContacts;

            // end contacts

            $organisation->cover_types = $organisation->organisationCovers();
            $organisation->products = $organisation->products()->get();
            $snhandle = Sector::find($organisation->sector);
            $organisation->sector_name = isset($snhandle->handle)
                ? $snhandle->handle
                : 'n/a';
            $organisation->policy_numbers = $organisation->policyUpdatedNumbers();
            $organisation->liberty_branch = $organisation->libertyBranch();

            if (!is_null($organisation->mga_scheme) && $organisation->mga_scheme != '' && $organisation->mga_scheme != 0) {
                $mgaScheme = MgaScheme::find($organisation->mga_scheme);
                $organisation->mga_scheme_name = $mgaScheme->name;
            }
            /* elseif(!is_null($organisation->broker_organisations) && $organisation->broker_organisations != '') {
                $organisation->mga_scheme_name = 'Open Market';
            } */

            $organisation->user_community_sectors = [];
            $userId = $request->get('user_id');
            $user = User::find($userId);
            if (!empty($userId) && !empty($user)) {
                $organisation->user_community_sectors = $this->getUserCommunitySubscription($request, $userId);
                $organisation->component_order = ClientDashboardComponentOrder::getOrCreate($userId);
                $organisation->has_unseen_survey = $user->hasUnseenSurvey();
                $organisation->has_unseen_client_info = $user->hasUnseenClientFolderReport();
            }

            // Count number of Previsico alerts in the organisation
            $floodAlertsCount = 0;
            if (!empty($organisation->has_previsico_access)) {
                PrevisicoAsset::where('organisation_id', $id)
                    ->where('alert_level', '!=', 0)
                    ->get()
                    ->each(
                        function ($asset) use (&$floodAlertsCount) {
                            if (!empty($asset->latestAlert())) {
                                $floodAlertsCount++;
                            }
                        }
                    );
            }

            $organisation->flood_alerts_count = $floodAlertsCount;
            $libertyId = ClientClaimId::where('organisation_id', $id)->first();
            if ($libertyId) {
                $organisation->liberty_id = $libertyId->liberty_id;
            }

            $organisation->user_community_sectors = [];
            $userId = $request->get('user_id');
            $user = User::find($userId);
            if (!empty($userId) && !empty($user)) {
                $organisation->user_community_sectors = $this->getUserCommunitySubscription($request, $userId);
                $organisation->component_order = ClientDashboardComponentOrder::getOrCreate($userId);
                $organisation->has_unseen_survey = $user->hasUnseenSurvey();
                $organisation->has_unseen_client_info = $user->hasUnseenClientFolderReport();
            }

            $organisation->section_setting = $this->getOrganisationSectionSetting($id);
            return Response::json(
                [
                    'response' => 'success',
                    'data' => $organisation->toArray(),
                ]
            );
        } else {
            $organisation = Organisation::where('name', 'LIKE', '%' . urldecode($id) . '%')->first();
            if (!isset($organisation)) {
                return Response::json(
                    [
                        'response' => 'error',
                        'message' => 'No Organisation by that id',
                    ]
                );
            }

            return Response::json(
                [
                    'response' => 'success',
                    'data' => $organisation,
                ]
            );
        }
    }

    private function getOrganisationSectionSetting($organisationId)
    {
        try {
            $sectionSetting = OrganisationSectionSetting::where('organisation_id', $organisationId)->first();
            return isset($sectionSetting) ? $sectionSetting->access_setting : [];
        } catch (Exception $e) {
            Log::error($e);
            return [];
        }
    }

    //Store

    /**
     * Add Cms Profile picture to Org user
     */
    private function addCmsData($user)
    {
        $cmsLibRep = [];
        if (isset($user->email)) {
            $cmsLibRep = PeopleProfile::getLibRepByEmail(strtolower($user->email));
        }

        if (!isset($user->email) || empty($cmsLibRep)) {

            if (isset($user->cms_profile_picture) && !empty($user->cms_profile_picture)) {
                $user->cms_profile_picture = null;
            }

            if (isset($user->business) && !empty($user->business)) {
                $user->business = null;
            }

            if (isset($user->cms_mobile) && !empty($user->cms_mobile)) {
                $user->cms_mobile = null;
            }

            if (isset($user->cms_mobile) && !empty($user->cms_job_title)) {
                $user->cms_job_title = null;
            }

            if (isset($user->cms) && !empty($user->cms)) {
                $user->cms = null;
            }

            return $user;
        }

        $user->cms_profile_picture = isset($cmsLibRep['profile_picture'])
            ? $cmsLibRep['profile_picture']
            : null;
        $user->business = isset($cmsLibRep['business'])
            ? $cmsLibRep['business']
            : null;
        $user->cms_mobile = isset($cmsLibRep['mobile'])
            ? $cmsLibRep['mobile']
            : null;
        $user->cms_job_title = isset($cmsLibRep['job_title'])
            ? $cmsLibRep['job_title']
            : null;
        $user->cms = $cmsLibRep;
        return $user;
    }

    // View All Organisation Report

    private function getUserCommunitySubscription($userId)
    {
        if (!$userId) {
            return [];
        }

        $userCommunitySubscriptions = UserType::getModel(request()->get('user_id'), 'client')->getUserSubscriptions();

        if (empty($userCommunitySubscriptions)) {
            return [];
        }

        $subscriptionIds = array_map(
            function ($community) {
                return $community['sector_id'];
            },
            is_array($userCommunitySubscriptions)
                ? $userCommunitySubscriptions
                : $userCommunitySubscriptions->toArray()
        );

        $cmsAllCommmunity = CommunitySector::all();

        if (!$cmsAllCommmunity) {
            return [];
        }

        $cmsAllCommmunity = json_decode($cmsAllCommmunity, true);
        $cmsCommunity = isset($cmsAllCommmunity['data'])
            ? $cmsAllCommmunity['data']
            : [];

        $subscribedCommunity = [];
        foreach ($cmsCommunity as $community) {
            if (in_array($community['_id'], $subscriptionIds)) {
                array_push($subscribedCommunity, $community);
            }
        }

        return $subscribedCommunity;
    }

    public function all(Request $request, $page = null, $limit = null)
    {
        //$this->updateReportDataForOrganisation();

        $search = $request->get('search');
        $the_total = 0;

        $column = 'name';
        $sortorder = 'asc';

        $sort = $request->get('sort');
        $col = $request->get('col');

        if (isset($search) && $search != '') {
            if ($request->has('branch_type') && $request->get('branch_type') == 'aspen') {
                $aspen_branches = Branch::where('is_aspen', 1)->pluck('id')->all();
                $organisations = Organisation::whereIn('liberty_branch', $aspen_branches)->where(
                    'name',
                    'LIKE',
                    '%' . $search . '%',
                    'AND'
                );
            } elseif ($request->has('branch')) {
                $organisations = Organisation::where('name', 'LIKE', '%' . $search . '%')->where(
                    'liberty_branch',
                    '=',
                    $request->get('branch'),
                    'AND'
                );
            } elseif ($request->has('mga_scheme')) {
                $organisations = Organisation::where('name', 'LIKE', '%' . $search . '%')->where(
                    'mga_scheme',
                    '=',
                    $request->get('mga_scheme'),
                    'AND'
                );
            } else {
                $organisations = Organisation::where('name', 'LIKE', '%' . $search . '%')->orWhere(
                    'email',
                    'LIKE',
                    '%' . $search . '%'
                );
            }
        } else {
            if ($request->has('branch_type') && $request->get('branch_type') == 'aspen') {
                $aspen_branches = Branch::where('is_aspen', 1)->pluck('id')->all();
                $organisations = Organisation::whereIn('liberty_branch', $aspen_branches);
            } else {
                $organisations = new Organisation();
            }
        }

        if ($request->has('branch')) {
            $organisations = $organisations->where('liberty_branch', $request->get('branch'))
                ->with('branch');
        } elseif ($request->has('mga_scheme')) {
            $organisations = Organisation::where('mga_scheme', $request->get('mga_scheme'));
            $total_rows = $organisations->count();
        } elseif ($request->has('underwriter')) {
            $orgids = OrganisationContact::where('user_id', $request->get('underwriter'))
                ->join('organisations', 'organisations.id', '=', 'organisation_contacts.organisation_id')
                ->where('type', 'underwriter')->pluck('organisation_id')->all();
            $organisations = Organisation::whereIn('id', $orgids);
            $total_rows = count($orgids);

            // $branch_ids = LibertyUser::where('id', $request->get('underwriter'))
            //                         ->where('role', 'underwriter')
            //                         ->pluck('branch_id');
            // $organisations = $organisations->whereIn('liberty_branch', $branch_ids);
        } elseif ($request->has('riskengineer')) {
            $orgids = OrganisationContact::where('user_id', $request->get('riskengineer'))
                ->join('organisations', 'organisations.id', '=', 'organisation_contacts.organisation_id')
                ->where('type', 'risk-engineer')->pluck('organisation_id')->all();
            $organisations = Organisation::whereIn('id', $orgids);
            $total_rows = count($orgids);

            // $branch_ids = LibertyUser::where('id', $request->get('riskengineer'))
            //                     ->where('role', 'risk-engineer')
            //                     ->pluck('branch_id');
            // $organisations = $organisations->whereIn('liberty_branch', $branch_ids);
        } elseif ($request->has('filter_by')) {
            $filter_by = $request->get('filter_by');

            if ($filter_by == 'no-mga') {
                $organisations = Organisation::whereNull('mga_scheme')
                    ->orWhere('mga_scheme', 0);
            } elseif ($filter_by == 'mga-only') {
                $organisations = Organisation::whereNotNull('mga_scheme')
                    ->where('mga_scheme', '>', 0);
            } else {
            }
        } else {
            $organisations = $organisations->with('branch');
        }

        $org_count = $organisations->count();

        if (isset($page) && isset($limit)) {
            if ((isset($sort)) && (isset($col))) {
                $sortorder = $sort;
                switch ($col) {
                    case 'org':
                        $column = 'name';
                        break;
                    case 'un':
                        $column = 'underwriter';
                        break;
                    case 're':
                        $column = 'riskengineer';
                        break;
                    case 'rg':
                        $column = 'risk_grading_value';
                        break;
                    case 'ed':
                        $column = 'policy_expiring_soon';
                        break;
                    case 'lr':
                        $column = 'loss_ratio';
                        break;
                    default:
                        $column = 'name';
                        $sortorder = 'asc';
                }
            }

            $keyaccount = $request->get('keyaccount');
            if ($keyaccount) {
                $orgids_keyaccount = OrganisationContact::where('type', 'risk-engineer')->pluck('organisation_id')->all();
                $organisations = $organisations->whereIn('id', $orgids_keyaccount);
                $org_count = $organisations->count();
            }
            $organisations = $organisations->orderBy(
                $column,
                $sortorder
            )->take($limit)->skip(($page * $limit) - $limit);
        } else {
            if ($request->has('branch')) {
                $organisations = $organisations->where('liberty_branch', $request->get('branch'));
            } elseif ($request->has('mga_scheme')) {
                $organisations = Organisation::where('mga_scheme', $request->get('mga_scheme'));
            } elseif ($request->has('underwriter')) {
                $orgids = OrganisationContact::where('user_id', $request->get('underwriter'))
                    ->join('organisations', 'organisations.id', '=', 'organisation_contacts.organisation_id')
                    ->where('type', 'underwriter')->pluck('organisation_id')->all();
                $organisations = Organisation::whereIn('id', $orgids);
            } elseif ($request->has('riskengineer')) {
                $orgids = OrganisationContact::where('user_id', $request->get('riskengineer'))
                    ->join('organisations', 'organisations.id', '=', 'organisation_contacts.organisation_id')
                    ->where('type', 'risk-engineer')->pluck('organisation_id')->all();
                $organisations = Organisation::whereIn('id', $orgids);
                $total_rows = count($orgids);
            } elseif ($request->has('filter_by')) {
                $filter_by = $request->get('filter_by');
                if ($filter_by == 'no-mga') {
                    $organisations = Organisation::whereNull('mga_scheme')
                        ->orWhere('mga_scheme', 0);
                } elseif ($filter_by == 'mga-only') {
                    $organisations = Organisation::whereNotNull('mga_scheme')
                        ->where('mga_scheme', '>', 0);
                } else {
                }
            } else {
            }
            $org_count = $organisations->count();
        }

        if ((isset($sort)) && (isset($col))) {
            $sortorder = $sort;
            switch ($col) {
                case 'org':
                    $column = 'name';
                    break;
                case 'un':
                    $column = 'underwriter';
                    break;
                case 're':
                    $column = 'riskengineer';
                    break;
                case 'rg':
                    $column = 'risk_grading_value';
                    break;
                case 'ed':
                    $column = 'policy_expiring_soon';
                    break;
                case 'lr':
                    $column = 'loss_ratio';
                    break;
                default:
                    $column = 'name';
                    $sortorder = 'asc';
            }
        }


        $organisations = $organisations->orderBy($column, $sortorder);
        // Exclude appended attributes as this will take time if there a lot of users and creating signed link
        if ($request->get('withoutAppends', false)) {
            $organisations = $organisations->get()->makeHidden(['users_count', 'link']);
        } else {
            $organisations = $organisations->get();
        }

        foreach ($organisations as $org) {
            $org->policy_numbers = $org->policyNumbers();

            //For phase 4

            foreach ($org->contacts as $contact) {
                if ($contact->type == 'underwriter') {
                    $org->underwriter = LibertyUser::find($contact->user_id);
                }
                if ($contact->type == 'risk-engineer') {
                    $org->riskengineer = LibertyUser::find($contact->user_id);
                }
            }

            if (isset($org->policy_numbers) && count($org->policy_numbers) > 0) {
                $policyendingSoon = $org->policy_numbers[0];
                foreach ($org->policy_numbers as $policies) {
                    if ($policies->expiry_date_of_cover < $policyendingSoon->expiry_date_of_cover) {
                        $policyendingSoon = $policies;
                    }
                }
                $org->policy_expiring_soon = $policyendingSoon;
            }

            $org->risk_grading = array_search($org->risk_grading, $this->options);

            //End For phase 4

            $org->user_count = count($org->users);
            if (!isset($org->branch)) {
                $org->branch = $org->libertyBranch();
            }
        }

        $branches = Branch::all();

        $the_total = count(Organisation::all());

        if (
            $request->has('filter_by') || $request->has('mga_scheme') || $request->has('branch')
            || $request->has('underwriter') || $request->has('riskengineer') || $request->has('broker_id') || $request->has('keyaccount')
        ) {
            $the_total = $org_count;
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => $organisations,
                'branches' => $branches,
                'total' => isset($total_rows)
                    ? $total_rows
                    : $the_total,
            ]
        );
    }

    public function store(Request $request)
    {
        $rules = [
            'email' => 'email',
            'name' => 'required|regex:/(^[A-Za-z0-9 ]+$)+/',
            'country' => 'required',
        ];

        if ($request->has('login_type') && $request->get('login_type') == 'broker-user') {
            $broker_rule = [
                'mga_scheme' => 'required_if:broker_id,==,""',
                'broker_id' => 'required_if:mga_scheme,==,""',
            ];
            $rules = array_merge($rules, $broker_rule);
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json(
                [
                    'response' => 'error',
                    'errors' => $validator->messages()->toArray(),
                ],
                200
            );
        } else {
            $uuid = substr(Str::uuid()->toString(), 0, 4);

            $data = $request->except('_token');

            unset($data['set_by']);
            unset($data['login_type']);

            if (isset($data['_rhs'])) {
                $data['is_rhs'] = 1;

                unset($data['_rhs']);
            }

            $data['safetymedia_url'] = $uuid;

            $organisation = Organisation::create($data);

            // Add a default location
            $organisation_id = $organisation->id;
            $postcode = $data['postcode'] ?? "";
            $address_line_1 = $data['address_line_1'] ?? "";
            $address_line_2 = $data['address_line_2'] ?? "";
            $country = $data['country'];

            $orgLocation = OrganisationLocations::create(
                [
                    'organisation_id' => $organisation_id,
                    'location_id' => 1,
                    'location_name' => $data['location_name'] ?? '',
                    'postcode' => $postcode,
                    'address_line_1' => $address_line_1,
                    'address_line_2' => $address_line_2,
                    'country' => $country,
                    'city' =>  $data['city'] ?? '',
                ]
            );

            //Policy Numbers
            $policy_numbers = $data['policy_numbers'] ?? [];
            $isResponsibleBusiness = false;
            foreach ($policy_numbers as $type_id => $policy_number) {
                OrganisationPolicy::create(
                    [
                        'organisation_id' => $organisation->id,
                        'policy_type_id' => $type_id,
                        'policy_number' => $policy_number['policy_number'],
                        'inception_date_of_cover' => Carbon::createFromTimestamp($policy_number['inception_date_of_cover']),
                        'expiry_date_of_cover' => Carbon::createFromTimestamp($policy_number['expiry_date_of_cover']),
                        'premium' => $policy_number['premium'],
                        'loss_ratio' => isset($policy_number['loss_ratio'])
                            ? $policy_number['loss_ratio']
                            : 0,
                        'renewal' => $this->getRenewalDate($policy_number),
                        'bound_lead' => $policy_number['bound_lead'] ?? null,
                        'bound_follow' => $policy_number['bound_follow'] ?? null,
                        'risk_grading' => $policy_number['risk_grading'] ?? null,
                    ]
                );
                $document_id = null;
                $policyDocData = [];
                $policyDocData['name'] = isset($policy_number['document_title'])
                    ? $policy_number['document_title']
                    : '';
                $policyDocData['description'] = '';
                $policyDocData['document_title'] = isset($policy_number['document_title'])
                    ? $policy_number['document_title']
                    : '';
                $policyDocData['document_store_name'] = isset($policy_number['document_store_name'])
                    ? $policy_number['document_store_name']
                    : '';
                $policyDocData['organisation_id'] = $organisation->id;
                $policyDocData['document_type_id'] = $type_id;
                $policy_doc = PolicyDocumentModel::updateOrCreate(['id' => $document_id], $policyDocData);
                $expiry_date_of_cover = $policy_number['expiry_date_of_cover'];

                // Check if it's a responsible business organisation
                $isResponsibleBusiness = ($policy_number['policy_number'] == '999999' && $policy_number['premium'] == '1' && $policy_number['loss_ratio'] == '1')
                    ? true
                    : false;
                if ($isResponsibleBusiness) {
                    $expiry_date_of_cover = time() + (365 * 24 * 60 * 60);
                }
            }

            // Remove SafetyMedia code
            // if (isset($expiry_date_of_cover)) {
                // $this->executeCreateOrgSoapRequest($organisation, $expiry_date_of_cover);
            // }


            if (!empty($data['contacts']['underwriters'])) {
                foreach ($data['contacts']['underwriters'] as $underwriter) {
                    $underwriter = is_numeric($underwriter)
                        ? $underwriter
                        : $underwriter['underwriter_id'];
                    $organisation->contacts()->create(
                        [
                            'type' => 'underwriter',
                            'user_id' => $underwriter,
                        ]
                    );
                }
            }

            if (!empty($data['contacts']['risk-engineers'])) {
                foreach ($data['contacts']['risk-engineers'] as $riskEngineer) {
                    $organisation->contacts()->create(
                        [
                            'type' => 'risk-engineer',
                            'user_id' => $riskEngineer,
                        ]
                    );
                }
            }

            //Sync function for report

            $reportEntity = $this->formatReportEntity($organisation);
            $this->updateOrganisation($reportEntity);

            $clientClaimId = new ClientClaimId();
            $clientClaimId->name = $organisation->name;
            $clientClaimId->organisation_id = $organisation->id;
            $clientClaimId->liberty_id = $request->get('liberty_id', '');
            $clientClaimId->save();

            Cache::put('organisation_last_updated', now()->timestamp);

            return Response::json(
                [
                    'response' => 'success',
                    'message' => 'Organisation created successfully',
                    'id' => $organisation->id,
                    'location_name_id' => $orgLocation->id,
                ]
            );
        }
    }

    //End of View All Organisation Report

    // private function executeCreateOrgSoapRequest(Organisation $organisation, int $expiry_date_of_cover) : void
    // {
    //     try {
    //         $this->soapWrapper->add(
    //             'client',
    //             function ($service) {
    //                 $service
    //                     ->wsdl('https://libertymutual.safetylearning.co.uk/api/1.3/ClientManagement?wsdl');
    //             }
    //         );
    
    //         $data = [
    //             'serviceCredentials' => [
    //                 'username' => config('app.sm.username'),
    //                 'password' => config('app.sm.password'),
    //                 'account' => '',
    //             ],
    //             'clients' => [
    //                 'item' =>
    //                 [
    //                     'id' => config('app.sm.org_id_prefix') . $organisation->id,
    //                     'name' => $organisation->name,
    //                     'enabled' => '1',
    //                     'url' => $organisation->safetymedia_url,
    //                     'licensed' => '1',
    //                     'userLimit' => '500',
    //                     'expiryDate' => ($expiry_date_of_cover) + (30 * 24 * 60 * 60),
    //                     'units' => '',
    //                 ],
    //             ],
    //         ];
    
    //         // Using the add service
    //         $this->soapWrapper->call('client.addClient', [$data]);
    
    //         $data1 = [
    //             'serviceCredentials' => [
    //                 'username' => config('app.sm.username'),
    //                 'password' => config('app.sm.password'),
    //                 'account' => config('app.sm.org_id_prefix') . $organisation->id,
    //             ],
    //             'departments' => [
    //                 'item' =>
    //                 [
    //                     'id' => config('app.sm.org_id_prefix') . $organisation->id,
    //                     'name' => $organisation->name,
    //                     'parentName' => '',
    //                 ],
    //             ],
    //         ];
    
    //         // Using the added service
    //         $this->soapWrapper->call('client.addDepartment', [$data1]);
    
    //         $this->soapWrapper->add('tra', function ($service) {
    //             $service->wsdl('https://libertymutual.safetylearning.co.uk/api/1.3/TrainingManagement?wsdl');
    //         });
    
    //         $sector = Sector::find($organisation->sector);
    //         $data2 = [
    //             'serviceCredentials' => [
    //                 'username' => config('app.sm.username'),
    //                 'password' => config('app.sm.password'),
    //                 'account' => config('app.sm.org_id_prefix') . $organisation->id,
    //             ],
    //             'trainingPlans' => [
    //                 'item' =>
    //                 [
    //                     'id' => config('app.sm.org_id_prefix') . $organisation->id,
    //                     'name' => $sector->handle,
    //                     'description' => $sector->handle,
    //                 ],
    //             ],
    //         ];
    
    //         // Using the added service
    //         $this->soapWrapper->call('tra.AddTrainingPlan', [$data2]);
    
    //         $courses = SectorCourses::where('sector_id', '=', $organisation->sector)->get();
    //         $trainingPlanCoursesArr = [];
    //         foreach ($courses as $course) {
    //             $trainingPlanCoursesArr[] = [
    //                 'trainingPlanID' => config('app.sm.org_id_prefix') . $organisation->id,
    //                 'courseID' => $course->course_id,
    //             ];
    //             $data3 = [
    //                 'serviceCredentials' => [
    //                     'username' => config('app.sm.username'),
    //                     'password' => config('app.sm.password'),
    //                     'account' => config('app.sm.org_id_prefix') . $organisation->id,
    //                 ],
    //                 'trainingPlanCourses' => $trainingPlanCoursesArr,
    //             ];
    //             // Using the added service
    //             $this->soapWrapper->call('tra.AddCourseToTrainingPlan', [$data3]);
    //         }
    //     } catch (\Exception $e) {
    //         Log::error($e->getMessage());
    //         //throw $e;
    //     }
    // }

    //Update

    private function formatReportEntity($org)
    {
        $reportEntity = [];
        $reportEntity['id'] = $org->id;
        $reportEntity['underwriter'] = null;
        $reportEntity['riskengineer'] = null;
        $reportEntity['policy_expiring_soon'] = null;
        $reportEntity['policy_expiring_description'] = null;
        $reportEntity['risk_grading_value'] = array_search($org->risk_grading, $this->options);

        //Policy Number
        $org->policy_numbers = $org->policyNumbers();
        $allpolicies = '';
        if (isset($org->policy_numbers) && count($org->policy_numbers) > 0) {
            $policyendingSoon = $org->policy_numbers[0];
            foreach ($org->policy_numbers as $policies) {
                $allpolicies .= $policies->type->name . '___' . $policies->expiry_date_of_cover . ',';

                if ($policies->expiry_date_of_cover < $policyendingSoon->expiry_date_of_cover) {
                    $policyendingSoon = $policies;
                }
            }
            $reportEntity['policy_expiring_soon'] = $policyendingSoon->expiry_date_of_cover;
            $reportEntity['policy_expiring_description'] = $allpolicies;
        }

        //Risk Engineer

        foreach ($org->contacts as $contact) {
            if ($contact->type == 'underwriter') {
                $re = LibertyUser::find($contact->user_id);
                $reportEntity['underwriter'] = (isset($re->first_name)
                    ? $re->first_name
                    : '') . ' ' . (isset($re->last_name)
                    ? $re->last_name
                    : '');
            }
            if ($contact->type == 'risk-engineer') {
                $re = LibertyUser::find($contact->user_id);
                $reportEntity['riskengineer'] = (isset($re->first_name)
                    ? $re->first_name
                    : '') . ' ' . (isset($re->last_name)
                    ? $re->last_name
                    : '');
            }
        }

        return (object)$reportEntity;
    }

    private function updateOrganisation($reportEntity)
    {
        $organisation = Organisation::find($reportEntity->id);
        if (isset($organisation)) {
            $organisation->underwriter = $reportEntity->underwriter;
            $organisation->riskengineer = $reportEntity->riskengineer;
            $organisation->policy_expiring_soon = $reportEntity->policy_expiring_soon;
            $organisation->policy_expiring_description = $reportEntity->policy_expiring_description;
            $organisation->risk_grading_value = $reportEntity->risk_grading_value;
            $organisation->save();
        }
    }

    public function deleteSov($organisation_id)
    {
        $organisation = Organisation::find($organisation_id);
        $organisation->update(
            [
                'sov_cloudname' => null,
                'sov_filename' => null,
                'sov_filesize' => null,
            ]
        );

        return Response::json(
            [
                'response' => 'success',
                'message' => 'Organisation SOV deleted',
            ]
        );
    }

    public function update(Request $request)
    {
        set_time_limit(60);

        //$rules
        $rules = [
            'id' => 'required',
            'name' => 'required|regex:/(^[A-Za-z0-9 ]+$)+/',
            'country' => 'required',
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json(
                [
                    'response' => 'error',
                    'errors' => $validator->messages()->toArray(),
                ],
                200
            );
        } else {
            //update
            $organisation = Organisation::where('id', $request->get('id'))->first();

            if (isset($organisation)) {
                $data = $request->except('_token', 'liberty_id');

                $sectorHasChanged = $organisation->sector != $request->get('sector');

                //temporary broker contact
                unset($data['broker-contact-hidden']);

                if (isset($data['broker_id']) && (int)($data['broker_id']) > 0) {
                    Organisation::where('id', $data['id'])
                        ->update(
                            [
                                'broker_id' => $data['broker_id'],
                                'mga_scheme' => null,
                            ]
                        );
                }

                if (isset($data['mga_scheme']) && (int)($data['mga_scheme']) > 0) {
                    Organisation::where('id', $data['id'])
                        ->update(
                            [
                                'broker_id' => null,
                                'mga_scheme' => $data['mga_scheme'],
                            ]
                        );
                }

                if ($request->has('cover_type')) {
                    unset($data['cover_type']);
                }

                $policy_numbers_existings = OrganisationPolicy::where('organisation_id', '=', $organisation->id)->get();
                $policy_numbers = $data['policy_numbers'];

                unset($data['policy_numbers']);
                unset($data['policy_type_id']);

                if (isset($data['prop_doc'])) {
                    $propDoc = OrganisationPropDoc::firstOrCreate([
                        'organisation_id' => $request->get('id'),
                    ], $data['prop_doc'] + [
                        'organisation_id' => $request->get('id'),
                    ]);
    
                    $propDoc->update($data['prop_doc']);
                    unset($data['prop_doc']);
                }


                if (isset($data['cati_id']) && $data['cati_id'] == '') {
                    unset($data['cati_id']);
                }

                if (!isset($data['community_organisation'])) {
                    $data['community_organisation'] = '0';
                }


                if (isset($data['product_sector'])) {
                    $organisation->product_sector = $data['product_sector'];
                    $organisation->save();
                    unset($data['product_sector']);
                }
                if (isset($data['product_subsector'])) {
                    $organisation->product_subsector = $data['product_subsector'];
                    $organisation->save();
                    unset($data['product_subsector']);
                }

                $organisation->products()->delete();

                if (isset($data['organisation_products'])) {
                    foreach ($data['organisation_products'] as $product) {
                        $organisation->products()->create(
                            [
                                'slug' => $product,
                                'loss_ratio' => isset($data['loss-ratio-' . $product])
                                    ? $data['loss-ratio-' . $product]
                                    : null,
                                'created_at' => now(),
                                'updated_at' => now(),
                            ]
                        );
                        unset($data['loss-ratio-' . $product]);
                    }
                    unset($data['organisation_products']);
                }

                if (isset($data['claims'])) {
                    $organisation->claims()->sync($data['claims']);
                    unset($data['claims']);
                }

                // $organisation->contacts()->where('type', 'broker')->delete();

                // if (isset($data['contacts']['broker'])) {
                //     $organisation->contacts()->create([
                //         'type' => 'broker',
                //         'first_name' => $data['contacts']['broker']['first_name'],
                //         'last_name' => $data['contacts']['broker']['last_name'],
                //         'email' => $data['contacts']['broker']['email'],
                //         'phone' => $data['contacts']['broker']['phone'],
                //     ]);
                // }


                $organisation->contacts()->where('type', 'underwriter')->delete();

                if (isset($data['contacts']['underwriters'])) {
                    foreach ($data['contacts']['underwriters'] as $underwriter) {
                        $underwriter = is_numeric($underwriter)
                            ? $underwriter
                            : $underwriter['underwriter_id'];
                        $organisation->contacts()->create(
                            [
                                'type' => 'underwriter',
                                'user_id' => $underwriter,
                            ]
                        );
                    }
                }

                $organisation->contacts()->where('type', 'risk-engineer')->delete();

                if (isset($data['contacts']['risk-engineers'])) {
                    foreach ($data['contacts']['risk-engineers'] as $riskEngineer) {
                        $organisation->contacts()->create(
                            [
                                'type' => 'risk-engineer',
                                'user_id' => $riskEngineer,
                            ]
                        );
                    }
                }

                $organisation->contacts()->where('type', 'key-contact')->delete();

                if (isset($data['contacts']['key-contact'])) {
                    foreach ($data['contacts']['key-contact'] as $riskEngineer) {
                        $organisation->contacts()->create(
                            [
                                'type' => 'key-contact',
                                'user_id' => $riskEngineer,
                            ]
                        );
                    }
                }

                $organisation->contacts()->where('type', 'client-contact')->delete();

                if (isset($data['contacts']['client-contacts'])) {
                    // uses organisation_contacts (ids) and users table(other details),
                    foreach ($data['contacts']['client-contacts'] as $clientContact) {
                        $organisation->contacts()->create(
                            [
                                'type' => 'client-contact',
                                'user_id' => $clientContact,
                            ]
                        );
                    }
                }

                $organisation->contacts()->where('type', 'secondary-client-contact')->delete();

                if (isset($data['contacts']['secondary-client-contacts'])) {
                    foreach ($data['contacts']['secondary-client-contacts'] as $clientContact) {
                        $organisation->contacts()->create(
                            [
                                'type' => 'secondary-client-contact',
                                'user_id' => $clientContact,
                            ]
                        );
                    }
                }

                $organisation->contacts()->where('type', 'claims-liaison-manager')->delete();

                if (isset($data['contacts']['claims-liaison-manager'])) {
                    // uses organisation_contacts (ids) and users table(other details),
                    foreach ($data['contacts']['claims-liaison-manager'] as $clientContact) {
                        $organisation->contacts()->create(
                            [
                                'type' => 'claims-liaison-manager',
                                'user_id' => $clientContact,
                            ]
                        );
                    }
                }

                $organisation->contacts()->where('type', 'broker-user-contact')->delete();

                if (isset($data['contacts']['broker-user-contact'])) {
                    // uses organisation_contacts (ids) and users table(other details),
                    foreach ($data['contacts']['broker-user-contact'] as $clientContact) {
                        $organisation->contacts()->create(
                            [
                                'type' => 'broker-user-contact',
                                'user_id' => $clientContact,
                            ]
                        );
                    }
                }

                unset($data['contacts']);

                $uw_ids = $organisation->contacts()->where('type', 'underwriter')->get()->pluck('user_id')->all();
                $underwriter_policies = UnderwriterPolicies::whereIn(
                    'underwriter_id',
                    $uw_ids
                )->where('organisation_id', $organisation->id);
                $underwriter_policies->delete();

                if (isset($data['selected_uw'])) {
                    $now = Carbon::now('utc')->toDateTimeString();
                    $policydata = [];
                    foreach ($data['selected_uw'] as $key => $values) {
                        foreach ($values as $value) {
                            $uwdata = [
                                'organisation_id' => $organisation->id,
                                'underwriter_id' => $key,
                                'policy_id' => $value,
                                'created_at' => $now,
                                'updated_at' => $now,
                            ];
                            array_push($policydata, $uwdata);
                        }
                    }
                    if ($policydata) {
                        UnderwriterPolicies::insert($policydata);
                    }
                }

                unset($data['selected_uw']);

                // Check if organisation's previsico access has changed
                // We do this here before we alter the model's data
                $shouldQueuePrevisicoJob = (isset($data['has_previsico_access'])
                    && (intval($data['has_previsico_access']) != intval($organisation->has_previsico_access)
                    )
                );

                $uw_ids = $organisation->contacts()->where('type', 'underwriter')->get()->pluck('user_id')->all();
                $underwriter_policies = UnderwriterPolicies::whereIn(
                    'underwriter_id',
                    $uw_ids
                )->where('organisation_id', $organisation->id);
                $underwriter_policies->delete();

                if (isset($data['selected_uw'])) {
                    $now = Carbon::now('utc')->toDateTimeString();
                    $policydata = [];
                    foreach ($data['selected_uw'] as $key => $values) {
                        foreach ($values as $value) {
                            $uwdata = [
                                'organisation_id' => $organisation->id,
                                'underwriter_id' => $key,
                                'policy_id' => $value,
                                'created_at' => $now,
                                'updated_at' => $now,
                            ];
                            array_push($policydata, $uwdata);
                        }
                    }
                    if ($policydata) {
                        UnderwriterPolicies::insert($policydata);
                    }
                }

                unset($data['selected_uw']);

                foreach ($data as $key => $d) {
                    if ($key != 'cati_status') {
                        $organisation->$key = $d;
                    }
                }

                $hasActivePolicy = false;

                foreach ($policy_numbers as $type_id => $policy_number) {
                    if (
                        Carbon::createFromTimestamp($policy_number['expiry_date_of_cover'])->diffInDays(
                            Carbon::now(),
                            false
                        ) < 30
                    ) {
                        $hasActivePolicy = true;
                    }
                }

                $hasExistingDate = false;
                foreach ($policy_numbers as $type_id => $policy_number) {
                    $exists = false;

                    //Unset those that do not exist
                    foreach ($policy_numbers_existings as $key => $existing) {
                        if ($existing->policy_number == $policy_number['policy_number']) {
                            //Update Values
                            if (
                                Carbon::createFromTimestamp(strtotime($existing->expiry_date_of_cover))->diffInDays(
                                    Carbon::now(),
                                    false
                                ) < 30
                            ) {
                                $hasExistingDate = true;
                            }
                            $existing->inception_date_of_cover = Carbon::createFromTimestamp($policy_number['inception_date_of_cover']);
                            $existing->expiry_date_of_cover = Carbon::createFromTimestamp($policy_number['expiry_date_of_cover']);

                            $existing->premium = isset($policy_number['premium'])
                                ? $policy_number['premium']
                                : null;
                            $existing->loss_ratio = isset($policy_number['loss_ratio'])
                                ? $policy_number['loss_ratio']
                                : null;

                            $existing->bound_lead = isset($policy_number['bound_lead'])
                                ? $policy_number['bound_lead']
                                : null;
                            $existing->bound_follow = isset($policy_number['bound_follow'])
                                ? $policy_number['bound_follow']
                                : null;

                            if (($policy_number['renewal'] ?? 0) != 0 && Carbon::createFromTimestamp($policy_number['renewal'])->isValid()) {
                                $existing->renewal = Carbon::createFromTimestamp($policy_number['renewal']);
                            }
                            // $existing->renewal = $policy_number['renewal'] != 0 ? Carbon::createFromTimestamp($policy_number['renewal']) : '0000-00-00 00:00:00';
                            $existing->policy_type_id = $type_id;
                            $existing->risk_grading   = $policy_number['risk_grading'] ?? null;
                            $existing->save();
                            unset($policy_numbers_existings[$key]);
                            $exists = true;
                        }
                    }

                    if ($exists === false) {
                        //Create new
                        OrganisationPolicy::create(
                            [
                                'organisation_id' => $organisation->id,
                                'policy_number' => $policy_number['policy_number'],
                                'inception_date_of_cover' => Carbon::createFromTimestamp($policy_number['inception_date_of_cover']),
                                'expiry_date_of_cover' => Carbon::createFromTimestamp($policy_number['expiry_date_of_cover']),
                                'premium' => $policy_number['premium'],
                                'loss_ratio' => $policy_number['loss_ratio'],
                                'bound_lead' => $policy_number['bound_lead'] ?? null,
                                'bound_follow' => $policy_number['bound_follow'] ?? null,
                                'renewal' => $this->getRenewalDate($policy_number),
                                'policy_type_id' => $type_id,
                            ]
                        );
                    }

                    if (isset($policy_number['policy_document_id']) && isset($policy_number['new_document_uploaded']) && $policy_number['new_document_uploaded']) {
                        $policydocument = PolicyDocumentModel::find($policy_number['policy_document_id']);
                        if ($policydocument) {
                            $policydocument->delete();
                        }
                    }

                    if (isset($policy_number['document_title'])) {
                        $policyDocData = [];
                        $policyDocData['name'] = $policy_number['document_title'];
                        $policyDocData['description'] = '';
                        $policyDocData['document_title'] = $policy_number['document_title'];
                        $policyDocData['document_store_name'] = $policy_number['document_store_name'];
                        $policyDocData['organisation_id'] = $organisation->id;
                        $policyDocData['document_type_id'] = $type_id;
                        $policy_doc = PolicyDocumentModel::updateOrCreate(
                            [
                                'organisation_id' => $organisation->id,
                                'document_type_id' => $type_id,
                            ],
                            $policyDocData
                        );
                    }

                    $expiry_date_of_cover = $policy_number['expiry_date_of_cover'];

                    // Check if it's a responsible business organisation
                    $isResponsibleBusiness = ($policy_number['policy_number'] == '999999' && $policy_number['premium'] == '1' && $policy_number['loss_ratio'] == '1')
                        ? true
                        : false;
                    if ($isResponsibleBusiness) {
                        $expiry_date_of_cover = time() + (365 * 24 * 60 * 60);
                    }
                }


                $usersWithErrors = [];
                if (isset($data['cati_status']) && strtolower($data['cati_status']) != $organisation->getOriginal('cati_status')) {
                    if (strtolower($data['cati_status']) == 'active') {
                        $cati = new Cati();
                        if (!$hasActivePolicy) {
                            return Response::json(
                                [
                                    'response' => 'error',
                                    'message' => '<b>Organisation update failed!</b><br/>You can not activate Cati access for this organisation unless you have at least one policy active. Please extend the expiry date of this policy to enable Cati.',
                                ]
                            );
                        }

                        if (!isset($data['cati_id']) || $data['cati_id'] == '') {
                            try {
                                $response = $cati->createClient(
                                    [
                                        'Name' => $data['name'],
                                        'EmailAddress' => $data['email'],
                                        'TelephoneNumber' => $data['phone'],
                                        'TelephoneNumberExt' => '',
                                        'Street' => '',
                                        'TownCity' => '',
                                        'County' => '',
                                        'Postcode' => '',
                                    ]
                                );

                                if ($response && $response->StatusCode == 'CC00') {
                                    $organisation->cati_id = $response->ClientID;
                                } else {
                                    return Response::json(
                                        [
                                            'response' => 'error',
                                            'message' => sprintf(
                                                $this->catiErrorMessage,
                                                $response->StatusCode,
                                                $response->StatusDescription
                                            ),
                                        ]
                                    );
                                }
                            } catch (Exception $e) {
                                Log::error($e);
                                return Response::json(
                                    [
                                        'response' => 'error',
                                        'message' => sprintf($this->catiErrorMessage, $e->getCode(), $e->getMessage()),
                                    ]
                                );
                            }
                        } else {
                            try {
                                $response = $cati->enableClient($data['cati_id']);
                                if ($response && $response->StatusCode != 'EC00') {
                                    return Response::json(
                                        [
                                            'response' => 'error',
                                            'message' => sprintf(
                                                $this->catiErrorMessage,
                                                $response->StatusCode,
                                                $response->StatusDescription
                                            ),
                                        ]
                                    );
                                }

                                /* should be queued */
                                $catiUsers = $organisation->users()->where(
                                    'cati_status',
                                    'active'
                                )->whereNotNull('cati_id')->get();
                                foreach ($catiUsers as $user) {
                                    $response = $cati->enableClientUser($user->cati_id);

                                    if ($response && $response->StatusCode != 'EU00' && $response->StatusCode != 'EU03') {
                                        $user->cati_status = 'suspense';
                                        $user->save();
                                        array_push($usersWithErrors, $user->email);
                                    }
                                }
                            } catch (Exception $e) {
                                Log::error($e);
                                return Response::json(
                                    [
                                        'response' => 'error',
                                        'message' => sprintf($this->catiErrorMessage, $e->getCode(), $e->getMessage()),
                                    ]
                                );
                            }
                        }
                    } elseif (strtolower($data['cati_status']) == 'suspense') {
                        try {
                            $cati = new Cati();
                            $response = $cati->suspendClient($data['cati_id']);

                            if ($response && $response->StatusCode != 'DC00') {
                                return Response::json(
                                    [
                                        'response' => 'error',
                                        'message' => sprintf(
                                            $this->catiErrorMessage,
                                            $response->StatusCode,
                                            $response->StatusDescription
                                        ),
                                    ]
                                );
                            }
                        } catch (Exception $e) {
                            Log::error($e);
                            return Response::json(
                                [
                                    'response' => 'error',
                                    'message' => sprintf($this->catiErrorMessage, $e->getCode(), $e->getMessage()),
                                ]
                            );
                        }
                    }
                    $organisation->cati_status = $data['cati_status'];
                }


                //Any left, unset
                foreach ($policy_numbers_existings as $policy_numbers_existing) {
                    $policy_numbers_existing->delete();
                    PolicyDocumentModel::where(
                        'organisation_id',
                        $policy_numbers_existing['organisation_id']
                    )->where(
                        'document_type_id',
                        $policy_numbers_existing['policy_type_id']
                    )->delete();
                }

                // if ($organisation->tpid == '' || is_null($organisation->tpid)) {
                //     $sm_account = config('app.sm.org_id_prefix') . $organisation->id;
                // } else {
                //     $sm_account = $organisation->tpid;
                // }

                $user_limit = '500';

                // Exception for Park Holidays
                if ($organisation->id == '379') {
                    $user_limit = '999';
                }

                if ($organisation->id == '454') {
                    $user_limit = '2000';
                }

                // exception for Royal Horticultural Society
                if ($organisation->id == '2628') {
                    $user_limit = '5000';
                }

                // exception for Cancer Research UK
                if ($organisation->id == '2790') {
                    $user_limit = '5000';
                }

                // exception for AFE GROUP LTD
                if ($organisation->id == '38') {
                    $user_limit = '1000';
                }

                // exception for Arena Racing Company
                if ($organisation->id == '3') {
                    $user_limit = '4000';
                }

                // exception for WDFG
                if ($organisation->id == '2736') {
                    $user_limit = '4000';
                }

                // exception for The Midcounties Co operative Limited
                if ($organisation->id == '2679') {
                    $user_limit = '1500';
                }

                // For Access Group LMS
                // If org has active policy, restore org users
                // Else, archive org users
                $updateUserAction = $hasActivePolicy ? 'restore' : 'archive';
                UpdateUserStatusJob::dispatch($organisation, $updateUserAction);

                // try {
                //     $this->soapWrapper->add('client', function ($service) {
                //         $service->wsdl('https://libertymutual.safetylearning.co.uk/api/1.3/ClientManagement?wsdl');
                //     });

                //     $data = [
                //         'serviceCredentials' => [
                //             'username' => config('app.sm.username'),
                //             'password' => config('app.sm.password'),
                //             'account' => '',
                //         ],
                //         'clients' => [
                //             'item' =>
                //             [
                //                 'id' => $sm_account,
                //                 'name' => $organisation->name,
                //                 'enabled' => '1',
                //                 'url' => $organisation->safetymedia_url,
                //                 'licensed' => '1',
                //                 'userLimit' => $user_limit,
                //                 'expiryDate' => ($expiry_date_of_cover) + (30 * 24 * 60 * 60),
                //                 'units' => '',
                //             ],
                //         ],
                //     ];

                //     // Using the add service
                //     $this->soapWrapper->call('client.AddClient', [$data]);

                //     // Using the edit service
                //     $this->soapWrapper->call('client.EditClient', [$data]);

                //     $data1 = [
                //         'serviceCredentials' => [
                //             'username' => config('app.sm.username'),
                //             'password' => config('app.sm.password'),
                //             'account' => $sm_account,
                //         ],
                //         'departments' => [
                //             'item' =>
                //             [
                //                 'id' => $sm_account,
                //                 'name' => $organisation->name,
                //                 'parentName' => '',
                //             ],
                //         ],
                //     ];

                //     // Using the added service
                //     $this->soapWrapper->call('client.EditDepartment', [$data1]);

                //     $this->soapWrapper->add('tra', function ($serviceTraining) {
                //         $serviceTraining->wsdl('https://libertymutual.safetylearning.co.uk/api/1.3/TrainingManagement?wsdl');
                //     });

                //     $sector = Sector::find($organisation->sector);

                //     $data2 = [
                //         'serviceCredentials' => [
                //             'username' => config('app.sm.username'),
                //             'password' => config('app.sm.password'),
                //             'account' => $sm_account,
                //         ],
                //         'trainingPlans' => [
                //             'item' =>
                //             [
                //                 'id' => $sm_account,
                //                 'name' => $sector->handle,
                //                 'description' => $sector->handle,
                //             ],
                //         ],
                //     ];

                //     // Using the added service
                //     $this->soapWrapper->call('tra.EditTrainingPlan', [$data2]);

                //     $courses = SectorCourses::where('sector_id', '=', $organisation->sector)->get();
                //     $trainingPlanCoursesArr = [];
                //     foreach ($courses as $course) {
                //         array_push(
                //             $trainingPlanCoursesArr,
                //             ['trainingPlanID' => $sm_account, 'courseID' => $course->course_id]
                //         );
                //         $data3 = [
                //             'serviceCredentials' => [
                //                 'username' => config('app.sm.username'),
                //                 'password' => config('app.sm.password'),
                //                 'account' => $sm_account,
                //             ],
                //             'trainingPlanCourses' => $trainingPlanCoursesArr,
                //         ];

                //         // Using the added service
                //         $this->soapWrapper->call('tra.AddCourseToTrainingPlan', [$data3]);
                //         //$this->soapqueue->queue(config('app.sm.username'), config('app.sm.password'), config('app.sm.org_id_prefix').$organisation->id, config('app.sm.org_id_prefix').$organisation->id, $course->course_id);
                //     }
                // } catch (Exception $e) {
                //     $soapWrapperErrorMessage = $e->getMessage();
                // }

                if (isset($data['broker_id']) && (int)($data['broker_id']) > 0) {
                    Organisation::where('id', $data['id'])
                        ->update(
                            [
                                'broker_id' => $data['broker_id'],
                                'mga_scheme' => null,
                            ]
                        );
                }

                if (isset($data['mga_scheme']) && (int)($data['mga_scheme']) > 0) {
                    Organisation::where('id', $data['id'])
                        ->update(
                            [
                                'broker_id' => null,
                                'mga_scheme' => $data['mga_scheme'],
                            ]
                        );
                }

                $clientClaimId = ClientClaimId::where('organisation_id', $organisation->id)->first();
                if (isset($clientClaimId)) {
                    $clientClaimId->liberty_id = $request->get('liberty_id') ?? '';
                } else {
                    $clientClaimId = new ClientClaimId();
                    $clientClaimId->name = $organisation->name;
                    $clientClaimId->organisation_id = $organisation->id;
                    $clientClaimId->liberty_id = $request->get('liberty_id') ?? '';
                }

                $clientClaimId->save();

                $organisation->save();

                // If sector has changed, we also need to update the Access Group LMS's sector
                // of every user that belongs to that org
                if ($sectorHasChanged) {
                    UpdatePersonSectorJob::dispatch($organisation);
                }

                if (!empty($shouldQueuePrevisicoJob)) {
                    // We queue a generate job so that this org's
                    // locations will be added to the asset list
                    GenerateAssetsList::queueJob();
                }

                $org_id = $request->get('id');
                if ($request->has('cover_type')) {
                    OrganisationCover::where('organisation_id', '=', $org_id)->delete();
                    foreach (explode(',', $request->get('cover_type')) as $cover_type) {
                        OrganisationCover::create(
                            [
                                'organisation_id' => $org_id,
                                'cover_type' => $cover_type,
                            ]
                        );
                    }
                }

                if (count($usersWithErrors) > 0) {
                    return Response::json(
                        [
                            'response' => 'warning',
                            'message' => sprintf(
                                '<b>Update successful!</b><br/><i>Cati is now re-enabled for this organisations.</i><br/>However the automatic user activation failed for: <br/>%s',
                                implode(',', $usersWithErrors)
                            ),
                        ]
                    );
                }
                if (!$hasActivePolicy && $hasExistingDate && $request->has('cati_status') && strtolower($request->get('cati_status')) == 'active') {
                    $cati = new Cati();
                    $response = $cati->suspendClient($request->get('cati_id'));
                    Organisation::where('id', $request->get('id'))->update(['cati_status' => 'suspense']);

                    return Response::json(
                        [
                            'response' => 'error',
                            'message' => '<b>Warning!</b><br/><i>Your update to the policy expiry date has been successfully saved, but the Cati access for this organisation and its users has been suspended.</i><br/>If you want to allow this organisation to access Cati again, please update the policy expiry date to any day in the future and set Cati access option to Active',
                        ]
                    );
                } elseif (!$hasActivePolicy && $request->has('cati_status') && strtolower($request->get('cati_status')) == 'active') {
                    $cati = new Cati();
                    $response = $cati->suspendClient($request->get('cati_id'));
                    Organisation::where('id', $request->get('id'))->update(['cati_status' => 'suspense']);

                    return Response::json(
                        [
                            'response' => 'error',
                            'message' => '<b>Organisation update failed!</b><br/>You can not activate Cati access for this organisation unless you have at least one policy active. Please extend the expiry date of this policy to enable Cati.',
                        ]
                    );
                }
                //exit;

                //Sync function for report

                $reportEntity = $this->formatReportEntity($organisation);
                $this->updateOrganisation($reportEntity);

                Cache::put('organisation_last_updated', now()->timestamp);

                return Response::json(
                    [
                        'response' => 'success',
                        'message' => 'Organisation Updated',
                    ]
                );
            } else {
                return Response::json(
                    [
                        'response' => 'error',
                        'message' => 'No valid Organisation',
                    ]
                );
            }
        }
    }

    public function delete(Request $request)
    {
        $id = $request->get('id');
        if (isset($id)) {
            $organisation = Organisation::find($id);
            if (isset($organisation)) {
                // $this->soapWrapper->add(
                //     'client',
                //     function ($service) {
                //         $service
                //             ->wsdl('https://libertymutual.safetylearning.co.uk/api/1.3/ClientManagement?wsdl');
                //     }
                // );

                // if ($organisation->tpid == '' || is_null($organisation->tpid)) {
                //     $sm_account = config('app.sm.org_id_prefix') . $organisation->id;
                // } else {
                //     $sm_account = $organisation->tpid;
                // }

                // $data = [
                //     'serviceCredentials' => [
                //         'username' => config('app.sm.username'),
                //         'password' => config('app.sm.password'),
                //         'account' => '',
                //     ],
                //     'clients' => [
                //         'item' => [
                //             'id' => $sm_account,
                //             'name' => $organisation->name,
                //             'enabled' => '0',
                //             'url' => $organisation->safetymedia_url,
                //             'licensed' => '1',
                //             'userLimit' => '500',
                //             'expiryDate' => ($organisation->expiry_date_of_cover) + (30 * 24 * 60 * 60),
                //             'units' => '',
                //         ],
                //     ],
                // ];
                if ($organisation->cati_status == 'active' && $organisation->cati_id) {
                    $cati = new Cati();
                    try {
                        $response = $cati->suspendClient($organisation->cati_id);
                        if ($response && $response->StatusCode !== 'DC00') {
                            return Response::json(
                                [
                                    'response' => 'error',
                                    'message' => sprintf(
                                        $this->catiErrorMessage,
                                        $response->StatusCode,
                                        $response->StatusDescription
                                    ),
                                ]
                            );
                        }
                    } catch (Exception $e) {
                        return Response::json(
                            [
                                'response' => 'error',
                                'message' => sprintf($this->catiErrorMessage, $e->getCode(), $e->getMessage()),
                            ]
                        );
                    }
                }
                // // Using the added service
                // $add_org_to_sm = $this->soapWrapper->call('client.EditClient', [$data]);

                $shouldGenerateAssetList = (!empty($organisation->has_previsico_access));
                $organisation->delete();

                if ($shouldGenerateAssetList) {
                    // Generate new asset list for previsico
                    GenerateAssetsList::queueJob();
                }

                Cache::put('organisation_last_updated', now()->timestamp);

                return Response::json(
                    [
                        'response' => 'success',
                        'message' => 'Organisation Deleted',
                    ]
                );
            }
        }

        return Response::json(
            [
                'response' => 'error',
                'message' => 'Could not delete organisation',
            ]
        );
    }


    //Find

    public function findOrg($id)
    {
        $organisation = Organisation::where('name', '=', urldecode($id))->first();
        if (!isset($organisation)) {
            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'No Organisation by that id',
                ]
            );
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => $organisation,
            ]
        );
    }

    public function brokerUsersOptions($id)
    {
        $organisation = Organisation::with(['mgascheme', 'broker_organisations'])->where('id', $id)->first();
        $brokers = [];

        if ($organisation->mgascheme) {
            $brokers = BrokerUser::where('broker_id', $organisation->mgascheme->broker_id)->get()->pluck(
                'fullname',
                'id'
            )->all();
        } elseif ($organisation->broker_organisations) {
            $brokers = BrokerUser::where('broker_id', $organisation->broker_organisations->id)->get()->pluck(
                'fullname',
                'id'
            )->all();
        }

        return Response::json($brokers);
    }

    public function getBrokerUserByMGA($mga_id, $broker_id)
    {
        $brokers = [];
        if ($mga_id > 0) {
            $mga = MgaScheme::where('id', $mga_id)->get(['broker_id'])->first();
            $broker_id = $mga->broker_id;
        }

        if ($broker_id > 0) {
            $brokers = BrokerUser::where('broker_id', $broker_id)->get()->pluck(
                'fullname',
                'id'
            )->all();
        }
        return Response::json($brokers);
    }

    public function options(Request $request)
    {
        if ($request->has('org_type') && $request->get('org_type') == 'is_aspen') {
            $aspen_branches = Branch::where('is_aspen', 1)->pluck('id')->all();
            $result = Organisation::whereIn('liberty_branch', $aspen_branches)->orderBy('name', 'asc')->pluck(
                'name',
                'id'
            )->all();
        } elseif ($request->has('client_users')) {
            $result = DB::table('organisations')->select(['id', 'sector', 'name'])
                ->orderBy('name', 'asc')
                ->get();
        } else {
            $result = Organisation::orderBy('name', 'asc')->pluck('name', 'id')->all();
        }

        $result = isset($result) || !empty($result)
            ? $result
            : [];

        return Response::json($result);
    }

    public function brokerOptions($id)
    {
        $mgas = MgaScheme::where('broker_id', $id)->pluck('id')->all();
        $organisations = Organisation::whereIn('mga_scheme', $mgas)->pluck('id')->all();

        $result = Organisation::whereIn('id', $organisations)->orderBy('name', 'asc')->pluck('name', 'id')->all();
        $result = isset($result) || !empty($result)
            ? $result
            : [];

        return Response::json($result);
    }

    public function optionsLMS()
    {
        $data = [];

        $organisations = Organisation::orderBy('name', 'asc')->pluck('name', 'id')->all();
        $courses = Course::get();
        //$data['courses'] = isset($courses) || !empty($courses) ? $courses : [];
        //$data['organizations'] = isset($organisations) || !empty($organisations) ? $organisations : [];
        $organisations = isset($organisations) || !empty($organisations)
            ? $organisations
            : [];

        return Response::json(
            [
                'response' => 'success',
                'data' => $organisations,
            ]
        );
    }

    //TEST function

    // public function add_tp_to_org() {
    //     // Using the added service
    //         $this->soapWrapper->add(function ($servicetraining) {
    //             $servicetraining
    //                 ->name('tra')
    //                 ->wsdl('https://libertymutual.safetylearning.co.uk/api/1.3/TrainingManagement?wsdl');
    //         });

    //         $sector = '2';
    //         $id = '11';

    //        $courses = SectorCourses::where('sector_id', '=', $sector)->get();
    //        $trainingPlanCoursesArr = [];
    //        foreach ($courses as $course) {
    //         array_push($trainingPlanCoursesArr, ['trainingPlanID' =>    config('app.sm.org_id_prefix').$id,'courseID' => $course->course_id]);
    //        }
    //         //print_r($trainingPlanCoursesArr);exit;
    //         $data3 = [
    //             'serviceCredentials' => ['username' => config('app.sm.username'),
    //                                      'password' => config('app.sm.password'),
    //                                      'account'    => config('app.sm.org_id_prefix').$id
    //                                     ],
    //             'trainingPlanCourses'=>
    //                                         /*[
    //                                             'trainingPlanID'     =>    config('app.sm.org_id_prefix').$id,
    //                                             'courseID'            =>  1001
    //                                         ],
    //                                         [
    //                                             'trainingPlanID'     =>    config('app.sm.org_id_prefix').$id,
    //                                             'courseID'            =>  1003
    //                                         ]*/
    //                     $trainingPlanCoursesArr

    //         ];

    //         // Using the added service
    //         $this->soapWrapper->service('tra', function ($servicetraining) use ($data3) {
    //             //var_dump($service->getFunctions());
    //             $add_course_to_org = $servicetraining->call('AddCourseToTrainingPlan', [$data3]);
    //             print_r($add_course_to_org);
    //         });
    // }#

    public function showPolicyNumbers($organisation_id)
    {
        $organisation = Organisation::find($organisation_id);

        if ($organisation != null) {
            $policy_numbers = OrganisationPolicy::where('organisation_id', '=', $organisation->id)
                ->get();

            foreach ($policy_numbers as $key => $policy_number) {
                $policy_numbers[$key]->type = $policy_number->type;
            }
            $mga_scheme = (object)[];
            if (!is_null($organisation->mga_scheme)) {
                $mga_scheme = $organisation->mgascheme;
            }

            return response()->json([
                'response'             => 'success',
                'data'                 => $policy_numbers,
                'organisation_details' => $organisation,
                'mga_scheme'           => $mga_scheme,
                'total'                => count($policy_numbers),
            ]);
        }

        return Response::json(['response' => 'error', 'message' => 'Organisation does not exist']);
    }

    public function themeOptions()
    {
        $themes = Theme::select('name')->get();

        foreach ($themes as $theme) {
            $theme_output[$theme->name] = $theme->name;
        }

        return Response::json($theme_output);
    }

    public function riskGradings($id)
    {
        $risk_gradings = OrganisationLocations::where('organisation_id', $id);

        $risk_grading_values = $risk_gradings->with('gradings')->get();

        $location_id_key = [];

        $locations = $risk_gradings->get();

        foreach ($locations as $location) {
            array_push($location_id_key, $location->id);
        }

        $attributes = RiskGrading::with('policy')
            ->whereIn('organisation_location_id', $location_id_key)
            ->get();

        return Response::json(
            [
                'risk_gradings' => $risk_grading_values,
                'attributes' => $attributes,
            ]
        );
    }

    private function updateReportDataForOrganisation()
    {
        $organisations = Organisation::all();
        foreach ($organisations as $org) {
            $reportEntity = $this->formatReportEntity($org);
            $this->updateOrganisation($reportEntity);
        }
    }

    private function getRenewalDate(array $policy): Carbon
    {
        if (!empty($policy['renewal']) && is_numeric($policy['renewal'])) {
            return Carbon::createFromTimestamp($policy['renewal'])->addHour();
        }

        return Carbon::now()->addHour();
    }

    public function getOrgUsersWithMaxSurveys($maxSurveys)
    {
        $query='select t.organisation_id, u.id as `user_id`,
        (select sector from organisations where id=t.organisation_id) as `sector_id`
        FROM (
            select organisation_id, count(organisation_id) as `count` from surveys group by organisation_id order by count(organisation_id) desc
        ) AS t left join users u on t.organisation_id=u.organisation_id
        where t.count >='.$maxSurveys.' and u.deleted_at is null order by organisation_id';

        $data=DB::select(DB::raw($query));
        return $data;
    }

    public function getOrgWitSurveysOrRiskEngineerType()
    {
        $query='SELECT DISTINCT t.organisation_id
        FROM (
            SELECT organisation_id, count(organisation_id) AS `count` FROM surveys GROUP BY organisation_id
        ) AS t JOIN organisation_contacts oc ON t.organisation_id=oc.organisation_id
        WHERE t.count >=4 OR oc.type="risk-engineer" ORDER BY t.organisation_id';

        return DB::select(DB::raw($query));
    }

    public function getBoundStatus($id)
    {
        try {
            $organization = Organisation::findOrFail($id);
            
            $latestLog = OrganisationLog::where('organisation_id', $id)
                ->where('bound_at', '!=', null)
                ->latest()
                ->first();

            return response()->json([
                'success' => true,
                'data' => [
                    'is_bound'           => $organization->bound,
                    'bound_date'         => $latestLog?->bound_at,
                    'pre_inception_date' => $latestLog?->pre_inception_at,
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching organization binding status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getRenewals($id, $startDate = null, $endDate = null)
    {
        if (!$startDate) {
            $startDate = now()->format('Y-m-d');
        }

        if (!$endDate) {
            $endDate = now()->format('Y-m-d');
        }

        $organisation = Organisation::findOrFail($id);
        $renewals = $organisation->policies()
            ->renewalsBetween($startDate, $endDate)
            ->get();
        return response()->json($renewals);
    }
}
