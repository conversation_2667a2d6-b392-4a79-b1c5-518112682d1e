<?php

namespace App\Http\Controllers\Api\V1\Previsico;

use App\Http\Controllers\Controller;
use App\Models\Previsico\PrevisicoApi;
use App\Models\Previsico\PrevisicoAsset;
use App\Models\Previsico\PrevisicoAssetAccess;
use App\Models\Previsico\PrevisicoAssetStations;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;

class FloodMapController extends Controller
{
    public function index($organisationId, $userId)
    {
        $assets = PrevisicoAsset::where('organisation_id', $organisationId)
            ->get();

        $allowedLocations = PrevisicoAssetAccess::where('user_id', $userId)
            ->get()
            ->pluck('previsico_asset_id');

        $rows = [];
        foreach ($assets as $asset) {
            if ($allowedLocations && !in_array($asset->id, $allowedLocations->toArray())) {
                continue;
            }

            if (is_null($asset->rs_nearest_station_ref) || is_null($asset->sw_nearest_station_ref)) {
                $station = $this->getNearestStation($asset);
                if (!empty($station)) {
                    $rows[] = $station;
                }
            } else {
                $res = $this->getResponse($asset);
                if (!empty($res)) {
                    $rows[] = $res;
                }
            }
        }

        return Response::json($rows);
    }

    protected function getNearestStation($asset)
    {
        $latitude = $asset->latitude;
        $longitude = $asset->longitude;
        if (!$latitude || !$longitude) {
            return [];
        }

        $params = [
            'lat'   => $latitude,
            'long'  => $longitude,
            'dist'  => config('app.previsico.flood_map.distance')
        ];

        $stations = PrevisicoApi::get('stations', null, $params);
        if (empty($stations)) {
            // Low risk
            return [];
        }

        // Get the nearest Groundwater and Stage/Downstage stations among the results using the Haversine formula
        list ($rsQualifier, $swQualifier, $lat, $lon) = $this->getNearestGroundwaterAndStageStations(
            $stations->items,
            $latitude,
            $longitude
        );

        // Store the each nearest station’s stationReference in the asset
        $asset->rs_nearest_station_ref = $rsQualifier;
        $asset->sw_nearest_station_ref = $swQualifier;

        // 1. Get each nearest station’s latest reading and stageScale using the Individual Station API
        $rsReadings = null;
        if ($rsQualifier) {
            $rsReadingsStation = PrevisicoApi::get('stations', $rsQualifier . '.json');
            if ($rsReadingsStation->items) {
                $rsReadings = $rsReadingsStation;
            }
        }

        $swReadings = null;
        if ($swQualifier) {
            $swReadingStation = PrevisicoApi::get('stations', $swQualifier . '.json');
            if ($swReadingStation->items) {
                $swReadings = $swReadingStation;
            }
        }

        if (!$rsQualifier && !$swQualifier) {
            return [];
        }

        // 2. Store each nearest station’s stationReference, latest RS and SW readings, and stageScale data
        // 3. Calculate the difference between the nearest station’s stageScale.typicalRangeHigh and stageScale.typicalRangeLow
        // 4. Based on the calculated value above, we will determine if the latest reading is % near the typicalRangeHigh value
        // 5. The value calculated in step i will then be mapped to a risk ranking (60% and below = low, 60-75% = med, > 75% = high. Not final yet.)
        // 6. Store the calculated risk ranking in the asset (rs_risk_ranking_raw, rs_risk_ranking, sw_risk_ranking, sw_risk_ranking)
        $asset = $this->upsertRSAssetStation($asset, $rsReadings);
        $asset = $this->upsertSWAssetStation($asset, $swReadings);
        $asset->last_reading_date = date('Y-m-d H:i:s', time());
        $asset->update();

        return $this->getResponse($asset);
    }

    protected function getResponse($asset)
    {
        $rsData = $asset->rsStation;
        $swData = $asset->swStation;
        $location = $asset->organisationLocation;

        if (!$location) {
            return [];
        }

        $height = $swData->latest_reading_value ?? 0;

        $rsRanking = $asset->rs_risk_ranking ?? 'low';
        $swRanking = $asset->sw_risk_ranking ?? 'low';
        $marker = 'marker_' . strtoupper(
            substr($rsRanking, 0, 1) .
            substr($swRanking, 0, 1)
        );

        return [
            'name' => $location->location_name,
            'address' => $location->address_line_1,
            'marker' => $marker,
            'latitude' => $asset->latitude,
            'longitude' => $asset->longitude,
            'reading_date' => 'Last updated: ' . date('g:ia \o\n j M Y', strtotime($asset->last_reading_date)),
            'rs' => [
                'station_ref'                   => $asset->rs_nearest_station_ref,
                'risk_ranking_raw'              => $asset->rs_risk_ranking_raw ?? 0,
                'risk_ranking'                  => $rsRanking,
                'latest_reading_date'           => $rsData->latest_reading_date ?? null,
                'latest_reading_value'          => $rsData->latest_reading_value ?? null,
                'stagescale_typical_range_high' => $rsData->stagescale_typical_range_high ?? null,
                'stagescale_typical_range_low'  => $rsData->stagescale_typical_range_low ?? null,

            ],
            'sw' => [
                'station_ref'                   => $asset->sw_nearest_station_ref,
                'risk_ranking_raw'              => $asset->sw_risk_ranking_raw ?? 0,
                'risk_ranking'                  => $swRanking,
                'latest_reading_date'           => $swData->latest_reading_date ?? null,
                'latest_reading_value'          => $swData->latest_reading_value ?? null,
                'stagescale_typical_range_high' => $swData->stagescale_typical_range_high ?? null,
                'stagescale_typical_range_low'  => $swData->stagescale_typical_range_low ?? null,
                'latitude'                      => $asset->latitude,
                'longitude'                     => $asset->longitude,
                'height'                        => $height,
            ]
        ];
    }

    protected function upsertSWAssetStation($asset, $swReadings)
    {
        if (!$swReadings) {
            return $asset;
        }

        if (!isset($swReadings->items->stageScale)) {
            return $asset;
        }

        $measures = $swReadings->items->measures;
        $stationRef = null;
        if (is_array($measures)) {
            foreach ($measures as $measure) {
                if ($measure->qualifier == 'Groundwater'
                    && isset($measure->latestReading)
                    && !is_string($measure->latestReading)) {
                    $latestReading = $measure->latestReading;
                    $stationRef = $measure->stationReference;
                    break;
                }
            }
        } else {
            $latestReading = $measures->latestReading;
            $stationRef = $measures->stationReference;
        }

        if (!$stationRef) {
            return $asset;
        }

        $swTypicalRangeHigh = $swReadings->items->stageScale->typicalRangeHigh ?? 0;
        $swTypicalRangeLow  = $swReadings->items->stageScale->typicalRangeLow ?? 0;
        $swReadingValue     = $latestReading->value ?? 0;

        $swStation = PrevisicoAssetStations::updateOrCreate(
            [
                'station_ref' => $stationRef
            ],
            [
                'station_ref' => $stationRef,
                'latest_reading_date' => date('Y-m-d H:i:s', strtotime($latestReading->dateTime)),
                'latest_reading_value' => $swReadingValue,
                'stagescale_typical_range_high' => $swTypicalRangeHigh,
                'stagescale_typical_range_low' => $swTypicalRangeLow,
                'type' => 'SW',
            ]);

        $asset->sw_nearest_station_latest_reading = $swStation->id;

        $swRiskRankRaw = ($swReadingValue / ($swTypicalRangeHigh-$swTypicalRangeLow)) * 100;
        $swRiskRank = $this->computeRiskRanking($swRiskRankRaw);

        $asset->sw_risk_ranking_raw = $swRiskRankRaw;
        $asset->sw_risk_ranking = $swRiskRank;

        return $asset;
    }

    protected function upsertRSAssetStation($asset, $rsReadings)
    {
        if (!$rsReadings) {
            return $asset;
        }

        if (!isset($rsReadings->items->stageScale)) {
            return $asset;
        }

        $measures = $rsReadings->items->measures;
        $stationRef = null;
        if (is_array($measures)) {
            foreach ($measures as $measure) {
                if (($measure->qualifier == 'Stage'
                    || $measure->qualifier == 'Downstream Stage')
                    && (isset($measure->latestReading) && !is_string($measure->latestReading))) {
                    $latestReading = $measure->latestReading;
                    $stationRef = $measure->stationReference;
                    break;
                }
            }
        } else {
            $latestReading = $measures->latestReading;
            $stationRef = $measures->stationReference;
        }

        if (!$stationRef) {
            return $asset;
        }

        $rsTypicalRangeHigh = $rsReadings->items->stageScale->typicalRangeHigh ?? 0;
        $rsTypicalRangeLow  = $rsReadings->items->stageScale->typicalRangeLow ?? 0;
        $rsReadingValue     = $latestReading->value ?? 0;

        $rsStation = PrevisicoAssetStations::updateOrCreate(
            [
                'station_ref' => $stationRef
            ],
            [
                'station_ref' => $stationRef,
                'latest_reading_date' => date('Y-m-d H:i:s', strtotime($latestReading->dateTime)),
                'latest_reading_value' => $rsReadingValue,
                'stagescale_typical_range_high' => $rsTypicalRangeHigh,
                'stagescale_typical_range_low' => $rsTypicalRangeLow,
                'type' => 'RS'
            ]
        );

        $asset->rs_nearest_station_latest_reading = $rsStation->id;

        $rsRiskRankRaw = ($rsReadingValue / ($rsTypicalRangeHigh - $rsTypicalRangeLow)) * 100;
        $rsRiskRank = $this->computeRiskRanking($rsRiskRankRaw);

        $asset->rs_risk_ranking_raw = $rsRiskRankRaw ?? 'low';
        $asset->rs_risk_ranking = $rsRiskRank ?? 0;

        return $asset;
    }

    protected function getNearestGroundwaterAndStageStations($items, $latitude, $longitude)
    {
        $rsQualifier = null;
        $swQualifier = null;
        $nearestPoint = null;
        $lat = null;
        $lon = null;
        foreach ($items as $item) {
            if (empty($item) || (!$item->lat || !$item->long) || empty($item->measures)) {
                continue;
            }

            $itemLat = $item->lat;
            $itemLon = $item->long;
            if (is_array($itemLat)) {
                $itemLat = $itemLat[0];
            }

            if (is_array($itemLon)) {
                $itemLon = $itemLon[0];
            }

            $distance = (3959 * acos(
                cos(deg2rad($latitude)) * cos(deg2rad($itemLat))
                * cos(deg2rad($itemLon) - deg2rad($longitude))
                + sin(deg2rad($latitude)) * sin(deg2rad($itemLat))
            ));

            $swMeasureQualifier = $this->getMeasureQualifier($item->measures, $item->stationReference, 'sw');
            $rsMeasureQualifier = $this->getMeasureQualifier($item->measures, $item->stationReference, 'rs');
            if ($swMeasureQualifier || $rsMeasureQualifier) {
                if ($nearestPoint) {
                    if ($nearestPoint > $distance) {
                        $nearestPoint = $distance;
                        $swQualifier = $swMeasureQualifier ?? $swQualifier;
                        $rsQualifier = $rsMeasureQualifier ?? $rsQualifier;
                        $lat = $item->lat;
                        $lon = $item->long;
                    }
                } else {
                    $nearestPoint = $distance;
                    $swQualifier = $swMeasureQualifier;
                    $rsQualifier = $rsMeasureQualifier;
                    $lat = $item->lat;
                    $lon = $item->long;
                }
            }
        }

        return [
            $rsQualifier,
            $swQualifier,
            $lat,
            $lon
        ];
    }

    protected function getMeasureQualifier($measures, $stationReference, $type)
    {
        $qualifier = null;
        // Get the readings
        foreach ($measures as $measure) {
            if ($type == 'sw') {
                if (!$qualifier && $measure->qualifier == 'Groundwater') {
                    $qualifier = $stationReference;
                    break;
                }
            }

            if ($type == 'rs') {
                if (
                    !$qualifier && ($measure->qualifier == 'Stage'
                    || $measure->qualifier == 'Downstream Stage')
                ) {
                    $qualifier = $stationReference;
                    break;
                }
            }
        }

        return $qualifier;
    }

    protected function computeRiskRanking($value)
    {
        $riskRaking = config('app.previsico.flood_map.risk_ranking');
        if ($value >= $riskRaking['high']['min'] && $value <= $riskRaking['high']['max']) {
            return 'high';
        }

        if ($value >= $riskRaking['med']['min'] && $value <= $riskRaking['med']['max']) {
            return 'med';
        }

        return 'low';
    }
}
