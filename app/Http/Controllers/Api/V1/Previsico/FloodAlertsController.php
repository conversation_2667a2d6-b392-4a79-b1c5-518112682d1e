<?php

namespace App\Http\Controllers\Api\V1\Previsico;

use App\Http\Controllers\BaseController;
use App\Models\ClientDashboardComponentOrder;
use App\Models\Cms;
use App\Models\Previsico\Previsico;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\Request;
use App\Models\MgaScheme;
use App\Models\Organisation;
use App\Models\Previsico\PrevisicoAsset;
use App\Models\Previsico\PrevisicoAlert;
use App\Models\Previsico\PrevisicoAssetAccess;
use App\Models\Previsico\PrevisicoAlertFiles;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class FloodAlertsController extends BaseController
{
    const PAST_ALERT_LEVEL_MAPPING = [
        1 => 'Low',
        2 => 'Moderate',
        3 => 'High'
    ];
    const ALERT_LEVEL_TEXT_MAPPING = [
        1 => 'Prepare',
        2 => 'Act',
        3 => 'Severe'
    ];

    /**
     * Fetches organisation's LATEST FLOOD ALERTS ONLY
     *
     * @param int $organisation_id
     * @param int|null $userId
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request, $organisation_id, $userId=null)
    {
        $alertsOnly = $request->query('alertsOnly', 0);
        $allowedLocations = [];
        $componentOrders = [1,2,3];
        $hiddenComponents = [];
        if ($userId) {
            $allowedLocations = PrevisicoAssetAccess::where('user_id', $userId)
                ->get()
                ->pluck('previsico_asset_id');

            $component = ClientDashboardComponentOrder::where('user_id', $userId)
                ->where('type', 'flood_alerts')
                ->first();
            if ($component) {
                $componentOrders = explode(',', $component->order);
                $hiddenComponents = explode(',', $component->hidden_components);
            }
        }

        // Check if asset and location exists
        $organisation = Organisation::where('id', $organisation_id)
            ->where('has_previsico_access', 1)
            ->first();

        if (empty($organisation)) {
            $message='Organisation does not exist or does not have Previsico access.';
            return $this->returnMessage($organisation_id, $message);
        }

        if (!is_null($organisation->mga_scheme) && $organisation->mga_scheme != '') {
            $mgaScheme = MgaScheme::find($organisation->mga_scheme);
            $organisation->mga_scheme_name = $mgaScheme->name;
        }

        $assets = PrevisicoAsset::with('organisationLocation')
            ->where('organisation_id', $organisation_id)
            // ->where('alert_level', '>', 0) // should return all assets regardless of alert level
            ->orderBy('alert_level', 'desc')
            ->get();

        if ($assets->isEmpty()) {
            $message = 'Organisation does not have registered Previsico assets.';
            return $this->returnMessage($organisation_id, $message);
        }

        // Remove filter by alert level so we can
        // show assets with alerts within
        // the past 48 hrs
        // https://app.clickup.com/t/8694hzxdc
        // $assets = $assets->filter(fn ($asset) => $asset->alert_level > 0)
        //     ->sortByDesc('alert_level');

        $assets->load('organisationLocation');

        $latestAlertFound = false;

        $items = [];

        foreach ($assets as $item) {
            $latestAlert = $item->latestAlert();

            if (!$latestAlert) continue;

            // If asset's current alert level < 1
            // but has latest alert that is
            // within 48hrs, we need to
            // set an alert_level value.
            // https://app.clickup.com/t/8694hzxdc
            if ($item->alert_level < 1) {
                $newAlertLevel = Previsico::convertWaterLevelToAlertLevel($latestAlert->max_water_level);

                // If asset's alert level is still < 1
                // We skip processing it
                if ($newAlertLevel < 1) {
                    continue;
                }

                $item->alert_level = $newAlertLevel;
            }

            if (!empty($latestAlert)) {
                $latestAlertFound = true;
            }

            if ($allowedLocations && !in_array($item->id, $allowedLocations->toArray())) {
                continue;
            }

            $items[] = [
                'asset' => $item,
                'alert' => $latestAlert,
            ];
        }

        if (!$latestAlertFound) {
            $message = 'No recent alerts found.';
        }

        if (empty($alertsOnly)) {

        }

        return Response::json(
            [
                'status' => 'success',
                'organisation' => empty($alertsOnly) ? $organisation : '',
                'items' => $items,
                'allowed_locations' => empty($alertsOnly) ? $allowedLocations : '',
                'past_alerts' => [],
                'past_alerts_cms' => empty($alertsOnly) ? $this->getFloodAlertCMS() : '',
                'component_orders' => empty($alertsOnly) ? $componentOrders : '',
                'hidden_components' => empty($alertsOnly) ? $hiddenComponents : '',
                'latest_alert_found' => $latestAlertFound,
                'message' => $message ?? '',
                'last_update' => !$latestAlertFound ? $this->generateLastUpdateData() : null,
            ]
        );
    }

    public function allFloodAlerts(Request $request, int $organisationId, ?int $userId = null)
    {
        $response = [
            'status' => 'success',
            'items' => [],
        ];

        $allowedLocations = [];
        if ($userId) {
            $allowedLocations = PrevisicoAssetAccess::where('user_id', $userId)
                ->get()
                ->pluck('previsico_asset_id');
        }

        // Check if asset and location exists
        $organisation = Organisation::where('id', $organisationId)
            ->where('has_previsico_access', 1)
            ->first();

        if (empty($organisation)) {
            $message='Organisation does not exist or does not have Previsico access.';
            return $this->returnMessage($organisationId, $message);
        }

        $alerts = PrevisicoAlert::with('previsicoAsset.organisationLocation')
                ->whereHas('previsicoAsset', function ($query) use ($organisationId, $userId, $allowedLocations) {
                    $query->where('organisation_id', $organisationId) // #1
                        ->when(!empty($userId), function ($userQuery) use ($allowedLocations) {
                            $userQuery->whereIn('id', $allowedLocations); // #3
                        });
                })
                ->groupBy('previsico_asset_id', 'max_water_level_at')
                ->orderBy('date', 'DESC')
                ->get();

        $items = [];
        foreach ($alerts as $item) {
            $asset = $item->previsicoAsset;

            // Get the alert level for the actual alert
            // not the asset's current alert level
            $newAlertLevel = Previsico::convertWaterLevelToAlertLevel($item->max_water_level);

            // If asset's alert level is < 1
            // We skip processing it
            if ($newAlertLevel < 1) {
                continue;
            }

            $asset->alert_level = $newAlertLevel;
            $item->alert_level = $newAlertLevel;

            $items[] = [
                'asset' => $asset,
                'alert' => $item,
            ];
        }

        $response['items'] = $items;

        return Response::json($response);
    }

    /**
     * Get Past Alerts section's initial data
     *
     * @param integer $organisationId
     * @param integer|null $userId
     * @return \Illuminate\Http\JsonResponse
     */
    public function initialPastAlerts(int $organisationId, ?int $userId)
     {
        $responseData = [
            'rows' => [],
            'most_affected' => [],
            'prevalent_month' => null,
        ];

        $rangeStart = Carbon::now()->subMonths(6)->format('Y-m-d 00:00:00');
        $rangeEnd   = Carbon::now()->subDay(1)->format('Y-m-d 23:59:59');

        // Removing conditions: https://app.clickup.com/t/8694he1h2
        // $currYrStart    = Carbon::now()->subMonths(12)->format('Y-m-d 00:00:00');
        // $currYrEnd      = Carbon::now()->subDay(1)->format('Y-m-d 23:59:59');

        $pastAssets   = [];
        $mostAffected = [];
        $prevalent    = [];
        $records      = [];

        $organisation = Organisation::where('id', $organisationId)
            ->where('has_previsico_access', 1)
            ->first();

        if (empty($organisation)) {
            return Response::json($responseData);
        }

        if (!empty($userId)) {
            $allowedLocations = PrevisicoAssetAccess::where('user_id', $userId)
                ->get()
                ->pluck('previsico_asset_id')
                ->toArray();

            if (empty($allowedLocations)) {
                // User is not allowed to any location
                // return empty
                return Response::json($responseData);
            }
        }

        // Get alerts that match the following criteria
        // 1. For the given organisation
        // 2. Within the last 12 months -- REMOVED https://app.clickup.com/t/8694he1h2
        // 2. If provided with a user id, user should have access to alert's asset

        $alerts = PrevisicoAlert::with('previsicoAsset.organisationLocation')
                ->whereHas('previsicoAsset', function ($query) use ($organisationId, $userId, $allowedLocations) {
                    $query->where('organisation_id', $organisationId) // #1
                        ->when(!empty($userId), function ($userQuery) use ($allowedLocations) {
                            $userQuery->whereIn('id', $allowedLocations); // #3
                        });
                })
                // Removing conditions: https://app.clickup.com/t/8694he1h2
                // ->where('from_period', '>=', $currYrStart) #2
                // ->where('to_period', '<=', $currYrEnd) #2
                ->groupBy('previsico_asset_id', 'max_water_level_at')
                ->orderBy('date', 'DESC')
                ->get();

        if ($alerts->isEmpty()) {
            return Response::json($responseData);
        }

        foreach ($alerts as $alert) {
            $maxWaterLevel = $alert->max_water_level;
            $alertLevel = Previsico::convertWaterLevelToAlertLevel($maxWaterLevel);
            if ($alertLevel < 1) continue;

            if (
                $alert->from_period >= $rangeStart
                && $alert->to_period <= $rangeEnd
            ) {
                $locationId = $alert->previsicoAsset->organisation_location_id;
                $risk = self::PAST_ALERT_LEVEL_MAPPING[$alertLevel];
                if (!isset($pastAssets[$risk])) {
                    $pastAssets[$risk]['count'] = 0;
                    $pastAssets[$risk]['locations'] = [];
                }

                if (!isset($pastAssets[$risk]['locations'][$locationId])) {
                    $pastAssets[$risk]['locations'][$locationId] = 0;
                }

                $pastAssets[$risk]['level'] = $maxWaterLevel;
                $pastAssets[$risk]['count'] += 1;

                if (!in_array($locationId, $pastAssets[$risk]['locations'])) {
                    $pastAssets[$risk]['locations'][$locationId] = $locationId;
                }

                $location = $alert->previsicoAsset->organisationLocation;
                $records[] = [
                    'location_name' => $location->location_name,
                    'address' => $location->address_line_1,
                    'date' => date('Y-m-d H:i:s', strtotime($alert->date)),
                    'water_level' => $alert->max_water_level,
                    'alert_level' => self::ALERT_LEVEL_TEXT_MAPPING[$alertLevel]
                ];
            }

            $mostAffected = $this->getMostAffectedAreas($alert->previsicoAsset, $alertLevel, $mostAffected);
            $prevalent = $this->getMostPrevalentMonth($alert, $prevalent);
        }

        $keys = array_column($records, 'date');
        array_multisort($keys, SORT_DESC, $records);

        $mostlyAffected = $this->filterMostAffected($mostAffected);
        $mostPrevalentMonth = $this->filterMostPrevalentMonth($prevalent);

        $responseData = [
            'rows' => $records,
            'most_affected' => $mostlyAffected,
            'prevalent_month' => $mostPrevalentMonth
        ];

        foreach ($pastAssets as $risk => $pastAsset) {
            $responseData['alerts'][$risk] = [
                'locations' => count($pastAsset['locations']),
                'alerts' => $pastAsset['count']
            ];
        }

        return Response::json($responseData);
     }

    public function pastAlerts($organisationId, $userId, $months, $location = null)
    {
        $responseData = [
            'totals' => [],
            'items' => []
        ];

        $allowedLocations = [];
        $range = Previsico::convertAlertLevelToWaterLevelRange($location);

        $organisation = Organisation::where('id', $organisationId)
            ->where('has_previsico_access', 1)
            ->first();

        if (empty($organisation)) {
            return Response::json($responseData);
        }

        if (!empty($userId)) {
            $allowedLocations = PrevisicoAssetAccess::where('user_id', $userId)
                ->get()
                ->pluck('previsico_asset_id')
                ->toArray();

            if (empty($allowedLocations)) {
                // User is not allowed to any location
                // return empty
                return Response::json($responseData);
            }
        }

        $alerts = PrevisicoAlert::with('previsicoAsset.organisationLocation')
            ->whereHas('previsicoAsset', function ($query) use ($organisationId, $userId, $allowedLocations) {
                $query->where('organisation_id', $organisationId) // #1
                    ->when(!empty($userId), function ($userQuery) use ($allowedLocations) {
                        $userQuery->whereIn('id', $allowedLocations); // #3
                    });
            })
            ->when($months != 'all', function ($rangeQuery) use ($months) {
                $rangeStart = Carbon::now()->subMonths($months)->format('Y-m-d 00:00:00');
                $rangeEnd   = Carbon::now()->subDay(1)->format('Y-m-d 23:59:59');

                $rangeQuery->where('from_period', '>=', $rangeStart)
                    ->where('to_period', '<=', $rangeEnd);
            })
            ->where('max_water_level', '>=', $range[0])
            ->where('max_water_level', '<', $range[1])
            ->groupBy('previsico_asset_id', 'max_water_level_at')
            ->orderBy('date', 'DESC')
            ->get();

        if ($alerts->isEmpty()) {
            return Response::json($responseData);
        }

        foreach ($alerts as $alert) {
            $maxWaterLevel = $alert->max_water_level;
            $alertLevel = Previsico::convertWaterLevelToAlertLevel($maxWaterLevel);
            if ($alertLevel < 1) continue;

            $alertLevelLabel = self::ALERT_LEVEL_TEXT_MAPPING[$alertLevel];
            $location = $alert->previsicoAsset->organisationLocation;

            $responseData['items'][] = [
                'location_name' => $location->location_name,
                'address' => $location->address_line_1,
                'date' => date('Y-m-d H:i:s', strtotime($alert->date)),
                'water_level' => $alert->max_water_level,
                'alert_level' => $alertLevelLabel
            ];

            if (!isset($responseData['totals'][strtolower($alertLevelLabel)])) {
                $responseData['totals'][strtolower($alertLevelLabel)] = [
                    'alerts' => 0,
                    'locations' => 0
                ];
            }

            $responseData['totals'][strtolower($alertLevelLabel)]['alerts'] += 1;

            if (!is_array($responseData['totals'][strtolower($alertLevelLabel)]['locations'])) {
                $responseData['totals'][strtolower($alertLevelLabel)]['locations'] = [];
            }

            if (!array_key_exists($location->id, $responseData['totals'][strtolower($alertLevelLabel)]['locations'])) {
                $responseData['totals'][strtolower($alertLevelLabel)]['locations'][$location->id] = 1;
            }
        }

        // Transform locations lists to counts
        // to get number of unique locations
        foreach (['severe', 'act', 'prepare'] as $alertKey) {
            if (!array_key_exists($alertKey, $responseData['totals'])) {
                continue;
            }

            $responseData['totals'][$alertKey]['locations'] = is_array($responseData['totals'][$alertKey]['locations'])
                ? count($responseData['totals'][$alertKey]['locations'])
                : 0;
        }

        return Response::json($responseData);
    }

    public function storeClientComponentOrder(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make(
            $input, [
                'user_id' => 'required',
                'order' => 'required',
                'type' => 'required|in:flood_alerts',
            ]
        );

        if ($validator->fails()) {
            return Response::json(
                [
                    'response' => 'error',
                    'errors' => $validator->messages()->toArray(),
                ]
            );
        }

        $changes = [
            'order' => $input['order'],
            'hidden_components' => $input['hidden']
        ];

        $componentOrder = ClientDashboardComponentOrder::updateOrCreate(
            [
                'user_id' => $input['user_id'],
                'type' => $input['type'],
            ],
            $changes
        );

        return Response::json(
            [
                'response' => 'success',
                'data' => $componentOrder,
            ]
        );
    }

    private function returnMessage($organisation_id, $message) {
        $organisation = Organisation::find($organisation_id);

        return Response::json(
            [
                'status' => 204, // No content
                'message' => $message,
                'organisation' => $organisation,
                'last_update' => $this->generateLastUpdateData(),
            ]
        );
    }

    private function generateLastUpdateData()
    {
        $now = Carbon::now()->timezone('Europe/London');
        $minutes = intval($now->format('i'));
        $minutes = ($minutes - ($minutes % 15));
        $minutes = str_pad((string)$minutes, 2, STR_PAD_LEFT);

        return [
            'time' => $now->format('H') . ":{$minutes}",
            'date' => $now->format('d/m/Y'),
        ];
    }

    public function floodAlertLocations($organisation_id)
    {
        $alerts = PrevisicoAlert::select('*')
            ->whereIn('previsico_asset_id', function($q) use ($organisation_id) {
                $q
                    ->select('id')
                    ->from('previsico_assets')
                    ->where('alert_level', '>', 0)
                    ->where('organisation_id', $organisation_id);
            })
            ->orderBy('id', 'DESC')
            ->limit(200)
            ->get()
            ->map(function ($alert) {
                $asset = $alert->previsicoAsset;
                $actionedBy = $alert->actionedBy();
                return [
                    'id'            => $alert->id,
                    'date'          => $alert->date,
                    'location_name' => $asset->name,
                    'alert_type'    => $asset->alert_level,
                    'actioned'      => $alert->is_actioned,
                    'notes'         => $alert->actioned_by_notes ?? '-',
                    'client_name'   => $actionedBy ?? '-',
                ];
            });

        return Response::json($alerts);
    }

    public function show($location_id)
    {
        // Check if asset and location exists
        $asset = PrevisicoAsset::with('organisationLocation.organisation')
            ->whereHas(
                'organisationLocation', function ($query) use ($location_id) {
                $query->where('id', $location_id);
            }
            )
            ->first();

        if (empty($asset)) {
            return response()->json([
                'status' => 404, // Not found
                'message' => 'Location is not in the Previsico Assets list.',
            ], 404);
        }

        // Check if asset has an alert
        $latestAlert = $asset->latestAlert();

        if (empty($latestAlert)) {
            return response()->json([
                'status' => 204, // No content
                'message' => 'No recent alert found.',
            ]);
        }

        return response()->json([
            'status' => 'success',
            'asset'  => $asset,
            'alert'  => $latestAlert,
        ]);
    }

    public function actioned(Request $request) {
        $data = $request->all();

        $userId = $data['user_id'];
        $previsicoAlertId = $data['alert_id'];
        $note = $data['note'] ?? null;
        $delete_files = $data['delete_files'] ?? null;

        PrevisicoAlert::where('id', $previsicoAlertId)->update([
            'is_actioned' => 1,
            'actioned_by' => $userId,
            'actioned_by_notes' => $note,
            'actioned_date' => date('Y-m-d H:i:s', time())
        ]);

        $images = $data['files'] ?? '';
        $images = json_decode($images);
        if ($images) {
            $filesData = [];
            foreach ($images as $image) {
                $data = [];
                $data['alert_id'] = $previsicoAlertId;
                $data['cloud_file_name'] = $image->key;
                $data['file_name'] = $image->value;
                $filesData[] = $data;
            }
            if (count($filesData) > 0) {
                PrevisicoAlertFiles::insert($filesData);
            }
        }

        $cloud_names=null;
        if($delete_files){
            $files=PrevisicoAlertFiles::whereIn('id', explode(',', $delete_files));
            $cloud_names=$files->get(['cloud_file_name']);
            $files->delete();
        }

        return response()->json([
            'status' => 'success',
            'cloud_names'=>$cloud_names
        ]);
    }

    public function getAlert($alert_id)
    {
        $alert = PrevisicoAlert::with('alertFiles')->find($alert_id);
        $assets = PrevisicoAsset::with('organisationLocation.organisation')->find($alert->previsico_asset_id);
        $data = (object)[];
        $data->date = $alert->date;
        $data->location_name = $assets->organisationLocation->location_name;
        $data->alert_level = $assets->alert_level;
        $data->actioned = $alert->actionedBy();
        $data->is_actioned = $alert->is_actioned;
        $data->note = $alert->actioned_by_notes;
        $data->actioned_date = $alert->actioned_date;
        $data->client_name = $alert->actioned_by;
        $data->organisation_id = $assets->organisationLocation->organisation->id;
        $data->organisation_name = $assets->organisationLocation->organisation->name;
        $files = [];
        foreach ($alert->alertFiles as $file) {
            $files[] = $file;
        }
        $data->files = $files;
        return response()->json([
            'status' => 'success',
            'data'  => $data,
        ]);
    }

    protected function checkPastAsset($asset, $allowedLocations)
    {
        if ($allowedLocations && !in_array($asset->id, $allowedLocations->toArray())) {
            return [];
        }
    }

    protected function getPastAssets($organisationId, $userId)
    {
        $rangeStart     = Carbon::now()->subMonths(6)->format('Y-m-d 00:00:00');
        $rangeEnd       = Carbon::now()->subDay(1)->format('Y-m-d 23:59:59');
        $currYrStart    = Carbon::now()->subMonths(12)->format('Y-m-d 00:00:00');
        $currYrEnd      = Carbon::now()->subDay(1)->format('Y-m-d 23:59:59');

        $pastAssets = PrevisicoAsset::where('organisation_id', $organisationId)->get();
        $allowedLocations = [];
        if ($userId) {
            $allowedLocations = PrevisicoAssetAccess::where('user_id', $userId)
                ->get()
                ->pluck('previsico_asset_id');
        }

        $rows = [];
        $records = [];
        $mostAffected = [];
        $prevalent = [];
        foreach ($pastAssets as $pastAsset) {
            if ($allowedLocations && !in_array($pastAsset->id, $allowedLocations->toArray())) {
                continue;
            }

            $alerts = PrevisicoAlert::where('previsico_asset_id', $pastAsset->id)
                ->where('from_period', '>=', $currYrStart)
                ->where('to_period', '<=', $currYrEnd)
                ->groupBy('max_water_level', DB::raw('DATE(date)'))
                ->orderBy('date', 'DESC')
                ->get();

            if (!$alerts) continue;

            foreach ($alerts as $alert) {
                $maxWaterLevel = $alert->max_water_level;
                $alertLevel = Previsico::convertWaterLevelToAlertLevel($maxWaterLevel);
                if ($alertLevel < 1) continue;

                if ($alert->from_period >= $rangeStart
                    && $alert->to_period <= $rangeEnd) {
                    $locationId = $pastAsset->organisation_location_id;
                    $risk = self::PAST_ALERT_LEVEL_MAPPING[$alertLevel];
                    if (!isset($rows[$risk])) {
                        $rows[$risk]['count'] = 0;
                        $rows[$risk]['locations'] = [];
                    }

                    if (!isset($rows[$risk]['locations'][$locationId])) {
                        $rows[$risk]['locations'][$locationId] = 0;
                    }

                    $rows[$risk]['level'] = $maxWaterLevel;
                    $rows[$risk]['count'] += 1;

                    if (!in_array($locationId, $rows[$risk]['locations'])) {
                        $rows[$risk]['locations'][$locationId] = $locationId;
                    }

                    $location = $pastAsset->organisationLocation;
                    $records[] = [
                        'location_name' => $location->location_name,
                        'address' => $location->address_line_1,
                        'date' => date('Y-m-d H:i:s', strtotime($alert->date)),
                        'water_level' => $alert->max_water_level,
                        'alert_level' => $alertLevel
                    ];
                }

                $mostAffected = $this->getMostAffectedAreas($pastAsset, $alertLevel, $mostAffected);
                $prevalent = $this->getMostPrevalentMonth($alert, $prevalent);
            }
        }

        $keys = array_column($records, 'date');
        array_multisort($keys, SORT_DESC, $records);

        $mostlyAffected = $this->filterMostAffected($mostAffected);
        $mostPrevalentMonth = $this->filterMostPrevalentMonth($prevalent);

        return [$rows, $records, $mostlyAffected, $mostPrevalentMonth];
    }

    protected function getMostAffectedAreas($pastAsset, $alertLevel, $mostAffected)
    {
        $locationId     = $pastAsset->organisation_location_id;
        $location       = $pastAsset->organisationLocation;

        if (!isset($mostAffected[$alertLevel])) {
            $mostAffected[$alertLevel] = [];
        }

        if (!isset($mostAffected[$alertLevel]['count'])) {
            $mostAffected[$alertLevel]['count'] = 0;
            $mostAffected[$alertLevel]['locations'] = [];
        }

        $mostAffected[$alertLevel]['count'] += 1;
        $mostAffected[$alertLevel]['locations'][$locationId] = $location->address_line_1;

        return $mostAffected;
    }

    protected function getMostPrevalentMonth($pastAlert, $prevalent)
    {
        $month = date('F', strtotime($pastAlert->created_at));
        if (!isset($prevalent[$month])) {
            $prevalent[$month] = 0;
        }

        $prevalent[$month] += 1;
        return $prevalent;
    }

    protected function filterMostAffected($mostAffected)
    {
        krsort($mostAffected);
        $sorted = [];
        foreach ($mostAffected as $alertLevel => $affected) {
            foreach ($affected['locations'] as $location) {
                if (count($sorted) >= 3) {
                    break;
                }

                $sorted[] = $location;
            }
        }

        return $sorted;
    }

    protected function filterMostPrevalentMonth($prevalent)
    {
        $prevalent = array_flip($prevalent);
        krsort($prevalent);
        return array_values($prevalent)[0] ?? null;
    }

    protected function getFloodAlertCMS()
    {
        $workspaceId = config('app.cms.previsico.workspace');
        $contentType = config('app.cms.previsico.content_type');

        $query = json_encode([
            'status' => 'publish',
            'operator' => '=',
        ]);

        $url = sprintf('workspaces/%s/content-types/%s/content-entries?query=%s', $workspaceId, $contentType, $query);
        $articles = json_decode(Cms::get($url))->data;

        $rows = [];
        if (!empty($articles)) {
            foreach ($articles as $article) {
                if (!empty($article)) {
                    $title = config('app.cms.previsico.article_title');
                    $subtitle = config('app.cms.previsico.article_subtitle');
                    $intro = config('app.cms.previsico.article_intro');
                    $logo = config('app.cms.previsico.image_logo');
                    $cards = config('app.cms.previsico.cards');

                    $rows['main'] = [
                        'title' => $article->{$title},
                        'subtitle' => $article->{$subtitle},
                        'intro' => $article->{$intro},
                        'logo' => $article->{$logo}[0]->url
                    ];

                    if (!empty($article->{$cards})) {
                        foreach ($article->{$cards} as $card) {
                            $cardImage = config('app.cms.previsico.card_image');
                            $cardTitle = config('app.cms.previsico.card_title');
                            $cardContent = config('app.cms.previsico.card_content');
                            $btnText = config('app.cms.previsico.card_btn_text');
                            $btnLink = config('app.cms.previsico.card_btn_link');

                            $rows['cards'][] = [
                                'title' => $card->{$cardTitle},
                                'content' => $card->{$cardContent},
                                'image' => $card->{$cardImage}[0]->url,
                                'btn_text' => $card->{$btnText},
                                'btn_link' => $card->{$btnLink}
                            ];
                        }
                    }

                    break;
                }
            }
        }

        return $rows;
    }
}
