<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\OrganisationClaim;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\Request;

class OrganisationClaimController extends BaseController
{
    public function all($organisation_id)
    {
        $notes = OrganisationClaim::where('organisation_id', $organisation_id)->get();

        return Response::json(
            [
            'response' => 'success',
            'data' => $notes,
            ]
        );
    }

    public function store(Request $request, $organisation_id)
    {
        $data = $request->except('_token');

        OrganisationClaim::create(
            $data + [
                'organisation_id' => $organisation_id,
            ]
        );

        return Response::json(
            [
            'response' => 'success',
            'message' => 'Organisation claim has been created successfully',
            ]
        );
    }

    public function show(Request $request, $organisation_id, $claim_id)
    {
        $data = $request->except('_token');

        $claim = OrganisationClaim::where('organisation_id', $organisation_id)
            ->where('_id', $claim_id)
            ->first();

        return Response::json(
            [
            'response' => 'success',
            'data' => $claim,
            ]
        );
    }

    public function update(Request $request, $organisation_id, $claim_id)
    {
        $data = $request->except('_token');

        OrganisationClaim::where('organisation_id', $organisation_id)
            ->where('_id', $claim_id)
            ->update($data);

        return Response::json(
            [
            'response' => 'success',
            'message' => 'Organisation claim has been updated successfully',
            ]
        );
    }
}
