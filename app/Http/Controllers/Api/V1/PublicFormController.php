<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use Carbon\Carbon;
use App\Models\PublicFormLink;
use App\Models\PublicFormy;
use App\Models\PublicFormySubmissions;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\Request;

class PublicFormController extends BaseController
{
    /*
     * get all forms
     */
    public function index(Request $request, $page = 1, $limit = 10)
    {
        $page = (int)$page;
        $limit = (int)$limit;
        $search = $request->get('search');

        if (isset($search) && $search != '') {
            $forms = PublicFormy::where('name', 'LIKE', '%' . $search . '%')
                ->take($limit)->skip(($page * $limit) - $limit)->get();
        } else {
            $forms = PublicFormy::take($limit)->skip(($page * $limit) - $limit)->get();
        }
        return Response::json(
            [
                'response' => 'success',
                'data' => $forms->toArray(),
                'total' => PublicFormy::where('_id', '!=', 'NULL')->get()->count(),
            ]
        );
    }

    /*
     * get a form
     */
    public function show($form)
    {
        $data = PublicFormy::find($form);
        if (isset($data)) {

            return Response::json(
                [
                    'response' => 'success',
                    'data' => json_encode($data->toArray()),
                ]
            );
        }
        return Response::json(
            [
                'response' => 'success',
                'data' => json_encode([]),
            ]
        );
    }

    /*
     * save a form
     */
    public function store(Request $request)
    {
        $form = json_decode($request->get('form'), true);
        $form['created_at'] = Carbon::now()->toDateTimeString();

        $id = PublicFormy::insertGetId($form);
        if ($id) {
            $form = PublicFormy::find($id);
            return Response::json(
                [
                    'response' => 'success',
                    'data' => (string)$id,
                ]
            );
        } else {
            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'Unable to create form',
                ]
            );
        }
    }

    /*
     * update a form
     */
    public function update(Request $request, $formId)
    {
        $formData = json_decode($request->get('form'), true);
        $form = PublicFormy::find($formId);
        $form->fill($formData);
        $form['updated_at'] = Carbon::now()->toDateTimeString();

        if ($form->save()) {
            return Response::json(
                [
                    'response' => 'success',
                    'data' => (string)$formId,
                ]
            );
        } else {
            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'Unable to update form',
                ]
            );
        }
    }

    /*
     * delete a form
     */
    public function destroy($form)
    {
        $form_object = PublicFormy::find($form);
        if (PublicFormy::destroy($form)) {
            return Response::json(
                [
                    'response' => 'success',
                    'message' => 'Form Deleted',
                ]
            );
        } else {
            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'Unable to delete form',
                ]
            );
        }
    }

    public function accidentReportingFormsForOrganisation($organisation_id, $page = 1, $limit = 10)
    {
        $page = (int)$page;
        $limit = (int)$limit;
        $forms = PublicFormySubmissions::where('organisation_id', '=', $organisation_id)
            ->with('publicFormy')
            ->take($limit)->skip(($page * $limit) - $limit)->orderBy('updated_at', 'asc')->get()->toArray();

        foreach ($forms as $key => $form) {
            $formy = $form['public_formy'];
            if (!isset($formy['formType']) || $formy['formType'] !== 'accident-reporting') {
                unset($forms[$key]);
            }
        }

        $hasPublicForm = PublicFormLink::where('organisation_id', '=', $organisation_id)->get()->count();

        return Response::json(
            [
                'response' => 'success',
                'data' => array_values($forms),
                'total' => PublicFormySubmissions::where('organisation_id', '=', $organisation_id)->get()->count(),
                'has_form' => $hasPublicForm > 0,
            ]
        );
    }

    public function formsForOrganisation($organisation_id, $page = 1, $limit = 10)
    {
        $page = (int)$page;
        $limit = (int)$limit;
        $forms = PublicFormLink::where('organisation_id', '=', $organisation_id)
            ->with('publicForm')
            ->take($limit)->skip(($page * $limit) - $limit)->orderBy('updated_at', 'asc')->get()->toArray();
        foreach ($forms as $key => $form) {
            if (!isset($form['public_form'])) {
                continue;
            }
            $formy = $form['public_form'];
            if ($formy['formType'] !== 'accident-reporting') {
                unset($forms[$key]);
                continue;
            }
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => array_values($forms),
                'total' => count($forms),
            ]
        );
    }

    public function showSharedForm($slug)
    {
        $form = PublicFormy::where('shared_link_slug', $slug)->first();
        if (empty($form)) {
            return Response::json(
                [
                    'response' => 'error',
                    'message' => '<b>Sorry</b>, the form does not exist',
                ]
            );
        }

        $links = PublicFormLink::where('form_id', $form->_id)->get();
        $orgs = [];
        foreach ($links as $link) {
            $organisation = $link->organisation()->get(['id', 'name'])->first();
            $org = (object)[];
            $org->id = $organisation->id;
            $org->name = $organisation->name;
            $orgs[] = $org;
        }

        $form->organisations = $orgs;
        return Response::json(
            [
                'response' => 'success',
                'data' => $form,
            ]
        );
    }

    public function getAllPublicForms()
    {   
        $forms = PublicFormy::all(['_id','name']);
        return Response::json([
            'response' => 'success',
            'data' => $forms,
        ]);
    }
}
