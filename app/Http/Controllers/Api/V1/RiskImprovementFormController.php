<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use App\Models\RiskImprovementFormy;
use App\Models\RiskRecTitle;
use App\Models\Survey;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class RiskImprovementFormController extends BaseController
{

    /*
     * get all forms for an organisation
     */
    public function formsfororganisation($organisation_id, $page = 1, $limit = 10)
    {
        $page = (int)$page;
        $limit = (int)$limit;
        $count = 0;
        $forms = RiskImprovementFormy::where('organisation', '=', $organisation_id)->orwhere(
            'organisation', '=',
            '0'
        )->where(
            'formType', '<>', 'accident-reporting',
            'AND'
        )->take($limit)->skip(($page * $limit) - $limit)->get();
        foreach ($forms as $index => $form) {
            if (isset($form->exclude_ids)) {
                $excluded_org_array = explode(',', $form->exclude_ids);
                if (in_array($organisation_id, $excluded_org_array)) {
                    $count = $count + 1;
                    $forms->offsetUnset($index);
                }
            }
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => $forms->toArray(),
            'total' => RiskImprovementFormy::where('organisation', '=', $organisation_id)->orwhere(
                'organisation', '=',
                '0'
            )->where('formType', '<>', 'accident-reporting', 'AND')->get()->count(),
            ]
        );

    }


    public function accidentReportingFormsForOrganisation($organisation_id, $page = 1, $limit = 10)
    {
        $page = (int)$page;
        $limit = (int)$limit;
        $forms = RiskImprovementFormy::where('organisation', '=', $organisation_id)->orwhere(
            'organisation', '=',
            '0'
        )->where(
            'formType', '=', 'accident-reporting',
            'AND'
        )->take($limit)->skip(($page * $limit) - $limit)->get()->toArray();
        //print_r($forms);exit;
        foreach ($forms as $key => $form) {
            if (isset($form['exclude_ids'])) {
                $excluded_org_array = explode(',', $form['exclude_ids']);
                //print_r($excluded_org_array);exit;
            } else {
                $excluded_org_array = [];
            }

            if ((isset($form['exclude_ids']) && in_array($organisation_id, $excluded_org_array))) {
                unset($forms[$key]);
            }
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => $forms,
            'total' => RiskImprovementFormy::where('organisation', '=', $organisation_id)->orwhere(
                'organisation', '=',
                '0'
            )->where('formType', '=', 'accident-reporting', 'AND')->get()->count(),
            ]
        );
    }


    /*
    * get all survey forms
    */
    public function index(Request $request, $page = 1, $limit = 10)
    {
        $search = $request->get('search');

        $surveyor_id = $request->get('surveyor_id');

        $page = (int)$page;
        $limit = (int)$limit;

        if ($request->has('formType')) {
            if (isset($surveyor_id) && $surveyor_id != '') {
                if (isset($search) && $search != '') {
                    $forms = RiskImprovementFormy::where('name', 'LIKE', '%' . $search . '%')
                        ->where('surveyor_id', '=', $surveyor_id)->where(
                            'formType', '=',
                            'RE Review'
                        )->take($limit)->skip(($page * $limit) - $limit)->get();

                } else {
                    $forms = RiskImprovementFormy::where('surveyor_id', '=', $surveyor_id)->where(
                        'formType', '=',
                        'RE Review'
                    )->take($limit)->skip(($page * $limit) - $limit)->get();
                }

            } else {
                if (isset($search) && $search != '') {
                    $forms = RiskImprovementFormy::where('name', 'LIKE', '%' . $search . '%')->where(
                        'formType', '=',
                        'RE Review'
                    )
                        ->take($limit)->skip(($page * $limit) - $limit)->get();

                } else {
                    $forms = RiskImprovementFormy::where(
                        'formType', '=',
                        'RE Review'
                    )->take($limit)->skip(($page * $limit) - $limit)->get();
                }
            }
        } else {

            if (isset($surveyor_id) && $surveyor_id != '') {
                if (isset($search) && $search != '') {
                    $forms = RiskImprovementFormy::where('name', 'LIKE', '%' . $search . '%')
                        ->where('surveyor_id', '=', $surveyor_id)->take($limit)->skip(($page * $limit) - $limit)->get();

                } else {
                    $forms = RiskImprovementFormy::where(
                        'surveyor_id', '=',
                        $surveyor_id
                    )->take($limit)->skip(($page * $limit) - $limit)->get();
                }

            } else {
                if (isset($search) && $search != '') {
                    $forms = RiskImprovementFormy::where('name', 'LIKE', '%' . $search . '%')
                        ->take($limit)->skip(($page * $limit) - $limit)->get();

                } else {
                    $forms = RiskImprovementFormy::take($limit)->skip(($page * $limit) - $limit)->get();
                }
            }
        }


        return Response::json(
            [
            'response' => 'success',
            'data' => $forms->toArray(),
            'total' => RiskImprovementFormy::get()->count(),
            ]
        );
    }

    public function listAllForms()
    {
        $forms=RiskImprovementFormy::get(['_id','name']);
        return response()->json([
            'response' => 'success',
            'data' => $forms
        ]);
    }


    /*
     * get forms List
     */
    public function listForms(Request $request, $page = 1, $limit = 1000)
    {
        $page = (int)$page;
        $limit = (int)$limit;
        $search = $request->get('search');

        $surveyor_id = $request->get('surveyor_id');

        if (isset($surveyor_id) && $surveyor_id != '') {
            if (isset($search) && $search != '') {
                $forms = RiskImprovementFormy::where('name', 'LIKE', '%' . $search . '%')
                    ->where('surveyor_id', '=', $surveyor_id)->take($limit)->skip(($page * $limit) - $limit)->get();
            } else {
                $forms = RiskImprovementFormy::where(
                    'surveyor_id', '=',
                    $surveyor_id
                )->take($limit)->skip(($page * $limit) - $limit)->get();
            }

        } else {
            if (isset($search) && $search != '') {
                $forms = RiskImprovementFormy::where('name', 'LIKE', '%' . $search . '%')
                    ->take($limit)->skip(($page * $limit) - $limit)->get();

            } else {
                if ($request->has('type')) {
                    $type = $request->get('type');
                    if($type=='options'){
                        $forms = RiskImprovementFormy::take($limit)->skip(($page * $limit) - $limit)->get();
                    }
                    else{
                        $forms = RiskImprovementFormy::where(
                            'formType', '=',
                            $type
                        )->take($limit)->skip(($page * $limit) - $limit)->get();
                    }
                    
                } else {
                    $forms = RiskImprovementFormy::take($limit)->skip(($page * $limit) - $limit)->get();
                }
            }
        }
        $listOfForms = [];
        foreach ($forms as $form) {

            $listOfForms[$form->_id] = $form->name;
        }

        if ($request->has('type') && $request->get('type') == 'options') {
            return Response::json($listOfForms);
        }


        return response()->json([
            'response' => 'success',
            'data' => $listOfForms,
            'total' => RiskImprovementFormy::get()->count(),
        ]);
    }

    /*
    * get a form
    */
    public function show(Request $request, $form)
    {
        //checking default values, this will go to UI in further request
        $this->addDefaultGradingDescription($form);
        
        $data = RiskImprovementFormy::with('gradingsDescription')->find($form);
        $data['risk_grading_attributes'] = [];

        if ($request->has('survey_id') && $request->get('survey_id') != 0) {
            $riskGradingAttributes = Survey::riskGradingAttributes($request->get('survey_id'));
            $data['risk_grading_attributes'] = $riskGradingAttributes;
        }

        $finalData = is_object($data) && method_exists($data, 'toArray')
            ? $data->toArray()
            : $data;

        if (isset($data)) {
            return Response::json(
                [
                'response' => 'success',
                'data' => json_encode($finalData),
                ]
            );
        }
        return Response::json(
            [
            'response' => 'success',
            'data' => json_encode([]),
            ]
        );

    }

    /*
     * save a form
     */
    public function store(Request $request)
    {
        $form = json_decode($request->get('form'), true);
        if (isset($form['fields']) && isset($form['section_names'])) {

            $section_names = explode(',', $form['section_names']);
            $form_fields = $form['fields'];
            // echo "<pre>";
            // print_r($form_fields); exit;
            $ordered_form_fields = [];
            $noSectionFields = [];
            $pushedKeys = [];
            foreach ($section_names as $section) {
                foreach ($form_fields as $fields) {
                    foreach ($fields as $field_type => $field_details) {
                        foreach ($field_details as $key => $field_detail) {
                            if ($field_detail['name'] == "section" && $field_detail['value'] == $section) {
                                array_push($ordered_form_fields, $fields);
                                continue;
                            }

                            $pushedKey = sprintf('%s-%s', $field_type, $key);
                            if (
                                $field_detail['name'] == "section"
                                && !in_array($pushedKey, $pushedKeys)
                                && !in_array($field_detail['value'], $section_names)
                            ) {
                                $fields[$field_type][$key]['value'] = '';
                                array_push($noSectionFields, $fields);
                                $pushedKeys[] = $pushedKey;
                            }
                        }
                    }
                }
            }
            // print_r($ordered_form_fields); exit;
            // $ordered_form_fields_obj = (object)$ordered_form_fields;

            $form['fields'] = array_merge($ordered_form_fields, $noSectionFields);
            // echo "<pre>";
            // print_r($form['fields']);
        }

        // exit;
        $id = RiskImprovementFormy::insertGetId($form);
        if ($id) {
            $form = RiskImprovementFormy::find($id);
            return Response::json(
                [
                'response' => 'success',
                'data' => (string)$id,
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to create form',
                ]
            );
        }
    }

    /*
    * update a form
    */

    /**
     * Get Risk Recommendation Titles
     */
    public function risk_rec_titles($id = 0)
    {
        if ($id == 0) {
            $risk_rec_data = RiskRecTitle::orderBy('title', 'asc')->get();
        } else {
            $risk_rec_data = RiskRecTitle::find($id);
        }
        if ($risk_rec_data) {
            return Response::json(
                [
                'response' => 'success',
                'data' => $risk_rec_data,
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to get Risk Recommendation titles',
                ]
            );
        }
    }

    /**
     * Add Risk Recommendation Titles
     */
    public function riskRecTitlesStore(Request $request): JsonResponse
    {
        $data = $request->all();
        $dateToday = Carbon::now();
        $data['created_at'] = $dateToday;
        $data['updated_at'] = $dateToday;
        $id = RiskRecTitle::insertGetId($data);
        if ($id) {
            return Response::json(
                [
                'response' => 'success',
                'data' => (string)$id,
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to create title',
                ]
            );
        }
    }

    /**
     * Update Risk Recommendation Titles
     */
    public function risk_rec_titles_update(Request $request, $id)
    {
        $data = $request->all();
        $id = RiskRecTitle::where('id', $id)->update($data);

        if ($id) {
            return Response::json(
                [
                'response' => 'success',
                'data' => [],
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to update title',
                ]
            );
        }
    }

    public function update(Request $request, $formId)
    {
        $formData = json_decode($request->get('form'), true);

        $form = RiskImprovementFormy::find($formId);

        if (isset($formData['fields']) && isset($formData['section_names'])) {

            $section_names = explode(',', $formData['section_names']);
            $form_fields = $formData['fields'];
            // echo "<pre>";
            // print_r($form_fields); exit;
            $ordered_form_fields = [];
            $noSectionFields = [];
            $pushedKeys = [];
            foreach ($section_names as $section) {
                foreach ($form_fields as $fields) {
                    foreach ($fields as $field_type => $field_details) {
                        foreach ($field_details as $key => $field_detail) {
                            // echo "<pre>";
                            // print_r($section); exit;
                            if ($field_detail['name'] == "section" && $field_detail['value'] == $section) {
                                array_push($ordered_form_fields, $fields);
                                continue;
                            }

                            $pushedKey = sprintf('%s-%s', $field_type, $key);
                            if (
                                $field_detail['name'] == "section"
                                && !in_array($pushedKey, $pushedKeys)
                                && !in_array($field_detail['value'], $section_names)
                            ) {
                                $fields[$field_type][$key]['value'] = '';
                                array_push($noSectionFields, $fields);
                                $pushedKeys[] = $pushedKey;
                            }
                        }
                    }
                }
            }
            // print_r($ordered_form_fields); exit;
            // $ordered_form_fields_obj = (object)$ordered_form_fields;

            $formData['fields'] = array_merge($ordered_form_fields, $noSectionFields);
            // echo "<pre>";
            // print_r($form['fields']);
        }
        $form->fill($formData);
        if ($form->save()) {
            return Response::json(
                [
                'response' => 'success',
                'data' => (string)$formId,
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to update form',
                ]
            );
        }
    }

    /**
     * Update Risk Recommendation Titles
     */
    public function risk_rec_titles_delete($id)
    {

        $id = RiskRecTitle::where('id', $id)->delete();

        if ($id) {
            return Response::json(
                [
                'response' => 'success',
                'data' => [],
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to delete title',
                ]
            );
        }
    }

    /*
    * delete a form
    */
    public function destroy($form)
    {
        $form_object = RiskImprovementFormy::find($form);
        if (RiskImprovementFormy::destroy($form)) {
            return Response::json(
                [
                'response' => 'success',
                'message' => 'Form Deleted',
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to delete form',
                ]
            );
        }
    }

    private function addDefaultGradingDescription($form_id){
        //The data will come from UI in future request
        $model=new \App\Models\RIFormsGradingsDescriptions();
        $dataCount=$model::where("form_id",$form_id)->count();
        if($dataCount <= 0){
            $data=(object)[];
            $data->form_id=$form_id;

            $data->not_applicable_not_assessed='Not Applicable';
            $data->superior='Superior';
            $data->above_average='Above Average';
            $data->average='Average';
            $data->below_average='Below Average';
            $data->poor='Poor';

            $data=(array)$data;
            $model::create($data);
        }
        
    }
}
