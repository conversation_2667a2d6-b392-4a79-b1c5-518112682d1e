<?php

namespace App\Http\Controllers\Api\V1\Community;

use App\Http\Controllers\BaseController;
use App\Models\Community\PollOption;
use App\Models\Community\PollQuestion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Carbon;

class PollController extends BaseController
{


    /**
     * Store new Poll
     */

    public function store(Request $request, $title, $poll_options)
    {
        $validator = Validator::make($request->all(), [
            'body' => 'required',
            'answers' => 'required',
        ]);

        if ($validator->fails()) {
            $response = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        } else {
            $poll = PollQuestion::create([
                'body' => $title,
            ]);

            if ($poll->_id) {
                $options = array_map(
                    function ($a) use ($poll) {
                        return array_merge($a, [
                            'question_id' => $poll->_id,
                            'created_at'  => Carbon::now()->toDateTimeString(),
                            'updated_at'  => Carbon::now()->toDateTimeString(),
                        ]);
                    }, $poll_options
                );

                PollOption::insert($options);

                $response = [
                    'response' => 'success',
                    'message' => 'The Liberty Community Poll has been created successfully',
                    'id' => $poll->_id,
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The Liberty Community Poll has failed to be created',
                ];
            }
        }

        return Response::json($response);
    }

    /**
     * Update Poll
     */

    public function update(Request $request, $id)
    {
        if (!empty($id) && is_numeric($id)) {
            $validator = Validator::make(
                $request->all(), [
                'body' => 'required',
                'answers' => 'required',
                ]
            );

            if ($validator->fails()) {
                $response = [
                    'response' => 'error',
                    'errors' => $validator->messages()->toArray(),
                ];
            } else {
                $poll = PollQuestion::with('options')->find($id);

                if ($poll->id) {
                    $poll->body = $request->get('title');
                    $poll->save();

                    $response = [
                        'response' => 'success',
                        'message' => 'The Liberty Community Poll has been updated successfully',
                    ];
                } else {
                    $response = [
                        'response' => 'error',
                        'message' => 'Invalid Liberty Community Poll',
                    ];
                }
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid Liberty Community Poll ID',
            ];
        }


        return Response::json($response);
    }


    /**
     * Find Poll
     */
    public function show($id)
    {
        if (!empty($id)) {
            $poll = PollQuestion::with('options', 'votes')->find($id);

            $response = [
                'response' => ($poll)
                    ? 'success'
                    : 'error',
            ];

            if ($poll) {
                $response['data'] = $poll;
            } else {
                $response['message'] = 'The specified Liberty Community Poll could not be found';
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid Liberty Community Poll ID',
            ];
        }

        return Response::json($response);
    }

    /**
     * Delete Poll
     *
     * @param $id
     *
     * @return mixed
     */
    public function delete($id)
    {
        if ($poll = PollQuestion::with('options', 'votes')->find($id)) {
            $poll_arr = $poll->toArray();

            if (!empty($poll_arr['options'])) {
                $poll->options()->delete();
            }
            if (!empty($poll_arr['votes'])) {
                $poll->votes()->delete();
            }


            if ($poll - destroy()) {
                $response = [
                    'response' => 'success',
                    'message' => 'The specified Liberty Community Poll was successfully deleted',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The specified Liberty Community Poll could not be deleted',
                ];
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'The specified Liberty Community Poll could not be found',
            ];
        }

        return Response::json($response);
    }
}
