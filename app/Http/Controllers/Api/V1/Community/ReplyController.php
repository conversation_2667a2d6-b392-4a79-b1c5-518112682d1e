<?php

namespace App\Http\Controllers\Api\V1\Community;

use App\Http\Controllers\BaseController;
use App\Models\Community\Reply;
use App\Models\Community\UserType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;

class ReplyController extends BaseController
{


    /**
     * Store new Reply
     */

    public function store(Request $request)
    {
        $validator = Validator::make(
            $request->all(), [
                'message' => 'required',

            ]
        );

        if ($validator->fails()) {
            $response = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        } else {
            $userTypeId = UserType::firstOrCreate(
                [
                    'user_id' => $request->get('user_id'),
                    'type' => $request->get('user_type', null),
                ]
            );

            $reply = Reply::create(
                [
                    'message_id' => $request->get('message_id'),
                    'message_reply_id' => $request->get('message_reply_id'),
                    'user_id' => $request->get('user_id'),
                    'message' => $request->get('message', null),
                    'user_type_id' => (string)$userTypeId->id,
                ]
            );

            if ($reply->_id) {
                $response = [
                    'response' => 'success',
                    'message' => 'The Liberty Community message has been created successfully',
                    'id' => $reply->_id,
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The Liberty Community message has failed to be created',
                ];
            }
        }

        return Response::json($response);
    }

    /**
     * Update Reply
     */

    public function update(Request $request, $id)
    {
        if (!empty($id)) {
            $validator = Validator::make(
                $request->all(), [
                    'message' => 'required',
                ]
            );

            if ($validator->fails()) {
                $response = [
                    'response' => 'error',
                    'errors' => $validator->messages()->toArray(),
                ];
            } else {
                $reply = Reply::find($id);

                if ($reply->id) {
                    $reply->message = $request->get('message');
                    $reply->save();

                    $response = [
                        'response' => 'success',
                        'message' => 'The Liberty Community message has been updated successfully',
                        'id' => $reply->_id,
                    ];
                } else {
                    $response = [
                        'response' => 'error',
                        'message' => 'Invalid Liberty Community message',
                    ];
                }
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid Liberty Community message ID',
            ];
        }


        return Response::json($response);
    }


    /**
     * Find Reply
     */
    public function show($id)
    {
        if (!empty($id)) {
            $reply = Reply::with(
                [
                    'userType',
                    'user',
                    'libertyUser',
                    'user.organisation',
                    'files',
                    'brokerUser',
                    'brokerUser.broker',
                    'allReplies',
                ]
            )->find($id);

            $response = [
                'response' => ($reply)
                    ? 'success'
                    : 'error',
            ];

            if ($reply) {
                $response['data'] = $reply;
            } else {
                $response['message'] = 'The specified Liberty Community Message could not be found';
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid Liberty Community Message ID',
            ];
        }

        return Response::json($response);
    }

    /**
     * Delete Reply
     *
     * @param $id
     *
     * @return mixed
     */
    public function delete($id)
    {
        if ($message = Reply::with('files', 'replies')->find($id)) {
            $message_arr = $message->toArray();

            if (!empty($message_arr['files'])) {
                $message->files()->destroy();
            }
            if (!empty($message_arr['replies'])) {
                $message->replies()->destroy();
            }


            if ($message->destroy($id)) {
                $response = [
                    'response' => 'success',
                    'message' => 'The specified Liberty Forum Reply was successfully deleted',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The specified Liberty Forum Reply could not be deleted',
                ];
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'The specified Liberty Forum Reply could not be found',
            ];
        }

        return Response::json($response);
    }


}
