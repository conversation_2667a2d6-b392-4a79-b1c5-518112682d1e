<?php

namespace App\Http\Controllers\Api\V1\Community;

use App\Http\Controllers\BaseController;
use App\Models\Community\CommunitySector;
use App\Models\Community\File;
use App\Models\Community\FileUpload;
use App\Models\Community\Like;
use App\Models\Community\Message;
use App\Models\Community\PollOption;
use App\Models\Community\PollQuestion;
use App\Models\Community\PollVote;
use App\Models\Community\Reply;
use App\Models\Community\Tag;
use App\Models\Community\UserType;
use App\Models\Community\WebinarUser;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;


class MessageController extends BaseController
{

    public function __construct(FileUpload $fileupload)
    {
        $this->files = $fileupload;
    }

    public function start()
    {
        return view('community/start', ['uuid' => Str::uuid()->toString()]);
    }

    /**
     * Store new Message
     */

    public function store(Request $request)
    {
        $validator = Validator::make(
            $request->all(), [
                'title' => 'required',
                'type' => 'required',
            ]
        );
        if ($validator->fails()) {
            return [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        }

        $data = [
            'sector_id' => $request->get('sector_id'),
            'user_id' => $request->get('user_id'),
            'type' => $request->get('type'),
            'webinar_type' => $request->get('webinar_type', null),
            'webinar_url' => $request->get('webinar_url', null),
            'video_url' => $request->get('video_url', null),
            'message' => $request->get('message', null),
            'topic' => $request->get('topic', null),
            'event_date' => $this->resolveDateTime($request),
            'timezone' => $request->get('timezone', null),
            'host' => $request->get('host', null),
            'location' => $request->get('location', null),
            'user_type_id' => (string)UserType::getModel($request->get('user_id'),
                $request->get('user_type', null))->id,
        ];

        if (!empty($request->get('has-poll'))) {
            for ($i = 1; $i <= 5; $i++) {
                if (!empty($request->get('poll-option-' . $i))) {
                    $options[]['body'] = $request->get('poll-option-' . $i);
                }
            }

            $poll = PollQuestion::create(
                [

                    'body' => $request->get('title'),
                ]
            );

            if ($poll->_id) {
                for ($i = 0; $i < count($options); $i++) {
                    $options[$i]['question_id'] = $poll->_id;
                    $options[$i]['created_at'] = Carbon::now()->toDateTimeString();
                    $options[$i]['updated_at'] = Carbon::now()->toDateTimeString();
                }

                //alternative
                // Session::put('poll_id',$poll->_id);
                //$options=array_map(function ($a) {   return array_merge($a,['question_id' => Session::get('poll_id') ,'created_at'=> Carbon::now()->toDateTimeString(),'updated_at'=> Carbon::now()->toDateTimeString()]  );  }, $arr_options);

                PollOption::insert($options);

                $data['poll_id'] = $poll->_id;
                $data['title'] = $request->get('title');
            }
        } else {
            $data['title'] = $request->get('title');
        }


        $message = Message::create($data);


        if ($message->_id) {
            if (!empty($request->get('tags'))) {
                $tags = json_decode($request->get('tags'), true);


                for ($i = 0; $i < count($tags); $i++) {
                    $tags[$i]['message_id'] = $message->_id;
                    $tags[$i]['created_at'] = Carbon::now()->toDateTimeString();
                    $tags[$i]['updated_at'] = Carbon::now()->toDateTimeString();
                }

                $tag = Tag::insert($tags);
            }


            $response = [
                'response' => 'success',
                'message' => 'The Liberty Forum ' . ucwords($request->get('type')) . ' has been created successfully',
                'id' => $message->id,
            ];
        } else {
            $response = [
                'response' => 'error',
                'message' => 'The Liberty ' . ucwords($request->get('type')) . ' has failed to be created',
            ];
        }

        return Response::json($response);
    }

    /**
     * Get All Messages
     */
    public function all(Request $request, $page = 1, $limit = 10)
    {
        $subscriptions = is_array($request->get('subscriptions'))
            ? $request->get('subscriptions')
            : json_decode($request->get('subscriptions'));

        if(empty($subscriptions)) {
            return Response::json(
                [
                    'response' => 'success',
                    'data' => [],
                    'total' => 0,
                ]
            );
        }

        foreach ($subscriptions ?? [] as $key => $value) {
            $querySubscrption[] = $key;
        }

        $discussionType = $request->get('discussion_type');
        $discussionType = ($discussionType)
            ? $discussionType
            : '';

        $tag = $request->get('tag');
        $tag = ($tag)
            ? $tag
            : '';

        $page = $request->get('page');
        $page = ($page)
            ? $page
            : 1;

        $query = $request->get('search');
        //$query = ($query) ? '%'.$query.'%' : '';
        $query = ($query)
            ? '/.*' . preg_quote($query) . '.*/i'
            : '';

        $joinWith = [
            'user',
            'libertyUser',
            'user.organisation',
            'files',
            'tags',
            'poll',
            'userType',
            'brokerUser',
            'brokerUser.broker',
        ];

        $messages = ($query)
            ? Message::where('title', 'regexp', $query)
            : Message::query();

        $messages = $messages->take(
            $limit
        )
            ->with($joinWith)
            ->skip(
                ($page * $limit) - $limit
            );

        if (isset($discussionType) && !empty($discussionType) && $discussionType != "all") {
            $messages = $messages->where('type', '=', $discussionType);
        }

        $messages = $messages->whereIn('sector_id', $querySubscrption)
            ->orderBy('created_at', 'DESC')
            ->get();

        $messagesSearchAll = ($query)
            ? Message::where('title', 'regexp', $query)
            : Message::query();

        if (isset($discussionType) && !empty($discussionType) && $discussionType != "all") {
            $messagesSearchAll = $messagesSearchAll->where('type', '=', $discussionType);
        }

        $messagesSearchAll = $messagesSearchAll->whereIn('sector_id', $querySubscrption)
            ->orderBy('created_at', 'DESC')
            ->get();

        // Prioritise Tag Search if Exist
        $tagSearch = Message::whereHas(
            'tags', function ($q) use ($query, $tag) {
            if ($query) {
                $q->orWhere('name', 'LIKE', $query);
            } else {
                $q->orWhere('tag_id', '=', $tag);
            }
        }
        );
        $tagSearch = $tagSearch->take(
            $limit
        )
            ->with($joinWith)
            ->skip(
                ($page * $limit) - $limit
            )
            ->whereIn('sector_id', $querySubscrption)
            ->orderBy('created_at', 'DESC')
            ->get();


        // Tag Search Count
        $tagSearchAll = Message::whereHas(
            'tags', function ($q) use ($query, $tag) {
            if ($query) {
                $q->orWhere('name', 'LIKE', $query);
            }

            if ($tag) {
                $q->orWhere('tag_id', '=', $tag);
            }
        }
        )->with($joinWith)
            ->whereIn('sector_id', $querySubscrption)
            ->orderBy('created_at', 'DESC')
            ->get();

        if (count($tagSearch) > 0) {
            $totalPage = ceil($tagSearchAll->count() / $limit);
        } else {
            if ($messagesSearchAll->count() > 0) {
                $totalPage = ceil($messagesSearchAll->count() / $limit);
            } else {
                if ($messages->count() > 0) {
                    $totalPage = ceil(count(Message::all()) / $limit);
                } else {
                    $totalPage = 1;
                }
            }
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => (count($tagSearch) > 0)
                    ? $tagSearch
                    : $messages,
                'total' => $totalPage,
            ]
        );
    }

    private function resolveDateTime(Request $request)
    {
        $dateTime = null;
        if (!empty($request->get('date'))) {
            $date = date("Y-m-d", strtotime($request->get('date')));
            $time = !empty($request->get('time'))
                ? date("H:i:s", strtotime($request->get('time')))
                : "00:00:00";
            $dateTime = Carbon::createFromFormat("Y-m-d H:i:s", "$date $time")->toDateTimeString();
        }

        return $dateTime;
    }

    /**
     * Update Message
     */

    public function update(Request $request, $id)
    {
        if (!empty($id)) {
            $validator = Validator::make(
                $request->all(), [
                    'title' => 'required',

                ]
            );

            if ($validator->fails()) {
                $response = [
                    'response' => 'error',
                    'errors' => $validator->messages()->toArray(),
                ];
            } else {
                $message = Message::with('poll', 'tags', 'files')->find($id);

                if ($message->_id) {
                    //$message->webinar_type = $request->get('webinar_type', null);
                    $message->webinar_url = $request->get('webinar_url', null);
                    $message->video_url = $request->get('video_url', null);
                    $message->message = $request->get('message', null);
                    $message->topic = $request->get('topic', null);
                    $message->event_date = $this->resolveDateTime($request);
                    $message->timezone = $request->get('timezone', null);
                    $message->host = $request->get('host', null);
                    $message->location = $request->get('location', null);
                    $message->sector_id = $request->get('sector_id', null);
                    if (empty($message->poll_id)) {
                        $message->title = $request->get('title');
                    } else {
                        $message->title = $request->get('title');
                        $message->poll->body = $request->get('title');
                        $message->poll->save();
                    }
                    $message->user_type = $request->get('user_type', null);
                    $message->save();


                    if (!empty($message->poll_id)) {
                        $poll = PollQuestion::with('options')->find($message->poll_id);
                        // dd($poll->options->toArray());
                        $options = $poll->options;
                        $destroy = [];
                        for ($i = 0; $i < 5; $i++) {
                            if (count($options) > $i) {
                                if (empty($request->get('poll-option-' . ($i + 1)))) {
                                    $poll->options[$i]->body = '';
                                    $destroy[] = $i;
                                } else {
                                    $poll->options[$i]->body = $request->get('poll-option-' . ($i + 1));
                                    $poll->options[$i]->save();
                                }
                            } else {
                                if (!empty($request->get('poll-option-' . ($i + 1)))) {
                                    PollOption::create(
                                        [
                                            'question_id' => $poll->_id,
                                            'body' => $request->get('poll-option-' . ($i + 1)),
                                        ]
                                    );
                                }
                            }
                        }

                        foreach ($destroy as $key => $val) {
                            $poll->options[$val]->delete();
                        }
                    }


                    $message->tags()->delete();

                    if (!empty($request->get('tags'))) {
                        $tags = json_decode($request->get('tags'), true);
                        $new_tags = [];


                        for ($i = 0; $i < count($tags); $i++) {
                            $tag = $message->tags()->withTrashed()->where('tag_id', $tags[$i]['tag_id']);


                            if (empty($tag->first())) {
                                $tags[$i]['message_id'] = $message->id;
                                $tags[$i]['created_at'] = Carbon::now()->toDateTimeString();
                                $tags[$i]['updated_at'] = Carbon::now()->toDateTimeString();
                                $new_tags[] = $tags[$i];
                            } else {
                                $tag->restore();
                            }
                        }

                        if (!empty($new_tags)) {
                            $tag = Tag::insert($new_tags);
                        }
                    }

                    $response = [
                        'response' => 'success',
                        'message' => 'The Liberty ' . ucwords($message->type) . ' has been updated successfully',
                        'id' => $message->_id,
                    ];
                } else {
                    $response = [
                        'response' => 'error',
                        'message' => 'Invalid Liberty Message ID',
                    ];
                }
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid Liberty Message ID',
            ];
        }


        return Response::json($response);
    }

    /**
     * Delete Message
     *
     * @param $id
     *
     * @return mixed
     */
    public function delete($id)
    {
        if ($message = Message::find($id)) {
            // if(!empty($message->tags->toArray())) $message->tags()->delete();
            // if(!empty($message->files->toArray())) $message->files()->delete();
            // if(!empty($message->all_replies->toArray())){

            //}


            if ($message->delete()) {
                Tag::where('message_id', $id)->delete();
                Reply::where('message_id', $id)->delete();
                File::where('message_id', $id)->delete();

                $response = [
                    'response' => 'success',
                    'message' => 'The specified Liberty Forum Message was successfully deleted',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The specified Liberty Forum Message could not be deleted',
                ];
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'The specified Liberty Forum Message could not be found',
            ];
        }

        return Response::json($response);
    }

    /**
     * Find Message
     */
    public function show($id)
    {
        if (!empty($id)) {

            $message = Message::with(
                [
                    'poll',
                    'userType',
                    'user',
                    'user.organisation',
                    'tags',
                    'files',
                    'libertyUser',
                    'allReplies' => [
                        'userType',
                        'libertyUser',
                        'user',
                        'brokerUser',
                        'brokerUser.broker',
                    ],
                    'brokerUser',
                    'brokerUser.broker',
                ]
            )->find($id);

            $response = [
                'response' => !empty($message)
                    ? 'success'
                    : 'error',
            ];

            if (!empty($message)) {
                $response['data'] = $message;
            } else {
                $response['message'] = 'The specified Liberty Forum Message could not be found';
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid Liberty Message ID',
            ];
        }

        return Response::json($response);
    }

    public function like(Request $request, $id, $reply_id)
    {
        $data = [
            'message_id' => $id,
            'message_reply_id' => $reply_id,
            'user_id' => $request->get('user_id'),
            'user_type' => $request->get('user_type'),
        ];

        $like = Like::withTrashed()->where($data);
        if (empty($like->first())) {
            Like::create($data);
        } else {
            if (!empty($like->first()->deleted_at)) {
                $like->restore();
            } else {
                $like->delete();
            }
        }
        unset($data['user_id']);
        unset($data['user_type']);
        $likes = Like::where($data)->get()->count();

        return Response::json($likes);
    }

    public function interested(Request $request, $id)
    {
        $data = ['message_id' => $id, 'user_id' => $request->get('user_id')];

        $user = WebinarUser::withTrashed()->where($data);
        if (empty($user->first())) {
            WebinarUser::create($data);
        } else {
            if (!empty($user->first()->deleted_at)) {
                $user->restore();
                return true;
            } else {
                $user->delete();
                return false;
            }
        }
    }

    public function vote(Request $request)
    {
        $data = $request->all();
        $type = $data['type'] ?? 'client';
        $userType = UserType::getModel($data['user_id'], $type);

        unset($data['user_id']);
        $data['user_type_id'] = $userType->id;
        $vote = PollVote::firstOrCreate($data);

        if (!empty($vote->first())) {
            $response = [
                'response' => 'success',
                'message' => 'Vote was posted',
            ];
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Failed to vote',
            ];
        }


        return Response::json($response);
    }

    public function getOrphanedSectors()
    {
        $all = $this->getSectors();

        return Response::json(
            [
                'response' => 'success',
                'data' => array_diff($this->getMessagesSectors(), $all),
            ]
        );
    }

    /**
     * Get all sectors from CMS
     */
    public function getSectors()
    {
        $data = json_decode(CommunitySector::all());
        foreach ($data->data as $sector) {
            $sectors[] = $sector->_id;
        }
        return $sectors;
    }

    /**
     * Get All Sectors that are having at least a message
     */

    public function getMessagesSectors()
    {
        $messages = Message::raw()->distinct('sector_id');

        foreach ($messages as $message) {
            if ($message) {
                $sectors[] = $message;
            }
        }
        return $sectors ?? [];
    }

    public function options()
    {
        return Response::json(
            Message::orderBy('title', 'ASC')->pluck(
                'name',
                'id'
            )->all()
        );
    }

    /**
     * Get All Upcoming Event
     *
     * @param  integer  $userId
     * @param  string  $type
     * @return mixed
     */
    public function getUpcomingEvent($userId, $type = 'client', $limit = 10)
    {
        $subscriptions = UserType::getModel($userId, $type)->getUserSubscriptions();

        $subscriptionIds = array_map(
            function ($subscription) {
                return $subscription['sector_id'];
            }, is_array($subscriptions)
            ? $subscriptions
            : $subscriptions->toArray()
        );

        $upcoming_events = Message::whereIn(
            'type', ['webinar', 'event']
        )
            ->whereIn('sector_id', $subscriptionIds)
            ->where('event_date', '>=', Carbon::now()->toDateTimeString())
            ->orderBy('event_date', 'ASC')
            ->limit($limit)
            ->get();

        return Response::json(
            [
                'response' => 'success',
                'data' => $upcoming_events,
            ]
        );
    }

    /**
     * Get Popular Event Per Type
     */
    public function getPopularEvent($type, $subscription, $user_type)
    {
        if ($subscription && $subscription != "0") {
            $subscriptions = json_decode($subscription);

            foreach ($subscriptions as $key => $value) {
                $querySubscrption[] = $key;
            }
        }
        // get all available discussion for the same type
        $popularEvents = Message::with('allReplies')
            ->where('type', '=', $user_type)
            ->whereIn('sector_id', $querySubscrption)
            ->get();

        $popularEventsCriteria = [];
        foreach ($popularEvents as $key => $value) {
            $datePosted = date('Y-m-d', strtotime($value->created_at));
            $now = date('Y-m-d');
            $datediff = round((time() - strtotime($value->created_at)) / (60 * 60 * 24));

            // Case: Post is created within the day then give 10 points
            if ($now === $datePosted) {
                if (isset($popularEventsCriteria[$value->_id]) && !empty($popularEventsCriteria[$value->_id])) {
                    $popularEventsCriteria[$value->_id] += 10;
                } else {
                    $popularEventsCriteria[$value->_id] = 0; // intialize new array
                    $popularEventsCriteria[$value->_id] += 10;
                }
            }

            if ($datediff >= 2 && $datediff <= 4) {
                if (isset($popularEventsCriteria[$value->_id]) && !empty($popularEventsCriteria[$value->_id])) {
                    $popularEventsCriteria[$value->_id] += 5;
                } else {
                    $popularEventsCriteria[$value->_id] = 0; // intialize new array
                    $popularEventsCriteria[$value->_id] += 5;
                }
            }

            // Case: Count All Comments
            if (isset($popularEventsCriteria[$value->_id]) && !empty($popularEventsCriteria[$value->_id])) {
                $popularEventsCriteria[$value->_id] += $value->comments_count * 2;
            } else {
                $popularEventsCriteria[$value->_id] = 0; // intialize new array
                $popularEventsCriteria[$value->_id] += $value->comments_count * 2;
            }

            // Case: Count All Comment Latest
            if (isset($value->allReplies) && !empty($value->allReplies)) {
                foreach ($value->allReplies as $reply) {
                    $dateReplied = date('Y-m-d', strtotime($reply->created_at));
                    $datediff2 = round((time() - strtotime($reply->created_at)) / (60 * 60 * 24));
                    if ($now === $dateReplied) {
                        if (isset($popularEventsCriteria[$value->_id]) && !empty($popularEventsCriteria[$value->_id])) {
                            $popularEventsCriteria[$value->_id] += 10;
                        } else {
                            $popularEventsCriteria[$value->_id] = 0; // intialize new array
                            $popularEventsCriteria[$value->_id] += 10;
                        }
                    }

                    if ($datediff >= 2 && $datediff <= 4) {
                        if (isset($popularEventsCriteria[$value->_id]) && !empty($popularEventsCriteria[$value->_id])) {
                            $popularEventsCriteria[$value->_id] += 5;
                        } else {
                            $popularEventsCriteria[$value->_id] = 0; // intialize new array
                            $popularEventsCriteria[$value->_id] += 5;
                        }
                    }
                }
            }
        }

        $popularEvents = [];
        if (!empty($popularEventsCriteria)) {
            arsort($popularEventsCriteria);

            $popularEventsCriteria = array_slice($popularEventsCriteria, 0, 4);

            foreach ($popularEventsCriteria as $key => $finalPost) {
                $posts[] = $key;
            }

            // //re-query the top 4 popular post
            $popularEvents = Message::with(
                [
                    'files',
                    'tags',
                ]
            )
                ->whereIn('_id', $posts);

            $popularEvents = $popularEvents->get();
        }


        $sortedPopularEvents = $this->sortPopularEvents($popularEvents, $popularEventsCriteria);
        return Response::json(
            [
                'response' => 'success',
                'data' => $sortedPopularEvents,
            ]
        );
    }

    private function sortPopularEvents($popularEvents, $sortedIds)
    {
        $sortedEvents = [];
        foreach ($sortedIds as $sortedId => $score) {
            foreach ($popularEvents as $popularEvent) {
                if ($popularEvent->_id === $sortedId) {
                    $popularEvent->score = $score;
                    $sortedEvents[] = $popularEvent;
                }
            }
        }

        return $sortedEvents;
    }
}
