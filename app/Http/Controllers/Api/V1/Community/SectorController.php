<?php

namespace App\Http\Controllers\Api\V1\Community;

use App\Http\Controllers\BaseController;
use App\Models\Community\CommunitySector;
use App\Models\Community\OrganisationSector;
use App\Models\Community\UserSector;
use App\Models\Community\UserType;
use App\Models\Sector;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;


class SectorController extends BaseController
{
    public function options()
    {
        return Response::json(
            Sector::all()->pluck(
                'name',
                'id'
            )
        );
    }

    public function getSector()
    {
        try {
            $data = CommunitySector::all();
            return Response::json(
                [
                    'message' => 'success',
                    'data' => $data,
                ]
            );
        } catch (\Exception $e) {
            return Response::json(
                [
                    'message' => 'error',
                    'data' => $e,
                ]
            );
        }
    }

    public function all()
    {
        $data = []; // get Sectors from CMS API

        if (isset($data)) {
            return Response::json(
                [
                'response' => 'success',
                'data' => $data,
                ]
            );
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'Could not return sectors',
            ]
        );
    }

    public function unsubscribe(Request $request)
    {
        $sectorId = $request->get('sector_id');
        $userId = $request->get('user');
        $type = $request->get('type')
            ?: 'client';

        UserSector::unsubscribe($userId, $type, $sectorId);

        return Response::json(
            [
            'message' => 'success',
            ]
        );
    }

    public function orgSubscribe(Request $request)
    {
        $id = $request->get('sector_id');
        $orgId = $request->get('organisation');

        //check if the user is subscribed already
        $exists = OrganisationSector::where('organisation_id', $orgId)->where('sector_id', $id)->first();
        if (!$exists) {
            $subscription = OrganisationSector::subscribe($id, $orgId);

            if ($subscription) {
                return Response::json(
                    [
                    'message' => 'success',
                    ]
                );
            }
        }

        return Response::json(
            [
            'message' => 'fail',
            ]
        );
    }

    public function subscribe(Request $request)
    {
        $sectorId = $request->get('sector_id');
        $userId = $request->get('user');
        $type = $request->get('type')
            ?: 'client';
        $userType = UserType::firstOrCreate(['user_id' => $userId, 'type' => $type]);

        //check if the user is subscribed already
        if (!$this->isSubscribed($userType->id, $sectorId)) {
            $subscription = UserSector::subscribe($userType, $sectorId);

            if ($subscription) {
                return Response::json(
                    [
                    'message' => 'success',
                    ]
                );
            }
        }

        return Response::json(
            [
            'message' => 'fail',
            ]
        );
    }

    private function isSubscribed($userTypeId, $sector)
    {
        return UserSector::where('user_type_id', $userTypeId)->where('sector_id', $sector)->first();
    }

    public function orgUnsubscribe(Request $request)
    {
        $id = $request->get('sector_id');
        $orgId = $request->get('organisation');

        OrganisationSector::unsubscribte($id, $orgId);

        return Response::json(
            [
            'message' => 'success',
            ]
        );
    }

    public function getUserSubscription($userId, $type = 'client')
    {
        $subscriptions = UserType::getModel($userId, $type)->getUserSubscriptions();

        $subscriptions = array_map(
            function ($subscription) {
                return $subscription['sector_id'];
            },
            is_array($subscriptions)
                ? $subscriptions
                : $subscriptions->toArray()
        );

        return Response::json(
            [
            'message' => 'success',
            'data' => $subscriptions,
            ]
        );
    }

    public function getOrganisationSubscription($id)
    {
        $subscriptions = OrganisationSector::where('organisation_id', $id)->get();

        if ($subscriptions) {
            $subscribedSectors = [];
            foreach ($subscriptions as $subscription) {
                $subscribedSectors[] = $subscription->sector_id;
            }

            return Response::json(
                [
                'message' => 'success',
                'data' => $subscribedSectors,
                ]
            );
        }

        return Response::json(
            [
            'message' => 'fail',
            ]
        );
    }
}
