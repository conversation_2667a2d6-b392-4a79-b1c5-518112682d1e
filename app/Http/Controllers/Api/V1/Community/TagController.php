<?php

namespace App\Http\Controllers\Api\V1\Community;

use App\Http\Controllers\BaseController;
use App\Models\Community\Cms;
use App\Models\Community\Tag;
use App\Models\Sector;
use Illuminate\Support\Facades\Response;

class TagController extends BaseController
{


    public function getPopularCommunityTag()
    {
        $existingTags = json_decode($this->getCommunityTag()->getContent());

        if ($existingTags->message == 'success') {
            $data = json_decode($existingTags->data);

            if ($data) {
                $data = $data->data;
                $availableTags = array_map(
                    function ($e) {
                        return $e->_id;
                    }, $data
                );

                $tag = Tag::all()->groupBy('tag_id');
                $tags = [];
                foreach ($tag as $key => $value) {
                    if (in_array($key, $availableTags)) {
                        $tags[$value[0]->name . ':' . $value[0]->tag_id] = count($tag[$key]);
                    }
                }

                return Response::json(
                    [
                        'message' => 'success',
                        'data' => $tags,
                    ]
                );
            }
        }
    }

    public function getCommunityTag()
    {
        try {
            $workspace_id = config('app.cmsForum.forum_sector_workspace_id');
            $content_type_id = config('app.cmsForum.forum_tag_content_type_id');

            $data = Cms::getForum('workspaces/' . $workspace_id . '/content-types/' . $content_type_id . '/content-entries?query={"status":"publish","operator":"="}&');

            $this->refresh($data);

            return Response::json(
                [
                    'message' => 'success',
                    'data' => $data,
                ]
            );
        } catch (\Exception $e) {
            return Response::json(
                [
                    'message' => 'error',
                    'data' => $e,
                ]
            );
        }
    }

    public function refresh($data = [])
    {
        if (empty($data)) {
            $existingTags = json_decode($this->getCommunityTag()->getContent());
            if ($existingTags->message == 'success') {
                $data = $existingTags->data;
            }
        }


        $dataObj = json_decode($data);
        if (!empty($data) && isset($dataObj->data)) {
            $availableTags = array_map(
                function ($e) {
                    return $e->_id;
                }, $dataObj->data
            );
    
            $tags = Tag::distinct('tag_id')->get();
    
            foreach ($tags as $tag) {
                $dbTags[] = $tag[0];
            }
    
            if (!empty($dbTags)) {
                $tagsToDelete = array_diff($dbTags, $availableTags);
            }
    
            if (!empty($tagsToDelete)) {
                Tag::whereIn('tag_id', $tagsToDelete)->delete();
            }
        }
        return Response::json(
            [
                'response' => 'success',
                'message' => 'Completed tag refresh',
            ]
        );
    }


    public function all()
    {
        $data = Tag::all();

        if (isset($data)) {
            return Response::json(
                [
                'response' => 'success',
                'data' => $data->toarray(),
                ]
            );
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'Could not return sectors',
            ]
        );
    }

    public function options()
    {
        return Response::json(
            Sector::pluck(
                'name',
                'id'
            )->all()
        );
    }

}
