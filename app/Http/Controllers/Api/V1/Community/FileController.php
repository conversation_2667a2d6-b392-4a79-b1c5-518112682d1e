<?php

namespace App\Http\Controllers\Api\V1\Community;

use App\Http\Controllers\BaseController;
use App\Models\Community\File;
use App\Models\Community\FileUpload;
use Illuminate\Http\Request;


class FileController extends BaseController
{
    private $files;

    public function __construct(FileUpload $fileupload)
    {
        $this->files = $fileupload;
    }

    /**
     * Upload File
     */
    public function upload(Request $request)
    {
        $newfile = File::create(
            [
                'message_id' => $request->get('root_parent_id'),
                'message_reply_id' => $request->get('parent_id'),
                'file' => $request->get('cloud_file'),
                'original_filename' => $request->get('original_file'),
                'type' => $request->get('type'),
            ]
        );

        if ($newfile) {
            $response = [
                'response' => 'success',
                'message' => 'The Liberty Community File has been uploaded successfully',
                'url' => $this->getLink($newfile->_id),
                'id' => $newfile->_id,
            ];
        } else {
            $response = [
                'response' => 'error',
                'message' => 'The Liberty Community File has failed to upload',
            ];
        }

        return $response;
    }


    private function getLink($id)
    {
        $url = ($file = File::find($id))
            ? $this->files->link('community/' . $file->message_id . '/' . $file->file)
            : '#';
        return $url;
    }


    public function link($id)
    {
        if ($file = File::find($id)) {
            $response = [
                'response' => 'success',
                'message' => 'The specified Liberty Community discussion file was successfully deleted',
                'url' => $this->files->link('community/' . $file->message_id . '/' . $file->file),
            ];
        } else {
            $response = [
                'response' => 'error',
                'message' => 'The specified Liberty Forum discussion file could not be found',
            ];
        }

        return $response;
    }


    public function delete($id)
    {
        if ($file = File::find($id)) {
            if (File::destroy($id)) {
                $response = [
                    'response' => 'success',
                    'message' => 'The specified Liberty Community discussion file was successfully deleted',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The specified Liberty Forum discussion file could not be deleted',
                ];
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'The specified Liberty Forum discussion file could not be found',
            ];
        }

        return $response;
        // Response::json($response);
    }
}
