<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\Course;
use App\Models\Lesson;
use App\Models\LmsTestAttempts;
use App\Models\LmsTestResults;
use App\Models\LmsTests;
use App\Models\LmsUserLesson;
use App\Models\Pages;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class LearningLessonTestController extends BaseController
{
    /**
     * Delete Page
     *
     * @param  $lesson_id
     * @param  $page_id
     * @return mixed
     */
    public function delete($lesson_id)
    {
        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json([
                'response' => 'error',
                'message' => 'Lesson does not exist',
            ]);
        }


        $test = LmsTests::whereIn('lesson_id', [(string)$lesson_id, (int)$lesson_id])->first();
        if (!$test) {
            return response()->json([
                'response' => 'error',
                'message' => 'Test does not exist',
            ]);
        }

        $test->delete();
        return response()->json([
            'response' => 'success',
            'message' => 'Test deleted',
        ]);
    }

    /**
     * Store page
     *
     * @param  $lesson_id
     * @return mixed
     */
    public function store($lesson_id)
    {

        $lesson = Lesson::find($lesson_id);

        if (!$lesson) {
            return response()->json([
                'response' => 'error',
                'message' => 'Lesson does not exist',
            ]);
        }

        $testCount = LmsTests::whereIn('lesson_id', [(string)$lesson_id, (int)$lesson_id])->get()->count();
        if ($testCount != 1) {
            $test = LmsTests::create([
                'lesson_id' => (int)$lesson_id,
                'pass_mark' => 0,
                'amount_questions' => 0,
                'correct_answer' => 0,
            ]);

            //$this->getRelations($test);
            return response()->json([
                'response' => 'success',
                'data' => $test,
                'message' => 'Test created successfully',
            ]);
        }

        return response()->json([
            'response' => 'error',
            'message' => 'Just one test per lesson',
        ]);
    }

    /**
     * Store Section
     *
     * @param  $lesson_id
     * @param  $page_id
     * @return mixed
     */
    public function storeSection(Request $request, $lesson_id)
    {
        $rules = [
            'type' => 'required',
        ];

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json(['response' => 'errors', 'error' => $validator->errors()]);
        }

        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json(['response' => 'error', 'message' => 'Lesson Does not exist']);
        }

        $data       = $request->except('_token');
        $data['id'] = uniqid($lesson_id);
        $test       = LmsTests::whereIn('lesson_id', [(string)$lesson_id, (int)$lesson_id])->first();
        if (isset($test->sections)) {
            $sections = $test->sections;
            array_push($sections, $data);
            $test->sections = $sections;
            $test->save();
        } else {
            //Add section
            $test->sections = [$data];
            $test->save();
        }

        return response()->json([
            'response' => 'success',
            'data'     => $data,
            'message'  => 'Section added successfully'
        ]);
    }

    /**
     * Delete Section
     *
     * @param  $lesson_id
     * @param  $page_id
     * @param  $section_id
     * @return mixed
     */
    public function deleteSection($lesson_id, $section_id)
    {
        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json(['response' => 'error', 'message' => 'Lesson does not exist']);
        }

        $test = LmsTests::whereIn('lesson_id', [(string)$lesson_id, (int)$lesson_id])->first();
        if (isset($test->sections)) {
            $sections = $test->sections;
            foreach ($sections as $key => $section) {
                if ($section['id'] == $section_id) {
                    unset($sections[$key]);
                }
            }

            $test->sections = $sections;
            $test->save();
            return response()->json(['response' => 'success', 'message' => 'Section deleted']);
        }
        return response()->json(['response' => 'error', 'message' => 'There was an error updating. Please try again']);
    }

    /**
     * Update Section
     *
     * @param  $lesson_id
     * @param  $page_id
     * @param  $section_id
     * @return mixed
     */
    public function updateSection(Request $request, $lesson_id, $section_id)
    {

        $lesson = Lesson::find($lesson_id);

        if (!$lesson) {
            return response()->json([
                'response' => 'error',
                'message'  => 'Lesson does not exist'
            ]);
        }

        $test = LmsTests::whereIn('lesson_id', [(string)$lesson_id, (int)$lesson_id])->first();
        $data = $request->except('_token');
        if (isset($test->sections)) {
            $sections = $test->sections;
            foreach ($sections as $key => $section) {
                if ($section['id'] == $section_id) {
                    foreach ($data as $name => $input) {
                        $sections[$key][$name] = $input;
                    }
                }
            }

            $test->sections = $sections;
            $test->save();
            return response()->json([
                'response' => 'success',
                'data' => [$lesson_id, $section_id]
            ]);
        }

        return response()->json([
            'response' => 'error',
            'message'  => 'There was an error updating. Please try again'
        ]);
    }

    /**
     * Reorder Sections
     *
     * @param  $lesson_id
     * @param  $page_id
     * @return mixed
     */
    public function sortSections(Request $request, $lesson_id, $page_id)
    {

        $rules = [
            'section_ids' => 'required|array',
        ];

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json(['response' => 'error', 'errors' => $validator->errors()]);
        }

        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json(['response' => 'error', 'message' => 'Lesson does not exist']);
        }

        $page = Pages::where('lesson_id', '=', (int)$lesson_id)
            ->where('_id', '=', $page_id)
            ->first();

        if (!$page) {
            return response()->json(['response' => 'error', 'message' => 'Page does not exist']);
        }

        $new_ordered_sections = [];
        $section_ids          = $request->get('section_ids');


        if (isset($page->sections)) {
            $sections = $page->sections;
            foreach ($section_ids as $id) {
                foreach ($sections as $key => $section) {
                    if ($section['id'] == $id) {
                        array_push($new_ordered_sections, $section);
                    }
                }
            }

            $page->sections = $new_ordered_sections;
            $page->save();
        }

        return response()->json(['response' => 'success', 'data' => $page]);
    }

    public function duplicateSection($lesson_id, $section_id)
    {
        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json(['response' => 'error', 'message' => 'Lesson does not exist']);
        }

        $test = LmsTests::whereIn('lesson_id', [(string)$lesson_id, (int)$lesson_id])->first();
        if (isset($test->sections)) {
            $sections = $test->sections;
            foreach ($sections as $key => $section) {
                if ($section['id'] == $section_id) {
                    $new_id        = uniqid($lesson_id);
                    $section['id'] = $new_id;
                    if (isset($section['options'])) {
                        $options = $section['options'];
                        foreach ($options as $optionKey => $option) {
                            $option['id'] = uniqid($lesson_id);
                            $options[$optionKey] = $option;
                        }
                        $section['options'] = $options;
                    }
                    $new_section = $section;
                    array_push($sections, $section);
                }

            }

            $test->sections = $sections;
            $test->save();

            return response()->json([
                'response' => 'success',
                'data'     => isset($new_section)
                    ? $new_section
                    : null,
            ]);
        }

        return response()->json(['response' => 'error', 'message' => 'Section does not exist']);
    }

    public function addOption(Request $request, $lesson_id, $section_id, $question_type)
    {
        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json(['response' => 'error', 'message' => 'Lesson does not exist']);
        }

        $test       = LmsTests::whereIn('lesson_id', [(string)$lesson_id, (int)$lesson_id])->first();
        $data       = $request->except('_token');
        $data['id'] = uniqid($lesson_id);

        if (isset($test->sections)) {
            $sections = $test->sections;
            foreach ($sections as $key => $section) {
                if ($section['id'] == $section_id) {
                    if (!isset($section['question_type'])) {
                        $section['question_type'] = '0';
                    }

                    if (isset($section['options'])) {
                        $options = $section['options'];
                    } else {
                        $options = [];
                    }

                    array_push($options, $data);
                    $section['options'] = $options;
                }
                $sections[$key] = $section;
            }

            $test->sections = $sections;
            $test->save();
            return response()->json(['response' => 'success', 'data' => $data]);
        }

        return response()->json([
            'response' => 'error',
            'message'  => 'There was an error inserting. Please try again'
        ]);
    }

    public function removeOption($lesson_id, $section_id, $option_id)
    {
        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json(['response' => 'error', 'message' => 'Lesson does not exist']);
        }

        $test = LmsTests::whereIn('lesson_id', [(string)$lesson_id, (int)$lesson_id])->first();
        if (isset($test->sections)) {
            $sections = $test->sections;
            foreach ($sections as $key => $section) {
                if ($section['id'] == $section_id) {
                    $options = $section['options'];
                    foreach ($options as $optionKey => $option) {
                        if ($option['id'] == $option_id) {
                            unset($options[$optionKey]);
                        }
                    }
                    $section['options'] = $options;
                }
                $sections[$key] = $section;
            }

            $test->sections = $sections;
            $test->save();
            return response()->json(['response' => 'success']);
        }

        return response()->json(['response' => 'error', 'message' => 'There was an error inserting. Please try again']);
    }

    public function changeQuestionType($lesson_id, $section_id, $question_type)
    {

        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json(['response' => 'error', 'message' => 'Lesson does not exist']);
        }

        $test = LmsTests::whereIn('lesson_id', [(string)$lesson_id, (int)$lesson_id])->first();
        if (isset($test->sections)) {
            $sections = $test->sections;
            foreach ($sections as $key => $section) {
                if ($section['id'] == $section_id) {
                    $section['question_type'] = $question_type;
                }
                $sections[$key] = $section;
            }

            $test->sections = $sections;
            $test->save();
            return response()->json(['response' => 'success']);
        }

        return response()->json(['response' => 'error', 'message' => 'There was an error updating. Please try again']);
    }

    public function setOptions(Request $request, $lesson_id)
    {
        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json(['response' => 'error', 'message' => 'Lesson does not exist']);
        }

        $data                  = $request->except('_token');
        $test                  = LmsTests::whereIn('lesson_id', [(string)$lesson_id, (int)$lesson_id])->first();
        $test->$data['option'] = $data['value'];
        $test->save();
        return response()->json(['response' => 'success']);

    }

    public function check(Request $request, $lesson_id)
    {
        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json(['response' => 'error', 'message' => 'Lesson does not exist - ' . $lesson_id]);
        }
        $data = $request->except('_token');
        $test = LmsTests::whereIn('lesson_id', [(string)$lesson_id, (int)$lesson_id])->first();

        return response()->json(['response' => 'success', 'data' => $test]);
    }

    public function finishTest(Request $request, $lesson_id)
    {
        $rules = [
            'user_id'   => 'required',
            'score'     => 'required',
            'status'    => 'required',
            'questions' => 'required',
        ];

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json(['response' => 'errors', 'error' => $validator->errors()]);
        }

        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json(['response' => 'error', 'message' => 'Lesson does not exist']);
        }

        $data       = $request->except('_token');
        $testStatus = 0;
        $newPage    = 1;
        if ($data['status'] == 'Fail') {
            $testStatus = 2;
            $statusUserLesson = 1;
        } else {
            if ($data['status'] == 'Complete') {
                $testStatus = 1;
                $statusUserLesson = 2;

            }
        }

        $questions = json_decode($data['questions']);
        $test = LmsTests::whereIn('lesson_id', [(string)$lesson_id, (int)$lesson_id])->first();
        $attempt = LmsTestAttempts::create([
            'user_id'          => $data['user_id'],
            'lesson_id'        => (int)$lesson_id,
            'pass_mark'        => $test->pass_mark,
            'amount_questions' => $test->amount_questions,
            'score'            => $data['score'],
            'status'           => $data['status'],
            'pass'             => $data['pass']
            //'sections'           => $test->sections
        ]);

        $arrQuestions = [];
        foreach ($questions as $questionKey => $question) {
            $optionsArray = [];
            foreach ($question->options as $keyOption => $option) {
                $optionsArray[$option->id] = $option;
            }

            $question->options = $optionsArray;
            $arrQuestions[$question->id] = $question;
        }

        $sections = [];
        foreach ($test->sections as $secKey => $section) {
            if (isset($section['options'])) {
                if (isset($arrQuestions[$section['id']])) {
                    $selectedQuestion = $arrQuestions[$section['id']];
                    $section['status'] = $selectedQuestion->status;
                    foreach ($section['options'] as $keyOption => $option) {
                        $option['selected'] = $selectedQuestion->options[$option['id']]->selected;
                        $section['options'][$keyOption] = $option;
                    }
                    $sections[] = $section;
                }
            }
        }

        $attempt->sections = $sections;
        $attempt->save();

        $test = LmsTestResults::create([
            'user_id'   => $data['user_id'],
            'lesson_id' => $lesson_id,
            'date'      => date("Y-m-d H:i:s"),
            'score'     => $data['score'],
            'status'    => $statusUserLesson,
        ]);

        $userLesson = LmsUserLesson::where('lesson_id', $lesson_id)
            ->where('user_id', $data['user_id'])
            ->update(['test' => $testStatus, 'page_progress' => $newPage]);
        return response()->json(['response' => 'success']);
    }

    public function update(Request $request, $lesson_id)
    {
        $rules = [
            'pass_mark'        => 'required',
            'amount_questions' => 'required',
            'correct_answer'   => 'required',
        ];

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json(['response' => 'errors', 'error' => $validator->errors()]);
        }

        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json(['response' => 'error', 'message' => 'Lesson Does not exist']);
        }

        $data = $request->except('_token');
        $test = LmsTests::whereIn('lesson_id', [(string)$lesson_id, (int)$lesson_id])->first();
        $test->pass_mark        = $data['pass_mark'];
        $test->amount_questions = $data['amount_questions'];
        $test->correct_answer   = $data['correct_answer'];
        $test->save();

        return response()->json(['response' => 'success', 'data' => $data, 'message' => 'Test saved successfully']);
    }

    /**
     * Get test attempts by lesson and user id
     *
     * @param int $lesson_id Lesson ID
     *
     * @return Response
     */
    public function getAttempt(Request $request, $lesson_id)
    {
        $rules = ['user_id' => 'required'];
        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json(['response' => 'errors', 'error' => $validator->errors()]);
        }

        $lesson = Lesson::find($lesson_id);
        $course = Course::find($lesson->course_id);
        if (!$lesson) {
            return response()->json(['response' => 'error', 'message' => 'Lesson does not exist']);
        }

        $lessonIds = [];
        foreach ($course->lessons as $lesson) {
            $lessonIds[] = $lesson->id;
        }

        $data = $request->except('_token');
        $attempts = LmsTestAttempts::whereIn('lesson_id', [(string)$lesson_id, (int)$lesson_id])
            ->where('user_id', '=', $data['user_id'])
            ->orderby('_id', 'DESC')
            ->first();

        if ($attempts) {
            $attempts->course_name = $course->title;
            $attempts->course_id = $course->id;
            $attempts->lesson_name = $lesson->title;
            $attempts->lesson_description = $lesson->description;
            $attempts->lesson_ids = $lessonIds;
            return response()->json(['response' => 'success', 'data' => $attempts]);
        }
        return response()->json(['response' => 'error', 'message' => 'There is no stored test for this User']);
    }

    public function sortSection(Request $request, $lesson_id)
    {
        $rules = [
            'section_ids' => 'required',
        ];

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json(['response' => 'errors', 'error' => $validator->errors()]);
        }

        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json(['response' => 'error', 'message' => 'Lesson does not exist']);
        }

        $test = LmsTests::whereIn('lesson_id', [(string)$lesson_id, (int)$lesson_id])->first();
        if (!$test) {
            return response()->json(['response' => 'error', 'message' => 'Test does not exist']);
        }

        $data = $request->get('section_ids');
        if (isset($test->sections)) {
            $newOrder = [];
            foreach ($data as $newOrderSection) {
                foreach ($test->sections as $currectOrderSection) {

                    if ($newOrderSection == $currectOrderSection['id']) {
                        $newOrder[] = $currectOrderSection;
                    }
                }
            }
            $test->sections = $newOrder;
        }

        $test->save();
        return response()->json(['response' => 'success', 'data' => $data]);
    }

    /**
     * Get Relations for object
     *
     * @param $item
     */
    private function getRelations(&$item)
    {
        $relations = [
            'lesson',
        ];

        foreach ($relations as $relation) {
            $item->$relation;
            unset($item->{sprintf('%s_id', $relation)});
        }
    }
}
