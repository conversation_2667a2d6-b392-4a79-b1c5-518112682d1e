<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use App\Models\Organisation;
use App\Models\OrganisationLog;

class RiskEngineerProductivityMetricsController extends BaseController
{
    public function index(Request $request)
    {
        $organisationId = $request->id;
        $filter = $request->filter;
        $boundStatus = $this->getBoundStatus($organisationId);
        $renewals = $this->getRenewals($organisationId, $filter['startDate'], $filter['endDate']);

        return response()->json([
            'success' => true,
            'data' => $boundStatus
        ], 200);
    }

    public function getBoundStatus($id)
    {
        try {
            $organization = Organisation::findOrFail($id);
            
            $latestLog = OrganisationLog::where('organisation_id', $id)
                ->where('bound_at', '!=', null)
                ->latest()
                ->first();

            return response()->json([
                'success' => true,
                'data' => [
                    'is_bound'           => $organization->bound,
                    'bound_date'         => $latestLog?->bound_at,
                    'pre_inception_date' => $latestLog?->pre_inception_at,
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching organization binding status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getRenewals($id, $startDate = null, $endDate = null)
    {
        if (!$startDate) {
            $startDate = now()->format('Y-m-d');
        }

        if (!$endDate) {
            $endDate = now()->format('Y-m-d');
        }

        $organisation = Organisation::findOrFail($id);
        $renewals = $organisation->policies()
            ->renewalsBetween($startDate, $endDate)
            ->get();
        return response()->json($renewals);
    }
}