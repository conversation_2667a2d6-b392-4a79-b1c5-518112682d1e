<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use App\Models\SuppressionList;
use Illuminate\Http\Request;

class SubscriptionController extends BaseController
{

    public function unsubscribeSms(Request $request)
    {
        $data = $request->except('_token');
        $data['suppression_type'] = 'sms';
        if (isset($data['Body']) && strtolower($data['Body']) == 'stop') {
            $data['is_suppressed'] = 1;
            $data['From'] = sha1($data['From']);
        }

        if (isset($data['Body']) && strtolower($data['Body']) == 'start') {
            SuppressionList::where('From', '=', sha1($data["From"]))->update(["is_suppressed" => 0]);
            return Response::json(
                [
                'response' => 'success',
                ]
            );
        }

        SuppressionList::create($data);

        return Response::json(
            [
            'response' => 'success',
            ]
        );

    }


    public function unsubscribeEmail($token)
    {
        $suppression = SuppressionList::where('token', '=', $token)->get();
        if (!$suppression) {
            return Response::json(
                [
                'response' => 'Invalid token',
                ]
            );
        }

        SuppressionList::where('token', '=', $token)->update(["is_suppressed" => 1]);
        return Response::json(
            [
            'response' => 'success',
            ]
        );
    }

    public function suppressions($type = null)
    {
        if ($type == null) {
            $suppressionList = SuppressionList::all();
        } else {
            $suppressionList = SuppressionList::where('suppression_type', '=', $type)->where(
                'is_suppressed', '=',
                1
            )->whereNotIn('type', ['Bounce', 'Complaint'])->get();
        }
        if ($suppressionList) {
            return Response::json(
                [
                'response' => 'success',
                'suppressions' => $suppressionList,
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                ]
            );
        }
    }

    public function deleteEmailSuppression($token)
    {
        $delete = SuppressionList::where('token', '=', $token)->update(["is_suppressed" => 0]);
        if ($delete) {
            return Response::json(
                [
                'response' => 'success',
                ]
            );
        }
    }

}
