<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use Twilio\Jwt\AccessToken;
use Twilio\Jwt\Grants\VideoGrant;
use App\Models\VideoCallParticipant;
use App\Models\VideoCallRoom;

class VideoTokenController extends BaseController
{
    public function token($identity, $roomName)
    {
        /**
 * @var VideoCallParticipant $user 
*/
        $user = $this->validateIdentity($identity, $roomName);
        if (!$user) {
            return Response::json(
                [
                'response' => 'fail',
                'data' => ['message' => 'identity not found or link has expired'],
                ]
            );
        }

        if (isset($user->token)) {
            return Response::json(
                [
                'response' => 'success',
                'data' => [
                    'token' => $user->token,
                    'name' => $user->user_name,
                ],
                ]
            );
        }


        $twilioConfig = config('app.twilio.video');
        $twilioAccountSid = $twilioConfig['sid'];
        $twilioApiKey = $twilioConfig['apiKey'];
        $twilioApiSecret = $twilioConfig['apiSecret'];

        // Create access token, which we will serialize and send to the client
        $token = new AccessToken(
            $twilioAccountSid,
            $twilioApiKey,
            $twilioApiSecret,
            VideoCallParticipant::TOKEN_EXPIRY,
            $user->user_name
        );

        // Create Video grant
        $videoGrant = new VideoGrant();
        $videoGrant->setRoom($roomName);

        // Add grant to token
        $token->addGrant($videoGrant);

        $user->token = $token->toJWT();
        $user->save();

        return Response::json(
            [
            'response' => 'success',
            'data' => [
                'token' => $token->toJWT(),
                'name' => $user->user_name,
            ],
            ]
        );
    }

    private function validateIdentity($code, $room)
    {
        $user = VideoCallParticipant::where('user_code', $code)->first();

        if ($user && $user->room->name === $room) {
            /**
 * @var VideoCallRoom $videoRoom 
*/
            $videoRoom = VideoCallRoom::where('name', $room)->first();

            if ($videoRoom && !$videoRoom->isExpired()) {
                return $user;
            }
        }

        return false;
    }
}
