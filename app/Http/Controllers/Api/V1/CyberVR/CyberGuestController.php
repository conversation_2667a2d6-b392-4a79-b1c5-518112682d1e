<?php

namespace App\Http\Controllers\Api\V1\CyberVR;

use App\Http\Controllers\BaseController;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\Request;
use App\Models\Cms;
use App\Models\LetsTalk\CmsLibertyRepresentative;
use App\Models\LetsTalk\SocialRoom;
use App\Models\LetsTalk\SocialRoomParticipant;
use App\Models\LetsTalk\UpcomingSchedule;
use App\Models\Mailqueue;
use App\Models\LetsTalk\Helper;
use Carbon\Carbon;
use Illuminate\Support\Facades\Route as FacadesRoute;

class CyberGuestController extends BaseController
{
    /* Traits
    -----------------------------------------------------------*/
    /* Constants
    -----------------------------------------------------------*/
    const ROOM_KEYWORD = 'Cyber Virtual Room';
    const TOKEN_KEY = 'CYBERVR::_ID';

    /* Public Properties
    -----------------------------------------------------------*/
    /* Protected Properties
    -----------------------------------------------------------*/
    protected $authUserCms = [];
    protected $authUserTz = [];
    protected $mail;

    /* Magic Methods
    -----------------------------------------------------------*/
    public function __construct(Mailqueue $mail)
    {
        $lsmLibReps = CmsLibertyRepresentative::fetchFromCacheOrCms();
        $lmreLibReps = CmsLibertyRepresentative::fetchFromCacheOrCms('lmre');

        $this->libReps = array_merge($lmreLibReps, $lsmLibReps);

        $personId           = FacadesRoute::current()->parameter('personId');
        $this->authUserCms  = isset($this->libReps[$personId]) ? $this->libReps[$personId] : [];
        $this->authUserTz   = isset($this->authUserCms['office_timezone']) ? $this->authUserCms['office_timezone'] : 'Europe/London';
        $this->mail         = $mail;
    }

    /* Public Methods
    -----------------------------------------------------------*/
    public function generateLink(Request $request)
    {
        $subject = 'Here is your magic link - Cyber Virtual Room';
        $recipientEmail = $request->get('email');
        $recipientName  = 'Underwriter';
        $viewParameters['token'] = Helper::encryptInt(Config::get('app.cyber_virtual_rooms.jennifer_id'), self::TOKEN_KEY);
        $viewParameters['user_type'] = 'underwriter';
        $viewParameters['title'] = 'Here is your magic link';
        $viewParameters['message'] = "<p style='font-size: 16px; color: #5c6064; line-height: 22px; text-align: left; font-family: Arial, Helvetica, sans-serif; margin: 20px 0;' align='left'>
                                        To access the Cyber Virtual Room, please click the button below.
                                    </p>";

        //Send Email
        $this->mail->queue(
            $recipientEmail,
            $recipientName,
            $subject,
            "emails.cyber-vr-lets-talk.passwordless-guest",
            $viewParameters,
            null
        );

        return Response::json([
            'response' => 'success',
            'data' => 0,
        ]);
    }

    public function index($personId)
    {
        $social_room = SocialRoom::where('lt_social_room_type_id', 3)
            ->where('created_by_id', $personId)
            ->where('status','approved')
            ->where('is_community',0)
            ->where('description', 'like', '%' . self::ROOM_KEYWORD . '%')
            ->with('socialRoomSchedule')
            ->with('deletedScheduleEntries')
            ->with('modifiedScheduleEntries')
            ->with('socialRoomParticipantsNoClientsOnly')
            ->get();
        $social_room = SocialRoom::setCmsData($social_room, false, 'socialRoomParticipantsNoClientsOnly');

        foreach($social_room as $schedule){
            if(!empty($schedule->socialRoomSchedule)){
                $currSched = Carbon::parse($schedule->socialRoomSchedule->start_date, 'Europe/London')->timezone($this->authUserTz);
                $freq_for_ui = UpcomingSchedule::getFrequencyForUi($schedule->socialRoomSchedule, $this->authUserTz);
                $schedForUi = $freq_for_ui . ' ' . Carbon::parse($currSched->copy()->format('H:i:s'), $this->authUserTz)->format('@ G:ia');
                $schedule->socialRoomSchedule->frequency_for_ui = $schedForUi;
                $now = Carbon::now()->timezone($this->authUserTz);

                $startDate =  $currSched->format('Y-m-d');
                $endTime = Carbon::parse($schedule->socialRoomSchedule->end_date, $this->authUserTz)->format('H:i:s');
                $validUntil = Carbon::parse($startDate . ' ' . $endTime)->setTimezone($this->authUserTz)->addMinutes(30);
                $isRoomReady = (((int) $currSched->diffInMinutes($now, false) >= -5) && $validUntil->gt($now) ) ?  true : false;

                $schedule->socialRoomSchedule->is_room_ready = $isRoomReady;
            }
        }

        $cmsUser = SocialRoom::getLibRepresentativeById($personId);

        $workspace      = Config::get('app.cms.cyber_virtual_rooms.workspace');
        $contentType    = Config::get('app.cms.cyber_virtual_rooms.content_type');
        $contentEntry   = Config::get('app.cms.cyber_virtual_rooms.content_entry');
        $cmsKey         = Config::get('app.cms.key');
        $cyberOnlineSession = json_decode(Cms::get(
            "workspaces/{$workspace}/content-types/{$contentType}/content-entries/{$contentEntry}?key={$cmsKey}"));

        $title = '';
        $description = '';
        if (!empty($cyberOnlineSession)) {
            $titleField     = Config::get('app.cms.cyber_virtual_rooms.title');
            $descField      = Config::get('app.cms.cyber_virtual_rooms.description');
            $title          = $cyberOnlineSession->{$titleField};
            $description    = $cyberOnlineSession->{$descField};
        }

        return Response::json([
            'status' => 'success',
            'data' => [
                'rooms' => $social_room,
                'cms' => $cmsUser,
                'title' => $title,
                'description' => $description
            ]
        ]);
    }

    public function space($roomCode, $personId)
    {
        $upcomingSchedules = [];
        $socialRoom = SocialRoom::where('lt_social_room_type_id', 3)
            ->where('created_by_id', $personId)
            ->where('status','approved')
            ->where('is_community',0)
            ->where('description', 'like', '%' . self::ROOM_KEYWORD . '%')
            ->where('room_code', $roomCode)
            ->with('socialRoomSchedule')
            ->with('deletedScheduleEntries')
            ->with('modifiedScheduleEntries')
            ->with('socialRoomParticipantsNoClientsOnly')
            ->first();

        if (empty($socialRoom)) {
            return Response::json([
                'response' => 'success',
                'data' => []
            ]);
        }

        $socialRoom = SocialRoom::setCmsData(
            new Collection([$socialRoom]), false,
            'socialRoomParticipantsNoClientsOnly'
        )->first();

        if(isset($socialRoom->socialRoomSchedule) && !empty($socialRoom->socialRoomSchedule)) {
            $currSched      = Carbon::parse($socialRoom->socialRoomSchedule->start_date, 'Europe/London')->timezone($this->authUserTz);
            $freq_for_ui    = UpcomingSchedule::getFrequencyForUi($socialRoom->socialRoomSchedule, $this->authUserTz);

            // include the word Today if date is today
            $today          = ($currSched->isToday()) ? " Today " : " ";
            $schedForUi     = $freq_for_ui . $today . $currSched->copy()->format('@ G:ia');

            // Set the frequency for ui
            $socialRoom->socialRoomSchedule->frequency_for_ui = $schedForUi;

            $now            = Carbon::now()->timezone($this->authUserTz);
            $startDate      = $currSched->format('Y-m-d');
            $endTime        = Carbon::parse($socialRoom->socialRoomSchedule->end_date, $this->authUserTz)->format('H:i:s');
            $validUntil     = Carbon::parse($startDate . ' ' . $endTime)->setTimezone($this->authUserTz)->addMinutes(30);
            $isRoomReady    = (((int) $currSched->diffInMinutes($now, false) >= -5) && $validUntil->gt($now) ) ?  true : false;

            $socialRoom->socialRoomSchedule->is_room_ready = $isRoomReady;
        }

        $socialRoom->social_room_participants = (!empty($socialRoom->social_room_participants_no_clients_only)) ? $socialRoom->social_room_participants_no_clients_only : [];
        if (isset($socialRoom->socialRoomSchedule) && !empty($socialRoom->socialRoomSchedule) && $socialRoom->socialRoomSchedule->duration_type != 'permanent') {
            $social_room       = new Collection([$socialRoom]);
            $tmpData           = UpcomingSchedule::generate($social_room, $this->authUserCms);
            $upcomingSchedules = $tmpData['upcomingSchedules'];
        }

        $social_rooms = isset($tmpData['rooms'][0]) && !empty($tmpData['rooms'][0]) ? $tmpData['rooms'][0] : $socialRoom;

        $joinRoomLink = '';
        // if (isset($social_room) && $social_room->first()) {
            // $participant = SocialRoomParticipant::where('lt_social_room_id', $socialRoom->first()->id)
            //     ->where('person_id', $personId)
            //     ->first();

            // if (!empty($participant)) {
                $joinRoomLink = SocialRoomParticipant::generateWaitingRoomCommonLink($roomCode);
            // }
        // }

        return Response::json([
            'response' => 'success',
            'data' => [
                'room' => $social_rooms,
                'upcoming_schedules' => $upcomingSchedules,
                'join_room_link' => $joinRoomLink
            ],
        ]);
    }

    /* Protected Methods
    -----------------------------------------------------------*/
    /* Private Methods
    -----------------------------------------------------------*/
    /* Public Static Methods
    -----------------------------------------------------------*/
}
