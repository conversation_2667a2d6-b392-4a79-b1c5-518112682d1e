<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\Course;
use App\Models\Lesson;
use App\Models\LmsTestAttempts;
use App\Models\LmsTestResults;
use App\Models\LmsTests;
use App\Models\LmsUserLesson;
use App\Models\Pages;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Route;

class LearningLessonController extends BaseController
{
    /**
     * Store new Lesson
     */
    public function store(Request $request)
    {
        $validator = Validator::make(
            $request->all(), [
                'title' => 'required',
                'method' => 'required',
            ]);

        if ($validator->fails()) {
            $response = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        } else {
            $method = !is_null($request->get('method')) ? $request->get('method') : 'new';

            if ($method == 'new') {
                $lastPos = Lesson::where('course_id', '=', $request->get('course_id', null))
                    ->select('order')
                    ->orderBy('order', 'DESC')
                    ->first();

                $newOrder = $lastPos == null ? 1 : $lastPos->order + 1;
                $lesson = Lesson::create([
                    'title'       => $request->get('title'),
                    'description' => $request->get('description', null),
                    'course_id'   => $request->Get('course_id', null),
                    'order'       => $newOrder,
                ]);

                if ($request->has('course_id')) {
                    $course = Course::find($request->get('course_id'));
                    if ($course != null) {
                        $lesson->course_id = $course->id;
                    }
                }

                if ($lesson->id) {
                    $response = [
                        'response' => 'success',
                        'message' => 'The Lesson has been created successfully',
                    ];
                } else {
                    $response = [
                        'response' => 'error',
                        'message' => 'The Lesson has failed to be created',
                    ];
                }
            } elseif ($method == 'duplicate') {
                $response = $this->duplicate($request->get('source'), $request->get('title'));
            }
        }

        return response()->json($response);
    }

    /**
     * Get All Lessons
     */
    public function all(Request $request, $page = 1, $limit = 10)
    {
        $page  = (int)$page;
        $limit = (int)$limit;
        $query = $request->get('search', '');
        $query = ($query) ? '%' . $query . '%' : '';

        $lessons = ($query)
            ? Lesson::where(
                'title', 'LIKE', $query
            )->orWhere(
                'description', 'LIKE', $query
            )
            : Lesson::query();

        // get total by filters above (exclude pagimation limits)
        $total = $lessons->count();

        // only paginate if a limit is specified (0 = no limit)
        if ($limit > 0) {
            $lessons = $lessons->take($limit)
                ->skip(($page * $limit) - $limit);
        }

        $lessons = $lessons->get();
        foreach ($lessons as $lesson) {
            $this->getRelations($lesson);
            $this->image = null;
        }

        return response()->json([
            'response' => 'success',
            'data'     => $lessons,
            'total'    => $total,
        ]);
    }

    private function getRelations(&$item)
    {
        $relations = ['course',];

        foreach ($relations as $relation) {
            $item->$relation;
            unset($item->{sprintf('%s_id', $relation)});
        }
    }

    public function duplicate(Request $request, $id, $title = null)
    {
        if ($id) {
            $lesson = Lesson::where('id', $id)->first();

            if ($lesson) {
                $newLessonTitle = $request->has('title')
                    ? $request->get('title')
                    : $lesson->title;
                if ($title !== null) {
                    $newLessonTitle = $title;
                }

                $lastPos = Lesson::where('course_id', '=', $lesson->course_id)
                    ->select('order')
                    ->orderBy('order', 'DESC')
                    ->first();
                $newOrder = is_null($lastPos)
                    ? 1
                    : $lastPos->order + 1;

                $newLesson = Lesson::create([
                    'title'       => $newLessonTitle,
                    'description' => $lesson->description,
                    'course_id'   => $lesson->course_id,
                    'order'       => $newOrder,
                ]);

                if ($newLesson) {
                    $response['response'] = 'success';
                }

                $lesson->test = LmsTests::whereIn('lesson_id', [(string)$lesson->id, (int)$lesson->id])->first();
                $lesson->pages = Pages::whereIn(
                    'lesson_id',
                    [(string)$lesson->id, (int)$lesson->id]
                )->orderby('sort_order', 'ASC')->get();

                if ($lesson->test) {
                    $newTest = LmsTests::create([
                        'lesson_id'        => (int)$newLesson->id,
                        'pass_mark'        => $lesson->test->pass_mark,
                        'amount_questions' => $lesson->test->amount_questions,
                    ]);

                    if (isset($lesson->test->sections)) {
                        $newTest->sections = $lesson->test->sections;
                        $newTest->save();
                    }
                    $newLesson->test = $newTest;
                }

                $files = [];
                if ($lesson->pages) {
                    $newPages = [];
                    foreach ($lesson->pages as $key => $page) {
                        $newPage = Pages::create([
                            'lesson_id'  => $newLesson->id,
                            'title'      => $page->title,
                            'sort_order' => $page->sort_order,
                        ]);

                        if (isset($page->sections)) {
                            $newsections = [];
                            foreach ($page->sections as $key => $section) {
                                $newSection       = $section;
                                $newSection['id'] = uniqid($page->_id);
                                array_push($newsections, $newSection);
                                if (isset($section['file_name'])) {
                                    array_push($files, $section['file_name']);
                                }
                            }
                            $newPage->sections = $newsections;
                        }

                        $newPage->save();
                        array_push($newPages, $newPage);
                    }
                    $newLesson->pages = $newPages;
                }

                $newLesson->files = $files;
                $data = [];
                $data['old_Lesson'] = $lesson;
                $data['new_Lesson'] = $newLesson;
                $response['message'] = 'The Lesson has been duplicated successfully';
                $response['data'] = $newLesson;

            } else {
                $response['response'] = 'error';
                $response['message'] = 'The specified Lesson could not be found';
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid Lesson ID',
            ];
        }

        return $title !== null
            ? $response
            : response()->json($response);
    }

    /**
     * Find Lesson
     */
    public function show(Request $request, $id)
    {
        if($request->has('course_id')){
            $id        = $request->get('id');
            $course_id = $request->get('course_id');
        }

        if ($id) {
            $lesson = Lesson::find($id);
            if (!$lesson) {
                $lesson = Lesson::where('id', '=', $id)->first();
            }

            // get previous and future lessons
            $course = Course::where('id', '=', $lesson->course_id)->first();
            $previousLessonId = null;
            $futureLessonId   = null;
            $courseLessons    = $course->lessons;
            $lessonLength     = count($courseLessons);

            for ($i = 0; $i < $lessonLength; $i++) {
                if ($lessonLength > 1) {
                    if ($lesson->order === 1) {
                        $futureLessonId = $courseLessons[$i + 1]->id;
                        break;
                    }

                    if ($lesson->order == $courseLessons[$i]->order) {
                        $futureLessonId = $i + 1 < $lessonLength
                            ? $courseLessons[$i + 1]->id
                            : null;
                        $previousLessonId = $courseLessons[$i - 1]->id;
                    }
                }
            }

            $response = [
                'response' => ($lesson)
                    ? 'success'
                    : 'error',
            ];

            if ($lesson) {
                $this->getRelations($lesson);
                $status = null;
                $userLesson = null;
                if ($request->has('user_id')) {
                    $user_id = $request->get('user_id');
                    $userLesson = LmsUserLesson::where('lesson_id', '=', $lesson->id)->where(
                        'user_id', '=',
                        $user_id
                    )->first();
                    $status = LmsTestResults::where('lesson_id', '=', $lesson->id)->where(
                        'user_id', '=',
                        $user_id
                    )->where('status', '=', 'Complete')->first();
                }

                $lesson->userLesson = $userLesson;
                $lesson->testResult = $status;
                $lesson->test = LmsTests::whereIn('lesson_id', [(string)$lesson->id, (int)$lesson->id])->first();
                $lesson->pages = Pages::whereIn(
                    'lesson_id',
                    [(string)$lesson->id, (int)$lesson->id]
                )->orderby('sort_order', 'ASC')->get();
                $lesson->prev_lesson_id = $previousLessonId;
                $lesson->next_lesson_id = $futureLessonId;
                $response['data'] = $lesson;

            } else {
                $response['message'] = 'The specified Lesson could not be found';
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid Lesson ID',
            ];
        }

        return response()->json($response);
    }

    /**
     * Delete Lesson
     *
     * @param $id
     *
     * @return mixed
     */
    public function destroy(Request $request, $id)
    {
        if ($request->get('course_id')) {
            $id = $request->get('id');
            $course_id = $request->get('course_id');
        }

        if ($lesson = Lesson::find($id)) {
            if (Lesson::destroy($id)) {
                LmsTests::whereIn('lesson_id', [(string)$lesson->id, (int)$lesson->id])->delete();
                Pages::whereIn('lesson_id', [(string)$lesson->id, (int)$lesson->id])->delete();
                $response = [
                    'response' => 'success',
                    'message' => 'The specified Lesson was successfully deleted',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The specified Lesson could not be deleted',
                ];
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'The specified Lesson could not be found',
            ];
        }

        return response()->json($response);
    }

    public function byCourseID($course_id, $page = 1, $limit = 10)
    {
        $page    = (int)$page;
        $limit   = (int)$limit;
        $lessons = Lesson::where('course_id', '=', $course_id);
        $total   = $lessons->count();

        if ($limit > 0) {
            $lessons = $lessons->take($limit)
                ->skip(($page * $limit) - $limit);
        }

        $lessons = $lessons->get();
        foreach ($lessons as $lesson) {
            $this->getRelations($lesson);
            $this->image = null;
        }

        return response()->json([
            'response' => 'success',
            'data' => $lessons,
            'total' => $total,
        ]);
    }

    public function userLessons($user_id)
    {

        $lessons = LmsUserLesson::where('user_id', '=', $user_id)->get();
        $pages   = Pages::get();
        $tests   = LmsTests::get();
        $groupPages = [];
        foreach ($pages as $page) {
            if (isset($groupPages[$page->lesson_id])) {
                $cnt = $groupPages[$page->lesson_id];
                $cnt++;
                $groupPages[$page->lesson_id] = $cnt;
            } else {
                $groupPages[$page->lesson_id] = 1;
            }
        }

        foreach ($lessons as $key => $lesson) {
            $lesson->hasTest = false;
            isset($groupPages[$lesson->lesson_id])
                ? $lesson->tot_pages = $groupPages[$lesson->lesson_id]
                : $lesson->tot_pages = 1;
            foreach ($tests as $test) {
                if ($test->lesson_id == $lesson->lesson_id) {
                    $lesson->hasTest = true;
                    $lesson->tot_pages = $lesson->tot_pages + 1;
                }
            }
            $lesson->lesson_progress = ($lesson->page_progress * 100) / $lesson->tot_pages;
            $attempts = LmsTestAttempts::whereIn(
                'lesson_id',
                [(string)$lesson->lesson_id, (int)$lesson->lesson_id]
            )->where(
                'user_id', '=',
                $user_id
            )->orderBy('created_at', 'desc')->first();

            if ($attempts) {
                if ($attempts->status == 'Complete') {
                    $lesson->lesson_progress = 100;
                }
            }
            $lessons[$key] = $lesson;
        }

        $total = $lessons->count();

        return response()->json([
            'response' => 'success',
            'data'     => $lessons,
            'total'    => $total,
        ]);


    }

    public function openLessons(Request $request, $lesson_id)
    {
        $validator = Validator::make(
            $request->all(), [
                'user_id' => 'required',
                'page'    => 'required',
            ]);

        if ($validator->fails()) {
            $response = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        } else {
            $user_id = $request->get('user_id');
            $page = $request->get('page');
            $matchThese = ['user_id' => $user_id, 'lesson_id' => $lesson_id];
            $results = LmsUserLesson::where($matchThese)->get();
            $lesson = '';
            if ($results) {
                $lesson = LmsUserLesson::create([
                    'user_id' => $user_id,
                    'lesson_id' => $lesson_id,
                    'read' => 1,
                    'test' => 0,
                    'page_progress' => 1,
                ]);
            } else {
                LmsUserLesson::where($matchThese)->update(['page_progress' => $page]);
            }
            $response = [
                'response' => 'success',
                'data' => $lesson,
            ];
        }
        return response()->json($response);


    }

    /**
     * Update Lesson
     */
    public function update(Request $request, $id)
    {
        if ($request->get('course_id')) {
            $id = $request->get('id');
            $course_id = $request->get('course_id');
        }

        if ($id && is_numeric($id)) {
            $validator = Validator::make(
                $request->all(), [
                    'title' => 'required',
                ]);

            if ($validator->fails()) {
                $response = [
                    'response' => 'error',
                    'errors' => $validator->messages()->toArray(),
                ];
            } else {
                $lesson = Lesson::find($id);
                if ($lesson->id) {
                    $lesson->title = $request->get('title');
                    $lesson->description = $request->get('description', null);
                    $lesson->save();

                    $response = [
                        'response' => 'success',
                        'message' => 'The Lesson has been updated successfully',
                    ];
                } else {
                    $response = [
                        'response' => 'error',
                        'message' => 'Invalid Lesson',
                    ];
                }
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid Lesson ID',
            ];
        }


        return response()->json($response);
    }

    public function view($lesson_id)
    {
        $lesson = Lesson::find($lesson_id);

        if ($lesson) {
            $this->getRelations($lesson);

            $response = [
                'response' => 'success',
                'message'  => 'The lesson is available.',
                'data'     => $lesson,
            ];
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Something went wrong, no lesson available.',
            ];
        }

        return response()->json($response);
    }

    public function options()
    {
        return response()->json(Lesson::pluck('title', 'id'));
    }

    public function sort(Request $request, $course_id)
    {
        $validator = Validator::make(
            $request->all(), [
                'lessons' => 'required',
            ]);

        if ($validator->fails()) {
            $response = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        } else {
            $course = Course::find(
                $course_id
            );

            if ($course->id) {
                $lessonsOrder = $request->get('lessons');

                foreach ($lessonsOrder as $lessonOrder) {
                    $lessons = Lesson::where('course_id', '=', $course_id);
                    $lessons->where('id', '=', (int)$lessonOrder['id'])->update(['order' => (int)$lessonOrder['seq']]);
                }

                $response = [
                    'response' => 'success',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The specified Course could not be found',
                ];
            }
        }
        return response()->json($response);
    }

}
