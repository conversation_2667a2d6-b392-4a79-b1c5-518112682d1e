<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\Activity;
use App\Models\ApiKey;
use App\Traits\Has2FA;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\BaseController;
use App\Models\Branch;
use App\Models\BrokerUser;
use Carbon\Carbon;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\Encryption\DecryptException;
use Illuminate\Support\Facades\Hash;
use App\Models\LibertyUser;
use App\Models\Mailqueue;
use App\Models\PreviousPassword;
use App\Models\Previsico\PrevisicoUiNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;
use App\Models\WebLog;
use App\Traits\ChecksExpiredPassword;
use Illuminate\Contracts\Support\Arrayable;

class LibertyUserController extends BaseController
{
    use Has2FA, ChecksExpiredPassword;

    public function __construct(Mailqueue $mailqueue)
    {
        $this->mail = $mailqueue;
    }

    /**
     * Authenticate LibertyUser
     *
     * @return mixed
     */
    public function auth(Request $request)
    {
        $email = $request->get('email');
        $password = $request->get('password');

        if ($email != null && $password != null) {
            try {
                $data = [
                    'email' => Crypt::decrypt($email),
                    'password' => Crypt::decrypt($password),
                ];
            } catch (DecryptException $e) {
                return Response::json(
                    [
                        'response' => 'error',
                        'message' => 'Liberty User login failed',
                    ], 200
                );
            }

            try {
                /** @var LibertyUser $user */
                $user = LibertyUser::where('email', '=', $data['email'])->firstOrFail();
                // banned!
                if ($user->attempts > 4) {
                    return Response::json(
                        [
                            'response' => 'error',
                            'message' => 'Account Temporarily Locked: You have exceeded the maximum number of login attempts for your account. <NAME_EMAIL> to unlock your account.',
                        ]
                    );
                }
            } catch (\Exception $e) {
                return Response::json(
                    [
                        'response' => 'error',
                        'message' => 'Wrong Username or Password.',
                    ]
                );
            }

            if (Auth::guard('liberty_users')->validate($data)) {
                if ($user->activated != 0 && $this->isPasswordExpired($user)) {
                    return $this->getPasswordExpiredResponse();
                }

                if ($user->activated != 0) {
                    DB::update('update liberty_users set attempts = 0 where email = ?', [$data['email']]);
                    if (!$user->secret) {
                        // generate a secret key for 2FA
                        $user->generateSecret();
                    }
                    return Response::json(
                        [
                            'response' => 'success',
                            'data' => $user,
                        ], 200
                    );
                } else {
                    return Response::json(
                        [
                            'response' => 'error',
                            'message' => 'The Liberty User has not been activated',
                        ]
                    );
                }
            }
            DB::update('update liberty_users set attempts = attempts + 1 where email = ?', [$data['email']]);
            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'Wrong Username or Password',
                ], 200
            );
        } else {
            DB::update('update liberty_users set attempts = attempts + 1 where email = ?', [$data['email']]);
            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'Liberty User login failed',
                ], 200
            );
        }
    }

    /**
     * Update Liberty User
     */

    public function update(Request $request)
    {
        $rules = [
            'first_name' => 'required',
            'last_name' => 'required',
            'email' => 'required',
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json(
                [
                    'response' => 'error',
                    'errors' => $validator->messages()->toArray(),
                ],
                200
            );
        } else {
            $liberty_user = LibertyUser::find($request->get('id'));

            if (isset($liberty_user->id)) {
                $liberty_user->first_name = $request->get('first_name');
                $liberty_user->last_name = $request->get('last_name');
                $liberty_user->email = $request->get('email');
                $liberty_user->phone = $request->get('phone');
                $liberty_user->branch_id = $request->get('branch_id');
                $liberty_user->role = $request->get('role');
                $liberty_user->role_override = $request->get('role_override');
                $liberty_user->qualified_for = $request->get('qualified_for');
                $liberty_user->claims_notification = $request->get('claims_notification');
                if ($liberty_user->role == 'underwriter') {
                    $liberty_user->image = $request->get('image');
                }
                if (filter_var($request->get('can_pause_notifications', false), FILTER_VALIDATE_BOOLEAN)) {  
                    $liberty_user->notification_paused_at = filter_var($request->get('notification_paused_at', false), FILTER_VALIDATE_BOOLEAN)
                        ? now()
                        : null;
                }
                $liberty_user->save();

                if ($request->has('send_invite') && $request->get('send_invite') == '1') {
                    $liberty_user->type = 'liberty-user';
                    $this->mail->queue(
                        $liberty_user->email,
                        $liberty_user->fullName(),
                        'Risk Reduce, Welcome to Risk Reduce',
                        'emails.authadmin.welcome',
                        ($liberty_user instanceof Arrayable)
                            ? $liberty_user->toArray()
                            : $liberty_user
                    );
                }

                return Response::json(
                    [
                        'response' => 'success',
                        'message' => 'The Liberty User has been updated successfully',
                    ]
                );
            } else {
                return Response::json(
                    [
                        'response' => 'error',
                        'message' => 'Invalid Liberty User',
                    ]
                );
            }
        }
    }

    public function allFiltered(Request $request): \Illuminate\Http\JsonResponse
    {
        $query = $request->get('search');
        $roles = $request->get('roles') ? explode(',', $request->get('roles')) : [];
        $limit = $request->get('limit') ?? 10;
        $order = $request->get('order') ?? 'last_name';
        $sort = $request->get('sort') ?? 'ASC';
        $columns = $request->get('columns') ? explode(',', $request->get('columns')) : null;

        $libertyUsers = $columns ? LibertyUser::select(...$columns) : LibertyUser::query();

        if (!empty($query)) {
            $libertyUsers->where(fn ($q) =>
                $q->where('first_name', 'like', '%' . $query . '%')
                    ->orWhere('last_name', 'like', '%' . $query . '%')
                    ->orWhere('email', 'like', '%' . $query . '%')
            );
        }

        if (!empty($roles)) {
            $libertyUsers->whereIn('role', $roles);
        }

        $libertyUsers->orderBy($order, $sort)->paginate($limit);

        $users = $libertyUsers->paginate($limit);

        if (in_array('broker-user', $roles)) {
            $brokerUser = $columns ? BrokerUser::select(...$columns) : BrokerUser::query();
            $brokerUser = $brokerUser->where(fn ($q) =>
                $q->where('first_name', 'like', '%' . $query . '%')
                    ->orWhere('last_name', 'like', '%' . $query . '%')
                    ->orWhere('email', 'like', '%' . $query . '%')
            )->orderBy($order, $sort)->paginate($limit);

            $users = $brokerUser->merge($users);
            $users = $users->sortBy('email')->flatten();
        }

        return Response::json([
            'response' => 'success',
            'data' => $users->all(),
            'total' => $users->count(),
        ]);
    }

    /**
     * Get All LibertyUsers
     */
    public function all(Request $request, $page = 1, $limit = 10, $role = '', $order = '')
    {
        $query = $request->get('search');
        $liberty_users = ($query)
            ? LibertyUser::query()->Where(
                DB::raw("CONCAT(email, ' ',first_name, ' ',last_name)"), 'LIKE',
                "%" . $query . "%"
            )
            : LibertyUser::query();

        // $query = $request->get('search');
        // $query = ($query)
        //     ? '%' . $query . '%'
        //     : '';

        // $liberty_users = ($query)
        //     ? LibertyUser::where(
        //         'email','LIKE',$query
        //       )->orWhere(
        //         'first_name','LIKE',$query
        //       )->orWhere(
        //         'last_name','LIKE',$query
        //       )->where(
        //           'deleted_at', NULL
        //       )
        //     : LibertyUser::query();

        if ($role && $role != 'null') {
            $liberty_users = $liberty_users->where('role', $role);
        }

        if ($order == 'orderByNameASC') {
            $liberty_users = $liberty_users->orderBy('first_name', 'ASC')
                ->orderBy('last_name', 'ASC')
                ->withCount('associatedOrgs')
                ->with('branch')
                ->take($limit)
                ->skip(($page * $limit) - $limit)
                ->get();
        } else {
            $liberty_users = $liberty_users->take($limit)
                ->skip(($page * $limit) - $limit)
                ->withCount('associatedOrgs')
                ->with('branch')
                ->get();
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => $liberty_users,
            'total' => count(LibertyUser::all()),
            ]
        );
    }

    public function tokenAuth($email)
    {
        $user = LibertyUser::where('email', '=', $email)->firstOrFail();

        // banned!
        if ($user->attempts > 4) {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Account Temporarily Locked: You have exceeded the maximum number of login attempts for your account. <NAME_EMAIL> to unlock your account.',
                ]
            );
        }

        if ($user->activated != 0 || ($user->activated == 0 && $user->role == 'virtual-rooms')) {
            $branch = Branch::find($user->branch_id);
            if ($branch) {
                $user->is_aspen = $branch->is_aspen;
                $user->branch_name = $branch->name;
            } else {
                $user->is_aspen = 0;
            }
            DB::update('update liberty_users set attempts = 0 where email = ?', [$email]);
            return Response::json(
                [
                'response' => 'success',
                'data' => $user,
                ], 200
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'The Liberty User has not been activated',
                ]
            );
        }
    }

    /**
     * Authenticate User
     *
     * @return mixed
     */
    public function mobileauth(Request $request)
    {
        if ($request->has('api_key')) {
            $api_key = $request->get('api_key');
            if (count(ApiKey::where('api_key', '=', $api_key)->get()) > 0) {
                return Response::json(
                    [
                    'response' => 'success',
                    'message' => 'logged in',
                    ],
                    200
                );
            } else {
                return Response::json(
                    [
                    'response' => 'error',
                    'message' => 'Unable to login member',
                    ],
                    200
                );
            }
        }
        $email = $request->get('email');
        $password = $request->get('password');

        if ($email != null && $password != null) {
            try {
                $data = [
                    'email' => Crypt::decrypt($email),
                    'password' => Crypt::decrypt($password),
                ];
                // $data = [
                //     'email'     => $email,
                //     'password'     => $password
                // ];
            } catch (DecryptException $e) {
                return Response::json(
                    [
                    'response' => 'error',
                    'message' => 'Unable to login member',
                    ],
                    200
                );
            }


            if (Auth::liberty_user()->validate($data)) {
                $user = LibertyUser::where('email', '=', $data['email'])->firstOrFail();
                if ($user->activated != 0) {
                    //Audit
                    $this->log(
                        $user, 'Login', 'User Login', $request->header('session_ip'),
                        $request->header('session_agent')
                    );
                    $user_id = $user->id;
                    $key_data = [
                        'user_id' => $user_id,
                        'api_key' => Str::uuid()->toString(),
                        'user_type' => 'liberty-user',
                    ];
                    ApiKey::create($key_data);
                    $user['api_key'] = $key_data['api_key'];
                    return Response::json(
                        [
                        'response' => 'success',
                        'data' => $user,
                        ],
                        200
                    );

                } else {
                    return Response::json(
                        [
                        'response' => 'error',
                        'message' => 'User is not activated',
                        ]
                    );
                }

            }
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Username or Password were incorrect',
                ],
                200
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Username or Password were incorrect',
                ],
                200
            );
        }

    }

    private function log($user, $action, $description, $session_ip = '127.0.0.1', $session_agent = 'No UserAgent')
    {
        $activity = new Activity();
        $activity->content_id = $user->id;
        $activity->user_id = (string)$user->id;
        $activity->content_type = 'User';
        $activity->action = $action;
        $activity->description = $description;
        $activity->details = 'Username: ' . $user->fullName();
        $activity->ip_address = $session_ip;
        $activity->user_agent = $session_agent;
        $activity->save();
    }

    /**
     * Store new LibertyUser
     */

    public function store(Request $request)
    {
        $validator = Validator::make(
            $request->all(), [
            'first_name' => 'required',
            'last_name' => 'required',
            'email' => 'unique:liberty_users',
            ]
        );

        if ($validator->fails()) {
            return Response::json(
                [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
                ], 200
            );
        } else {
            $expiry = time() + (14 * 24 * 60 * 60); //14 days
            $data = [
                'first_name' => $request->get('first_name'),
                'last_name' => $request->get('last_name'),
                'email' => $request->get('email'),
                'phone' => $request->get('phone'),
                'branch_id' => $request->get('branch_id'),
                'broker_id' => $request->get('broker_id'),
                'role' => $request->get('role'),
                'role_override' => $request->get('role_override'),
                'qualified_for' => $request->get('qualified_for'),
                'claims_notification' => $request->get('claims_notification'),
                'password' => Hash::make(uniqid()),
                'secret' => $request->get('secret'),
                'image' => $request->get('image'),
                'activation_code' => Str::uuid()->toString(),
                'activation_code_expires' => $expiry,
            ];

            $liberty_user = LibertyUser::create($data);

            if ($liberty_user->id) {
                $liberty_user->type = 'liberty-user';
                if ($request->has('send_invite') && $request->get('send_invite') == '1') {
                    $this->mail->queue(
                        $liberty_user->email,
                        $liberty_user->fullName(),
                        'Risk Reduce, Welcome to Risk Reduce',
                        'emails.authadmin.welcome',
                        ($liberty_user instanceof Arrayable)
                            ? $liberty_user->toArray()
                            : $liberty_user
                    );
                }

                return Response::json(
                    [
                    'response' => 'success',
                    'message' => 'The Liberty User has been created successfully',
                    ],
                    200
                );
            } else {
                return Response::json(
                    [
                    'response' => 'error',
                    'message' => 'Unable to create Liberty User',
                    ],
                    200
                );
            }
        }
    }

    /**
     * Get All LibertyUsers not AspenUsers
     */
    public function allLibertyUsers()
    {
        $liberty_users = LibertyUser::where('role', '!=', 'aspen-user')->take(
            99999999
        )->skip(
            (1 * 99999999) - 99999999
        )->get();

        return Response::json(
            [
            'response' => 'success',
            'data' => $liberty_users,
            'total' => count($liberty_users),
            ]
        );
    }

    public function generateLink($userID)
    {
        if (isset($userID)) {
            $user = LibertyUser::find($userID);
            if (isset($user)) {
                $user->activation_code = \Illuminate\Support\Str::uuid();
                $user->activation_code_expires = strtotime('+14 days');
                $user->save();
                return Response::json(
                    [
                    'response' => 'success',
                    'data' => $user->toArray(),
                    ]
                );
            }

        }
        return Response::json(
            [
            'response' => 'error',
            'data' => 'Could not change activation timeline',
            ]
        );
    }

    /**
     * Send Reset Password code
     */

    public function sendResetPasswordCode(Request $request)
    {
        $email = $request->get('email');

        if (isset($email)) {
            $liberty_user = LibertyUser::where('email', '=', $email)->first();

            if (isset($liberty_user->id)) {
                //generate reset code

                $uuid = Str::uuid()->toString();
                $hours = config('auth.reminder.expire');
                $expiry = time() + ($hours * 60 * 60);

                $liberty_user->reset_password_code = $uuid;
                $liberty_user->reset_password_code_expires = $expiry;
                $liberty_user->save();

                //todo: send email to consumer

                //Queue:
                $liberty_user->user_type = 'liberty-user';
                $this->mail->queue(
                    $liberty_user->email,
                    $liberty_user->fullName(),
                    'Risk Reduce - Password Reset',
                    'emails.authadmin.passwordChange',
                    $liberty_user
                );

                //Mailqueue::queue($to_email, $to_name, $subject, $view, $view_variable);

                return Response::json(
                    [
                    'response' => 'success',
                    'message' => 'LibertyUser has been sent an email with details to reset their password',
                    ]
                );
            }
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'Failed to reset Liberty User password',
            ]
        );
    }

    /**
     * Reset Password using new password and code to verify user
     */

    public function resetPassword(Request $request)
    {
        $password = $request->get('password');
        $code = $request->get('code');

        if (isset($code) && isset($password)) {
            $User = LibertyUser::where('reset_password_code', '=', $code)->first();
            
            if (isset($User) && $User->reset_password_code_expires >= time() && $User->reset_password_code == $code) {

                //reset password and reset codes to 0
                $User->password = Hash::make(Crypt::decrypt($password));
                $User->reset_password_code = null;
                $User->reset_password_code_expires = null;
                $User->password_change_updated_at = now();
                $User->attempts = 0;
                $User->save();

                $this->createPreviousPasswordEntry($User);

                return Response::json(
                    [
                    'response' => 'success',
                    'message' => 'User password has been reset',
                    'user_id' => $User->id
                    ]
                );

            }
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'Could not reset User password',
            ]
        );

    }

    /**
     * Validated
     */

    public function validated(Request $request)
    {
        $valid = $request->get('valid');
        $user = $request->get('user_id');

        if ($user && isset($valid)) {
            $liberty_user = LibertyUser::where('id', '=', $user)->first();
            $liberty_user->valid = $valid;
            $liberty_user->save();

            return Response::json(
                [
                'response' => 'success',
                'message' => 'Validated the user successfully',
                ]
            );
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'Failed to validate user',
            ]
        );
    }

    /**
     * Activate a LibertyUser
     */

    public function activate(Request $request)
    {
        $code = $request->get('code', null);
        $email = $request->get('email', null);

        if ($code && $email) {
            $liberty_user = LibertyUser::where('email', '=', $email)->first();

            if ($liberty_user && $liberty_user->activation_code == $code && $liberty_user->activation_code_expires >= time()) {
                $password = Crypt::decrypt($request->get('password'));
                $liberty_user->password = Hash::make($password);
                $liberty_user->activated = true;
                $liberty_user->activation_code = null;
                $liberty_user->activation_code_expires = null;
                $liberty_user->password_change_updated_at = now();
                $liberty_user->save();

                $this->createPreviousPasswordEntry($liberty_user);

                return Response::json(
                    [
                    'response' => 'success',
                    'message' => 'LibertyUser activated',
                    ]
                );
            }
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'Could not activate admin',
            ]
        );
    }

    /**
     * Find LibertyUser
     */
    public function show($id)
    {
        if ($id && is_numeric($id)) {
            $liberty_user = LibertyUser::find($id);

            if (!$liberty_user) {
                $liberty_user = LibertyUser::where('id', '=', $id)->first();
            }

            $response = [
                'response' => ($liberty_user)
                    ? 'success'
                    : 'error',
            ];

            if ($liberty_user) {
                $liberty_user->branch;
                unset($liberty_user->branch_id);

                $response['data'] = $liberty_user;
            } else {
                $response['message'] = 'The specified Liberty User could not be found';
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid Liberty User ID',
            ];
        }

        return Response::json($response);
    }

    /**
     * Delete LibertyUser
     *
     * @param $id
     *
     * @return mixed
     */
    public function destroy(Request $request, $id)
    {
        if (is_numeric($id)) {
            $liberty_user = LibertyUser::find($id);
            
            if (LibertyUser::destroy($id)) {
                $this->log(
                    $liberty_user,
                    'Destroy',
                    'LibertyUser Destroyed',
                    $request->header('session_ip'),
                    $request->header('session_agent')
                );

                return Response::json(
                    [
                    'response' => 'success',
                    'message' => 'The Liberty User has been deleted successfully',
                    ]
                );
            } else {
                return Response::json(
                    [
                    'response' => 'error',
                    'message' => 'The Liberty User could not be found',
                    ]
                );
            }
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Invalid Liberty User ID',
                ]
            );
        }
    }

    public function options(Request $request, $role = null)
    {
        if ($request->has('deleted') && $request->get('deleted') == 'show') {
            $options = ($role)
                ? LibertyUser::where('role', '=', $role)->withTrashed()->orderBy('first_name', 'ASC')
                : LibertyUser::query()->orderBy('first_name', 'ASC');
        } else {
            $options = ($role)
                ? LibertyUser::where('role', '=', $role)->orderBy('first_name', 'ASC')
                : LibertyUser::query()->orderBy('first_name', 'ASC');
        }
        return Response::json(
            $options->get()->pluck(
                'fullname',
                'id'
            )
        );
    }

    public function optionsMultiple(Request $request)
    {
        $roles = explode(',', $request->get('roles', ''));
    
        if (empty($roles)) {
            return Response::json([]);
        }

        $options = LibertyUser::whereIn('role', $roles)
            ->select('first_name', 'last_name', 'id', 'role')
            ->orderBy('first_name', 'ASC')
            ->get()
            ->groupBy('role');

        return response()->json($options->map(function($optionsByRole) {
            return $optionsByRole->pluck('fullname','id')->toArray();
        }));
    }

    public function webLog(Request $request)
    {
        WebLog::create($request->all());

        $count_ew = WebLog::where('segments', '=', 'aspen-ew')
            ->where('user_id', $request->get('user_id'), 'AND')
            ->where('updated_at', '>=', Carbon::now()->subDay(), 'AND')
            ->count();

        $count_dcr = WebLog::where('segments', '=', 'aspen-dcr')
            ->where('user_id', $request->get('user_id'), 'AND')
            ->where('updated_at', '>=', Carbon::now()->subDay(), 'AND')
            ->count();

        $aspen_user = LibertyUser::find($request->get('user_id'));

        if (($count_ew > 5 || $count_dcr > 5) && isset($aspen_user)) {
            $this->mail->queue(
                $aspen_user->email, 'User Logging', 'User Logging', 'emails.auth.logging', [
                'module' => ($count_ew > 5)
                    ? 'Endorsement Wordings'
                    : 'Daily Claims Run',
                'aspen_user' => $aspen_user,
                ]
            );
        }
    }

    public function getPrevisicoUiNotif(Request $request)
    {
        $libertyUserId = $request->get('liberty_user_id');
        $previsicoUiNotification = PrevisicoUiNotification::select('has_notification')
            ->where('liberty_user_id', $libertyUserId)
            ->first();

        $hasNotification = !empty($previsicoUiNotification->has_notification)
            ? $previsicoUiNotification->has_notification
            : 0;

        return $hasNotification;
    }

    public function clearPrevisicoUiNotif(Request $request)
    {
        $organisationId = $request->get('organisation_id');
        $libertyUserId = $request->get('liberty_user_id');

        $notification = PrevisicoUiNotification::where('liberty_user_id', $libertyUserId)
            ->where('organisation_id', $organisationId)
            ->update(
                [
                'has_notification' => 0,
                ]
            );
    }

    public function getMultipleUserDetails(Request $request)
    {
        $libertySubmissionUserIds = $request->all();
        $users = LibertyUser::select('id','first_name','last_name')->whereIn('id', $libertySubmissionUserIds)->get();
        return Response::json(['response' => 'success', 'data' => $users], 200);
    }

    private function createPreviousPasswordEntry(LibertyUser $user)
    {
        PreviousPassword::create([
            'user_id' => $user->id,
            'password' => $user->password,
            'table' => 'liberty_users',
        ]);
    }

    public function getAllAccountEngineers()
    {
        $accountEngineers = LibertyUser::where('role_override', 'account-engineer')->get();
        return Response::json(['response' => 'success', 'data' => $accountEngineers], 200);
    }
}
