<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\ExternalSurveyCompany;
use App\Models\ExternalSurveyor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;

class ExternalSurveyCompanyController extends BaseController
{
    /**
     * Store new External Survey Company
     */

    public function store(Request $request)
    {
        $validator = Validator::make(
            $request->all(), [
            'name' => 'required',
            'address_1' => 'required',
            'postcode' => 'required',
            'city' => 'required',
            'country' => 'required',
            'email' => 'unique:external_survey_companies',
            'phone' => 'unique:external_survey_companies',
            ]
        );

        if ($validator->fails()) {
            $response = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        } else {
            $external_survey_company = ExternalSurveyCompany::create(
                [
                'name' => $request->get('name'),
                'address_1' => $request->get('address_1'),
                'address_2' => $request->get('address_2', null),
                'postcode' => $request->get('postcode', null),
                'city' => $request->get('city', null),
                'country' => $request->get('country', null),
                'email' => $request->get('email', null),
                'phone' => $request->get('phone', null),
                ]
            );

            if ($external_survey_company->id) {
                /*
                $this->mail->queue(
                    $external_survey_company->email,
                    $external_survey_company->name,
                    'Risk Reduce, Welcome to Risk Reduce',
                    'emails.authadmin.welcome',
                    $external_survey_company
                );
                */

                $response = [
                    'response' => 'success',
                    'message' => 'The External Survey Company has been created successfully',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The External Survey Company has failed to be created',
                ];
            }
        }

        return Response::json($response);
    }

    /**
     * Get All External Survey Companies
     */
    public function all(Request $request, $page = 1, $limit = 10)
    {
        $query = $request->get('search');
        $query = ($query)
            ? '%' . $query . '%'
            : '';

        $external_survey_companies = ($query)
            ? ExternalSurveyCompany::where(
                'name', 'LIKE', $query
            )->orWhere(
                'city', 'LIKE', $query
            )
            : ExternalSurveyCompany::query();

        $external_survey_companies = $external_survey_companies->take(
            $limit
        )->skip(
            ($page * $limit) - $limit
        )->get();

        return Response::json(
            [
            'response' => 'success',
            'data' => $external_survey_companies,
            'total' => count(ExternalSurveyCompany::all()),
            ]
        );
    }

    /**
     * Update External Survey Company
     */

    public function update(Request $request, $id)
    {
        if ($id && is_numeric($id)) {
            $validator = Validator::make(
                $request->all(), [
                'name' => 'required',
                'address_1' => 'required',
                'postcode' => 'required',
                'city' => 'required',
                'country' => 'required',
                'email' => 'unique:external_survey_companies,email,' . $id,
                'phone' => 'unique:external_survey_companies,phone,' . $id,
                ]
            );

            if ($validator->fails()) {
                $response = [
                    'response' => 'error',
                    'errors' => $validator->messages()->toArray(),
                ];
            } else {
                $external_survey_company = ExternalSurveyCompany::find(
                    $id
                );

                if ($external_survey_company->id) {
                    $external_survey_company->name = $request->get('name');
                    $external_survey_company->address_1 = $request->get('address_1');
                    $external_survey_company->address_2 = $request->get('address_2', null);
                    $external_survey_company->postcode = $request->get('postcode', null);
                    $external_survey_company->city = $request->get('city', null);
                    $external_survey_company->country = $request->get('country', null);
                    $external_survey_company->email = $request->get('email', null);
                    $external_survey_company->phone = $request->get('phone', null);
                    $external_survey_company->save();

                    $response = [
                        'response' => 'success',
                        'message' => 'The External Survey Company has been updated successfully',
                    ];
                } else {
                    $response = [
                        'response' => 'error',
                        'message' => 'Invalid ExternalSurveyCompany',
                    ];
                }
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid External Survey Company ID',
            ];
        }


        return Response::json($response);
    }

    /**
     * Find External Survey Company
     */
    public function show($id)
    {
        if ($id && is_numeric($id)) {
            $external_survey_company = ExternalSurveyCompany::find($id);

            if (!$external_survey_company) {
                $external_survey_company = ExternalSurveyCompany::where('id', '=', $id)->first();
            }

            $response = [
                'response' => ($external_survey_company)
                    ? 'success'
                    : 'error',
            ];

            if ($external_survey_company) {
                $response['data'] = $external_survey_company;
            } else {
                $response['message'] = 'The specified External Survey Company could not be found';
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid External Survey Company ID',
            ];
        }

        return Response::json($response);
    }

    /**
     * Delete External Survey Company
     *
     * @param $id
     *
     * @return mixed
     */
    public function destroy($id)
    {
        if ($external_survey_company = ExternalSurveyCompany::find($id)) {
            if (ExternalSurveyCompany::destroy($id)) {
                $response = [
                    'response' => 'success',
                    'message' => 'The specified External Survey Company was successfully deleted',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The specified External Survey Company could not be deleted',
                ];
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'The specified External Survey Company could not be found',
            ];
        }

        return Response::json($response);
    }

    /**
     * Get All Surveyors for a External Survey Company
     */
    public function surveyors(Request $request, $company_id)
    {
        $query = $request->get('search');
        //var_dump($company_id); exit;
        $external_surveyors = ExternalSurveyor::where('external_survey_company_id', '=', (int)$company_id)->get();

        return Response::json($external_surveyors->pluck('full_name', 'id')->all());

        // return Response::json([
        //     'response' => 'success',
        //     'data'     => $external_surveyors,
        //     'total'    => count(ExternalSurveyor::where('external_survey_company_id', '=', $company_id))
        // ]);
    }

    public function options()
    {
        return Response::json(
            ExternalSurveyCompany::orderBy('name', 'ASC')->pluck(
                'name',
                'id'
            )->all()
        );
    }
}
