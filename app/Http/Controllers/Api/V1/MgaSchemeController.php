<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\MgaScheme;
use App\Models\RiskImprovementFormy;
use App\Models\RiskImprovementFormySubmissions;
use App\Models\RiskRecommendationFormy;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;

class MgaSchemeController extends BaseController
{
    /**
     * Get Paginated schemes
     */
    public function index(Request $request)
    {
        $orderby = $request->has('orderby')
            ? $request->get('orderby')
            : 'name';
        $dir = $request->has('dir')
            ? $request->get('dir')
            : 'ASC';
        $page = $request->has('page')
            ? $request->get('page')
            : 1;
        $schemes = MgaScheme::with('broker')
            ->with('clients')
            ->with('surveys')
            ->with('clients.trade')
            ->orderby($orderby, $dir)
            ->skip(($page * 10) - 10)
            ->paginate(10);

        $schemes_array = $schemes->toArray();

        if ($request->has('submissions')) {
            foreach ($schemes_array['data'] as $scheme_key => $scheme) {
                foreach ($scheme['surveys'] as $survey_key => $survey) {
                    $schemes_array['data'][$scheme_key]['surveys'][$survey_key]["submissions"] = RiskImprovementFormySubmissions::whereIn(
                        'survey_id',
                        [(string)$survey['id'], $survey['id']]
                    )->whereIn(
                        'surveyor_id',
                        [(string)$survey['surveyor_id'], $survey['surveyor_id']]
                    )->first();
                    $schemes_array['data'][$scheme_key]['surveys'][$survey_key]["risk_rec_form_fields"] = RiskRecommendationFormy::where(
                        'form_id',
                        '=', $survey['survey_form']
                    )->first();
                }

            }
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => $schemes_array,
            ]
        );
    }

    public function show($id)
    {
        if ($id && is_numeric($id)) {
            $mga_scheme = MgaScheme::where('id', '=', $id)->with('broker')->first();

            if (!$mga_scheme) {
                $mga_scheme = MgaScheme::where('id', '=', $id)->first();
            }

            $response = [
                'response' => $mga_scheme
                    ? 'success'
                    : 'error',
            ];

            if ($mga_scheme) {
                $response['data'] = $mga_scheme;

            } else {
                $response['message'] = 'The specified MGA Scheme could not be found';
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid MGA Scheme ID',
            ];
        }

        return Response::json($response);
    }

    public function update(Request $request, MgaScheme $mgaScheme)
    {
        $input = $request->all();

        $validator = Validator::make(
            $request->all(), [
                'name' => 'required',
            ]
        );

        if ($validator->fails()) {
            return Response::json(
                [
                    'response' => 'error',
                    'errors' => $validator->messages()->toArray(),
                ], 200
            );
        } else {
            if (isset($mgaScheme->id)) {
                $mgaScheme->name = $request->get('name');
                $mgaScheme->policy_type = $request->get('policy_type');
                $mgaScheme->broker_id = $request->get('broker_organisation');
                $mgaScheme->survey_form_id = $request->get('survey_form_id');
                $mgaScheme->liberty_manager_id = $request->get('liberty_manager');
                $mgaScheme->trade_grouping_id = $request->get('trade_grouping');
                $mgaScheme->enable_uwr = isset($input['enable_uwr'])
                    ? 1
                    : 0;

                $mgaScheme->save();

                return Response::json(
                    [
                        'response' => 'success',
                        'message' => 'The MGA Scheme has been updated successfully',
                    ]
                );
            } else {
                return Response::json(
                    [
                        'response' => 'error',
                        'message' => 'Invalid MGA Scheme',
                    ]
                );
            }
        }
    }

    public function all(Request $request, $page = 1, $limit = 10000)
    {
        $query = $request->get('search');
        $query = $query
            ? '%' . $query . '%'
            : '';

        $mga_schemes = $query
            ? MgaScheme::where(
                'name', 'LIKE', $query
            )
            : MgaScheme::query();

        if ($request->has('broker_id')) {
            $mga_schemes = $mga_schemes->where('broker_id', $request->get('broker_id'));
        }

        if ($request->get('getNameAndIdOnly', false) || $request->get('fieldsNeeded', false)) {
            $selectFields = ['id','name'];
            if ($request->has('fieldsNeeded')) {
                $selectFields = explode(',', $request->get('fieldsNeeded'));
            }
            
            $mga_schemes = $mga_schemes
            ->take($limit)
            ->skip(($page * $limit) - $limit)
            ->orderBy('name', 'ASC')
            ->select($selectFields);

            if ($request->has('broker_id')) {
                $mga_schemes->where('broker_id', $request->get('broker_id'));
            }

            $mga_schemes = $mga_schemes->get();

            return Response::json(
                [
                    'response' => 'success',
                    'data' => $mga_schemes,
                ]
            );
        }

        $mga_schemes = $mga_schemes
            ->with('broker')
            ->with('trade_grouping')
            ->with('liberty_manager')
            ->take($limit)
            ->skip(($page * $limit) - $limit)
            ->orderBy('name', 'ASC')
            ->get();

        foreach ($mga_schemes as $b_scheme) {
            $form = RiskImprovementFormy::find($b_scheme->survey_form_id);
            if (!$form) {
                $b_scheme->survey_form_name = "Deleted Survey form";
            } else {
                $b_scheme->survey_form_name = $form->name;
            }
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => $mga_schemes,
                'total' => count(MgaScheme::all()),
            ]
        );
    }

    public function store(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make(
            $request->all(), [
                'name' => 'required',
                'policy_type' => 'required',
                'broker_organisation' => 'required',
                'survey_form_id' => 'required',
                'liberty_manager' => 'required',
                'trade_grouping' => 'required',
            ]
        );

        if ($validator->fails()) {
            return Response::json(
                [
                    'response' => 'error',
                    'errors' => $validator->messages()->toArray(),
                ], 200
            );
        } else {
            $data = [
                'name' => $request->get('name'),
                'policy_type' => $request->get('policy_type'),
                'broker_id' => $request->get('broker_organisation'),
                'liberty_manager_id' => $request->get('liberty_manager'),
                'trade_grouping_id' => $request->get('trade_grouping'),
                'survey_form_id' => $request->get('survey_form_id'),
                'enable_uwr' => isset($input['enable_uwr'])
                    ? 1
                    : 0,
            ];

            $mga_scheme = MgaScheme::create($data);

            if (isset($mga_scheme->id)) {
                return Response::json(
                    [
                        'response' => 'success',
                        'message' => 'The MGA Scheme has been created successfully' . $mga_scheme->id,
                    ], 200
                );
            } else {
                return Response::json(
                    [
                        'response' => 'error',
                        'message' => 'Unable to create MGA Scheme',
                    ], 200
                );
            }
        }
    }

    public function destroy($id)
    {
        if ($id && is_numeric($id)) {
            if (MgaScheme::destroy($id)) {
                return Response::json(
                    [
                        'response' => 'success',
                        'message' => 'The MGA Scheme has been deleted successfully',
                    ]
                );
            } else {
                return Response::json(
                    [
                        'response' => 'error',
                        'message' => 'The MGA Scheme could not be found',
                    ]
                );
            }
        } else {
            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'Invalid MGA Scheme ID',
                ]
            );
        }
    }
    
    public function hasMgaSchemes($id)
    {
        $mgaScheme = MgaScheme::where('broker_id', $id)->first();
        if($mgaScheme){
            return true;
        }
        
        return false;
    }
}
