<?php

namespace App\Http\Controllers\Api\V1\ClientClaims;

use App\Http\Controllers\BaseController;
use App\Decorators\AllClaimTypesDecorator;
use App\Decorators\BenchmarkingDecorator;
use App\Decorators\CauseOfLossSplitDecodator;
use App\Decorators\ClaimLifecycleDecorator;
use App\Decorators\ClaimsTypeSplitDecorator;
use App\Models\ClientClaimsDashboard\ClientClaim;
use App\Models\ClientClaimsDashboard\ClientClaimId;
use App\Decorators\ClientClaimsFilterDataDecorator;
use App\Models\ClientDashboardComponent;
use App\Models\ClientDashboardComponentOrder;
use App\Decorators\FactoryDecorator;
use App\Models\Organisation;
use App\Decorators\ProductSnapshotDecorator;
use App\Decorators\ProductValuesDecorator;
use App\Decorators\ProductVolumesDecorator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use App\Decorators\TopTenIncurredClaimsDecorator;
use App\Decorators\TopTenOpenClaimsDecorator;

class ClientClaimsController extends BaseController
{
    public function all(Request $request)
    {
        $organisationId = $request->get('organisation_id', '');
        $policyNumber = $request->get('policy_number', '');
        $product = $request->get('product', '');
        $tradingName = $request->get('trading_name', '');
        $classOfBusiness = $request->get('class_of_business', '');
        $lossType = $request->get('loss_type', '');
        $status = $request->get('status', '');
        $userId = $request->get('user_id', '');

        $claimId = ClientClaimId::where('organisation_id', $organisationId)->first();

        if ($request->get('claims_check')) {
            return Response::json(!empty($claimId));
        }

        if (!$claimId) {
            return Response::json([]);
        }

        $clientClaimClass = ClientClaim::getClientClaimModelForDemo($organisationId);
        $claims = $clientClaimClass::where('liberty_id', $claimId->liberty_id);
        $claimsForTypeSplit = clone $claims;
        $claimsCollection = clone $claims;
        $topTenOpenClaimsCollection = clone $claims;
        $claimLifecycleCollection = clone $claims;

        $filterDataCollection = clone $claims;
        $filterDataCollection = $this->applyFiltersUsingOrWhere(
            $filterDataCollection, $policyNumber, $product,
            $tradingName, $classOfBusiness, $lossType, $status
        );

        $claims = $this->applyFilters(
            $claims, $policyNumber, $product, $tradingName, $classOfBusiness, $lossType,
            $status
        );
        $claimsForTypeSplit = $this->applyFilters($claimsForTypeSplit, null, null, null, null, null, $status);
        $topTenOpenClaimsFilter = $this->applyFilters(
            $topTenOpenClaimsCollection, $policyNumber, $product,
            $tradingName, $classOfBusiness, $lossType, null
        );
        $claimLifecycleFilter = $this->applyFilters(
            $claimLifecycleCollection, $policyNumber, $product, $tradingName,
            $classOfBusiness, $lossType, null
        );

        $allClaimTypesCollection = clone $claims;
        $productVolumesCollection = clone $claims;

        $topTenIncurredClaimsCollection = clone $claims;
        $causeOfLossSplitCollection = clone $claims;

        $cobColors = $this->getClassOfBusinessColors($organisationId);
        $claimLifecycleMonths = $this->generateMonths(0);
        $claimsFactory = new FactoryDecorator($organisationId);
        $claimLifecycle = new ClaimLifecycleDecorator($organisationId, $claimLifecycleFilter, $claimId->liberty_id);
        $allClaimTypes = new AllClaimTypesDecorator(
            $organisationId, $cobColors, $allClaimTypesCollection,
            $claimId->liberty_id
        );
        $claimsTypeSplit = new ClaimsTypeSplitDecorator($organisationId, $cobColors, $claimsForTypeSplit);
        $productSnapshot = new ProductSnapshotDecorator($organisationId, $productVolumesCollection);
        $productVolumes = new ProductVolumesDecorator(
            $organisationId, $productVolumesCollection,
            $claimId->liberty_id
        );
        $productValues = new ProductValuesDecorator($organisationId, $productVolumesCollection, $claimId->liberty_id);
        $topTenOpenClaims = new TopTenOpenClaimsDecorator($organisationId, $topTenOpenClaimsFilter);
        $topTenIncurredClaims = new TopTenIncurredClaimsDecorator($topTenIncurredClaimsCollection);
        $benchmark = new BenchmarkingDecorator(
            $organisationId, $claimId->liberty_id, $product, $tradingName, $status,
            $classOfBusiness, $lossType
        );
        $causeOfLossSplit = new CauseOfLossSplitDecodator($causeOfLossSplitCollection);
        $organisationObject = Organisation::where('id', $organisationId)->first();
        $response = [
            'claim_lifecycle' => $claimLifecycle->getClaimLifecycle($claimLifecycleMonths),
            'all_claim_types' => $allClaimTypes->getAllClaimTypes(),
            'claims_type_split' => $claimsTypeSplit->getClaimsTypeSplit(),
            'snapshot' => $productSnapshot->getProductSnapshot(),
            'product_volumes' => $productVolumes->getProductVolumes(),
            'product_values' => $productValues->getProductValues(),
            'top_ten_open_claims' => $topTenOpenClaims->getTopTenOpenClaims(),
            'top_ten_incurred_claims' => $topTenIncurredClaims->getTopTenIncurredClaims(),
            'benchmarking' => [
                'claim_volumes' => $benchmark->getClaimVolumesPedestal(),
                'claim_values' => $benchmark->getClaimValuesPedestal(),
                'repudiation' => $benchmark->getRepudiationPedestal($lossType),
                'banner' => $benchmark->getBanner(),
            ],
            'cause_of_loss_split' => $causeOfLossSplit->get(),
            'organisation_object' => $organisationObject,
            'uploaded_date' => $claimsFactory->getCreatedDate($claimsCollection),
            'filter_data' => (new ClientClaimsFilterDataDecorator($organisationId, $filterDataCollection))->get(),
        ];

        return Response::json($response);
    }

    private function applyFiltersUsingOrWhere(
        $query,
        $policyNumber,
        $product,
        $tradingName,
        $classOfBusiness,
        $lossType,
        $status
    ) {
        $query->where(
            function ($search) use (
                $policyNumber,
                $product,
                $tradingName,
                $classOfBusiness,
                $lossType,
                $status
            ) {

                if (!empty($policyNumber)) {
                    $search->orWhere(
                        function ($query) use ($policyNumber) {
                            $query->whereIn('policy_number', explode(',', $policyNumber));
                        }
                    );
                }

                if (!empty($product)) {
                    $search->orWhere(
                        function ($query) use ($product) {
                            $query->whereIn('product', explode(',', $product));
                        }
                    );
                }

                if (!empty($tradingName)) {
                    $search->orWhere(
                        function ($query) use ($tradingName) {
                            $query->whereIn('trading_name', explode(',', $tradingName));
                        }
                    );
                }

                if (!empty($classOfBusiness)) {
                    $search->orWhere(
                        function ($query) use ($classOfBusiness) {
                            $query->whereIn('class_of_business', explode(',', $classOfBusiness));
                        }
                    );
                }

                if (!empty($lossType)) {
                    $search->orWhere(
                        function ($query) use ($lossType) {
                            $query->whereIn('loss_type', explode(',', $lossType));
                        }
                    );
                }

                if (!empty($status)) {
                    $search->orWhere(
                        function ($query) use ($status) {
                            $query->whereIn('status', explode(',', $status));
                        }
                    );
                }
            }
        );

        return $query;
    }

    private function applyFilters($search, $policyNumber, $product, $tradingName, $classOfBusiness, $lossType, $status)
    {
        if (!empty($policyNumber)) {
            $search->whereIn('policy_number', explode(',', $policyNumber));
        }

        if (!empty($product)) {
            $search->whereIn('product', explode(',', $product));
        }

        if (!empty($tradingName)) {
            $search->whereIn('trading_name', explode(',', $tradingName));
        }

        if (!empty($classOfBusiness)) {
            $search->whereIn('class_of_business', explode(',', $classOfBusiness));
        }

        if (!empty($lossType)) {
            $search->whereIn('loss_type', explode(',', $lossType));
        }

        if (!empty($status)) {
            $search->whereIn('status', explode(',', $status));
        }

        return $search;
    }

    private function getClassOfBusinessColors($organisationId)
    {
        $clientClaimClass = ClientClaim::getClientClaimModelForDemo($organisationId);
        $cobs = $clientClaimClass::select('class_of_business')
            ->distinct()
            ->get();

        $factory = new FactoryDecorator($organisationId);
        return $factory->getClassOfBusinessColor($cobs);
    }

    private function generateMonths($defaultValue)
    {
        $months = [];
        for ($month = 1; $month <= 12; $month++) {
            $monthName = date('M', mktime(0, 0, 0, $month, 1, date('Y')));
            $months[$monthName] = $defaultValue;
        }
        return $months;
    }

    public function getFilterData(Request $request)
    {
        $organisationId = $request->get('organisation_id', '');
        $filterData = (new ClientClaimsFilterDataDecorator($organisationId))->get();
        return Response::json($filterData);
    }

    public function getComponentOrder(Request $request)
    {
        if (!$request->get('user_id')) {
            return Response::json([]);
        }
        return Response::json([
            'component_order' => ClientDashboardComponentOrder::getOrCreate(
                $request->get('user_id'),
                ClientDashboardComponent::TYPE_CLAIMS
            )
        ]);
    }
}
