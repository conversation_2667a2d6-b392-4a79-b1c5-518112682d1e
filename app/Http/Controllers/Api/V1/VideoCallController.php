<?php


namespace App\Http\Controllers\Api\V1;

use Aloha\Twilio\Twilio;
use App\Http\Controllers\BaseController;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Response;
use Twilio\Rest\Client;
use App\Models\VideoCallParticipant;
use App\Models\VideoCallRoom;
use App\Models\VideoLog;
use App\Models\VideoLogData;
use Illuminate\Http\Request;

class VideoCallController extends BaseController
{

    private $twilio;
    private $twilioClient;
    private $twilioConfigUS;

    public function __construct()
    {
        $twilioConfig = config('app.twilio.sms');
        $this->twilio = new Twilio($twilioConfig['sid'], $twilioConfig['token'], $twilioConfig['from']);
        $this->twilioConfigUS = new Twilio($twilioConfig['sid'], $twilioConfig['token'], $twilioConfig['from-us']);
        $this->twilioClient = new Client($twilioConfig['sid'], $twilioConfig['token']);
    }

    /**
     * Save the details of the room and the participants
     *
     * @return json
     */
    public function generateRoom(Request $request)
    {
        $data = $request->all();
        $roles = ['client', 'broker', 'liberty_staff'];
        $errors = $this->validateNumber($data, $roles);
        if (!empty($errors)) {
            return Response::json(
                [
                'response' => 'fail',
                'data' => $errors,
                ]
            );
        }

        $room = VideoCallRoom::create(
            [
            'name' => VideoCallRoom::createRoomName(),
            'created_at' => Carbon::now(),
            'created_by_id' => $data['created_by_id'],
            'created_by_login_type' => $data['created_by_login_type'],
            'created_by_role' => $data['created_by_role'],
            ]
        );

        $response = [
            'room' => $room->name,
            'room_id' => $room->_id,
        ];
        $statusCallBack = config('app.twilio.statusCallBackUrl');
        $params = !empty($statusCallBack)
            ? ['statusCallback' => config('app.client_frontend') . '/' . $statusCallBack]
            : [];

        $log = VideoLog::create(
            [
            'room_id' => $room->id,
            'type' => VideoLog::TYPE_SMS,
            ]
        );
        foreach ($roles as $role) {
            if (!empty($data[$role]) && !empty($data[$role . '_phone'])) {
                $userCode = VideoCallParticipant::generateUserCode();


                $participant = VideoCallParticipant::create(
                    [
                    'room_id' => $room->_id,
                    'user_name' => $data[$role],
                    'user_code' => $userCode,
                    'mobile_number' => $data[$role . '_phone'],
                    'role' => $role,
                    ]
                );

                try {
                    if ($this->isNumberUS($data[$role . '_phone'])) {
                        $twilioSms = $this->twilioConfigUS->message(
                            $data[$role . '_phone'],
                            VideoCallParticipant::generateMessage($userCode),
                            [],
                            $params
                        );
                    } else {
                        $twilioSms = $this->twilio->message(
                            $data[$role . '_phone'],
                            VideoCallParticipant::generateMessage($userCode),
                            [],
                            $params
                        );
                    }

                    $participant->sms_id = $twilioSms->sid;
                    $participant->save();

                    VideoLogData::create(
                        [
                        'log_id' => $log->_id,
                        'sms_id' => $twilioSms->sid,
                        'human_readable' => 'Sending message to ' . $participant->user_name,
                        'data' => $twilioSms->toArray(),
                        ]
                    );

                    $log->status = $twilioSms->status;
                    $log->save();

                    $response['participants']['success'] = $participant->toArray();
                } catch (Exception $e) {
                    $response['participants']['failed'][] = [
                        'name' => $participant['user_name'],
                        'message' => $e->getMessage(),
                    ];

                    // save the error in the record
                    $participant->error = $e->getMessage();
                    $participant->save();

                    // exit immediately if there was an error with any participant
                    break;
                }
            }
        }

        if (empty($response['participants']['failed'])) {
            return Response::json(
                [
                'response' => 'success',
                'data' => $response,
                ]
            );
        }

        return Response::json(
            [
            'response' => 'fail',
            'data' => $response,
            ]
        );
    }

    /**
     * @param  $data
     * @param  $roles
     * @return array
     */
    private function validateNumber($data, $roles)
    {
        $errorNumbers = [];
        foreach ($roles as $role) {
            $number = $data[$role . '_phone'];

            try {
                $this->twilioClient->lookups->v1->phoneNumbers($number)->fetch();
            } catch (Exception $e) {
                $errorNumbers[] = $data[$role] . ' (' . $number . ')';
            }
        }

        return $errorNumbers;
    }

    private function isNumberUS($stringNum)
    {
        if (substr($stringNum, 0, 1) == '1' || substr($stringNum, 0, 2) == '+1') {
            return true;
        }
        return false;
    }

    /**
     * Return the details of the call so users can login to call setup
     *
     * @return mixed
     */
    public function login(Request $request)
    {
        $code = $request->get('code');
        $participant = VideoCallParticipant::where('user_code', $code)->first();

        if ($participant) {
            /**
 * @var VideoCallRoom $videoRoom 
*/
            $videoRoom = VideoCallRoom::where('name', $participant->room->name)->first();

            if (!$videoRoom->isExpired()) {
                return Response::json(
                    [
                    'response' => 'success',
                    'data' => [
                        'room' => $participant->room->name,
                        'name' => $participant->user_name,
                        'role' => $participant->role,
                    ],
                    ]
                );
            }
        }

        return Response::json(
            [
            'response' => 'fail',
            ]
        );
    }
}
