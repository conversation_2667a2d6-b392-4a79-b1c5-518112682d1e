<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\DcrDocument;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\Request;

class DcrDocumentController extends BaseController
{

    public function upload(Request $request)
    {
        $data = $request->all();

        DcrDocument::create($data);

        return Response::json(
            [
            'response' => 'success',
            ]
        );
    }

    public function all()
    {
        $documents = DcrDocument::with('libertyUser')
            ->orderBy('created_at', 'desc')->get();

        if (!$documents) {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to get uploaded',
                ]
            );
        }

        $data['documents'] = $documents;

        return Response::json(
            [
            'response' => 'success',
            'message' => 'Results found',
            'data' => $data,
            ]
        );
    }

}
