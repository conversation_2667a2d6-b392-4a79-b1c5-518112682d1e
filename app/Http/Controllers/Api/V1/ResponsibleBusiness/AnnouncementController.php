<?php

namespace App\Http\Controllers\Api\V1\ResponsibleBusiness;

use App\Http\Controllers\BaseController;
use App\Models\Cms;
use Exception;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\Request;

class AnnouncementController extends BaseController
{
    const SECTOR_TYPE_NOT_FOR_PROFIT = "Not for Profit";

    public function getAnnouncements(Request $request)
    {
        $input = $request->all();
        $workspaceId = config('app.cms.responsible_business.workspace');
        $contentType = config('app.cms.responsible_business.content_type');
        $publishDate = config('app.cms.responsible_business.publish_date');
        $responsibleBusinessArticles = [];

        $query = json_encode(
            [
            'status' => 'publish',
            'operator' => '=',
            'order' => $publishDate,
            ]
        );

        $articles = json_decode(Cms::get('workspaces/' . $workspaceId . '/content-types/' . $contentType . '/content-entries?query=' . $query))->data;

        $page = ($request->has('page'))
            ? $input['page']
            : 1;
        $pageLowerLimit = ($page * 10) - 11;
        $pageUpperLimit = $page * 10;
        $pageCtr = 0;

        if ($articles && !empty($articles)) {
            foreach ($articles as $article) {

                if ($article && !empty($article)) {

                    $sectors = config('app.cms.responsible_business.sectors');
                    $article_title = config('app.cms.responsible_business.article_title');
                    $content = config('app.cms.responsible_business.article_content');
                    $featured_image = config('app.cms.responsible_business.featured_image');
                    $key_sectors = [];

                    foreach ($article->{$sectors} as $info) {
                        if (isset($info->name) && ($info->name === self::SECTOR_TYPE_NOT_FOR_PROFIT || $info->name === "Announcement")) {
                            $key_sectors[] = $info->name;
                        }
                    }

                    if ((in_array(self::SECTOR_TYPE_NOT_FOR_PROFIT, $key_sectors)) 
                        && (in_array("Announcement", $key_sectors))
                    ) {

                        if ((in_array(self::SECTOR_TYPE_NOT_FOR_PROFIT, $key_sectors)) 
                            && (in_array("Announcement", $key_sectors))
                        ) {

                            try {
                                if (($pageCtr > $pageLowerLimit) && ($pageCtr < $pageUpperLimit)) {
                                    $responsibleBusinessArticles[] = [
                                        'article_id' => $article->_id,
                                        'name' => $article->name,
                                        'featured_image' => $article->{$featured_image}[0]->url,
                                        'article_title' => $article->{$article_title},
                                        'content' => $article->{$content},
                                        'created_at' => $article->created_at,
                                        'updated_at' => $article->updated_at,
                                    ];
                                }
                                $pageCtr++;
                            } catch (Exception $e) {
                                continue;
                            }
                        }
                    }
                }
            }
            return [
                'data' => $responsibleBusinessArticles,
                'page_count' => $pageCtr,
            ];
        }

        return Response::json(
            [
            'msg' => 'Something went wrong',
            ]
        );
    }

    public function getAnnouncement($article_id)
    {
        $article_title = config('app.cms.responsible_business.article_title');
        $content = config('app.cms.responsible_business.article_content');
        $featured_image = config('app.cms.responsible_business.featured_image');
        $responsibleBusinessArticle = [];

        try {
            $workspaceId = config('app.cms.responsible_business.workspace');
            $contentType = config('app.cms.responsible_business.content_type');

            $article = json_decode(Cms::get('workspaces/' . $workspaceId . '/content-types/' . $contentType . '/content-entries/' . $article_id));

            if ($article && !empty($article)) {
                $responsibleBusinessArticle = [
                    'article_id' => $article->_id,
                    'name' => $article->name,
                    'featured_image' => $article->{$featured_image}[0]->url,
                    'article_title' => $article->{$article_title},
                    'content' => $article->{$content},
                    'created_at' => $article->created_at,
                    'updated_at' => $article->updated_at,
                ];
            }

            return $responsibleBusinessArticle;

        } catch (Exception $e) {
            return Response::json(
                [
                'msg' => 'Something went wrong',
                'details' => $e->getMessage(),
                ]
            );
        }
    }
}
