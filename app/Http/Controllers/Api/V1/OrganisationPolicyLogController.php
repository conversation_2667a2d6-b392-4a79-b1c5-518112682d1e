<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\OrganisationPolicyLog;
use Illuminate\Http\Request;
class OrganisationPolicyLogController extends Controller
{
    /**
     * Get historical logs for an organisation
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index($id, Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $sortBy = $request->input('sort_by', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');

        $query = OrganisationPolicyLog::with(['organisation', 'changedByUser'])
            ->when($id, function ($query, $organisationId) {
                return $query->ofOrganisation($organisationId);
            })
            ->when($request->policy_number, function ($query, $policyNumber) {
                return $query->ofPolicyNumber('policy_number', $policyNumber);
            })
            ->when($request->date_from, function ($query, $dateFrom) {
                return $query->where('created_at', '>=', $dateFrom);
            })
            ->when($request->date_to, function ($query, $dateTo) {
                return $query->where('created_at', '<=', $dateTo);
            });

        $logs = $query->orderBy($sortBy, $sortOrder)
            ->paginate($perPage);

        return response()->json([
            'data'    => $logs,
            'message' => 'Organisation policy logs retrieved successfully'
        ]);
    }
}
