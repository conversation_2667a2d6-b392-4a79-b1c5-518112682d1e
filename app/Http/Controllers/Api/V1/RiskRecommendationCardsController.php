<?php

namespace App\Http\Controllers\Api\V1;

use App\Enums\KanbanStatus;
use App\Exports\RiskRecExport;
use App\Http\Controllers\Controller;
use App\Http\Resources\RiskRecCardCollection;
use App\Models\BrokerUser;
use App\Models\FileUpload;
use App\Models\Mailqueue;
use App\Models\RiskImprovementFormySubmissions;
use App\Models\RiskRecommendationCards;
use App\Services\QueueService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\File;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Str;

class RiskRecommendationCardsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param  Request  $request
     * @return RiskRecCardCollection|array
     *
     * @throws \ValueError thrown if column provided column is invalid
     */
    public function index(Request $request): RiskRecCardCollection|array
    {
        // if columns is present in query, get data for single column only
        if ($request->get('column') && KanbanStatus::from($request->get('column'))) {
            $cards = $this->generateCardsPerType($request);
            return $this->collect($cards);
        }

        // get cards for all statuses
        $open = $this->generateCardsPerType($request, KanbanStatus::OPEN);
        $feedback = $this->generateCardsPerType($request, KanbanStatus::FEEDBACK_RECEIVED);
        $overdue = $this->generateCardsPerType($request, KanbanStatus::OVERDUE);
        $closed = $this->generateCardsPerType($request, KanbanStatus::CLOSED);

        return [
            KanbanStatus::OPEN->value => $this->collect($open),
            KanbanStatus::FEEDBACK_RECEIVED->value => $this->collect($feedback),
            KanbanStatus::OVERDUE->value => $this->collect($overdue),
            KanbanStatus::CLOSED->value => $this->collect($closed),
        ];
    }

    public function export(Request $request)
    {
        $pathToSave = storage_path('rr-files');
        File::ensureDirectoryExists($pathToSave, 775, true, true);
   
        $filename   = '/risk-recommendations-' . date('d-m-Y') . '.xlsx';
        Excel::store(new RiskRecExport($request), $pathToSave . $filename);
        return response()->download(storage_path('app' . $pathToSave . $filename))
            ->deleteFileAfterSend(true);
    }

    public function getCardsBySurvey(int $surveyId)
    {
        if (!empty($surveyId)) {
            $cards = RiskRecommendationCards::where('survey_id', (string)$surveyId)->get();
            if ($cards->isEmpty()) {
                return response()->json([
                    'data' => $this->getRiskRecStatuses($surveyId),
                    'response' => 'success',
                ]);
            }

            return response()->json([
                'data' => $cards,
                'response' => 'success'
            ]);
        }

        return response()->json([
            'data' => [],
            'response' => 'success',
        ]);
    }

    private function getRiskRecStatuses(int $surveyId)
    {
        $cards = [];
        $submission = RiskImprovementFormySubmissions::where('survey_id', (string)$surveyId)->first();
        if (!empty($submission)) {
            $risk_rec_array = [];
            if (isset($submission->riskRecommendationForm->risk_recommendation_fields)) {
                $risk_rec_array = $submission->riskRecommendationForm->risk_recommendation_fields;
            } else {
                $form = $submission->formy;
                if (isset($form->fields)) {
                    foreach ($form->fields as $field) {
                        foreach ($field as $key => $value) {
                            if ($key == 'risk_recommendation') {
                                foreach ($value as $val) {
                                    if ($val['name'] == 'name') {
                                        $risk_rec_array[] = $val['value'];
                                    }
                                }
                            }
                        }
                    }
                }
            }

            $schedule = $submission->formatSchedule($submission->survey->schedule);
            if ($schedule) {
                $schedule->actual_submission_deadline = $submission->survey?->scheduleMeta?->value;
            }

            $fields = [
                '_ref',
                '_message',
                '_classification',
                '_required_by',
                '_issue_closed',
                '_description',
                '_action',
                '_title',
                '_issue_closed',
            ];
            $policies = ['1' => 'Property', '2' => 'Casualty', '3' => 'Commercial Combined', '5' => 'Construction All Risk'];

            foreach ($risk_rec_array as $risk_rec) {
                for ($i = 1; $i <= 15; ++$i) {
                    $card = ['submission_id' => $submission->_id];
                    $risk_rec_prefix = $risk_rec . '_' . $i;
                    $hasMessage = false;
                    $exportProperties = [];

                    foreach ($fields as $field) {
                        \Log::info(['risk_rec_prefix' => $risk_rec_prefix, 'field' => $field]);
                        if (isset($submission->{$risk_rec_prefix . $field})) {
                            if ($field === '_classification' && $submission->{$risk_rec_prefix . '_classification'} != '') {
                                $policyTypeId = $submission->survey->existingPolicyNumber?->policy_type_id;

                                $properties = [
                                    'client' => $submission->survey->organisation != null
                                        ? $submission->survey->organisation->name
                                        : 'N/A',
                                    'srf' => isset($submission->survey->legacy_srf) && $submission->survey->legacy_srf != null
                                        ? 'SRF' . $submission->survey->legacy_srf
                                        : 'SRF' . $submission->survey->id,
                                    'survey_type' => in_array($policyTypeId, array_keys($policies))
                                        ? $policies[$policyTypeId]
                                        : '',
                                    'survey_date' => isset($schedule)
                                        ? (isset($schedule->actual_submission_deadline)
                                            ? date('d/m/Y', strtotime($schedule->actual_submission_deadline))
                                            : '-')
                                        : null,
                                    'mga_scheme' => $submission->survey->organisation->mga_scheme,
                                    'required_by' => isset($submission->{$risk_rec_prefix . '_required_by'})
                                        ? $submission->{$risk_rec_prefix . '_required_by'}
                                        : '-',
                                    'location' => [
                                        'location_name' => $submission->survey->location?->location_name,
                                        'postcode' => $submission->survey->location?->postcode,
                                    ]
                                ];

                                // region Fields for export
                                $postcode = 'N/A';
                                if (isset($submission->survey->visit_arrangement) && $submission->survey->visit_arrangement != '' && isset($submission->surveyContacts)) {
                                    foreach ($submission->surveyContacts as $contact) {
                                        if ($contact->postcode != '' && !is_null($contact->postcode)) {
                                            $postcode = $contact->postcode;
                                        }
                                    }
                                }

                                $exportProperties['organisation'] = isset($submission->survey->organisation->name) && $submission->survey->organisation->name != null
                                    ? $submission->survey->organisation->name
                                    : '';
                                $exportProperties['description'] = isset($submission->{$risk_rec_prefix . '_description'})
                                    ? $submission->{$risk_rec_prefix . '_description'}
                                    : '-';
                                $exportProperties['action'] = isset($submission->{$risk_rec_prefix . '_action'})
                                    ? $submission->{$risk_rec_prefix . '_action'}
                                    : '-';
                                $exportProperties['title'] = isset($submission->{$risk_rec_prefix . '_title'})
                                    ? $submission->{$risk_rec_prefix . '_title'}
                                    : '-';
                                $exportProperties['closed'] = isset($submission->{$risk_rec_prefix . '_issue_closed'})
                                    ? $submission->{$risk_rec_prefix . '_issue_closed'}
                                    : '0';
                                $exportProperties['csr_status'] = isset($submission->csr_ststus)
                                    ? $submission->csr_ststus
                                    : 'completed';
                                $exportProperties['postcode'] = $postcode;
                                $exportProperties['classification'] = isset($submission->{$risk_rec_prefix . '_classification'})
                                    ? $submission->{$risk_rec_prefix . '_classification'}
                                    : '-';
                                // endregion

                                // region Distribute cards to correct columns
                                $current_date = isset($submission->{$risk_rec_prefix . '_required_by'}) && ($submission->{$risk_rec_prefix . '_required_by'} != '')
                                    ? Carbon::createFromFormat(
                                        'd-m-Y',
                                        str_replace('/', '-', $submission->{$risk_rec_prefix . '_required_by'})
                                    )
                                    : null;
                                $column = KanbanStatus::OPEN->value;
                                if (isset($submission->{$risk_rec_prefix . '_issue_closed'}) && $submission->{$risk_rec_prefix . '_issue_closed'} == '1') {
                                    $column = KanbanStatus::CLOSED->value;
                                } else {
                                    if (isset($current_date) && $current_date->lt(Carbon::now())) {
                                        $column = KanbanStatus::OVERDUE->value;
                                    } else {
                                        if ($hasMessage && ((isset($current_date) && $current_date->gte(Carbon::now())) || !isset($current_date))) {
                                            $column = KanbanStatus::FEEDBACK_RECEIVED->value;
                                        }
                                    }
                                }
                                $card['column'] = $column;
                                // endregion

                                $card['survey_id'] = $submission->survey_id;
                                $card['title'] = $submission->{$risk_rec_prefix . '_ref'};
                                $card['url'] = '/surveys/report/' . $submission->survey->id . '/risk-recommendation/' . $risk_rec_prefix . '_';
                                $card['properties'] = $properties;

                                // used for filtering
                                $card['export_properties'] = $exportProperties;
                                $card['organisation_id'] = $submission->survey->organisation_id;
                                $card['branch_id'] = $submission->survey->branch_id;
                                $card['underwriter_id'] = $submission->survey->underwriter_id;

                                // used for sorting
                                $card['survey_date'] = isset($properties['survey_date']) && $properties['survey_date'] !== '-'
                                    ? Carbon::createFromFormat('d/m/Y', $properties['survey_date'])->toString()
                                    : Carbon::parse('Jan 1, 1970')->toString();

                                $cards[] = $card;
                            }
                        }
                    }
                }
            }
        }
        return $cards;
    }

    /**
     * @param  Request  $request
     * @return Paginator
     */
    private function generateCardsPerType(Request $request, KanbanStatus $kanbanStatus = null): Paginator
    {
        $limit = $request->get('limit') ?? 10;
        $cards = RiskRecommendationCards::columns($request);
        if ($request->get('user_type') == 'broker-user') {
            $broker = BrokerUser::find($request->get('user_id'));
            $orgIds = $broker->associatedOrganisations();
            $cards  = $cards->whereIn('organisation_id', $orgIds);
        }

        return $cards // choose columns
            ->filter($request, $kanbanStatus) // apply filter
            ->sort($request) // apply sorting
            ->simplePaginate($limit); // apply limit
    }

    private function collect(Paginator $cards)
    {
        return (new RiskRecCardCollection($cards))->response()->getData(true);
    }

    public function processExport(Request $request)
    {
        try {
            $queueService = new QueueService(config('app.aws.risk_recommendation_export_sqs'));
            $queueService->sendMessages([
                ['request' => $request->all()]
            ]);

            return response()->json([
                'response' => 'success',
            ]);
        } catch (\Exception $e) {
            \Log::error("[Export Risk Recommendation Error]" . $e->getMessage());
        }
    }

    public function queueExport(Request $request)
    {
        try {       
            $filename   = 'risk-recommendations-' . date('d-m-Y his') . '.xlsx';
            $pathToFile = "file_to_download/{$filename}";

            Excel::store(new RiskRecExport($request), $pathToFile, 'storage');
    
            $this->sendFileToClient($request, $pathToFile);
    
            return response()->json([
                'response' => 'success',
            ]);
        } catch (\Exception $e) {
            \Log::info('[RiskRecommendationExportCommand] > queueExport: ' . $e->getMessage());
        }
    }

    private function sendFileToClient($user, $pathToFile)
    {
        try {
            $mail     = new Mailqueue();
            $s3       = new FileUpload();
            $filename = 'risk-recommendations-' . date('d-m-Y his') . '.xlsx';
            $dir      = "excel/exports/{$filename}";

            if ($s3->upload("storage/{$pathToFile}", $dir)) {
                $messageParams = [];
                
                $link = $s3->link($dir, '2 days');

                File::delete("storage/{$pathToFile}"); // Delete After upload to s3
                
                $messageParams['excel_download_link'] = $link;
                $messageParams['email']         = $user->email;
                $messageParams['first_name']    = $user->first_name;
                $messageParams['last_name']     = $user->last_name;
                $messageParams['email_subject'] = 'Risk Reduce - Risk Recommendation Excel Export Ready';
                $messageParams['email_body']    = 'test';
                $messageParams['name']          = $user->first_name . " " . $user->last_name;
                $mail->queue(
                    $messageParams['email'],
                    $messageParams['name'],
                    'Risk Reduce - Risk Recommendation Excel Export Ready',
                    'emails.risk-recommendation.excel-ready',
                    $messageParams
                );
            }
        } catch (\Exception $e) {
            \Log::info('[RiskRecommendationExportCommand] > sendFileToClient: ' . $e->getMessage());
        }
    }
}
