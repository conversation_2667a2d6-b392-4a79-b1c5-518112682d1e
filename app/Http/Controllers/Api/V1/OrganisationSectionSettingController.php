<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\OrganisationSectionSetting;
use Illuminate\Http\Request;

class OrganisationSectionSettingController extends BaseController
{
    /**
     * Store or update organisation settings
     *
     * @param Request $request
     * @return string
     */
    public function storeOrUpdate(Request $request)
    {
        $organisationId   = $request->organisation_id;
        $settings         = $request->settings;
        $existingSettings = OrganisationSectionSetting::where('organisation_id', $organisationId)->first();
        $data             = ['organisation_id' => $organisationId];
        $hiddenSettings   = $this->getSettingsToBeHidden($settings);
        if (is_null($existingSettings)) {
            $data['access_setting']  = json_encode($hiddenSettings);
            $result = OrganisationSectionSetting::create($data);
        } else {
            $existingSettings->access_setting = json_encode($hiddenSettings);
            $result = $existingSettings->save();
        }

        return json_encode([
            'response' => !empty($result) ? 'success' : 'error',
            'message'  => is_bool($result) ? 'Successfully updated' : 'Successfully created',
        ]);
    }

    /**
     * Return organisation settings
     *
     * @param int $organisationId
     * @return string
     */
    public function getSettings($organisationId)
    {
        return json_encode(OrganisationSectionSetting::where('organisation_id', $organisationId)->first());
    }

    /**
     * Return settings to be hidden
     *
     * @param array $settings
     * @return array
     */
    private function getSettingsToBeHidden($settings)
    {
        $hideSettings = [
            'risk_engineering' => $settings['risk_engineering'],
            'survey'           => $settings['survey'],
            'your_team'        => $settings['your_team'],
            'services'         => [],
        ];

        $services = is_array($settings['services']) ? $settings['services'] : json_decode($settings['services'], true);
        foreach ($services as $setting => $value) {
            if ($value === "0") {
                $hideSettings['services'][] = $setting;
            }
        }
        return $hideSettings;
    }
}
