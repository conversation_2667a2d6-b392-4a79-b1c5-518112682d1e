<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use Carbon\Carbon;
use App\Models\OrganisationSubmission;
use App\Models\OrganisationSubmissionLink;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\Request;

class UploadBorderauController extends BaseController
{
    public function store(Request $request)
    {
        $data = $request->except('_token');

        $facilityName = $data['facility_name'];
        $brokerId = $data['broker_id'];

        unset($data['facilityName']);
        unset($data['broker_id']);

        $failedRows = [];

        $submissionLinkData = [
            'facility_name' => $facilityName,
            'broker_id' => $brokerId,
            'revoked_at' => Carbon::now(),
            'is_borderau' => 1,
        ];
        $submissionLink = OrganisationSubmissionLink::create($submissionLinkData);

        foreach ($data['insert'] as $key => $insert) {
            $insert = (object)$insert;

            if ($this->failsValidation($insert)) {
                $failedRows[] = $key + 2;
            } else {

                $submissionData = [
                    'name' => $insert->name,
                    'policy_number' => $insert->policy_number,
                    'inception_date_of_cover' => $insert->inception_date_of_cover,
                    'expiry_date_of_cover' => $insert->expiry_date_of_cover,
                    'premium' => $insert->premium,
                    'description' => $insert->description,
                    'email' => $insert->email,
                    'phone' => $insert->phone,
                    'address_line_1' => $insert->address_line_1,
                    'address_line_2' => $insert->address_line_2,
                    'postcode' => $insert->postcode,
                    'country' => $insert->country,
                    'admin' => [
                        'first_name' => $insert->admin_first_name,
                        'last_name' => $insert->admin_last_name,
                        'job_title' => $insert->admin_job_title,
                        'email' => $insert->admin_email,
                        'phone' => $insert->admin_phone,
                    ],
                    'manager' => $insert->manager,
                    'croner_access' => $insert->croner_access,
                    'status' => 'pending',
                ];

                OrganisationSubmission::create(
                    $submissionData + [
                        'submission_link_id' => $submissionLink->_id,
                    ]
                );
            }

        }

        if ($failedRows) {
            return Response::json(
                [
                'status' => 'error',
                'message' => 'Import failed due to empty rows ' . implode(', ', $failedRows) . '.',
                ]
            );
        }

        return Response::json(
            [
            'status' => 'success',
            'message' => 'Upload completed.',
            ]
        );
    }

    protected function failsValidation($insert)
    {
        if (!isset($insert->name) 
            || !isset($insert->description) 
            || !isset($insert->email) 
            || !isset($insert->phone) 
            || !isset($insert->address_line_1) 
            || !isset($insert->address_line_2) 
            || !isset($insert->postcode) 
            || !isset($insert->country) 
            || !isset($insert->admin_first_name) 
            || !isset($insert->admin_last_name) 
            || !isset($insert->admin_job_title) 
            || !isset($insert->admin_email) 
            || !isset($insert->admin_phone)
        ) {
            return true;
        }

        return false;
    }
}
