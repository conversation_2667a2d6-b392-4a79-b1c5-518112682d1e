<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\Course;
use App\Models\CourseOrganisation;
use App\Models\CourseUser;
use App\Models\LmsUserLesson;
use Illuminate\Support\Facades\Response;
use App\Models\User;

class LearningAssignmentController extends BaseController
{
    protected $relations = [
        'organisation',
        'lessons',
        'category',
    ];

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index()
    {
        //
    }


    /**
     * Show the form for creating a new resource.
     *
     * @return Response
     */
    public function create()
    {
        //
    }


    /**
     * Store a newly created resource in storage.
     *
     * @return Response
     */
    public function store()
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return Response
     */
    public function show($user_id)
    {
        /**
         * TO BE REFACTORED.
         */
        if ($user_id) {
            // get a list of all courses/lessons assigned to the user
            $courses = $this->getCourseLessonProgressArray($user_id);
            //dd($courses);
            // get a list of all courses/lessons currently in progress
            $in_progress = [];

            foreach ($courses as $course) {
                // calculate the progress depending on number of lessons in the course and how many are complete
                $course->progress = 100;

                if ((isset($course->read) && $course->read == 1) && (isset($course->test) && $course->test <= 1)) {
                    array_push($in_progress, $course);
                }
            }

            // get a list of all courses/lessons currently pending
            $pending = [];

            foreach ($courses as $course) {
                if ((isset($course->read) && $course->read == 0) && (isset($course->test) && $course->test == 0)) {
                    array_push($pending, $course);
                }
            }

            // get a list of all courses/lessons currently complete
            $complete = [];

            foreach ($courses as $course) {
                if ((isset($course->read) && $course->read == 1) && (isset($course->test) && $course->test == 2)) {
                    array_push($complete, $course);
                }
            }

            $response = [
                'response' => 'success',
                'data' => [
                    'all' => $courses,
                    'in_progress' => $in_progress,
                    'pending' => $pending,
                    'complete' => $complete,
                ],
            ];
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid user ID',
            ];
        }

        return Response::json($response);
    }

    /**
     * Get a list of all courses, lessons and lesson
     * progression for the specified user.
     *
     * @param  int $user_id
     * @return Array
     */
    public function getCourseLessonProgressArray($user_id)
    {
        $user = User::find($user_id);
        $coursesToFind = [];
        $coursesUser = CourseUser::where('user_id', '=', $user_id)->select('course_id')->get();
        foreach ($coursesUser as $courseUser) {
            array_push($coursesToFind, $courseUser->course_id);
        }
        $coursesOrg = CourseOrganisation::where(
            'organisation_id', '=',
            $user->organisation_id
        )->select('course_id')->get();
        foreach ($coursesOrg as $courseOrg) {
            array_push($coursesToFind, $courseOrg->course_id);
        }
        //dd($coursesToFind);
        $coursesAssigned = Course::whereIn('id', $coursesToFind)->get();
        $this->getRelations($coursesAssigned);

        $userLessons = LmsUserLesson::where('user_id', '=', $user_id)->get();

        $courses = [];

        foreach ($coursesAssigned as $key => $courseAssigned) {

            if ($courseAssigned != null) {

                foreach ($courseAssigned->lessons as $key2 => $les) {

                    foreach ($userLessons as $pro) {
                        if ($les->id == $pro->lesson_id) {
                            $les->read = $pro->read;
                            $les->test = $pro->test;
                            $courseAssigned->lessons[$key2] = $les;
                        }
                    }

                    //$les->progress = $progress;
                }

                //$courseAssigned->lesson = $lesson;
                $courseAssigned->total_lessons = count($courseAssigned->lesson);

                //array_push($courses, $courseAssigned);
                $coursesAssigned[$key] = $courseAssigned;
            }
        }

        return $coursesAssigned;
    }

    private function getRelations(&$item)
    {
        foreach ($this->relations as $relation) {
            //$item->$relation;
            unset($item->{sprintf('%s_id', $relation)});
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     * @return Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int $id
     * @return Response
     */
    public function update($id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return Response
     */
    public function destroy($id)
    {
        //
    }

}
