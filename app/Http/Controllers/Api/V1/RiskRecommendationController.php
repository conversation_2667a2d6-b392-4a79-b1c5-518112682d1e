<?php

namespace App\Http\Controllers\Api\V1;

use App\Exports\SurveyReportExport;
use App\Http\Controllers\BaseController;
use App\Http\Controllers\Controller;
use App\Models\RiskImprovementFormy;
use App\Models\RiskImprovementFormySubmissions;
use Illuminate\Http\Request;

class RiskRecommendationController extends BaseController
{
    public function exportSurveyReport(Request $request)
    {
        $riSubmission = RiskImprovementFormySubmissions::with([
            'survey:id,organisation_id', 
            'survey.organisation:id,postcode,name',
        ])->where('survey_id', (string)$request->survey_id)->first();

        if (!isset($riSubmission->survey)) {
            return response()->json([
                'response' => 'error',
                'message'  => 'Survey Not Found',
            ]);
        }

        $riForm = RiskImprovementFormy::select('fields')->find($riSubmission->form_id);

        if (!empty($riForm)) {
            $rrData = [];
            foreach ($riForm->fields as $fieldWrapper) {
                foreach ($fieldWrapper as $fieldType => $fieldAttribute) {
                    if ($fieldType == 'risk_recommendation') {
                        foreach ($fieldAttribute as $element) {
                            if ($element['name'] == 'name') {
                                $rrData[] = $element['value'];
                            }
                        }
                    }
                }
            }
        }

        $exportData = [
            [
                'SRF',
                'Postcode',
                'Organisation',
                'Ref',
                'Title',
                'Classification',
                'Required By',
                'Status',
                'Description',
                'Action',
            ]
        ];

        foreach ($rrData as $riskRecKey) {
            for ($i = 1; $i <= 10; $i++ ) {
                $prefix         = $riskRecKey . '_' . $i . '_';
                $ref            = $prefix . 'ref';
                $classification = $prefix . 'classification';
                $title          = $prefix . 'title';
                $requiredBy     = $prefix . 'required_by';
                $status         = isset($riSubmission->{$prefix . 'issue_closed'}) && $riSubmission->{$prefix . 'issue_closed'} == '1' ? 'Closed' : 'Open';
                $description    = $prefix . 'description';
                $action         = $prefix . 'action';

                if (!empty($riSubmission->{$ref}) && !empty($riSubmission->{$title}) && !empty($riSubmission->{$classification})
                    && !empty($riSubmission->{$description}) && !empty($riSubmission->{$action})) {
                    $exportData[] = [
                        'SRF' . $riSubmission->survey_id,
                        $riSubmission->survey->organisation->postcode,
                        $riSubmission->survey->organisation->name,
                        $riSubmission->{$ref},
                        $riSubmission->{$title},
                        $riSubmission->{$classification},
                        $riSubmission->{$requiredBy},
                        $status,
                        $riSubmission->{$description},
                        $riSubmission->{$action},
                    ];
                }
            }
        }

        return response()->json([
            'response' => 'success',
            'data'     => $exportData,
            'message'  => count($exportData) > 1 ?: 'No risk recommendations available', // if count of exportData is equal to 1 means only the title
        ]);
    }
}
