<?php

namespace App\Http\Controllers\Api\V1;

use App\Exports\SurveyOverviewsExport;
use App\Http\Controllers\BaseController;
use App\Models\Branch;
use App\Models\BrokerUser;
use App\Models\RiskRecommendationCards;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Models\DeletedResurvey;
use Maatwebsite\Excel\Facades\Excel;
use Exception;
use App\Models\ExternalSurveyCompany;
use App\Models\ExternalSurveyor;
use App\Models\FileUpload;
use App\Models\LegacyRiskImprovementFormySubmissions;
use App\Models\LegacySurvey;
use App\Models\LegacySurveyFiles;
use App\Models\LibertyUser;
use App\Models\Mailqueue;
use App\Models\MgaScheme;
use App\Models\Organisation;
use App\Models\PolicyType;
use Illuminate\Http\Request;
use App\Models\RiskImprovementFormySubmissions;
use App\Models\Schedule;
use Illuminate\Support\Facades\Session;
use App\Models\Survey;
use App\Models\SurveyAgendaFile;
use App\Models\SurveyContact;
use App\Models\SurveyFileAttachments;
use App\Models\Surveyfiles;
use App\Models\SurveySRF;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Event;
use App\Events\ExcellProcessed;
use App\Models\Broker;
use App\Models\RiskRecommendationLogs;
use App\Models\SurveyCommentaryInfo;
use App\Services\QueueService;
use Ichtrojan\Otp\Otp;
use Illuminate\Support\Facades\Artisan;

class SurveyController extends BaseController
{
    public function __construct(Mailqueue $mailqueue, FileUpload $fileupload)
    {
        $this->queue = $mailqueue;
        $this->files = $fileupload;
    }

    public function getSurveysForSurveyors($id, $sub = 0, $user_type = 'none')
    {
        if ($id && is_numeric($id)) {
            $surveys = Survey::where('surveyor_id', '=', $id)->where('survey_type', '=', 'survey')->get();
            // return Response::json($surveys);
            $response = [
                'response' => ($surveys)
                    ? 'success'
                    : 'error',
            ];

            if ($surveys) {
                foreach ($surveys as $survey) {
                    $this->getRelations($survey);
                    $survey->srf;
                    $survey->surveyFiles;

                    if ($sub != 0) {
                        $survey->submissions = RiskImprovementFormySubmissions::where(
                            'survey_id', '=',
                            (string)$survey->id
                        )->first();
                    }
                }
                $response['data'] = $surveys;
            } else {
                $response['message'] = 'No Survey could be found';
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid Surveyor ID',
            ];
        }

        return response()->json($response);

    }

    /*
     *  Destroy survey attatchment with given id
     */

    private function getRelations(&$item, $relations = [], $all = false)
    {
        if (empty($relations)) {
            $relations = [
                'organisation',
                'branch',
                'underwriter',
                'brokerUnderwriter',
                'broker',
                'externalSurveyCompany',
                'schedule',
                'policyNumber',
                'surveyor',
                'agendaFile',
                'location',
            ];
        }


        foreach ($relations as $relation) {
            // if($relation == 'underwriter') {
            //     $item->underwriter_id = $item->broker_underwriter_id;
            // }
            $item->$relation;
            if ($relation == 'policyNumber' && isset($item->policy_number->policy_type_id)) {
                $item->policy_type = PolicyType::find($item->policy_number->policy_type_id);
            }
            unset($item->{sprintf('%s_id', $relation)});
        }

        $contacts = $item->contacts;

        foreach ($contacts as $contact) {
            $key = sprintf('%s_contact', $contact->type);
            $item->$key = $contact;
            unset(
                $item->$key->type,
                $item->$key->survey_id
            );
        }
        if (!is_null($item->schedule)) {
            $item->schedule->getParsedMeta();
        }

    }

    /*
     *  Destroy survey attatchment with given id
     */

    public function destroy_legacy_survey_attatchment_empty(Request $request)
    {
        $file = LegacySurveyFiles::find($request->get('file_id'));

        if ($file) {
            $file->delete();

            return response()->json([
                'response' => 'success',
            ]);
        }

        return response()->json([
            'response' => 'error',
        ]);
    }

    /*
     *  Get survey srf
     */

    public function delete(Request $request, $id)
    {

        $survey = Survey::find($id);

        if ($request->has('auto_scheduled_surveys') && $request->get('auto_scheduled_surveys') == 'yes') {
            $delete = DeletedResurvey::create(['survey_id' => $id]);
            if ($delete) {
                return response()->json([
                    'response' => 'success',
                    'message'  => 'Deletion successful.',
                    'type'     => $survey->survey_type,
                ]);
            } else {
                return response()->json([
                    'response' => 'error',
                    'message'  => 'Could not delete.',
                ]);
            }
        }

        $submission = RiskImprovementFormySubmissions::whereIn('survey_id', [(string)$id, (int)$id])->get();
        if (is_array($submission) && count($submission) > 0) {
            return response()->json([
                'response' => 'error',
                'message'  => 'Could not delete',
            ]);
        } else {
            $delete = Survey::find($id)->delete();
            if ($delete) {
                if ($survey->external_survey_company_id != null && $survey->surveyor_id != null) {
                    $surveyor = ExternalSurveyor::find($id);
                } else {
                    $surveyor = LibertyUser::find($id);
                }

                // delete from Risk rec cards
                RiskRecommendationCards::where('survey_id', $id)->delete();

                if ($surveyor) {
                    $surveyor->organisation_name = $survey->organisation->name;
                    $surveyor->survey_id = $survey->id;
                    $surveyor->survey_type = $survey->survey_type;
                    $this->queue->queue(
                        $surveyor['email'], $surveyor->fullName(),
                        'Risk Reduce - ' . $surveyor->survey_type . ' Deleted', 'emails.surveys.deleted', $surveyor
                    );
                }
                return response()->json(
                    [
                    'response' => 'success',
                    'message' => 'deletion successful',
                    'type' => $survey->survey_type,
                    ]
                );
            } else {
                return response()->json([
                    'response' => 'error',
                    'message' => 'Could not delete',
                ]);
            }
        }

    }

    /*
     *  Get survey attatchments
     */

    public function destroy_survey_attatchment_empty(Request $request)
    {
        // get the file with the input id
        $file = SurveyFileAttachments::find($request->get('file_id'));

        if ($file) {
            $file->delete();

            return response()->json([
                'response' => 'success',
            ]);
        }

        return response()->json([
            'response' => 'error',
        ]);
    }

    /*
     *  Add new survey attatchment (empty)
     */

    public function get_survey_srf_empty($id)
    {
        $data = SurveySRF::where('survey_id', $id)->get();

        if ($data) {
            return response()->json([
                'response' => 'success',
                'data' => $data,
            ]);
        }

        return response()->json([
            'response' => 'error',
        ]);
    }

    public function get_survey_attatchment_empty($id)
    {
        $data = SurveyFileAttachments::where('survey_id', $id)->get();

        if ($data) {
            return response()->json([
                'response' => 'success',
                'data' => $data,
            ]);
        }

        return response()->json([
            'response' => 'error',
        ]);
    }


    /*
     *  Update survey attatchment (empty)
     */

    public function add_survey_attachment_empty(Request $request)
    {
        $attachment = SurveyFileAttachments::create($request->all());

        if ($attachment->id ?? 0) {
            $response = [
                'id' => $attachment->id,
            ];

            return response()->json($response);
        }
    }

    /**
     * Add duplicated attachments from resurvey
     *
     * @param Request $request
     * @return json
     */
    public function addResurveyAttachments(Request $request)
    {
        $files = $request->all();
        if (empty($files)) {
            return [
                'response' => 'error',
                'result'  => 'No data provided',
            ];
        }

        foreach ($files as $file) {
            if (empty($file['survey_id']) || empty($file['file_name']) || empty($file['cloud_file_name'])) {
                return [
                    'response' => 'error',
                    'message'  => 'Invalid attachment file provided',
                ];
            }
        }

        $response = SurveyFileAttachments::insert($files);
        if ($response) {
            return [
                'response' => 'success',
                'result'  => $response,    
            ];
        }

        return [
            'response' => 'error',
            'result'  => $response,
        ];
    }

    public function getAllSurveysLegacySrf()
    {
        return response()->json([
            'message' => 'success',
            'data' => Survey::select('id', 'legacy_srf')->whereNull('deleted_at')->get(),
        ]);
    }

    /**
     * Get All Surveys
     */
    public function all(Request $request, $page = 1, $limit = 10000, $isCron = false)
    {
        //$surveys=$this->getSurveyById(6376);

        $survey_due_dates = Survey::whereNotNull('next_survey_due_date')->get(['next_survey_due_date']);
        if (count($survey_due_dates) <= 0) {
            $this->updateSurveyWithDueDate();
        }

        // For export, we need to process it in queue
        if ($request->has('export') && !$isCron)  {
            $this->sendToQueue($request, $page, $limit);
            return response()->json([
                'response' => 'success',
            ]);
        }

        $order = $request->has('order')
            ? $request->get('order')
            : 'desc';
        $search = (urldecode($request->get('search', '')));
        $column = $request->has('col')
            ? $request->get('col')
            : null;
        $sort = $request->has('sort')
            ? $request->get('sort')
            : null;
        $month = $request->has('month')
            ? $request->get('month')
            : null;
        $year = $request->has('year')
            ? $request->get('year')
            : null;
        $filters = $request->get('filters', []);

        $surveys = Survey::query();

        $organisation_id = $request->has('organisation_id')
            ? $request->get('organisation_id')
            : null;

        if ($organisation_id != null) {

            if ($request->has('client')) {
                $surveys = $surveys->where('organisation_id', '=', $organisation_id);

            } else {
                $surveys = $surveys->where('organisation_id', '=', $organisation_id);
            }
        }

        $branch_id = $request->has('branch_id')
            ? $request->get('branch_id')
            : null;

        if ($branch_id != null) {
            $org_branches = Organisation::select('id')
                ->where('liberty_branch', $branch_id)
                ->pluck('id');
            $surveys = $surveys->whereIn('organisation_id', $org_branches);
        }

        if ($request->has('branch_type') && $request->get('branch_type') == 'aspen-user') {
            $branches = Branch::select('id')
                ->where('is_aspen', 1)
                ->pluck('id');
            $org_branches = Organisation::select('id')
                ->whereIn('liberty_branch', $branches)
                ->pluck('id');
            $surveys = $surveys->whereIn('organisation_id', $org_branches);
        }

        $mga_scheme = $request->has('mga_scheme')
            ? $request->get('mga_scheme')
            : null;

        if ($mga_scheme) {
            $mgas = MgaScheme::select('id')->where('id', $mga_scheme)->pluck('id');
            $organisations = Organisation::select('id')
                ->whereIn('mga_scheme', $mgas)
                ->pluck('id');

            $surveys = $surveys->whereIn('organisation_id', $organisations);
            $surveys = $surveys->with('organisation.mgascheme');
        }


        // auto scheduled surveys check
        if ($request->has('auto_scheduled_surveys')) {
            $excluded_survey_ids = DeletedResurvey::select('survey_id')->where('id', '>', 0)->pluck('survey_id');
            $surveyIds = RiskImprovementFormySubmissions::select('survey_id')
                ->whereNotNull('csr_next_survey_due_date')
                ->where('csr_next_survey_due_date', '!=', '')
                ->where('csr_status', 'submitted')
                ->pluck('survey_id');

            $surveys = $surveys->where(
                function ($query) use ($surveyIds) {
                    $query->whereIn('surveys.id', $surveyIds)
                        ->orWhereIn('legacy_srf', $surveyIds);
                }
            )->whereNotIn('id', $excluded_survey_ids);
        }

        if($request->has('client_side')){ /** Client Side survey all optimisation */
            return $this->clientSideSurveysAll($surveys);
        }
       

        if ($request->get('is_admin_surveys') === 'yes') {
            $surveys = $surveys->with([
                'organisation:id,mga_scheme,name,broker_id',
                'organisation.mgascheme:id,name',
                'branch:id,name',
                'schedule',
                'scheduleMeta:id,schedule_id,key,value',
                'scheduleMetas:id,schedule_id,key,value',
                'Location',
                'externalSurveyCompany',
                'surveyor',
                'externalSurveyor',
            ]);
        } else {
            $surveys = $surveys->with([
                'organisation.mgascheme',
                'branch',
                'underwriter',
                'brokerUnderwriter',
                'broker',
                'schedule',
                'scheduleMeta',
                'scheduleMetas',
                'agendaFile',
                'policyNumber.type',
                'contacts',
                'Location',
                'externalSurveyCompany',
                'surveyor',
                'externalSurveyor',
            ]);
        }

        if ($request->has('broker_org')) {
            $surveys = $surveys->where('broker_id', '=', $request->get('broker_org'));
        }

        if ($search) {
            $organisationIds = Organisation::select('id')
                ->where('name', 'LIKE', '%' . $search . '%')
                ->pluck('id');

            $externalSurveyCompanyIds = ExternalSurveyCompany::select('id')
                ->where('name', 'LIKE', '%' . $search . '%')
                ->pluck('id');

            $libertyUserIds = LibertyUser::select('id')->where('role', 'risk-engineer')
                ->where('first_name', 'LIKE', '%' . $search . '%')
                ->orWhere('last_name', 'LIKE', '%' . $search . '%')
                ->orWhereRaw('concat(first_name, \' \', last_name) LIKE ?', ['%' . $search . '%'])
                //->toSql();
                ->pluck('id');
            // dd($libertyUserIds);

            $externalSurveyorIds = ExternalSurveyor::select('id')->where('first_name', 'LIKE', '%' . $search . '%')
                ->orWhere('last_name', 'LIKE', '%' . $search . '%')
                ->orWhereRaw('concat(first_name, \' \', last_name) LIKE ?', ['%' . $search . '%'])
                ->pluck('id');

            $surveyIds = Survey::select('id')->where('id', preg_replace('/[^0-9]/', '', $search))
                ->orWhere('legacy_srf', preg_replace('/[^0-9]/', '', $search))->pluck('id');

            $branchIds = Branch::select('id')->where('name', 'LIKE', '%' . $search . '%')->pluck('id');

            $surveys = $surveys->where(
                function ($query) use (
                    $organisationIds,
                    $surveyIds,
                    $branchIds,
                    $externalSurveyCompanyIds,
                    $libertyUserIds,
                    $externalSurveyorIds,
                    $search
                ) {
                    $query->whereIn('organisation_id', $organisationIds)
                        ->orWhereIn('id', $surveyIds)
                        ->orWhereIn('branch_id', $branchIds)
                        ->orWhereIn('external_survey_company_id', $externalSurveyCompanyIds)
                        ->orWhereIn('surveyor_id', $libertyUserIds)->whereNull('external_survey_company_id')
                        ->orWhere(
                            function ($q) use ($externalSurveyorIds) {
                                $q->whereIn('surveyor_id', $externalSurveyorIds)->whereNotNull('external_survey_company_id');
                            }
                        );
                }
            );
        }

        // external surveyor check
        if ($request->has('external_survey_company_id')) {
            $surveys = $surveys->where('external_survey_company_id', $request->get('external_survey_company_id'));
        }

        // stuff
        if ($request->has('user_id') && $request->has('user_role') && $request->has('user_type') && $request->has('user_login_type')) {
            $user = $request->only(
                'user_id', 'user_role', 'user_type', 'user_login_type',
                'user_external_survey_company_id'
            );

            if ($user['user_type'] == 'external-surveyor' && $user['user_role'] == 'admin') {
                // ...
            } else {
                if ($user['user_type'] == 'external-surveyor') {
                    $externalSurveyor = ExternalSurveyor::where('id', $user['user_id'])->first();

                    $surveys = $surveys->where(
                        function ($query) use ($externalSurveyor) {
                            $query->where(
                                function ($q) use ($externalSurveyor) {
                                    $q->where('surveyor_id', $externalSurveyor->id)->whereNotNull('external_survey_company_id');
                                }
                            );
                        }
                    );
                } else {
                    $libertyUser = LibertyUser::where('id', $user['user_id'])->where(
                        function ($q) {
                            $q->where('role', 'risk-engineer')->orWhere('role', 'underwriter');
                        }
                    )->first();

                    $surveys = $surveys->where(
                        function ($query) use ($libertyUser) {
                            $query->where(
                                function ($q) use ($libertyUser) {
                                    $q->where('surveyor_id', $libertyUser->id)->orWhere('underwriter_id', $libertyUser->id);
                                }
                            )->whereNull('external_survey_company_id');
                        }
                    );
                }
            }
        }


        if ($request->has('survey_type') && $request->get('survey_type') === 'rereview') {
            $surveys = $surveys->where('survey_type', '=', 'rereview');
        } elseif ($request->get('survey_type') === 'dtr') {
            $surveys = $surveys->where('survey_type', '=', 'dtr');
        } else {
            $surveys = $surveys->where('survey_type', '=', 'survey');
        }

        if ($year != null) {
            $surveys = $surveys->whereYear('next_survey_due_date', '=', $year);
        }

        if ($month != null) {
            $surveys = $surveys->whereMonth('next_survey_due_date', '=', $month);
        }

        if (isset($filters['year']) && !in_array(trim(strtolower($filters['year'])), ['', 'all'])) {
            $surveys = $surveys->whereYear('created_at', '=', $filters['year']);
        }

        $total = $surveys->count();

        //sorting

        if ($column != '') {
            $column = $column == 'as_org'
                ? 'organisation_name'
                : 'next_survey_due_date';

        } else {
            $column = 'id';
            $sort = $order;
        }

        $surveys = $surveys->orderBy($column, $sort);

        $surveys = $surveys->take($limit)->skip(($page * $limit) - $limit)->orderBy('id', $order)->get();

        $csr_next_survey_due_dates = [];

        foreach ($surveys as $survey) {

            if ($request->has('tracker') || $request->has('client')) {
                //for survey count, it require nocache, cache data does not contain info. It could be due to data being huge.

                if ($request->has('fetchnocache')){
                    $this->getRelations($survey, []);
                } else {
                    if (Cache::has('ri_submission_relations_' . $survey->id)) {
                        $survey = Cache::get('ri_submission_relations_' . $survey->id);
                    } else {
                        $this->getRelations($survey, []);
                        Cache::put('ri_submission_relations_' . $survey->id, $survey, 3000);
                    }
                }
            }

            if (!$request->has('ff_test')) {
                if ($request->has('export')) {
                    $survey->submission = RiskImprovementFormySubmissions::where(
                        'survey_id', '=',
                        (string)$survey->id
                    )->whereIn(
                        'surveyor_id',
                        [(string)$survey->surveyor_id, $survey->surveyor_id]
                    )->first();
                } else {
                    //To override forever cache, we can comment the below line once forever cache cleared
                    $this->clearSurveySubmissionCache($survey->id);

                    if (Cache::has('ri_submission_' . $survey->id)) {
                        $survey->submission = Cache::get('ri_submission_' . $survey->id, []);
                    } else {
                        $survey->submission = RiskImprovementFormySubmissions::select(
                            '_id',
                            'csr_submission_date',
                            'csr_next_survey_due_date', 'csr_status', 'uwr_status'
                        )
                        ->where('survey_id', '=',(string)$survey->id)
                        ->whereIn('surveyor_id', [(string)$survey->surveyor_id, $survey->surveyor_id])
                        ->first();
                        Cache::forever('ri_submission_' . $survey->id, $survey->submission);
                    }
                }

                if ($request->has('auto_scheduled_surveys') && isset($survey->submission->csr_next_survey_due_date) && $survey->submission->csr_next_survey_due_date != '') {
                    try {
                        $year = Carbon::createFromFormat(
                            'd/m/Y',
                            $survey->submission->csr_next_survey_due_date
                        )->format('Y');
                        $csr_next_survey_due_dates[] = (int)$year;
                    } catch (Exception $e) {
                        //echo 'invalid date';
                    }
                    $csr_next_survey_due_dates[] = (int)$year;
                }
            }
        }

        // export surveys as excel sheet to download via S3 link in email
        if ($request->has('export')) {
            $isDtr = $request->get('survey_type') === 'dtr';

            $data = [
                [
                    'SRF',
                    'Location',
                    'Insured',
                    'Number of Risk Recs',
                    'Survey type',
                    'Surveyor',
                    'MGA scheme',
                    'Created at',
                    'Survey date',
                    'CSR submission date',
                    'UWR submission date',
                    'Next Survey Due Date',
                    'Broker Organisation',
                    'CSR approved',
                    'UWR approved',
                    'Underwriter name',
                    'Underwriter email',
                ],
            ];

            if($isDtr) {
                $data = $this->getDtrExcelHeaders();
            }

            Event::dispatch(new ExcellProcessed($data, $surveys, $request));

            return response()->json([
                'response' => 'success',
            ]);
        }


        //Overview Report

        if ($request->has('overview_report')) {
            $data = [
                [
                    'SRF',
                    'Client Org',
                    'RE/External Survey Org',
                    'Date Raised',
                    'Date Booked',
                    'Elapsed days',
                    'CSR Overdue',
                    'UWR Overdue',
                ],
            ];

            foreach ($surveys as $reportdata) {
                $srf = isset($reportdata->legacy_srf)
                    ? $reportdata->legacy_srf
                    : $reportdata->id;
                $srf = 'SRF' . $srf;

                if ($reportdata->survey_type === 'dtr') {
                    $srf = 'DTR' . $srf;
                }
                
                $clientorg = isset($reportdata->organisation->name)
                    ? $reportdata->organisation->name
                    : 'N/A';


                if (isset($reportdata->external_survey_company)) {
                    $surveyorg = $reportdata->external_survey_company->name;
                } else {
                    if (isset($reportdata->surveyor->first_name) || isset($reportdata->surveyor->last_name)) {
                        $surveyorg = (isset($reportdata->surveyor->first_name)
                                ? $reportdata->surveyor->first_name
                                : '') . ' ' . (isset($reportdata->surveyor->last_name)
                                ? $reportdata->surveyor->last_name
                                : '');
                    } else {
                        $surveyorg = 'unassigned';
                    }
                }

                $dateraised = date('Y-m-d', strtotime($reportdata->created_at));
                $datebooked = isset($reportdata->schedule_meta->value)
                    ? date('Y-m-d', strtotime($reportdata->schedule_meta->value))
                    : 'N/A';

                $surveyApiRequest = $this->show($reportdata->id);

                $surveyData = $surveyApiRequest->getData()->data;

                $csr_data = (isset($surveyData->schedule->actual_submission_deadline))
                    ? (isset($surveyData->submissions->csr_submission_date) && isset($surveyData->submissions->csr_status) && $surveyData->submissions->csr_status == 'submitted'
                        ? 'submitted'
                        : $surveyData->schedule->client_survey_report_deadline)
                    :
                    (isset($surveyData->submissions->csr_submission_date)
                        ? 'submitted'
                        : '-');

                $uwr_data = (isset($surveyData->schedule->actual_submission_deadline))
                    ? (isset($surveyData->submissions->uwr_submission_date) && isset($surveyData->submissions->uwr_status) && $surveyData->submissions->uwr_status == 'submitted'
                        ? 'submitted'
                        : $surveyData->schedule->underwriter_deadline)
                    :
                    (isset($surveyData->submissions->uwr_submission_date)
                        ? 'submitted'
                        : '-');

                if ($csr_data != '-' && $csr_data != 'submitted') {
                    $csr_data = strtotime($csr_data) > time()
                        ? 'N'
                        : 'Y';
                } else {
                    if ($csr_data == 'submitted') {
                        $csr_data = 'N';
                    } else {
                        $csr_data = '-';
                    }
                }

                if ($uwr_data != '-' && $uwr_data != 'submitted') {
                    $uwr_data = strtotime($uwr_data) > time()
                        ? 'N'
                        : 'Y';
                } else {
                    if ($uwr_data == 'submitted') {
                        $uwr_data = 'N';
                    } else {
                        $uwr_data = '-';
                    }
                }

                $elapsedDays = '-';

                if (isset($reportdata->schedule_meta->value)) {
                    try {
                        $date = Carbon::parse($reportdata->schedule_meta->value);
                        $created = Carbon::parse($reportdata->created_at);
                        $elapsedDays = $date->diffInDays($created) + 1;
                    } catch (Exception $e) {
                        //'invalid date';
                        $elapsedDays = '-';
                    }
                }

                array_push($data, [
                    $srf,
                    $clientorg,
                    $surveyorg,
                    $dateraised,
                    $datebooked,
                    $elapsedDays,
                    $csr_data,
                    $uwr_data,
                ]);
            }

            // user info for the email
            $info = [
                'first_name' => $request->get('first_name'),
                'last_name'  => $request->get('last_name'),
                'email'      => $request->get('email'),
            ];

            // generate the excel sheet for the data
            $fullPath         = Storage::url('excel/exports/' . date('d-m-Y') . '-surveys-' . uniqid() . '.xlsx');
            $excelOverviewRes = Excel::store(new SurveyOverviewsExport($data), $fullPath);
            if ($excelOverviewRes) {
                $excelKey = Str::uuid()->toString();

                // Upload the sheet to bucket
                if ($this->files->upload_excel($fullPath, 'excel/exports/' . $excelKey . '.xlsx')) {
                    $file = $this->files->link('excel/exports/' . $excelKey . '.xlsx', '2 days');

                    // Send the email to the user who requested the surveys excel sheet download
                    $this->queue->queue(
                        $info['email'], ($info['first_name'] . ' ' . $info['last_name']),
                        'Risk Reduce - Surveys Overview Excel Export Ready', 'emails.surveys.excel-overview-report', [
                            'first_name'          => $info['first_name'],
                            'last_name'           => $info['last_name'],
                            'excel_download_link' => $file,
                        ]
                    );

                    return response()->json('OK');
                }
            }
        }
        //End Overview Report


        //List All due dates
        $csr_next_survey_due_dates = [];
        $survey_due_dates = Survey::select('next_survey_due_date')
            ->whereNotNull('next_survey_due_date')
            ->get();

        foreach ($survey_due_dates as $date) {
            $year = Carbon::parse($date->next_survey_due_date)->format('Y');
            $csr_next_survey_due_dates[] = (int)$year;
        }

        $csr_next_survey_due_dates = array_unique($csr_next_survey_due_dates);
        asort($csr_next_survey_due_dates);

        //End List All due dates

        return response()->json([
            'response' => 'success',
            'data'     => $surveys,
            'dates'    => implode(", ", $csr_next_survey_due_dates),
            'total'    => $total,
        ]);
    }

    private function getDtrExcelHeaders() {
        return [
            [
                'DTR',
                'Location',
                'Insured',
                'Number of Risk Recs',
                'Report type',
                'Surveyor',
                'MGA scheme',
                'Created at',
                'Survey date',
                'DTR submission date',
                'Next Survey Due Date',
                'Broker Organisation',
                'DTR approved',
            ]
        ];
    }

    private function clientSideSurveysAll($surveys)
    {
        $surveys = $surveys->with([
            'schedule',
            'scheduleMeta',
            'scheduleMetas',
            'contacts',
            'Location',
        ]);
        
        $surveys = $surveys->where('survey_type', '=', 'survey');
        $total = $surveys->count();
        $surveys = $surveys->orderBy('id', 'desc');

        $surveys = $surveys->get();
        
        return response()->json([
            'response' => 'success',
            'data'     => $surveys,
            'total'    => $total,
        ]);
    }
    public function updateSurveyWithDueDate($id = 0)
    {
        if ($id > 0) {
            $surveys = Survey::where('id', $id)->get();
        } else {
            $surveys = Survey::where('surveyor_id', '>', 0)->get();
        }

        foreach ($surveys as $key => $survey) {
            $submission = RiskImprovementFormySubmissions::select('csr_next_survey_due_date')->where(
                'survey_id', '=',
                (string)$survey->id
            )->where('surveyor_id', '=', (string)$survey->surveyor_id)->first();
            if (isset($submission->csr_next_survey_due_date) && $submission->csr_next_survey_due_date != '') {
                try {
                    $date = Carbon::createFromFormat('d/m/Y', $submission->csr_next_survey_due_date)->format('Y-m-d');
                    $survey->next_survey_due_date = $date;
                } catch (Exception $e) {
                    //'invalid date';
                    $survey->next_survey_due_date = null;
                }
                $survey->organisation_name = Organisation::find($survey->organisation_id)->name;
                $survey->save();
            }
        }
    }

    /**
     * Store new Survey
     */

    public function store(Request $request)
    {
        $data = $request->all();
        $validator = Validator::make(
            $data,
            static::getValidationRules($data, 'store')
        );

        if ($validator->fails()) {
            $response = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        } else {

            $survey = Survey::create([
                'organisation_id'   => $request->get('organisation_id'),
                'branch_id'         => $request->get('branch_id'),
                'underwriter_id'    => $request->has('underwriter_id')
                    ? $request->get('underwriter_id')
                    : 0,
                'broker_underwriter_id'      => $request->get('broker_underwriter_id'),
                'broker_id'                  => $request->get('broker_id'),
                'surveyor_id'                => $request->get('surveyor_id', null),
                'survey_form'                => $request->get('survey_form'),
                'external_survey_company_id' => $request->get('external_survey_company_id', null),
                'visit_arrangement'          => $request->get('visit_arrangement'),
                'special_instructions'       => $request->get('special_instructions'),
                'policy_id'                  => $request->get('policy_id'),
                'additional_info'            => $request->get('additional_info'),
                'survey_type'                => $request->get('survey_type'),
                'legacy_srf'                 => $request->get('legacy_srf') == ''
                    ? null
                    : $request->get('legacy_srf'),
                'resurvey_id' => $request->has('resurvey_id')
                    ? ($request->get('resurvey_id') == ''
                        ? null
                        : $request->get('resurvey_id'))
                    : null,
                'notify_underwriter' => ($request->has('notify_underwriter') && $request->get('notify_underwriter') == 1)
                    ? 1
                    : 0,
                'location_id' => $request->has('location')
                    ? $request->get('location')
                    : $request->get('location_id'),
                'schedule_id' => 0,
                'risk_grading_types' => empty($request->get('policy_id')) ? $request->get('risk_grading_types', null) : null,
            ]);

            if ($request->has('srf_key')) {
                $keys = $request->get('srf_key');
                $values = $request->get('srf_value');
                $types = $request->get('srf_type');

                foreach ($keys as $key => $value) {
                    if (isset($keys[$key]) && isset($types[$key]) && !empty($keys[$key])) {
                        $srf = SurveySRF::create(
                            [
                            'survey_id' => $survey->id,
                            'srf_key' => $keys[$key],
                            'srf_value' => (isset($values[$key]))
                                ? str_replace([' ', ',', '£'], '', $values[$key])
                                : '0',
                            'srf_type' => $types[$key],
                            ]
                        );
                    }
                }
            }

            static::updateRelations($request, $survey);

            if ($survey->id) {
                if ($request->get('survey_type') == "rereview") {
                    $user = LibertyUser::where('email', 'LIKE', config('app.risk_control'))->first();
                    $user->survey_id = $survey->id;
                    $this->queue->queue(
                        $user['email'], $user->fullName(), 'Risk Reduce - New RE Review Requested',
                        'emails.surveys.re_review_requested', $user
                    );
                }

                $response = [
                    'response' => 'success',
                    'message' => 'The Survey has been created successfully',
                    'survey_id' => $survey->id,
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The Survey has failed to be created',
                ];
            }
        }

        return response()->json($response);
    }

    private static function getValidationRules($data, $method)
    {
        $rules = [
            'organisation_id'            => 'required|integer',
            'branch_id'                  => 'required|integer',
            'underwriter_id'             => 'required|integer',
            'broker_id'                  => 'required|integer',
            'surveyor_id'                => 'integer',
            'external_survey_company_id' => 'integer',
            'visit_arrangement'          => 'required|string',
            'policy_id'                  => 'required|integer',
        ];

        if (($data['survey_type'] ?? '') === 'dtr' && in_array($method, ['store', 'update']) ) {
            unset($rules['policy_id']);
            unset($rules['underwriter_id']);
            unset($rules['visit_arrangement']);
            $rules['location'] = 'required|integer';
        }

        switch ($method) {
        case 'update':
            return array_intersect_key($rules, Arr::dot($data));
        default:
            return $rules;
        }
    }

    private static function updateRelations(Request $request, &$survey)
    {
        $contacts = array_fill_keys(['client', 'broker'], null);

        foreach ($contacts as $contact_type => $contact_values) {
            if ($request->has($contact_type)) {
                $contacts[$contact_type] = SurveyContact::firstOrNew(
                    [
                    'survey_id' => $survey->id,
                    'type' => $contact_type,
                    ]
                );

                // fail safe: this will maintain the current implementation (legacy functions)
                if ($request->get($contact_type . '.name', null)) {
                    $contacts[$contact_type]->name = $request->get($contact_type . '.name');
                    $contacts[$contact_type]->address_1 = $request->get($contact_type . '.address_1');
                    $contacts[$contact_type]->address_2 = $request->get($contact_type . '.address_2', null);
                    $contacts[$contact_type]->postcode = $request->get($contact_type . '.postcode', null);
                    $contacts[$contact_type]->city = $request->get($contact_type . '.city', null);
                    $contacts[$contact_type]->country = $request->get($contact_type . '.country', null);
                    $contacts[$contact_type]->email = $request->get($contact_type . '.email', null);
                    $contacts[$contact_type]->phone = $request->get($contact_type . '.phone', null);
                    $contacts[$contact_type]->save();
                } else {
                    $surveyContact =  $request->all();
                    $contacts[$contact_type]->name = $surveyContact[$contact_type]['name'] ?? null;
                    $contacts[$contact_type]->address_1 = $surveyContact[$contact_type]['address_1'] ?? null;
                    $contacts[$contact_type]->address_2 = $surveyContact[$contact_type]['address_2'] ?? null;
                    $contacts[$contact_type]->postcode = $surveyContact[$contact_type]['postcode'] ?? null;
                    $contacts[$contact_type]->city =  $surveyContact[$contact_type]['city'] ?? null;
                    $contacts[$contact_type]->country = $surveyContact[$contact_type]['country'] ?? null;
                    $contacts[$contact_type]->email =  $surveyContact[$contact_type]['email'] ?? null;
                    $contacts[$contact_type]->phone = $surveyContact[$contact_type]['phone'] ?? null;
                    $contacts[$contact_type]->save();
                }
            }
        }

        // Update or create the associated schedule
        if ($request->has('deadlines')) {
            $deadlines      = $request->get('deadlines');
            $schedule       = $survey->schedule ?: new Schedule;
            $schedule->type = 'survey';

            if (array_key_exists('survey', $deadlines)) {
                $schedule->start = date('Y-m-d 00:00:00', strtotime($deadlines['survey']));
                $schedule->end = date('Y-m-d 23:59:59', strtotime($deadlines['survey']));
            }

            $schedule->save();
            $survey->schedule_id = $schedule->id;
            $survey->save();

            $schedule_meta = [
                'survey_id' => $survey->id,
            ];

            foreach ($request->get('deadlines') as $key => $value) {
                if ($key !== 'survey') {
                    if ($request->has('survey_type') && $request->get('survey_type') == 'rereview') {
                        if ($key == 'underwriter' || $key == 'client_survey_report') {

                        } else {
                            if ($key !== 'survey') {
                                $schedule_meta[sprintf('%s_deadline', $key)] = $value;
                            }
                        }

                    } else {
                        if ($key !== 'survey') {
                            if ($request->has('survey_type') && $request->get('survey_type') == 'survey' && $key != 're_review') {
                                $schedule_meta[sprintf('%s_deadline', $key)] = $value;
                            }
                            if (!$request->has('survey_type')) {
                                $schedule_meta[sprintf('%s_deadline', $key)] = $value;
                            }
                        }
                    }
                }
            }

            $schedule->saveMeta($schedule_meta);
        }

        $submission = RiskImprovementFormySubmissions::where('survey_id', (string)$survey->id)->first();
        $ri_submissions = RiskImprovementFormySubmissions::where('survey_id', (string)$survey->resurvey_id)->get();
        if (!$submission && $ri_submissions) {

            foreach ($ri_submissions as $ri_submission) {
                if ($ri_submission) {
                    $new_ri_submission = $ri_submission->replicate();

                    $new_ri_submission->unset('csr_submission_date');
                    $new_ri_submission->unset('uwr_submission_date');
                    $new_ri_submission->surveyor_id = (string)$survey->surveyor_id;
                    $new_ri_submission->survey_id = (string)$survey->id;
                    $new_ri_submission->csr_submission_date = '';
                    $new_ri_submission->uwr_submission_date = '';
                    $new_ri_submission->uwr_status = '';
                    $new_ri_submission->csr_status = '';
                    $new_ri_submission->save();

                    $newRiSubmissionId = $new_ri_submission->_id;

                    // Copy the cards of the resurvey
                    Artisan::call('riskrec:cards ' . $newRiSubmissionId);

                    // get the ri prefix
                    $fields = $ri_submission->toArray();
                    $re = '/(risk-recommendations-\d*)_1_ref/m';
                    $field_prefix = null;
                    foreach ($fields as $field => $value) {
                        preg_match_all($re, $field, $matches, PREG_SET_ORDER, 0);
                        if (!empty($matches)) {
                            $field_prefix = $matches[0][1];
                            break;
                        }
                    }

                    if ($field_prefix) {
                        for ($i = 1; $i <= 15; $i++) {
                            $field = $field_prefix . "_{$i}_classification";

                            if (!empty($ri_submission->$field)) {
                                // create new fields to close the issue
                                $ri_submission->{$field_prefix . "_{$i}_issue_closed"} = "1";
                            }
                        }
                    }

                    $ri_submission->save();
                }
            }
        } elseif ($submission) {
            $submission->surveyor_id = (string)$survey->surveyor_id;
            $submission->save();
        }
    }

    /**
     * Find Survey
     */
    public function show($id)
    {
        $surveyId = (int)$id;
        if (!empty($surveyId)) {
            $survey = Survey::with('location', 'policyNumber.type', 'surveyFiles')->find($id);

            if (!$survey) {
                $survey = Survey::with('location')->where('id', '=', $id)->first();
            }

            if ($survey) {
                $srf = SurveySRF::where('survey_id', $survey->id)->get();

                $r = [];

                foreach ($srf as $s) {
                    if (!isset($r[$s->srf_type])) {
                        $r[$s->srf_type] = [];
                    }

                    $arr = [$s->srf_key, $s->srf_value];

                    array_push($r[$s->srf_type], $arr);
                }

                $response = [
                    'response' => ($survey)
                        ? 'success'
                        : 'error',
                ];

                $submissions = RiskImprovementFormySubmissions::where('survey_id',(string)$surveyId)->where('surveyor_id',(string)$survey->surveyor_id)->first();

                $legacy_submissions = LegacyRiskImprovementFormySubmissions::whereIn(
                    'survey_id',
                    [(string)$id, $id]
                )->first();

                $this->getRelations($survey);
                $response['data'] = $survey;

                if (empty($survey->broker_contact) && !empty($survey->organisation->broker)) {
                    $survey->broker_contact = $survey->organisation->broker;
                }

                $riskEngineers = !is_null($survey->organisation) && !is_null($survey->organisation->orgRiskEngineer) && !empty($survey->organisation->orgRiskEngineer->count())
                    ? $survey->organisation->orgRiskEngineer
                    : [];

                foreach ($riskEngineers as $re) {
                    if (isset($re->libertyUser->email)) {
                        $response['risk_engineers'][] = [
                            'email' => $re->libertyUser->email,
                        ];
                    }
                }

                $surveyCommentaries = SurveyCommentaryInfo::whereIn('survey_id', [(string)$surveyId, $surveyId])->first();

                $response['data']['commentaries'] = $surveyCommentaries;
                $response['data']['submissions'] = $submissions;
                $response['data']['legacy_submissions'] = $legacy_submissions;
                $response['data']['srf'] = $r;
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The specified Survey could not be found',
                    'data' => null,
                ];
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid Survey ID',
            ];
        }
        return response()->json($response);
    }

    public function survey_agenda(Request $request)
    {
        $agenda_file = SurveyAgendaFile::firstOrNew(['survey_id' => $request->get('survey_id')]);
        $agenda_file->file_name = $request->get('file_name');
        $agenda_file->cloud_file_name = $request->get('cloud_file_name');

        if ($agenda_file->save()) {
            $response = [
                'id' => $agenda_file->cloud_file_name,
                'file_name' => $agenda_file->file_name,
            ];

            return response()->json($response);
        }
    }

    public function update_survey_attachment_empty(Request $request)
    {
        if ($request->get('sessions')) {
            $sessions = $request->get('sessions');

            for ($i = 0; $i < count($sessions); $i++) {
                SurveyFileAttachments::where('id', $sessions[$i])->update(['survey_id' => $request->get('survey_id')]);

                Session::forget('sessions.' . $i);
            }

            return response()->json(['success' => false]);
        }
    }

    /**
     * Update Survey
     */
    public function update(Request $request, $id)
    {

        $data = $request->all();
        

        if ($id && is_numeric($id)) {
            $validator = Validator::make(
                $data,
                static::getValidationRules($data, 'update')
            );

            if ($validator->fails()) {
                $response = [
                    'response' => 'error',
                    'errors' => $validator->messages()->toArray(),
                ];
            } else {
                // srf stuff here
                if ($request->has('srf_key')) {
                    SurveySRF::where('survey_id', $id)->delete();

                    $keys = $request->get('srf_key');
                    $values = $request->get('srf_value');
                    $types = $request->get('srf_type');

                    foreach ($keys as $key => $value) {
                        if (isset($keys[$key]) && isset($types[$key]) && !empty($keys[$key])) {

                            $srf = SurveySRF::create([
                                'survey_id' => $id,
                                'srf_key'   => $keys[$key],
                                'srf_value' => (isset($values[$key]))
                                    ? (float)str_replace([' ', ',', '£'], '', $values[$key])
                                    : '0',
                                'srf_type' => $types[$key],
                            ]);
                        }
                    }
                }

                $survey = Survey::find($id);
                $input = $request->all();
                if ($survey->id) {
                    foreach ($survey->getFillable() as $attr) {
                        if ($request->has($attr)) {
                            $survey->$attr = $request->get($attr)
                                ? $request->get($attr)
                                : (in_array($attr, $survey->getNullable())
                                    ? null
                                    : '');
                            // echo $survey->$attr;
                            // echo "<br>";
                            if ($attr == "csr_qa_status" && $request->get($attr) == '0' && $survey->csr_qa_status != 0) {
                                $ri_submission = RiskImprovementFormySubmissions::where(
                                    'survey_id', '=',
                                    (string)$id
                                )->update(['csr_status' => 'completed', 'csr_submission_date' => '']);
                            }

                            if ($attr == "uwr_qa_status" && $request->get($attr) == '0' && $survey->uwr_qa_status != 0) {
                                $ri_submission = RiskImprovementFormySubmissions::where(
                                    'survey_id', '=',
                                    (string)$id
                                )->update(['uwr_status' => 'completed', 'uwr_submission_date' => '']);
                            }

                            if ($attr == "csr_qa_status" && $request->get($attr) == '1') {
                                $user = LibertyUser::where('id', '=', $survey->re_id)->first();
                                if ($user) {
                                    $user->survey_id = $survey->id;
                                    $this->queue->queue(
                                        $user['email'], $user->fullName(),
                                        sprintf('Risk Reduce - New %s Assigned for QA', $survey?->survey_type === 'dtr' ? 'Report' : 'Survey'),
                                        'emails.surveys.send_to_qa', $user
                                    );
                                }
                            }

                            if ($attr == "uwr_qa_status" && $request->get($attr) == '1') {
                                $user = LibertyUser::where('id', '=', $survey->re_id)->first();
                                if ($user) {
                                    $user->survey_id = $survey->id;
                                    $this->queue->queue(
                                        $user['email'], $user->fullName(),
                                        sprintf('Risk Reduce - New %s Assigned for QA', $survey?->survey_type === 'dtr' ? 'Report' : 'Survey'),
                                        'emails.surveys.send_to_qa', $user
                                    );
                                }
                            }

                            if ($attr == "csr_uw_status" && $request->get($attr) == '1') {
                                if (is_null($survey->broker_underwriter_id)) {
                                    $user = LibertyUser::where('id', '=', $survey->underwriter_id)->first();
                                    if ($user) {
                                        $user->survey_id = $survey->id;
                                        $this->queue->queue(
                                            $user['email'], $user->fullName(),
                                            sprintf('Risk Reduce - New %s Assigned for approval', $survey?->survey_type === 'dtr' ? 'Report' : 'Survey'),
                                            'emails.surveys.send_to_uw', $user
                                        );
                                    }
                                } else {

                                    $user = BrokerUser::where('id', '=', $survey->broker_underwriter_id)->first();
                                    if ($user && !$survey->isDtr()) {
                                        $user->survey_id = $survey->id;
                                        $user->userType = 'broker-user';
                                        $this->queue->queue(
                                            $user['email'], $user->fullName(),
                                            sprintf('Risk Reduce - New %s Assigned for approval', $survey?->survey_type === 'dtr' ? 'Report' : 'Survey'),
                                            'emails.surveys.send_to_uw', $user
                                        );
                                    }

                                }
                            }

                            if ($attr == "csr_uw_status" && $request->get($attr) == '2') {

                                $broker_set = ['organisation', 'broker', 'location', 'contactsBroker'];

                                $brokers = Survey::with($broker_set)
                                    ->where('id', $survey->id)
                                    ->first();

                                $organisation_broker = Organisation::with('broker')
                                    ->where('id', $brokers->organisation_id)
                                    ->first();

                                $survey_broker = $brokers->contactsBroker;

                                $email = '';

                                $organisation_name = $organisation_broker->name ?? '';
                                $organisation_location = $brokers->Location->location_name ?? '';

                                // Broker Organisation Level Notification
                                if (!empty($organisation_broker)
                                    && isset($organisation_broker->broker->email)
                                    && !empty($organisation_broker->broker->email)
                                    && !$survey->isDtr()
                                ) {
                                    $email = $organisation_broker->broker->email;
                                    $first_name = $organisation_broker->broker->name;
                                    $last_name = '';
                                    $this->notify(
                                        $email, $first_name, $last_name, $survey->id, $organisation_name,
                                        $organisation_location, $survey
                                    );
                                }

                                // Broker Organisation Level Notification
                                $orgBrokerContact = $organisation_broker?->assign_brokers?->first()?->brokerUser;
                                if (!$organisation_broker?->broker?->email && $orgBrokerContact?->email && !$survey->isDtr()) {
                                    $email = $orgBrokerContact->email;
                                    $first_name = $orgBrokerContact->first_name;
                                    $last_name = $orgBrokerContact->last_name;
                                    $this->notify(
                                        $email, $first_name, $last_name, $survey->id, $organisation_name,
                                        $organisation_location, $survey
                                    );
                                }

                                // Broker SRF Level Notification
                                if (isset($survey_broker[0]->email)
                                    && !empty($survey_broker[0]->email)
                                    && count($survey_broker) > 0
                                    && ($email != $survey_broker[0]->email)
                                    && !$survey->isDtr()
                                ) {
                                    $email = $survey_broker[0]->email;
                                    $first_name = $survey_broker[0]->name;
                                    $last_name = '';
                                    $this->notify(
                                        $email, $first_name, $last_name, $survey->id, $organisation_name,
                                        $organisation_location, $survey
                                    );
                                }

                                // $users = User::where('organisation_id', '=', $survey->organisation_id)->where('manager', '=', 1, 'AND')->get();
                                // if($users) {
                                //     foreach($users as $user) {
                                //         $user->survey_id = $survey->id;
                                //         $this->queue->queue($user['email'], $user->fullName(),'Risk Reduce - CSR Approved', 'emails.surveys.approved_by_uw',$user);
                                //     }
                                // } else {
                                if (!is_null($survey->broker_underwriter_id) && !$survey->isDtr()) {
                                    $user = BrokerUser::where('id', '=', $survey->broker_underwriter_id)->first();
                                    if ($user) {
                                        $user->survey_id = $survey->id;
                                        $user->userType = 'broker-user';
                                        $this->queue->queue(
                                            $user['email'], $user->fullName(),
                                            sprintf('Risk Reduce - New %s Assigned for approval', $survey?->survey_type === 'dtr' ? 'Report' : 'Survey'),
                                            'emails.surveys.send_to_uw', $user
                                        );
                                    }
                                }
                                //}

                                $riSubmission = RiskImprovementFormySubmissions::select('csr_external_emails')
                                    ->where('survey_id', (string)$survey->id)
                                    ->first();
                                if (!empty($riSubmission)) {
                                    $externalEmails  = [];
                                    if (!empty($riSubmission->csr_external_emails)) {
                                        if (is_string($riSubmission->csr_external_emails)) {
                                            $externalEmails = json_decode($riSubmission->csr_external_emails);
                                        } else {
                                            $externalEmails = $riSubmission->csr_external_emails;
                                        }
                                    }

                                    foreach ($externalEmails as $email) {
                                        $trimEmail = trim($email);
                                        $rc = LibertyUser::where('email', $trimEmail)->first();
                                        if (!empty($rc)) {
                                            $rc['survey_id'] = $survey->id;
                                            $this->queue->queue(
                                                $rc['email'],
                                                $rc->fullName(),
                                                sprintf('Risk Reduce - New %s Submitted', $survey?->isDtr() ? 'Desktop Report' : 'CSR'),
                                                'emails.surveys.csr_submitted',
                                                $rc
                                            );
                                        } else { // For csr external users
                                            $otp            = $this->generateOtp($trimEmail);
                                            $otpToken       = encrypt($otp->token);
                                            $emailEnc       = encrypt($trimEmail);
                                            $surveyTypeExt  = $survey?->isDtr() ? ' Desktop Report' : ' Survey Report';
                                            $typeTitle      = $survey?->isDtr() ? ' DTR' . $survey->id : ' CSR' . $survey->id;
                                            $location       = !empty($organisation_location) ? ' - ' . $organisation_location : '';
                                            $subjectTitle   = 'Risk Reduce: ' . $organisation_name . $location . $typeTitle . $surveyTypeExt;

                                            $this->queue->queue(
                                                $trimEmail,
                                                $subjectTitle,
                                                $subjectTitle,
                                                'emails.surveys.external_csr_submitted',
                                                [
                                                    'enc_email'    => $emailEnc,
                                                    'orgId'        => $survey->organisation_id,
                                                    'orgName'      => $organisation_name,
                                                    'survey_id'    => $survey->id,
                                                    'city'         => $location,
                                                    'token'        => $otpToken,
                                                    'contentTitle' => $subjectTitle,
                                                    'isDtr'        => $survey->isDtr(),
                                                ]
                                            );
                                        }
                                    }
                                }
                            }

                            if ($attr == "uwr_uw_status" && $request->get($attr) == '1' && !$survey->isDtr()) {
                                if (is_null($survey->broker_underwriter_id)) {
                                    $user = LibertyUser::where('id', '=', $survey->underwriter_id)->first();
                                    if ($user) {
                                        $user->survey_id = $survey->id;
                                        $this->queue->queue(
                                            $user['email'], $user->fullName(),
                                            sprintf('Risk Reduce - New %s Assigned for approval', $survey?->survey_type === 'dtr' ? 'Report' : 'Survey'),
                                            'emails.surveys.send_to_uw', $user
                                        );
                                    }
                                } else {
                                    $user = BrokerUser::where('id', '=', $survey->broker_underwriter_id)->first();

                                    if ($user) {
                                        $user->survey_id = $survey->id;
                                        $user->userType = 'broker-user';
                                        $this->queue->queue(
                                            $user['email'], $user->fullName(),
                                            sprintf('Risk Reduce - New %s Assigned for approval', $survey?->survey_type === 'dtr' ? 'Report' : 'Survey'),
                                            'emails.surveys.send_to_uw', $user
                                        );
                                    }
                                }
                            }
                            // if($attr == 'external_survey_company_id') {
                            //     $notify_surveyor = '1';
                            //     $notify_to = 'external_survey_company_admin';
                            // }
                            // if($attr == 'surveyor_id' && (($survey->external_survey_company_id == '' || (is_null($survey->external_survey_company_id))) || ($request->has('external_survey_company_id') && is_null($request->get('external_survey_company_id'))) ))
                            // {
                            //     $notify_surveyor = '1';
                            //     $notify_to = 'risk_engineer';
                            // }
                            // if($attr == 'surveyor_id' && (($survey->external_survey_company_id != '' && !is_null($survey->external_survey_company_id)) || ($request->has('external_survey_company_id') && !is_null($request->get('external_survey_company_id'))) ) )
                            // {
                            //     $notify_surveyor = '1';
                            //     $notify_to = 'external_surveyor';
                            // }

                        }
                    }

                    $orgRiskEngineers = !empty($input['org_risk_engineers'])
                        ? LibertyUser::whereIn('email', $input['org_risk_engineers'])->get()
                        : [];

                    if ($survey->external_survey_company_id && $survey->surveyor_id) {
                        $surveyor = ExternalSurveyor::find($survey->surveyor_id);
                    } else {
                        $surveyor = LibertyUser::find($survey->surveyor_id);
                    }

                    $locationName = $survey->location->location_name ?? $survey->location->city;
                    if (isset($input['notify'])) {
                        if ($input['notify'] == 'risk_engineer') {
                            $flag = false;

                            foreach ($orgRiskEngineers as $riskEngineer) {
                                if ($riskEngineer->id == $input['surveyor_id']) {
                                    $flag = true;
                                }

                                $riskEngineer->survey_id = $survey->id;
                                $riskEngineer->is_risk_engineer = true;
                                $riskEngineer->surveyor_name = $surveyor?->fullName();
                                $riskEngineer->location_name = $locationName;
                                $this->queue->queue(
                                    $riskEngineer->email, $riskEngineer->fullName(),
                                    sprintf('Risk Reduce - New %s Raised', $survey?->survey_type === 'dtr' ? 'Desktop Report' : 'Survey'),
                                    'emails.surveys.assigned_to_surveyor',
                                    $riskEngineer
                                );
                            }

                            if (!$flag) { // If not found in above foreach iteration send email for the pick surveyor
                                $user = LibertyUser::where('id', '=', $input['surveyor_id'])->first();
                                $user->survey_id = $survey->id;
                                $user->location_name = $locationName;
                                $this->queue->queue(
                                    $user['email'], $user->fullName(),
                                    sprintf('Risk Reduce - New %s Assigned', $survey?->survey_type === 'dtr' ? 'Desktop Report' : 'Survey'),
                                    'emails.surveys.assigned_to_surveyor', $user
                                );
                            }
                        }

                        if ($input['notify'] == 'external_surveyor') {
                            $user = ExternalSurveyor::where('id', '=', $input['surveyor_id'])->first();
                            $user->survey_id = $survey->id;
                            $user->location_name = $locationName;
                            $this->queue->queue(
                                $user['email'], $user->fullName(),
                                sprintf('Risk Reduce - New %s Assigned', $survey?->survey_type === 'dtr' ? 'Desktop Report' : 'Survey'),
                                'emails.surveys.assigned_to_surveyor', $user
                            );

                            foreach ($orgRiskEngineers as $riskEngineer) {
                                $riskEngineer->survey_id = $survey->id;
                                $riskEngineer->is_risk_engineer = true;
                                $riskEngineer->surveyor_name = $surveyor?->fullName();
                                $riskEngineer->location_name = $locationName;
                                $this->queue->queue(
                                    $riskEngineer->email, $riskEngineer->fullName(),
                                    sprintf('Risk Reduce - New %s Raised', $survey?->survey_type === 'dtr' ? 'Desktop Report' : 'Survey'),
                                    'emails.surveys.assigned_to_surveyor',
                                    $riskEngineer
                                );
                            }
                        }

                        if ($input['notify'] == 'external_survey_company_admin') {
                            $users = ExternalSurveyor::where(
                                'external_survey_company_id', '=',
                                $input['external_survey_company_id']
                            )->where('role', '=', 'admin', 'AND')->get();

                            foreach ($users as $user) {
                                $user->survey_id = $survey->id;
                                $user->location_name = $locationName;
                                $this->queue->queue(
                                    $user['email'], $user->fullName(),
                                    sprintf('Risk Reduce - New %s Assigned', $survey?->survey_type === 'dtr' ? 'Desktop Report' : 'Survey'),
                                    'emails.surveys.assigned_to_surveyor', $user
                                );
                            }
                        }
                    }

                    if (!isset($input["legacy_srf"]) && is_null($survey->legacy_srf)) {
                        $survey->legacy_srf = null;
                    }
                    if (isset($input["legacy_srf"]) && $input["legacy_srf"] == '') {
                        $survey->legacy_srf = null;
                    }

                    //dd($survey);

                    static::updateRelations($request, $survey);


                    // print_r($survey);
                    if (isset($input['external_survey_company_id']) && $input['external_survey_company_id'] == '') {
                        $survey->external_survey_company_id = null;
                        //dd('gere');
                    }

                    if (isset($input['external_survey_company_id']) || isset($input['surveyor_id'])) {
                        $survey->surveyor_id = empty($input['surveyor_id']) ? null : $input['surveyor_id'];
                    }

                    if (isset($input['location']) && !empty($input['location'])) {
                        $survey->location_id = $input['location'];
                    }

                    $survey['uwr_qa_status'] = $survey['uwr_qa_status'] == '' ? 0 : $survey['uwr_qa_status'];
                    $survey['uwr_uw_status'] = $survey['uwr_uw_status'] == '' ? 0 : $survey['uwr_uw_status'];
                    $survey['csr_uw_status'] = $survey['csr_uw_status'] == '' ? 0 : $survey['csr_uw_status'];
                    $survey['csr_qa_status'] = $survey['csr_qa_status'] == '' ? 0 : $survey['csr_qa_status'];

                    $survey->save();
                    $this->updateSurveyWithDueDate($survey->id);
                    $this->clearSurveySubmissionCache($survey->id);

                    $response = [
                        'response' => 'success',
                        'message' => 'The Survey has been updated successfully',
                    ];
                } else {
                    $response = [
                        'response' => 'error',
                        'message' => 'Invalid Survey',
                    ];
                }
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid Survey ID',
            ];
        }

        return response()->json($response);
    }

    private function generateOtp($email)
    {
        $otp = new Otp();
        $generatedOtp = $otp->generate($email, 6, 15);
        return $generatedOtp;
    }

    private function notify(
        $email,
        $first_name,
        $last_name,
        $survey_id,
        $organisation_name = '',
        $organisation_location = '',
        $survey = null
    ) {
        $viewParameters['name'] = trim($first_name . ' ' . $last_name);
        $viewParameters['survey_id'] = $survey_id;
        $viewParameters['organisation_name'] = $organisation_name;
        $viewParameters['organisation_location'] = $organisation_location;

        $this->queue->queue(
            $email,
            trim($first_name . ' ' . $last_name),
            sprintf('Risk Reduce - New %s Report', $survey?->survey_type === 'dtr' ? 'Desktop' : 'Survey'),
            'emails.surveys.send_to_broker_approved',
            $viewParameters,
            null
        );
    }

    /**
     * Add new attachment
     */
    public function legacy_add_attachment(Request $request)
    {
        $input = $request->all();
        $id = LegacySurveyFiles::insertGetId($input);
        if ($id) {
            $response = [
                'response' => 'success',
                'message'  => 'File saved.',
                'id'       => $id,
            ];
            return response()->json($response);
        }
    }

    /**
     * Add new attachment
     */
    public function add_attachment(Request $request)
    {
        $input = $request->all();

        if (empty($input['created_at'])) {
            $input['created_at'] = date('Y-m-d H:i:s');
            $input['updated_at'] = date('Y-m-d H:i:s');
        }

        $id = Surveyfiles::insertGetId($input);
        if ($id) {
            $response = [
                'response' => 'success',
                'message'  => 'File saved.',
                'id'       => $id,
            ];
            return response()->json($response);
        }
    }

    /**
     * Get survey attachment
     */

    public function legacy_get_attachment($field_name, $survey_id)
    {
        $surveys = LegacySurveyFiles::where('field_name', '=', '_' . $field_name)->whereIn(
            'survey_id',
            [(int)$survey_id, (string)$survey_id]
        )->where('deleted', '=', 0)->with('survey')->get();


        if ($surveys) {
            foreach ($surveys as $survey) {
                $submission = LegacyRiskImprovementFormySubmissions::whereIn(
                    'survey_id',
                    [$survey->survey_id, (string)$survey->survey_id]
                )->first();
                // print_r($submission->notes); exit;
                $notes = $survey->notes;
                if ($submission && isset($submission->notes)) {
                    $notes_array = $submission->notes;
                    $survey->notes = isset(
                        $notes_array[strtolower(
                            str_replace(
                                ['.', ' '], ['_', '_'],
                                $survey->file_name
                            )
                        )]
                    )
                        ? $notes_array[strtolower(str_replace(['.', ' '], ['_', '_'], $survey->file_name))]
                        : $notes;

                }
                if (isset($submission->annotations)) {
                    $annotations_array = $submission->annotations;
                    $survey->annotations = isset(
                        $annotations_array[strtolower(
                            str_replace(
                                ['.', ' '], ['_', '_'],
                                $survey->file_name
                            )
                        )]
                    )
                        ? $annotations_array[strtolower(str_replace(['.', ' '], ['_', '_'], $survey->file_name))]
                        : [];
                } else {
                    $survey->annotations = [];
                }
                if (isset($submission->geolocation)) {
                    $geolocation_array = $submission->geolocation;
                    $survey->geolocation = isset(
                        $geolocation_array[strtolower(
                            str_replace(
                                ['.', ' '], ['_', '_'],
                                $survey->file_name
                            )
                        )]
                    )
                        ? $geolocation_array[strtolower(str_replace(['.', ' '], ['_', '_'], $survey->file_name))]
                        : [];
                } else {
                    $survey->geolocation = [];
                }
            }
            $response = [
                'response' => 'success',
                'data' => $surveys,
            ];
            return response()->json($response);
        }
    }

    /**
     * Return all the risk recommendations attachment
     *
     * @param int $surveyId
     * @return Illuminate\Http\JsonResponse
     */
    public function getAttachmentForRiskRec($surveyId)
    {
        $surveyId    = (int)$surveyId;
        $surveyFiles = Surveyfiles::where('survey_id', $surveyId)
            ->where('field_name', 'NOT LIKE', 'photographs-%')
            ->where('deleted', 0)
            ->get();

        return response()->json([
            'response' => 'success',
            'data'     => $surveyFiles,
        ]);
    }

    /**
     * Get survey attachment
     */
    public function get_attachment($field_name, $survey_id)
    {
        $surveys = Surveyfiles::where('field_name', $field_name)
            ->where('survey_id', (int)$survey_id)
            ->where('deleted', '=', 0)
            ->with('survey')
            ->get();

        if ($surveys) {
            foreach ($surveys as $survey) {
                $submission = RiskImprovementFormySubmissions::where(
                    'form_id', '=',
                    $survey->survey->survey_form
                )->whereIn(
                    'survey_id',
                    [$survey->survey_id, (string)$survey->survey_id]
                )->first();
                // print_r($submission->notes); exit;
                $notes = $survey->notes;
                if ($submission && isset($submission->notes)) {
                    $notes_array = $submission->notes;
                    $survey->notes = isset(
                        $notes_array[strtolower(
                            str_replace(
                                ['.', ' '], ['_', '_'],
                                $survey->file_name
                            )
                        )]
                    )
                        ? $notes_array[strtolower(str_replace(['.', ' '], ['_', '_'], $survey->file_name))]
                        : $notes;

                }
                if (isset($submission->annotations)) {
                    $annotations_array = $submission->annotations;
                    $survey->annotations = isset(
                        $annotations_array[strtolower(
                            str_replace(
                                ['.', ' '], ['_', '_'],
                                $survey->file_name
                            )
                        )]
                    )
                        ? $annotations_array[strtolower(str_replace(['.', ' '], ['_', '_'], $survey->file_name))]
                        : [];
                } else {
                    $survey->annotations = [];
                }
                if (isset($submission->geolocation)) {
                    $geolocation_array = $submission->geolocation;
                    $survey->geolocation = isset(
                        $geolocation_array[strtolower(
                            str_replace(
                                ['.', ' '], ['_', '_'],
                                $survey->file_name
                            )
                        )]
                    )
                        ? $geolocation_array[strtolower(str_replace(['.', ' '], ['_', '_'], $survey->file_name))]
                        : [];
                } else {
                    $survey->geolocation = [];
                }
            }
            $response = [
                'response' => 'success',
                'data' => $surveys,
            ];
            return response()->json($response);
        }
    }

    public function add_attachment_caption(Request $request)
    {
        $field_name = $request->get('field_name');
        $file_name  = $request->get('file_name');
        $notes      = $request->get('note');

        $sanitised_file_name = str_replace('.', '_', str_replace(' ', '_', $file_name));
        $sanitised_file_name_notes_string = "notes['" . $sanitised_file_name . "']";

        $surveys = Surveyfiles::where('field_name', '=', $field_name)->where(
            'file_name', '=',
            $file_name
        )->where('deleted', '=', 0)->first();

        if ($surveys) {
            $update = Surveyfiles::where('field_name', '=', $field_name)->where(
                'file_name', '=',
                $file_name
            )->where('deleted', '=', 0)->update(['notes' => $notes]);
            $submission = RiskImprovementFormySubmissions::whereIn(
                'survey_id',
                [(string)$surveys['survey_id'], (int)$surveys['survey_id']]
            )->first();
            // print_r($submission['notes']); exit;
            if (isset($submission['notes'])) {
                $notes_array = $submission['notes'];
                $notes_array[$sanitised_file_name] = $notes;
                // $submission['notes'][$sanitised_file_name] = $notes;
                $update_submission = RiskImprovementFormySubmissions::whereIn(
                    'survey_id',
                    [(string)$surveys['survey_id'], (int)$surveys['survey_id']]
                )->update(['notes' => $notes_array]);
            } else {
                $update_submission = RiskImprovementFormySubmissions::whereIn(
                    'survey_id', [
                    (string)$surveys['survey_id'],
                    (int)$surveys['survey_id'],
                    ]
                )->update(['notes' => [$sanitised_file_name => $notes]]);
            }


            if ($update) {
                $response = [
                    'response' => 'success',
                    'data' => $update,
                ];
                return response()->json($response);
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Unable to add caption.',
            ];
            return response()->json($response);
        }
    }

    public function add_attachment_annotation(Request $request)
    {
        $field_name  = $request->get('field_name');
        $file_name   = $request->get('file_name');
        $annotations = $request->get('annotations');

        $sanitised_file_name = str_replace('.', '_', str_replace(' ', '_', $file_name));
        $sanitised_file_name_notes_string = "annotations['" . $field_name . "']['" . $sanitised_file_name . "']";

        $surveys = Surveyfiles::where('field_name', '=', $field_name)->where(
            'file_name', '=',
            $file_name
        )->where('deleted', '=', 0)->first();

        if ($surveys) {
            $submission = RiskImprovementFormySubmissions::whereIn(
                'survey_id',
                [(string)$surveys['survey_id'], (int)$surveys['survey_id']]
            )->first();
            // print_r($submission['notes']); exit;
            if (isset($submission['annotations'])) {
                $notes_array = $submission['annotations'];
                $notes_array[$sanitised_file_name] = $annotations;
                // $submission['notes'][$sanitised_file_name] = $notes;
                $update_submission = RiskImprovementFormySubmissions::whereIn(
                    'survey_id', [
                    (string)$surveys['survey_id'],
                    (int)$surveys['survey_id'],
                    ]
                )->update(['annotations' => $notes_array]);
            } else {
                $update_submission = RiskImprovementFormySubmissions::whereIn(
                    'survey_id', [
                    (string)$surveys['survey_id'],
                    (int)$surveys['survey_id'],
                    ]
                )->update(['annotations' => [$sanitised_file_name => $annotations]]);
            }


            if ($update_submission) {
                $response = [
                    'response' => 'success',
                    'data' => $update_submission,
                ];
                return response()->json($response);
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Unable to add annotations.',
            ];
            return response()->json($response);
        }
    }

    public function add_attachment_geolocation(Request $request)
    {
        $field_name = $request->get('field_name');
        $file_name  = $request->get('file_name');
        $lat        = $request->get('lat');
        $long       = $request->get('long');

        $sanitised_file_name = str_replace('.', '_', str_replace(' ', '_', $file_name));
        $sanitised_file_name_notes_string = "geolocation['" . $field_name . "']['" . $sanitised_file_name . "']";

        $surveys = Surveyfiles::where('field_name', '=', $field_name)->where(
            'file_name', '=',
            $file_name
        )->where('deleted', '=', 0)->first();

        if ($surveys) {
            $submission = RiskImprovementFormySubmissions::whereIn(
                'survey_id',
                [(string)$surveys['survey_id'], (int)$surveys['survey_id']]
            )->first();
            // print_r($submission['notes']); exit;
            if (isset($submission['geolocation'])) {
                $notes_array = $submission['geolocation'];
                $notes_array[$sanitised_file_name] = ['lat' => $lat, 'long' => $long];
                // $submission['notes'][$sanitised_file_name] = $notes;
                $update_submission = RiskImprovementFormySubmissions::whereIn(
                    'survey_id', [
                    (string)$surveys['survey_id'],
                    (int)$surveys['survey_id'],
                    ]
                )->update(['geolocation' => $notes_array]);
            } else {
                $update_submission = RiskImprovementFormySubmissions::whereIn(
                    'survey_id', [
                    (string)$surveys['survey_id'],
                    (int)$surveys['survey_id'],
                    ]
                )->update(['geolocation' => [$sanitised_file_name => ['lat' => $lat, 'long' => $long]]]);
            }


            if ($update_submission) {
                $response = [
                    'response' => 'success',
                    'data' => $update_submission,
                ];
                return response()->json($response);
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Unable to add annotations.',
            ];
            return response()->json($response);
        }
    }

    /**
     * Get survey attachment
     */

    public function delete_attachment($survey_id, $attachment_id)
    {
        $surveys = Surveyfiles::where('id', '=', $attachment_id)->where(
            'survey_id', '=', (int)$survey_id)->where('deleted', 0)->first();

        if ($surveys) {
            $surveys->deleted = 1;
            $surveys->save();
            $response = [
                'response' => 'success',
                'data' => $surveys,
            ];
            return response()->json($response);
        }
    }

    /**
     * Update Survey - actual deadlines
     */

    public function updateSurvey(Request $request, $id)
    {
        if ($id && is_numeric($id)) {
            $survey = Survey::find($id);
            if ($request->has('decline_survey') && ($request->has('company_id') && $request->get('company_id') == $survey->external_survey_company_id)) {
                $survey->external_survey_company_id = null;
            }

            if ($survey->id) {
                foreach ($survey->getFillable() as $attr) {
                    if ($request->has($attr)) {
                        $survey->$attr = $request->get($attr)
                            ?: (in_array($attr, $survey->getNullable())
                                ? null
                                : '');
                        $survey->save();
                    }
                }

                \Log::info([
                    __LINE__,
                    __CLASS__,
                    $request->all()
                ]);
                $emaildata = $request->has('notify_emaildata') ? (object) $request->get('notify_emaildata') : null;
                if (!isset($survey->scheduleMeta) && is_null($survey->scheduleMeta) && isset($emaildata) && !empty($emaildata)) {
                    $orgContacts = !is_null($survey->organisation->contacts) && isset($survey->organisation->contacts)
                        ?
                        $survey->organisation->contacts
                        : [];

                    foreach ($orgContacts as $contact) {
                        if ($contact->type === 'risk-engineer') {
                            $reUserInfo = $contact->libertyUser;
                            if (empty($reUserInfo->email)) {
                                continue;
                            }

                            $this->queue->queue(
                                $reUserInfo->email,
                                $reUserInfo->fullName(),
                                sprintf('Risk Reduce - %s Approve Date Confirmed', $survey->survey_type === 'dtr' ? 'Reports' : 'Surveys'),
                                'emails.surveys.underwriter-notification',
                                [
                                    'survey_id' => $survey->id,
                                    'client_org_id' => isset($emaildata->client_org_id) ? $emaildata->client_org_id : '',
                                    'client_org_name' => isset($emaildata->client_org_name) ? $emaildata->client_org_name : '',
                                    'address_1' => isset($emaildata->address_1) ? $emaildata->address_1 : '',
                                    'address_2' => isset($emaildata->address_2) ? $emaildata->address_2 : '',
                                    'city' => isset($emaildata->city) ? $emaildata->city : '',
                                    'postcode' => isset($emaildata->postcode) ? $emaildata->postcode : '',
                                    'country' => isset($emaildata->country) ? $emaildata->country : '',
                                    'actual_date' => isset($emaildata->actual_date) ? $emaildata->actual_date : '',
                                    'risk_engineer_label' => 'Field Engineer',
                                    'risk_engineer_name' => $emaildata->risk_engineer_name ?? '',
                                    'risk_engineer_phone' => $emaildata->risk_engineer_phone ?? '',
                                    'srf_id' => isset($emaildata->srf_id) ? $emaildata->srf_id : '',
                                    'srf_link' => isset($emaildata->srf_link) ? $emaildata->srf_link : '',
                                    'underwriter_name' => $reUserInfo->fullName(),
                                ]
                            );
                        }
                    }
                }

                static::updateRelations($request, $survey);

                $survey->save();

                if ($request->get('notify_underwriter') && $survey?->notify_underwriter && isset($emaildata->underwrite_email) && !empty($emaildata->underwrite_email)) {
                    //Send the email to the underwriter for notification
                    $this->queue->queue(
                        $emaildata->underwrite_email, $emaildata?->underwrite_name,
                        sprintf('Risk Reduce - %s Approve Date Confirmed', $survey?->survey_type === 'dtr' ? 'Reports' : 'Surveys'),
                        'emails.surveys.underwriter-notification', [
                            'survey_id' => $survey->id,
                            'client_org_id' => $emaildata->client_org_id,
                            'client_org_name' => $emaildata->client_org_name,
                            'address_1' => $emaildata->address_1 ?? '',
                            'address_2' => $emaildata->address_2 ?? '',
                            'city' => $emaildata->city,
                            'postcode' => $emaildata->postcode,
                            'country' => $emaildata->country,
                            'actual_date' => $emaildata->actual_date,
                            'risk_engineer_label' => 'Field Engineer',
                            'risk_engineer_name' => $emaildata->risk_engineer_name,
                            'risk_engineer_phone' => $emaildata->risk_engineer_phone,
                            'srf_id' => $emaildata->srf_id,
                            'srf_link' => $emaildata->srf_link,
                            'underwriter_name' => $emaildata->underwrite_name,
                        ]
                    );
                }

                $response = [
                    'response' => 'success',
                    'message' => 'The Survey has been updated successfully',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'Invalid Survey',
                ];
            }

        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid Survey ID',
            ];
        }

        return response()->json($response);
    }

    /**
     * Find Legacy Survey
     */
    public function getLegacy($id)
    {
        if ($id && is_numeric($id)) {
            $survey = LegacySurvey::where('srf', '=', $id)->first();

            $response = [
                'response' => ($survey)
                    ? 'success'
                    : 'error',
            ];

            $legacy_submissions = LegacyRiskImprovementFormySubmissions::whereIn(
                'survey_id',
                [(string)$id, $id]
            )->first();

            if ($survey) {
                $files = LegacySurveyFiles::where('survey_id', '=', $id)->get();
                $file_array = [];
                foreach ($files as $file) {
                    $submission = LegacyRiskImprovementFormySubmissions::whereIn(
                        'survey_id',
                        [$survey->survey_id, (string)$survey->survey_id]
                    )->first();
                    // print_r($submission->notes); exit;
                    $notes = $file->notes;
                    if ($submission && isset($submission->notes)) {
                        $notes_array = $submission->notes;
                        $file->notes = isset(
                            $notes_array[strtolower(
                                str_replace(
                                    ['.', ' '], ['_', '_'],
                                    $survey->file_name
                                )
                            )]
                        )
                            ? $notes_array[strtolower(str_replace(['.', ' '], ['_', '_'], $survey->file_name))]
                            : $notes;

                    }
                    if (isset($submission->annotations)) {
                        $annotations_array = $submission->annotations;
                        $file->annotations = isset(
                            $annotations_array[strtolower(
                                str_replace(
                                    ['.', ' '], ['_', '_'],
                                    $survey->file_name
                                )
                            )]
                        )
                            ? $annotations_array[strtolower(str_replace(['.', ' '], ['_', '_'], $survey->file_name))]
                            : [];
                    } else {
                        $file->annotations = [];
                    }
                    if (isset($submission->geolocation)) {
                        $geolocation_array = $submission->geolocation;
                        $file->geolocation = isset(
                            $geolocation_array[strtolower(
                                str_replace(
                                    ['.', ' '], ['_', '_'],
                                    $survey->file_name
                                )
                            )]
                        )
                            ? $geolocation_array[strtolower(str_replace(['.', ' '], ['_', '_'], $survey->file_name))]
                            : [];
                    } else {
                        $file->geolocation = [];
                    }

                    array_push($file_array, $file);
                }

                $response['data'] = $survey;
                $response['data']['legacy_submissions'] = $legacy_submissions;
                $response['data']['attached_files'] = $file_array;
                $response['data']['legacy_srf'] = LegacySurvey::where('id', $survey->id)->count();
            } else {
                $response['message'] = 'The specified Survey could not be found';
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid Survey ID',
            ];
        }

        return response()->json($response);
    }

    /*
     *  Get survey attatchments
     */

    /**
     * Check if it has a resurvey
     *
     * @param $id
     *
     * @return mixed
     */
    public function hasresurvey($survey_id)
    {

        $survey = Survey::where('resurvey_id', '=', $survey_id)->first();

        $response = [
            'response' => ($survey)
                ? 'success'
                : 'error',
        ];

        if ($survey) {
            $this->getRelations($survey);

            $response['data'] = $survey;

        } else {
            $response['message'] = 'Not a resurvey';
        }

        return response()->json($response);

    }

    /**
     * Delete Survey
     *
     * @param $id
     *
     * @return mixed
     */
    public function destroy($id)
    {
        if ($survey = Survey::find($id)) {
            if (Survey::destroy($id)) {
                $response = [
                    'response' => 'success',
                    'message' => 'The specified Survey was successfully deleted',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The specified Survey could not be deleted',
                ];
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'The specified Survey could not be found',
            ];
        }

        return response()->json($response);
    }

    /**
     * Get All SRFs
     */
    public function allSrfs(Request $request)
    {
        $surveys = Survey::query();

        $organisation_id = $request->has('organisation_id')
            ? $request->get('organisation_id')
            : null;

        if ($organisation_id != null) {
            $surveys = $surveys->where('organisation_id', '=', $organisation_id);
        }

        $mga_scheme = $request->has('mga_scheme')
            ? $request->get('mga_scheme')
            : null;

        $surveys = $surveys->where('survey_type', '<>', 'rereview')->get();

        $total = count($surveys);

        return response()->json([
            'response' => 'success',
            'data' => $surveys,
            'total' => $total,
        ]);
    }

    /**
     * Get All Broker Surveys
     */
    public function broker(Request $request, $id, $page = 1, $limit = 10000)
    {
        $order = $request->get('order', 'asc');
        $search = urldecode($request->get('search', ''));
        //$surveys = Survey::where('csr_uw_status', '=', '2');
        $surveys = Survey::where('survey_form', '<>', null);
        $broker_id=$request->has('broker_id')?$request->get('broker_id'):null;
       
        if($broker_id){
            $brokerUser=BrokerUser::find($broker_id);
            $orgIds=$brokerUser->associatedOrganisations();
            $surveys = $surveys->whereIn('organisation_id', $orgIds);
        }

        $organisation_id = $request->has('organisation_id')
            ? $request->get('organisation_id')
            : null;
        
        if (!is_null($organisation_id)) {
            $surveys = $surveys->where('organisation_id', '=', $organisation_id);
        }

        $branch_id = $request->has('branch_id')
            ? $request->get('branch_id')
            : null;
        if (!is_null($branch_id)) {
            $org_branches = Organisation::where('liberty_branch', $branch_id)->pluck('id');
            $surveys = $surveys->whereIn('organisation_id', $org_branches);
        }

        if ($request->has('branch_type') && $request->get('branch_type') == 'aspen-user') {
            $branches = Branch::where('is_aspen', 1)->pluck('id');
            $org_branches = Organisation::whereIn('liberty_branch', $branches)->pluck('id');
            $surveys = $surveys->whereIn('organisation_id', $org_branches);
        }

        $mgas = MgaScheme::where('broker_id', $id)->pluck('id')->all();
        $organisations = Organisation::whereIn('mga_scheme', $mgas)->orWhere('broker_id', $id);
        if ($request->has('user_login_type') && $request->get('user_login_type') == 'broker-user' && !$request->has('auto_scheduled_surveys')) {
            $organisations->orWhere('broker_id', $id);
        }

        if ($request->has('mga_scheme')) {
            $organisations = $organisations->where('mga_scheme', $request->get('mga_scheme'));
        }

        $organisations = $organisations->pluck('id');

        $surveys = $surveys->whereIn('organisation_id', $organisations);

        // auto scheduled surveys check
        if ($request->has('auto_scheduled_surveys')) {
            $excluded_survey_ids = DeletedResurvey::where('id', '>', 0)->pluck('survey_id');
            $surveyIds = RiskImprovementFormySubmissions::whereNotNull('csr_next_survey_due_date')
                ->where('csr_next_survey_due_date', '!=', '')
                ->where('csr_status', 'submitted')->pluck('survey_id');


            $surveys = $surveys->where(
                function ($query) use ($surveyIds) {
                    $query->whereIn('id', $surveyIds)
                        ->orWhereIn('legacy_srf', $surveyIds);
                }
            )->whereNotIn('id', $excluded_survey_ids);
        }

        if($id){
            $broker = Broker::find($id);
            $surveys = $surveys->where('broker_id', $broker->id);
        }

        $surveys = $surveys->with('organisation.mgascheme', 'externalSurveyor');
        $filterableSurveysIds = $surveys->get()->pluck('id');

        if ($search) {
            $brokerIds = BrokerUser::where('first_name', 'LIKE', '%' . $search . '%')
                ->orWhere('last_name', 'LIKE', '%' . $search . '%')->pluck('id');

            $organisationIds = Organisation::whereIn('id', $organisations)->where(
                'name', 'LIKE',
                '%' . $search . '%'
            )->pluck('id');

            $externalSurveyCompanyIds = ExternalSurveyCompany::where('name', 'LIKE', '%' . $search . '%')->pluck('id');

            $libertyUserIds = LibertyUser::where('role', 'risk-engineer')
                ->where('first_name', 'LIKE', '%' . $search . '%')
                ->orWhere('last_name', 'LIKE', '%' . $search . '%')
                ->orWhereRaw("concat(first_name, ' ', last_name) LIKE '%" . $search . "%'")
                ->pluck('id');

            $externalSurveyorIds = ExternalSurveyor::where('first_name', 'LIKE', '%' . $search . '%')
                ->orWhere('last_name', 'LIKE', '%' . $search . '%')
                ->orWhereRaw("concat(first_name, ' ', last_name) LIKE '%" . $search . "%'")
                ->pluck('id');

            $surveyIds = Survey::where('id', preg_replace('/[^0-9]/', '', $search))
                ->orWhere('legacy_srf', preg_replace('/[^0-9]/', '', $search))->pluck('id');

            $branchIds = Branch::where('name', 'LIKE', '%' . $search . '%')->pluck('id');
            $surveys = $surveys->where(
                function ($query) use (
                    $brokerIds,
                    $surveyIds,
                    $branchIds,
                    $organisationIds,
                    $externalSurveyCompanyIds,
                    $libertyUserIds,
                    $externalSurveyorIds
                ) {
                    $query->whereIn('broker_id', $brokerIds)
                        ->orWhereIn('id', $surveyIds)
                        ->orWhereIn('branch_id', $branchIds)
                        ->orWhereIn('organisation_id', $organisationIds)
                        ->orWhereIn('external_survey_company_id', $externalSurveyCompanyIds)
                        ->orWhereIn('surveyor_id', $libertyUserIds)->whereNull('external_survey_company_id')
                        ->orWhere(
                            function ($q) use ($externalSurveyorIds) {
                                $q->whereIn('surveyor_id', $externalSurveyorIds)->whereNotNull('external_survey_company_id');
                            }
                        );
                }
            )->whereIn('id', $filterableSurveysIds);
        }

        if ($request->has('survey_type')) {
            $surveys = $surveys->where('survey_type', '=', 'rereview')->count();
            $total = $surveys->count();
            $surveys = $surveys->orderBy('id', $order);
            $surveys = $surveys->take($limit)->skip(($page * $limit) - $limit)->get();
        } else {
            $surveys = $surveys->where('survey_type', '<>', 'rereview');
            $total = $surveys->count();
            $surveys = $surveys->orderBy('id', $order);
            $surveys = $surveys->take($limit)->skip(($page * $limit) - $limit)->get();
        }

        foreach ($surveys as $survey) {
            $this->getRelations($survey);
            $survey->submission = isset($survey->surveyor->id)
                ? RiskImprovementFormySubmissions::where('survey_id', '=', (string)$survey->id)
                ->where('surveyor_id', '=', (string)$survey->surveyor->id)
                ->first()
                : [];
        }

        

        return response()->json([
            'response' => 'success',
            'data'     => $surveys,
            'total'    => $total,
        ]);
    }

    public function options($column_name = 'organisation_id')
    {
        $model = new Survey;
        if (in_array($column_name, $model->getFillable())) {
            return response()->json(
                Survey::pluck(
                    $column_name,
                    'id'
                )
            );
        } else {
            return response()->json([
                'error' => 'Invalid column name provided',
            ], 400);
        }
    }

    public function storeLegacy(Request $request)
    {
        $data = $request->all();
        $validator = Validator::make(
            $data, [
                'srf'        => 'required|integer',
                'legacy_srf' => 'required|integer',
            ]
        );

        if ($validator->fails()) {
            return response()->json([
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ]);
        }

        $submission = $request->except(['srf', 'legacy_srf']);
        $submission['survey_id'] = $request->get('srf');

        $survey = Survey::find($request->get('srf'));

        if (!$survey) {
            return response()->json([
                'response' => 'error',
                'message' => 'The given survey does not exist',
            ]);
        }

        $exists = LegacySurvey::where('srf', $data['srf'])->count();

        if ($exists) {
            return response()->json([
                'response' => 'error',
                'message' => 'This survey already has a legacy survey',
            ]);
        }

        $survey = LegacySurvey::create([
            'srf' => $data['srf'],
            'legacy_srf' => $data['legacy_srf'],
        ]);

        if (isset($submission['csr_status']) && $submission['csr_status'] == 'submitted') {
            $submission['csr_submission_date'] = date('d/m/Y');
        }

        if (isset($submission['uwr_status']) && $submission['uwr_status'] == 'submitted') {
            $submission['uwr_submission_date'] = date('d/m/Y');
        }

        if (isset($submission['rereview_status']) && $submission['rereview_status'] == 'submitted') {
            $submission['rereview_submission_date'] = date('d/m/Y');
        }

        $id = LegacyRiskImprovementFormySubmissions::insertGetId($submission);

        if ($id) {
            $sub = LegacyRiskImprovementFormySubmissions::find($id);

            return response()->json([
                'response' => 'success',
                'data' => (string)$id,
                '_id' => (string)$id,
                'type' => isset($survey['survey_type'])
                    ? $survey['survey_type']
                    : '',
            ]);
        }

        return response()->json([
            'response' => 'error',
            'message' => 'Unable to create form submission',
        ]);
    }

    public function updateLegacy(Request $request, $id)
    {
        $data = $request->all();
        $validator = Validator::make(
            $data, [
                'srf' => 'required|integer',
                'legacy_srf' => 'required|integer',
            ]
        );

        if ($validator->fails()) {
            return response()->json([
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ]);
        }

        $submission = $request->except(['srf', 'legacy_srf']);
        $submission['survey_id'] = $id;

        $survey = Survey::find($id);

        if (!$survey) {
            return response()->json([
                'response' => 'error',
                'message' => 'The given survey does not exist',
            ]);
        }

        $exists = LegacySurvey::where('srf', $data['srf']);

        if ($exists->count()) {
            $survey = $exists->first();
            $survey->update($request->only(['srf', 'legacy_srf', 'status']));

            $next = LegacyRiskImprovementFormySubmissions::where('survey_id', $id)->first();
            $next->update($request->except(['srf', 'legacy_srf', 'status']));

            return response()->json([
                'response' => 'success',
                'message' => 'Saved form submission',
            ]);
        }

        return response()->json([
            'response' => 'error',
            'message' => 'Unable to save form submission',
        ]);
    }

    public function updateSurveyStatus(Request $request)
    {
        $data = $request->except('_token');
        $data = (object)$data;

        switch ($data->operation) {
        case 'unsubmit-csr':
            $this->unsubmitCSR($data->survey_id);
            break;
        case 'unsubmit-uwr':
            $this->unsubmitCSR($data->survey_id, false);
            break;
        case 'unsubmit-delete':
            $this->deleteSurvey($data->survey_id);
            break;
        }

        return response()->json([
            'response' => 'success',
            'message' => 'Saved form submission',
        ]);
    }

    private function unsubmitCSR($survey_id, $csr = true)
    {
        $submission = RiskImprovementFormySubmissions::where('survey_id', (string)$survey_id)->first();
        if ($csr) {
            $submission->csr_status = 'completed';
            $submission->csr_submission_date = '';
        } else {
            $submission->uwr_status = 'completed';
            $submission->uwr_submission_date = '';
        }
        $submission->save();
        Cache::forever('ri_submission_' . $survey_id, $submission);
    }

    private function deleteSurvey($survey_id)
    {
        $schedule_id = Survey::find($survey_id)->schedule_id;
        if ($schedule_id > 0) {
            DB::table('schedule')->where('id', $schedule_id)->delete();
            DB::table('schedule_file_attachments')->where('schedule_id', $schedule_id)->delete();
            DB::table('schedule_meta')->where('schedule_id', $schedule_id)->delete();
        }

        DB::table('survey_agenda_file')->where('survey_id', $survey_id)->delete();
        DB::table('survey_contacts')->where('survey_id', $survey_id)->delete();
        DB::table('survey_files')->where('survey_id', $survey_id)->delete();
        DB::table('survey_srf')->where('survey_id', $survey_id)->delete();
        DB::table('surveys')->where('ID', $survey_id)->delete();

        RiskRecommendationCards::where('survey_id', $survey_id)->delete();
    }

    public function resurveyCheck(Request $request)
    {
        $data = $request->except('_token');
        $organisation_id = $data['organisation_id'];
        $location_id = $data['location_id'];

        $survey = Survey::where('organisation_id', $organisation_id)
            ->where('location_id', $location_id)
            ->whereNotNull('next_survey_due_date');

        $survey_exist = $survey->exists();
        $survey_data = $survey->orderBy('id', 'desc')
            ->first();

        return response()->json([
            'status'       => 'success',
            'survey_exist' => $survey_exist,
            'survey_data'  => $survey_data,
        ]);
    }

    public function checkClientOrgHasSurvey(Request $request)
    {
        $organisationId = $request->organisation_id;
        if (!isset($organisationId)) {
            return response()->json([
                'response' => 'error',
                'message'  => 'Missing organisation id'
            ]);
        }

        return response()->json([
            'response' => 'succes',
            'data'     => Survey::select('id')
                ->where('organisation_id', '=', $organisationId)->exists(),
        ]);
    }

    private function clearSurveySubmissionCache(int $id)
    {
        Cache::forget('ri_submission_' . $id);
    }

    private function getSurveyById($surveyId)
    {
        //$surveyId=3794;

        $surveys = Survey::where('survey_type', '<>', 'rereview')
        ->with([
            'organisation.mgascheme',
            'branch',
            'underwriter',
            'brokerUnderwriter',
            'broker',
            'schedule',
            'scheduleMeta',
            'scheduleMetas',
            'agendaFile',
            'policyNumber.type',
            'contacts',
            'Location',
            'externalSurveyCompany',
        ])->where('id', $surveyId)->get();

        //$survey=json_decode($surveys);
        $survey=$surveys[0];
        $survey->submission=$survey->submission();
        
        if (!is_null($survey->schedule)) {
            $survey->schedule->getParsedMeta();
        }
       

        $survey=json_decode($survey);

        print_r('<pre>');
        print_r($survey);
        exit;
    }

    private function sendToQueue(Request $request, $page, $limit)
    {
        try {
            $queueService = new QueueService(config('app.aws.surveys_export_sqs'));
            $queueService->sendMessages([
                [
                    'request' => $request->all(),
                    'page' => $page,
                    'limit' => $limit,
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error("[Export Surveys Error]" . $e->getMessage());
        }
    }

    public function getRiskRecommendations(Request $request, $survey_id)
    {
        // Validate survey_id and other inputs
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'status' => 'nullable|string|in:open,closed,past_due'
        ]);
    
        // Set default dates if not provided
        $startDate = $request->start_date ?? now()->format('Y-m-d');
        $endDate = $request->end_date ?? now()->format('Y-m-d');
        $status = $request->status ?? 'open';
    
        // Build the query
        $query = RiskRecommendationLogs::where('survey_id', $survey_id)
            ->whereBetween('created_at', [
                $startDate . ' 00:00:00',
                $endDate . ' 23:59:59'
            ]);
    
        // Add status filter
        if ($status) {
            $query->where('status', $status);
        }
    
        // Get the results
        $recommendations = $query->get();
    
        return response()->json([
            'success' => true,
            'data' => $recommendations
        ]);
    }
}
