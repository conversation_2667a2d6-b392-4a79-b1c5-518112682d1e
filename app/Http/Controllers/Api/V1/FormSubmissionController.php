<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\Documentlevels;
use App\Models\Formy;
use App\Models\FormyDocuments;
use App\Models\FormySubmissions;
use App\Models\LibertyUser;
use App\Models\Mailqueue;
use App\Models\Organisation;
use App\Models\OrganisationContact;
use App\Models\PublicFormy;
use App\Models\PublicFormyDocuments;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\BrokerUser;


class FormSubmissionController extends BaseController
{

    public function __construct(Mailqueue $queue)
    {
        $this->queue = $queue;
    }

    public function index(Request $request, string $form)
    {
        $pageLength = 200;
        $data = $request->all();
        $page = $data['page'];
        $offset = ($page * $pageLength);

        $totalRecord = FormySubmissions::select([
            '_id',
            'form_id',
            'submission_sequence',
            'branch-where-accident-happened',
        ])
            ->where('form_id', '=', $form)
            ->where('submitted', '=', '1', 'AND')
            ->count();

        $submissions = FormySubmissions::select([
            '_id',
            'form_id',
            'submission_sequence',
            'branch-where-accident-happened',
        ]);

        if ($totalRecord >= $pageLength) {
            $submissions = $submissions->take($pageLength)
                ->offset($offset)
                ->where('form_id', '=', $form)
                ->where('submitted', '=', '1', 'AND')
                ->orderBY('_id', 'DESC')
                ->get();
        } else {
            $submissions = $submissions->where('form_id', '=', $form)
                ->where('submitted', '=', '1', 'AND')
                ->orderBY('_id', 'DESC')
                ->get();
        }

        // get users of submissions with `branch-where-accident-happened` and add to $userIds
        $usersWithBranch = $submissions->filter(fn($s) => isset($s->{'branch-where-accident-happened'}));

        if ($usersWithBranch->isNotEmpty()) {
            // collect all user IDs involved in the submissions
            $userIds = $usersWithBranch->map(fn($u) => (int)$u->{'branch-where-accident-happened'});
            $users = User::select(['id', 'branch_name'])
                ->whereIn('id', $userIds)
                ->get();

            // for every users found, get their branch names
            $usersWithBranch->each(function ($b) use ($submissions, $users) {
                $submission = $submissions->first(fn($s) => $s->_id == $b->_id);
                $branchName = null;
                if ($submission) {
                    // get the branch name of the user with ID matching the
                    // 'branch-where-accident-happened' of the submission
                    $branchName = (isset($users->first(
                        fn($u) => $u->id === (int)$submission->{'branch-where-accident-happened'}
                    )->branch_name)) ? $users->first(
                        fn($u) => $u->id === (int)$submission->{'branch-where-accident-happened'}
                    )->branch_name : null;
                    unset($submission->{'branch-where-accident-happened'});
                }
                $submission->branch_name = $branchName ?: '- ';
            });
        }

        return [
            'response' => 'success',
            'data' => json_encode($submissions->toArray()),
            'total' => $totalRecord
        ];
    }

    /*
     * get incomplete submissions from a user
     */
    public function savedFormsForOrg($organisation_id, $user_id, $manager = 0, $level1 = null, $level3 = null)
    {


        // $level4 = str_replace("*slash*","/",$level4);

        if (!is_null($level1) && !is_null($level3)) {
            $level1 = str_replace("*slash*", "/", $level1);
            // $level2 = str_replace("*slash*","/",$level2);
            $level3 = str_replace("*slash*", "/", $level3);

            $level1_data = Documentlevels::where('level_name', '=', urldecode($level1))->first();
            $level1 = $level1_data->level_id;

            $level3_data = Documentlevels::where('level_name', '=', urldecode($level3))->first();
            $level3 = $level3_data->level_id;

        }

        if ($manager == '1') {
            $data = FormySubmissions::where('organisation_id', '=', $organisation_id)
                ->orWhere('organisation_id', '=', (int)$organisation_id)
                ->where('submitted', '=', '0')
                ->get();
        } else {
            $data = FormySubmissions::where('user_id', '=', $user_id)
                ->orWhere('user_id', '=', (int)$user_id)
                ->where('submitted', '=', '0')
                ->get();
        }

        foreach ($data as $key => $submission) {
            $form = Formy::find($submission->form_id);
            if (isset($form->formType) && $form->formType == "accident-reporting") {
                unset($data[$key]);
            } else {
                if (isset($form->name)) {
                    $submission['form_name'] = $form->name;
                }

                if (isset($form->fileUploads)) {
                    $submission['fileUploads'] = $form->fileUploads;
                }

                if (!is_null($level1) && !is_null($level3)) {
                    if (isset($form->level1_type) && isset($form->level3_type)) {
                        if ($level1 != $form->level1_type || $level3 != $form->level3_type) {
                            unset($data[$key]);
                        }
                    } else {
                        unset($data[$key]);
                    }
                }

            }
        }
        return response()->json([
            'response' => 'success',
            'data'     => json_encode($data->toArray()),
        ]);
    }

    /*
     * get submitted forms from a user
     */
    public function submittedFormsForOrg($organisation_id, $user_id, $manager = 0, $level1 = null, $level3 = null)
    {
        // $level4 = str_replace("*slash*","/",$level4);
        if (!is_null($level1) && !is_null($level3)) {
            $level1 = str_replace("*slash*", "/", $level1);
            $level3 = str_replace("*slash*", "/", $level3);

            $level1_data = Documentlevels::where('level_name', '=', urldecode($level1))->first();
            $level1 = $level1_data->level_id;

            $level3_data = Documentlevels::where('level_name', '=', urldecode($level3))->first();
            $level3 = $level3_data->level_id;

        }

        if ($manager == '1') {
            $data = FormySubmissions::whereIn('organisation_id', [$organisation_id, (int)$organisation_id])
                ->where('submitted', '=', '1')
                ->get();
        } else {
            $data = FormySubmissions::where('user_id', '=', $user_id)->where('submitted', '=', '1')->get();
        }

        foreach ($data as $key => $submission) {

            $form = Formy::find($submission->form_id);
            if (isset($form->formType) && $form->formType == "accident-reporting") {
                unset($data[$key]);
            } else {
                if (isset($form->name)) {
                    $submission['form_name'] = $form->name;
                }

                if (isset($form->fileUploads)) {
                    $submission['fileUploads'] = $form->fileUploads;
                }

                if (!is_null($level1) && !is_null($level3)) {
                    if (isset($form->level1_type) && isset($form->level3_type)) {
                        if ($level1 != $form->level1_type || $level3 != $form->level3_type) {
                            unset($data[$key]);
                        }
                    } else {
                        unset($data[$key]);
                    }
                }
            }
        }
        return response()->json([
            'response' => 'success',
            'data'     => json_encode($data->toArray()),
        ]);
    }

    /*
     * get submissions for a form
     */
    public function submissions($form)
    {
        $data = FormySubmissions::where('form_id', '=', $form)->where('submitted', '=', '1', 'AND')->get();
        foreach ($data as $submission) {
            //print_r($submission['form_id']); exit;
            if (isset($submission['branch-where-accident-happened'])) {
                $branch_details = User::find($submission['branch-where-accident-happened']);
                $submission['branch_name'] = isset($branch_details['branch_name']) ? $branch_details['branch_name'] : null;
            }
        }
        return response()->json([
            'response' => 'success',
            'data'     => json_encode($data->toArray()),
        ]);
    }

    /*
    * get submissions for a form for an organisation
    */
    public function submissionsOrg($form, $organisation_id)
    {
        $data = FormySubmissions::where('form_id', '=', $form)->where('submitted', '=', '1', 'AND')
            ->whereIn('organisation_id', [$organisation_id, (int)$organisation_id], 'AND')
            ->get();
        $branches = User::where('organisation_id', '=', $organisation_id)->where('branch', '=', '1', 'AND')
            ->where('activated', '=', '1', 'AND')
            ->get();

        $branch = [];
        foreach ($branches as $b) {
            $branch[$b['id']] = $b['branch_name'];
        }
        //print_r($branch); exit;
        $branch_string = 'branch-where-accident-happened';
        //print_r($data);exit;
        foreach ($data as $submission) {
            if (isset($submission[$branch_string]) && isset($branch[$submission[$branch_string]])) {
                //print_r($submission->$branch_string);exit;
                $submission[$branch_string] = $branch[$submission[$branch_string]];
            }

            $date_accident = 'date-of-accident';
            if (isset($submission[$date_accident]) && $submission[$date_accident] != '') {
                $date_of_accident = $submission[$date_accident];
                //print_r($date_accident);exit;
                $submission[$date_accident] = date('d-m-Y', $date_of_accident);
            } else {
                $submission[$date_accident] = 'N/A';
            }
        }
        return response()->json([
            'response' => 'success',
            'data'     => $data,
        ]);
    }


 /**
     * Get Acciedent Reporting Forms
     *
     * @param  Request  $request
     * @param $organisationId
     * @param $user_id
     * @param  int  $manager
     *
     * @return JsonResponse
     */
    public function submittedAccidentFormsForOrg(
        Request $request,
        $organisationId,
        $user_id,
        int $manager = 0
    ): JsonResponse
    {
        $filters = $request->get('filters', []);

        date_default_timezone_set('Europe/London');
        if ($manager == 1) {
            $data = FormySubmissions::where(function ($query) use ($organisationId) {
                $query->where('organisation_id', $organisationId)
                    ->orWhere('organisation_id', (int)$organisationId);
            })
                ->where('submitted', '1')
                ->get();
        } else {
            $data = FormySubmissions::where(function ($query) use ($user_id) {
                $query->where('user_id', $user_id)
                    ->orWhere('user_id', (int)$user_id);
            })
                ->where('submitted', '1')
                ->get();
        }

        foreach ($data as $key => $submission) {
            if (isset($filters['year']) && !in_array(trim(strtolower($filters['year'])), ['', 'all'])) {
                if (!empty($submission['date-of-accident']) && is_numeric($submission['date-of-accident'])) {
                    $dateOfAccidentYear = date('Y', $submission['date-of-accident']);
                    if ($dateOfAccidentYear !== $filters['year']) {
                        continue;
                    }
                } else {
                    continue;
                }
            }

            $updated = $submission['updated_at'] != ''
                ? $submission['updated_at']->timezone('Europe/London')->format('d/m/Y H:i:s')
                : 'N/A';

            $dateOfAccident = (
                isset($submission['date-of-accident'])
                && $submission['date-of-accident'] != ''
                && is_numeric($submission['date-of-accident'])
            ) ? date('d/m/Y', $submission['date-of-accident']) : 'N/A';

            $claimSubmittedAt = (
                isset($submission['claim_submitted_at'])
                && $submission['claim_submitted_at'] != ''
                && is_numeric($submission['claim_submitted_at'])
            ) ? date('d/m/Y', $submission['claim_submitted_at']) : 'N/A';

            $submissionSequence = $submission['submission_sequence'] ?? 0;

            $form = isset($submission->is_public)
                ? PublicFormy::find($submission->form_id)
                : Formy::find($submission->form_id);

            if (isset($form->formType) && $form->formType != "accident-reporting") {
                unset($data[$key]);
            } else {
                if (isset($form->name)) {
                    $submission['form_name'] = $form->name;
                    $submission['updated'] = $updated;
                    $submission['submission_sequence'] = $submissionSequence;
                    $submission['date_of_accident'] = $dateOfAccident;
                    $submission['claim_submitted_at'] = $claimSubmittedAt;

                    // org_name
                    $organisation = Organisation::find($submission->organisation_id);
                    $submission['org_name'] = $organisation->name;

                    // branch_name
                    $userQueryParams = [
                        'id' => $submission->{'branch-where-accident-happened'},
                        'branch' => 1,
                    ];
                    if ($organisationId) {
                        $userQueryParams['organisation_id'] = $organisationId;
                    }
                    $branch = User::where($userQueryParams)->first();
                    $submission['branch_name'] = $branch
                        ? $branch->branch_name
                        : 'N/A';
                }
            }
        }

        return response()->json([
            'response' => 'success',
            'data' => json_encode($data->toArray()),
        ]);
    }

    /**
     * Get Acciedent Reporting Forms that resulted in claims
     *
     * @return mixed
     */
    public function submittedAccidentFormsClaims($org_id = 0)
    {
        date_default_timezone_set('Europe/London');
        if ($org_id == 0) {
            $data = FormySubmissions::where('claim', '=', '1')->where('submitted', '=', '1', 'AND')->get();
        } else {
            $data = FormySubmissions::where('claim', '=', '1')->whereIn(
                'organisation_id', [$org_id, (int)$org_id],
                'AND'
            )->where('submitted', '=', '1', 'AND')->get();
        }

        $organisations = Organisation::all();
        $org_array = [];
        foreach ($organisations as $org) {
            $org_array[$org->id] = $org->name;
        }

        if ($org_id == 0) {
            $branches = User::where('branch', '=', '1')->get();
        } else {
            $branches = User::where('branch', '=', '1')->where('organisation_id', '=', $org_id)->get();
        }

        //print_r($branches);exit;
        $branch_array = [];
        foreach ($branches as $branch) {
            $branch_array[$branch->id] = $branch->branch_name;
        }

        $submission = [];
        foreach ($data as $key => $submission) {
            $submission['org_name'] = isset($org_array[$submission->organisation_id]) 
                ? $org_array[$submission->organisation_id]: '';
            if ($submission['updated_at'] != '') {
                $updated = $submission['updated_at']->timezone('Europe/London')->format('d/m/Y H:i:s');
            } else {
                $updated = 'N/A';
            }
            if (isset($submission['date-of-accident']) && $submission['date-of-accident'] != '') {
                $date_of_accident = date(
                    'd/m/Y',
                    $submission['date-of-accident']
                );//->timezone('Europe/London')->format('d/m/Y H:i:s');
            } else {
                $date_of_accident = 'N/A';
            }
            if (isset($submission['claim_submitted_at']) && $submission['claim_submitted_at'] != '') {
                $claim_submitted_at = date(
                    'd/m/Y',
                    $submission['claim_submitted_at']
                );//->timezone('Europe/London')->format('d/m/Y H:i:s');
            } else {
                $claim_submitted_at = 'N/A';
            }
            if (isset($submission['submission_sequence'])) {
                $submission_sequence = $submission['submission_sequence'];
            } else {
                $submission_sequence = '0';
            }
            $form = Formy::find($submission->form_id);
            if (isset($form->formType) && $form->formType != "accident-reporting") {

                unset($data[$key]);
            } else {
                if (isset($form->name)) {
                    $branch_str = 'branch-where-accident-happened';
                    $submission['form_name'] = $form->name;
                    $submission['updated'] = $updated;
                    $submission['submission_sequence'] = $submission_sequence;
                    $submission['date_of_accident'] = $date_of_accident;
                    $submission['claim_submitted_at'] = $claim_submitted_at;
                    if (isset($branch_array[$submission->$branch_str])) {
                        $submission['branch_name'] = $branch_array[$submission->$branch_str];
                    }
                }
            }

        }

        return response()->json([
            'response' => 'success',
            'data'     => json_encode($data->toArray()),
        ]);
    }


    public function savedAccidentFormsForOrg($organisation_id, $user_id, $manager = 0)
    {
        if ($manager == 1) {
            $data = FormySubmissions::where('user_id', '=', $user_id)->where('submitted', '=', '0')->get();
        } else {
            $data = FormySubmissions::where('user_id', '=', $user_id)->where('submitted', '=', '0')->get();
        }


        foreach ($data as $key => $submission) {
            //print_r($submission['updated_at']);exit;
            if ($submission['updated_at'] != '') {
                $updated = $submission['updated_at']->timezone('Europe/London')->format('d/m/Y H:i:s');
            } else {
                $updated = 'N/A';
            }

            if (isset($submission['submission_sequence'])) {
                $submission_sequence = $submission['submission_sequence'];
            } else {
                $submission_sequence = '0';
            }

            //$updated = $submission->updated_at;
            //$updated = $updated->format('d/m/Y');
            //$updated = \Carbon\Carbon::parse($submission->updated_at)->format('d/m/Y H:i:s');
            $form = Formy::find($submission->form_id);
            if (isset($form)) {
                if ($form->formType != 'accident-reporting') {
                    unset($data[$key]);
                } else {
                    $submission['form_name'] = $form->name;
                    $submission['updated'] = $updated;
                    $submission['submission_sequence'] = $submission_sequence;
                }
            } else {
                unset($data[$key]);
            }

        }

        return response()->json([
            'response' => 'success',
            'data'     => json_encode($data->toArray()),
        ]);
    }

    /*
    * get a form submission
    */
    public function show($form)
    {
        $data = FormySubmissions::find($form);
        $attachments = FormyDocuments::where('submission_id', '=', $form)->get();

        if (isset($data)) {
            $data->attachments = $attachments;
            if (isset($data['branch-where-accident-happened'])) {
                $branch_details = User::find($data['branch-where-accident-happened']);
                //print_r($branch_details); exit;
                if ($branch_details) {
                    $branch_name = ($branch_details['branch_name'] != ''
                        ? $branch_details['branch_name']
                        : $branch_details['first_name'] . ' ' . $branch_details['last_name']);
                    $data['branch-where-accident-happened-name'] = $branch_name;
                }
            }
            return response()->json([
                'response' => 'success',
                'data'     => json_encode($data->toArray()),
            ]);
        }

        return response()->json([
            'response' => 'error',
            'message'  => 'Submission does not exist',
        ]);

    }

    /*
     * save a form
     */
    public function store(Request $request)
    {        
        $sequence = '0';
        $submission = json_decode($request->get('submission'), true);

        $sequence = FormySubmissions::where(
            'organisation_id', '=',
            $submission['organisation_id']
        )->orderBy('submission_sequence', 'desc')->first();

        if (isset($sequence) && isset($sequence->submission_sequence)) {
            $sequence = $sequence->submission_sequence;
        }

        $submission['submission_sequence'] = $sequence + 1;
        $id = FormySubmissions::insertGetId($submission);
        if ($id) {
            $sub = FormySubmissions::find($id);
            return response()->json([
                'response' => 'success',
                'data'     => (string)$id,
            ]);
        } else {
            return response()->json([
                'response' => 'error',
                'message'  => 'Unable to create form submission',
            ]);
        }
    }


    /*
    * update a form
    */
    public function update(Request $request)
    {
        $data       = $request->all();
        $id         = $request->get('_id');
        $submission = FormySubmissions::find($id);

        if ($submission->submitted == '1' || (!isset($submission->claim) && isset($data['claim']) && $data['claim'] == '1')) {
            if (((!isset($submission->claim) && isset($data['claim']) && $data['claim'] == '1')) || ($request->has('isAdmin') && 
                $request->get('isAdmin') == '1' && !isset($submission->claim))) {

            } else {
                return response()->json([
                    'response' => 'error',
                    'message'  => 'Unable to update form submission',
                ]);
            }
        }

        $submission->fill($data);

        $saved = false;

        if ($submission->save()) {
            $saved = true;
        } else {
            return response()->json([
                'response' => 'error',
                'message'  => 'Unable to update form submission',
            ]);
        }

        if ((isset($data['submitted']) && $data['submitted'] == true) || isset($data['claim'])) {
            //send emails to admin/group users
            if ($submission->is_public) {
                $form = PublicFormy::find($submission->form_id);
            } else {
                $form = Formy::find($submission->form_id);
            }

            if (isset($form)) {
                $organisation_id = $data['client_organisation_id'];                
                if ($form->notify_group_user && !isset($data['claim'])) {
                    //notify all users in organisation with manager
                    $organisation = Organisation::find($organisation_id);
                    if (isset($organisation)) {
                        foreach ($organisation->users as $user) {
                            if ($user->manager && $user->activated) {
                                $user->formName = $form->name;
                                $user->formLink = '/forms/submission/' . $submission->id;
                                $user->linkType = 'client';
                                $user->frontEndLink = config('app.client_frontend');
                                $user->seq = $submission->submission_sequence;
                                $this->queue->queue(
                                    $user->email, $user->fullName(),
                                    'Risk Reduce - New form submission', 'emails.forms.submitted', $user
                                );
                            }
                        }
                    }
                }

                if ($form->notify_liberty_admin && !isset($data['claim'])) {

                    $admins = LibertyUser::where('role', 'admin')->get();
                    foreach ($admins as $admin) {
                        if ($admin->activated == '1') {
                            $admin->formName = $form->name;
                            $admin->formLink = '/forms/submission/' . $submission->id;
                            $admin->seq = $submission->submission_sequence;
                            //print_r($admin);exit;
                            $this->queue->queue(
                                $admin->email, $admin->fullName(), 'Risk Reduce - New form submission',
                                'emails.forms.submitted', $admin
                            );
                        }
                    }
                }

                if (($form->notifications_admin_users != '') && (!isset($submission->is_public) && !isset($data['claim']))) {
                    $admins = LibertyUser::whereIn('email', explode(', ', $form->notifications_admin_users))->get();
                    foreach ($admins as $admin) {
                        if ($admin->activated == '1') {
                            $admin->formName = $form->name;
                            $admin->formLink = '/forms/submission/' . $submission->_id;
                            $admin->seq = $submission->submission_sequence;
                            $this->queue->queue(
                                $admin->email,
                                $admin->fullName(),
                                'Risk Reduce - New form submission',
                                'emails.forms.protected_verified_notification',
                                $admin
                            );
                        }
                    }
                    
                    $brokers = BrokerUser::whereIn('email', explode(', ', $form->notifications_admin_users))->get();
                    foreach ($brokers as $broker) {
                        $broker->formName = $form->name;
                        $broker->formLink = '/forms/submission/' . $submission->_id . '?user-type=broker-user';
                        $broker->seq = $submission->submission_sequence;
                        $this->queue->queue(
                            $broker->email,
                            $broker->fullName(),
                            'Risk Reduce - New form submission',
                            'emails.forms.protected_verified_notification',
                            $broker
                        );
                    }
                }

                if ($form->notify_broker && !isset($data['claim'])) {
					$orgBrokers = OrganisationContact::with('brokerUser')
						->where('organisation_id', $organisation_id)
						->where('type', 'broker-user-contact')->get();
					if (!empty($orgBrokers)) {
						foreach ($orgBrokers as $orgBroker) {
							$firstName = !empty($orgBroker->first_name) ? $orgBroker->first_name : $orgBroker->brokerUser->first_name;
							$lastName  = !empty($orgBroker->last_name) ? $orgBroker->last_name : $orgBroker->brokerUser->last_name;
							$fullName  = $firstName . ' ' . $lastName;
							$orgBroker->first_name = $firstName;
							$orgBroker->last_name  = $lastName;
							$orgBroker->formName   = $form->name;
							$orgBroker->formLink   = '/forms/submission/' . $submission->id . '?user-type=broker-user';
							$orgBroker->seq        = $submission->submission_sequence;
							$email = !is_null($orgBroker->email) ? $orgBroker->email : $orgBroker->brokerUser->email;

							if (!is_null($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
								$this->queue->queue(
									$email,
									$fullName,
									'Risk Reduce - New form submission',
									'emails.forms.submitted',
									$orgBroker
								);
							}
						}
					}
				}

                if (($form->notifications != '') && (!isset($submission->is_public) && !isset($data['claim']))) {

                    $emails = explode(',', $form->notifications);
                    foreach ($emails as $email) {
                        $x = [];
                        $x['email'] = $email;
                        $x['formName'] = $form->name;
                        $x['formLink'] = '/forms/submission/' . $submission->id;
                        $x['seq'] = $submission->submission_sequence;
                        // $x['linkType'] = 'client';
                        //print_r($x); exit;
                        $this->queue->queue(
                            $email, $email, 'Risk Reduce - New form submission',
                            'emails.forms.submitted_notification', $x
                        );
                    }
                }

                if (isset($data['claim'])) {
                    $admins = LibertyUser::all();
                    foreach ($admins as $admin) {
                        if ($admin->activated == '1' && $admin->claims_notification == '1') {
                            $admin->formName = $form->name;
                            $admin->formLink = '/forms/submission/' . $submission->id;
                            $admin->seq = $submission->submission_sequence;
                            $this->queue->queue(
                                $admin->email, $admin->fullName(), 'Risk Reduce - New claim submission',
                                'emails.forms.claim', $admin
                            );
                        }
                    }
                }
            }
        }

        if ($saved) {
            return response()->json([
                'response' => 'success',
                'data'     => (string)$id,
            ]);
        } else {
            return response()->json([
                'response' => 'error',
                'message'  => 'Unable to update form submission',
            ]);
        }
    }

    /*
    * delete a submission
    */
    public function destroy($submission)
    {
        if (FormySubmissions::destroy($submission)) {
            return response()->json([
                'response' => 'success',
                'message'  => 'Submission Deleted',
            ]);
        } else {
            return response()->json([
                'response' => 'error',
                'message'  => 'Unable to delete form',
            ]);
        }
    }

    /*
    * send email notification to an attached verified public form
    */

    public function addDoc(Request $request)
    {
        $formData = $request->except('_token');
        $submission_id = $formData['submission_id'];

        $this->notifyNewAttachedDoc($submission_id, $formData);

        $id = FormyDocuments::insertGetId($formData);
        if ($id) {
            return response()->json([
                'response' => 'success',
                'data'     => (string)$id,
            ]);
        } else {
            return response()->json([
                'response' => 'error',
                'message'  => 'Unable to create form submission',
            ]);
        }
    }

    /**
     * update a submission
     *
     * @return void
     */
    private function notifyNewAttachedDoc($submission_id, $formData)
    {
        $submission = FormySubmissions::where('_id', $submission_id)->first();

        if (isset($submission->verified_at)
            && !empty($submission->verified_at)
            && isset($submission->public_submissions_id)
            && !empty($submission->public_submissions_id)
        ) {

            $formData['submission_id'] = $submission->public_submissions_id;
            PublicFormyDocuments::insertGetId($formData);

            $notifiable_admins = [];
            $notifiable_clients = [];
            $public_form = PublicFormy::where('_id', $submission->form_id)->first();

            if (isset($public_form->notifications_admin_users) && !empty($public_form->notifications_admin_users)) {

                $notification_admin_users = explode(',', $public_form->notifications_admin_users);

                foreach ($notification_admin_users as $notification_admin_user) {
                    $liberty_user = LibertyUser::where('email', trim($notification_admin_user))->first();

                    if (isset($liberty_user->first_name) && !empty($liberty_user->first_name)) {
                        $notifiable_admins[$liberty_user->first_name . ' ' . $liberty_user->last_name] = trim($notification_admin_user);
                    }
                }
            }

            if (isset($public_form->notifications_client_users) && !empty($public_form->notifications_client_users)) {

                $notification_client_users = explode(',', $public_form->notifications_client_users);

                foreach ($notification_client_users as $notification_client_user) {
                    $client_user = User::where('email', trim($notification_client_user))->first();

                    if (isset($client_user->first_name) && !empty($client_user->first_name)) {
                        $notifiable_clients[$client_user->first_name . ' ' . $client_user->last_name] = trim($notification_client_user);
                    }
                }
            }

            // Send attachment notification to admins only
            if ($notifiable_admins) {
                $baseUrl = config('app.admin_frontend');

                foreach ($notifiable_admins as $key_name => $notifiable) {
                    $data['name'] = $key_name;
                    $data['form_name'] = $public_form->name;
                    $data['submission_sequence'] = $submission->submission_sequence;
                    $data['submission_link'] = $baseUrl . '/forms/submission/' . $submission->public_submissions_id . '/public';
                    $this->queue->queue(
                        $notifiable, $key_name, 'Risk Reduce - New attachment to verified form',
                        'emails.forms.new_verified_attachment_notification', $data
                    );
                }
            }

            if ($notifiable_clients) {
                $baseUrl = config('app.client_frontend');

                foreach ($notifiable_clients as $key_name => $notifiable) {
                    $data['name'] = $key_name;
                    $data['form_name'] = $public_form->name;
                    $data['submission_sequence'] = $submission->submission_sequence;
                    $data['submission_link'] = $baseUrl . '/forms/submission/' . $submission->_id;
                    $this->queue->queue(
                        $notifiable, $key_name, 'Risk Reduce - New attachment to verified form',
                        'emails.forms.new_verified_attachment_notification', $data
                    );
                }
            }


        }
    }

    /**
     * retrieve attachment
     *
     * @param [type] $id
     * @return json
     */
    public function retrieveAttachment($id)
    {
        $document = FormyDocuments::where('document_store_name', '=', $id)->first();

        if (isset($document)) {
            $submission = FormySubmissions::where('_id', '=', $document->submission_id)->first();
            return response()->json([
                'response'   => 'success',
                'data'       => $document,
                'submission' => $submission,
            ]);
        }

        return response()->json([
            'response' => 'error',
            'error'    => 'Could not find document',
        ]);
    }

    public function deleteDoc(Request $request)
    {
        $document_store_name = $request->get('document_store_name');
        $document = FormyDocuments::where('document_store_name', '=', $document_store_name)->delete();
        if ($document) {
            return response()->json([
                'response' => 'success',
                'data'     => 'document deleted',
            ]);
        } else {
            return response()->json([
                'response' => 'error',
                'message'  => 'Unable to delete document',
            ]);
        }
    }

    public function ajaxSubmissions($form_id)
    {   
        $data = FormySubmissions::select([
            '_id',
            'submission_sequence',
            'branch-where-accident-happened',
        ])
            ->where('form_id', '=', $form_id)
            ->where('submitted', '=', '1')
            ->get();

        return response()->json([
            'response' => 'success',
            'data'     => $data,
        ]);
    }
}
