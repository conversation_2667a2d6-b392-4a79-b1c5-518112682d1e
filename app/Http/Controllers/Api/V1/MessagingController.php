<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\BrokerUser;
use Carbon\Carbon;
use App\Models\ExternalSurveyor;
use App\Models\LibertyUser;
use App\Models\Mailqueue;
use App\Models\Messaging;
use App\Models\OrganisationContact;
use Illuminate\Http\Request;
use App\Models\RiskImprovementFormySubmissions;
use App\Models\Survey;
use App\Models\SurveyContact;
use App\Models\User;
use App\Models\Formy;
use App\Models\CsrMicrosite\Otp;

class MessagingController extends BaseController
{
    public function __construct(Mailqueue $mailqueue)
    {
        $this->mail = $mailqueue;
    }

    public function send(Request $request)
    {
        date_default_timezone_set('Europe/London');
        $input = $request->all();

        if (isset($input['notify'])) {
            foreach ($input['notify'] as $key => $value) {
                $input["notify_" . $key] = implode(',', array_keys($value));
            }

        }

        $input['notified'] = '0';
        $input['sent_at'] = time();
        $input['created_at'] = date('Y-m-d H:i:s', $input['sent_at']);

        if (isset($input['show_client']) && $input['show_client'] == true) {
            if (isset($input['notify_client']) && !$input['notify_client']) {

            } else {
                $input['notify_client'] = true;
            }
        }

        $emailViewParams = $this->getEmailViewParams($input);

        $id = Messaging::insertGetId($input);

        if(isset($input['from_cron'])){
            return response()->json([
                'response' => 'success',
                'data'     => (string)$id,
            ]);
        }

        $rc = null;
        if(config('app.message_notification_admin')) {
            $rc = LibertyUser::where('email', 'LIKE', config('app.message_notification_admin'))->first();
        }
        $srf = Survey::find($input['survey_id']);

        if (isset($input['login_type']) && $input['login_type'] != 'risk-control' && $rc) { // if login user is not admin
            $this->mail->queue(
                $rc->email,
                $rc->fullName(),
                'Risk Reduce, New message received',
                'emails.messaging.notification',
                array_merge(
                    [
                        'full_name' => $rc->fullName(),
                        'survey_id' => $srf->id,
                        'survey_link' => '/surveys/report/' . $srf->id . '/risk-recommendation/' . substr(
                            $input['rr_ref'],
                            0, -7
                        ),
                        'admin_link' => true,
                    ],  
                    $emailViewParams
                )
            );
        }

        $externalUsers = Otp::select('identifier')
            ->where('survey_id', $request['survey_id'])
            ->get()
            ->pluck('identifier')
            ->unique()
            ->toArray();

        if ($externalUsers) {
            foreach ($externalUsers as $externalUser) {

                // Prevent email notification to sender
                if ($externalUser != $input['sender_name']) {
                    $this->mail->queue(
                        $externalUser, $externalUser, 'Risk Reduce, New message received',
                        'emails.messaging.notification',
                        array_merge(
                            [
                                'full_name' => '',
                                'survey_id' => $srf->id,
                                'admin_link' => false,
                                'survey_link' => '',
                                'external_user' => true,
                            ],
                            $emailViewParams
                        )
                    );
                }
            }
        }

        $riskEngineers = OrganisationContact::where('organisation_id', $srf['organisation_id'])
            ->where('type', 'risk-engineer')->get();

        foreach ($riskEngineers as $re) {
            $reUser = $re->libertyUser;
            $reUserEmail = data_get($reUser, 'email');
            $inputEmail = data_get($input, 'email');
            if (isset($reUserEmail, $inputEmail) && $reUserEmail != $inputEmail) {
                $this->mail->queue(
                    $reUser->email, $reUser->fullName(), 'Risk Reduce, New message received',
                    'emails.messaging.notification',
                    array_merge(
                        [
                            'full_name' => $reUser->fullName(),
                            'survey_id' => $srf->id,
                            'survey_link' => '/surveys/report/' . $srf->id . '/risk-recommendation/' . substr(
                                $input['rr_ref'],
                                0, -7
                            ),
                            'admin_link' => true,
                        ],
                        $emailViewParams
                    )
                );
            }
        }

        $this->sendNotifications();

        if ($id) {
            $broker_user = !empty($srf->broker_underwriter_id)
                ? BrokerUser::find($srf->broker_underwriter_id)
                : null;


            if (isset($input['notify_broker_contact']) && $input['notify_broker_contact']) {
                $survey_contact = SurveyContact::where('survey_id', '=', $input['survey_id'])->where(
                    'type', '=',
                    'broker'
                )->first();

                if ($survey_contact && isset($survey_contact->email)) {
                    $this->mail->queue(
                        $survey_contact->email,
                        $survey_contact->name,
                        'Risk Reduce, New message received',
                        'emails.messaging.notification',
                        array_merge(
                            [
                                'full_name' => $survey_contact->name,
                                'survey_id' => $srf->id,
                                'survey_link' => '/surveys/report/' . $srf->id . '/risk-recommendation/' . substr(
                                    $input['rr_ref'],
                                    0, -7
                                ) . '?type=broker_user',
                                'admin_link' => true,
                            ],
                            $emailViewParams
                        )
                    );
                }

            }

            if (!empty($broker_user)) {
                $this->mail->queue(
                    $broker_user->email,
                    $broker_user->Fullname(),
                    'Risk Reduce, New message received',
                    'emails.messaging.notification',
                    array_merge(
                        [
                            'full_name' => $broker_user->fullName(),
                            'survey_id' => $srf->id,
                            'survey_link' => '/surveys/report/' . $srf->id . '/risk-recommendation/' . substr(
                                $input['rr_ref'],
                                0, -7
                            ) . '?type=broker_user',
                            'admin_link' => true,
                        ],
                        $emailViewParams
                    )
                );
            }

            if (isset($input['login_type']) && $input['login_type'] == 'broker-user') {
                $user = LibertyUser::find($input['user_id']);

                $risk_engineer = !empty($srf->re_id)
                    ? LibertyUser::find($srf->re_id)
                    : null;
                $underwriter = !empty($srf->underwriter_id)
                    ? LibertyUser::find($srf->underwriter_id)
                    : null;

                if (!empty($risk_engineer)) {
                    $this->mail->queue(
                        $risk_engineer->email,
                        $risk_engineer->Fullname(),
                        'Risk Reduce, New message received',
                        'emails.messaging.notification',
                        array_merge(
                            [
                                'full_name' => $risk_engineer->fullName(),
                                'survey_id' => $srf->id,
                                'survey_link' => '/surveys/report/' . $srf->id . '/risk-recommendation/' . substr(
                                    $input['rr_ref'],
                                    0, -7
                                ),
                                'admin_link' => true,
                            ],
                            $emailViewParams
                        )
                    );
                }

                $broker_underwriter = !empty($srf->broker_underwriter_id)
                    ? BrokerUser::find($srf->broker_underwriter_id)
                    : null;
                if (!empty($broker_underwriter)) {
                    $this->mail->queue(
                        $broker_underwriter->email,
                        $broker_underwriter->Fullname(),
                        'Risk Reduce, New message received',
                        'emails.messaging.notification',
                        array_merge(
                            [
                                'full_name' => $broker_underwriter->Fullname(),
                                'survey_id' => $srf->id,
                                'survey_link' => '/surveys/report/' . $srf->id . '/risk-recommendation/' . substr(
                                    $input['rr_ref'],
                                    0, -7
                                ),
                                'admin_link' => true,
                            ],
                            $emailViewParams
                        )
                    );
                } elseif (!empty($underwriter)) {
                    $this->mail->queue(
                        $underwriter->email,
                        $underwriter->Fullname(),
                        'Risk Reduce, New message received',
                        'emails.messaging.notification',
                        array_merge(
                            [
                                'full_name' => $underwriter->fullName(),
                                'survey_id' => $srf->id,
                                'survey_link' => '/surveys/report/' . $srf->id . '/risk-recommendation/' . substr(
                                    $input['rr_ref'],
                                    0, -7
                                ),
                                'admin_link' => true,
                            ],
                            $emailViewParams
                        )
                    );
                }
            }

            return response()->json([
                'response' => 'success',
                'data'     => (string)$id,
            ]);
        } else {
            return response()->json([
                'response' => 'error',
                'message'  => 'Message could not be sent',
            ]);
        }
    }

    /**
     * Send email notifications
     */
    public function sendNotifications()
    {
        $emailViewParams = $this->getEmailViewParams(request()->all());

        $not_notified = Messaging::where('notified', '=', '0')->get();

        foreach ($not_notified as $notify) {

            $survey = Survey::where('id', '=', $notify->survey_id)->first();

            $submission = RiskImprovementFormySubmissions::whereIn(
                'survey_id',
                [$notify->survey_id, (int)$notify->survey_id]
            )->first();

            $risk_rec_id = isset($notify->rr_ref)
                ? '/risk-recommendation/' . substr($notify->rr_ref, 0, -7)
                : '';

            if (isset($submission->_id)) {
                $risk_rec_id_client = isset($notify->rr_ref)
                    ? '/submission/' . $submission->_id . '/' . substr($notify->rr_ref, 0, -7)
                    : '';
            }

            if (isset($notify->notify_surveyor)) {

                if (is_null($survey->external_survey_company_id) || $survey->external_survey_company_id == '') {
                    $surveyor = LibertyUser::where('id', '=', $survey->surveyor_id)->first();
                } else {
                    $surveyor = ExternalSurveyor::where('id', '=', $survey->surveyor_id)->first();
                }
                $surveyor->survey_id = $survey->surveyor_id;

                $this->mail->queue(
                    $surveyor->email,
                    $surveyor->Fullname(),
                    'Risk Reduce, New message received',
                    'emails.messaging.notification',
                    array_merge(
                        [
                            'full_name' => $surveyor->fullName(),
                            'survey_id' => $survey->id,
                            'survey_link' => '/surveys/report/' . $survey->id . $risk_rec_id,
                            'admin_link' => true,
                        ],
                        $emailViewParams
                    )
                );

            }


            if (isset($notify->notify_client) && $notify->notify_client && isset($risk_rec_id_client)) {

                $admin_users = User::where('organisation_id', '=', $survey->organisation_id)
                    ->where('manager', '=', 1)
                    ->get();

                foreach ($admin_users as $user) {
                    $this->mail->queue(
                        $user->email,
                        $user->fullName(),
                        'Risk Reduce, New message received',
                        'emails.messaging.notification',
                        array_merge(
                            [
                                'full_name' => $user->fullName(),
                                'survey_id' => $survey->id,
                                'survey_link' => '/surveys/' . $survey->id . $risk_rec_id_client,
                            ],
                            $emailViewParams
                        )
                    );
                }

            }

            $lib_users = [];
            if (isset($notify->notify_admin) || isset($notify->{'notify_risk-engineer'}) || isset($notify->notify_underwriter)) {
                if (isset($notify->notify_admin)) {

                    $admin_ids = explode(',', $notify->notify_admin);
                    foreach ($admin_ids as $key => $value) {
                        array_push($lib_users, $value);
                    }

                }
                if (isset($notify->{'notify_risk-engineer'})) {

                    $re_ids = explode(',', $notify->{'notify_risk-engineer'});
                    foreach ($re_ids as $key => $value) {
                        array_push($lib_users, $value);
                    }

                }
                if (isset($notify->notify_underwriter)) {

                    $uwr_ids = explode(',', $notify->notify_underwriter);

                    foreach ($uwr_ids as $key => $value) {
                        array_push($lib_users, $value);
                    }


                }

                $admins = LibertyUser::whereIn('id', $lib_users)->get();

                foreach ($admins as $admin) {
                    $admin->survey_id = $survey->surveyor_id;


                    $this->mail->queue(
                        $admin->email,
                        $admin->Fullname(),
                        'Risk Reduce, New message received',
                        'emails.messaging.notification',
                        array_merge(
                            [
                                'full_name' => $admin->fullName(),
                                'survey_id' => $survey->id,
                                'survey_link' => '/surveys/report/' . $survey->id . '' . $risk_rec_id,
                                'admin_link' => true,
                            ],
                            $emailViewParams
                        )
                    );
                }
            }
            $notify->notified = "1";
            $notify->save();
        }
    }

    /*
     * get all messages for a survey
     */
    public function threadForSurvey($survey_id)
    {
        $count = 0;
        $messages = Messaging::where('survey_id', '=', $survey_id)->get();

        foreach ($messages as $message) {
            $message->time_ago = Carbon::createFromTimeStamp($message->sent_at)->diffForHumans();
        }

        return response()->json([
            'response' => 'success',
            'data'     => $messages->toArray(),
        ]);

    }

    /*
     * get all messages for a survey's Risk Rec
     */
    public function threadForRiskRec($survey_id, $risk_rec)
    {
        $count = 0;
        $messages = Messaging::where('survey_id', '=', $survey_id)->where(
            'rr_ref', '=', $risk_rec . '_message',
            'AND'
        )->get();

        foreach ($messages as $message) {
            $message->time_ago = Carbon::createFromTimeStamp($message->sent_at)->diffForHumans();
        }

        return response()->json([
            'response' => 'success',
            'data'     => $messages->toArray(),
        ]);

    }

    public function threadCount($survey_id) {

        try {
            $messageThreadCount = Messaging::raw(function ($collection) use ($survey_id) {
                return $collection->aggregate([
                    [
                        '$match' => [
                            'survey_id' => $survey_id,
                        ],
                    ],
                    [
                        '$group' => [
                            '_id' => '$rr_ref',
                            'message_count' => ['$sum' => 1],
                        ],
                    ],
                ]);
            });

            return response()->json([
                'status' => 200,
                'message_thread_count' => $messageThreadCount
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'message' => $e->getMessage()
            ], 500);
        }
    }


    /*
     * save a form
     */
    public function store(Request $request)
    {
        $form = json_decode($request->get('form'), true);
        $form['created_at'] = date('Y-m-d');
        $id = Formy::insertGetId($form);
        if ($id) {
            $form = Formy::find($id);
            return response()->json([
                'response' => 'success',
                'data'     => (string)$id,
            ]);
        } else {
            return response()->json([
                'response' => 'error',
                'message'  => 'Unable to create form',
            ]);
        }
    }

    /*
    * update a form
    */
    public function update(Request $request, $formId)
    {
        $formData = json_decode($request->get('form'), true);
        $form = Formy::find($formId);
        $form->fill($formData);
        if ($form->save()) {
            return response()->json([
                'response' => 'success',
                'data' => (string)$formId,
            ]);
        } else {
            return response()->json([
                'response' => 'error',
                'message' => 'Unable to update form',
            ]);
        }
    }

    /*
    * delete a form
    */
    public function destroy($form)
    {
        $form_object = Formy::find($form);
        if (Formy::destroy($form)) {
            return response()->json([
                'response' => 'success',
                'message' => 'Form Deleted',
                ]
            );
        } else {
            return response()->json([
                'response' => 'error',
                'message' => 'Unable to delete form',
            ]);
        }
    }

    private function getEmailViewParams(array $input): array
    {
        $messageForEmail = $input['message'] ?? '';
        $senderName = $input['sender_name'] ?? '';

        $submission = RiskImprovementFormySubmissions::where('survey_id', $input['survey_id'])->first();
        $riskRecTitleRef = rtrim($input['rr_ref'], '_message') . '_title_end';
        $riskRecTitle = $submission->{$riskRecTitleRef} ?? '';

        return [
            'sender_name' => $senderName,
            'message' => $messageForEmail,
            'risk_rec_title' => $riskRecTitle,
        ];
    }
}
