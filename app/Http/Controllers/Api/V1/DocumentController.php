<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\Document;
use App\Models\Documentlevels;
use App\Models\Mailqueue;
use App\Models\Organisation;
use App\Models\PolicyDocumentModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use App\Models\SectorDocument;;
use Illuminate\Support\Facades\Validator;

class DocumentController extends BaseController
{

    /**
     * @param Documentlevels $documentLevels
     * @param SectorDocument $sectorDocument
     * @param Document       $document
     */
    public function __construct(
        Documentlevels $documentLevels,
        SectorDocument $sectorDocument,
        Document $document,
        Mailqueue $mailqueue
    ) {
        $this->documentLevels = $documentLevels;
        $this->sectorDocument = $sectorDocument;
        $this->documents = $document;
        $this->type = 'documents';
        $this->mail = $mailqueue;
    }

    public function getdoclib()
    {
        $documents = Document::whereNull('organisation_id')
            ->where('category', '<>', 'LOSSLESSON')
            ->where('category', '<>', 'CLIENTFOLDER')
            ->orderBy('updated_at', 'desc')->get();

        foreach ($documents as $doc) {
            $doc->sector = $doc->sectors();
            $doc->cover;
        }
        return Response::json(
            [
            'response' => 'success',
            'data' => $documents->toArray(),
            ]
        );
    }

    public function findDocumentsPerOrganisation($id)
    {
        $documents = Document::where('organisation_id', '=', $id)->where(
            'category', '<>',
            'LOSSLESSON'
        )->where('category', '<>', 'CLIENTFOLDER')->where('category', '<>', 'SURVEYREPORT')->get();

        //get all documents for sector
        $organisation = Organisation::find($id);
        if (isset($organisation)) {
            foreach ($organisation->sectorDocuments as $doc) {
                if ($doc->document->organisation_id == $id || is_null($doc->document->organisation_id)) {
                    $documents->push($doc->document);
                }
            }
        }


        foreach ($documents as $doc) {
            $doc->sectors = $doc->sectors();
            $doc->cover;
        }

        $documents = $documents->unique();
        return Response::json(
            [
            'response' => 'success',
            'data' => $documents->toArray(),
            ]
        );
    }

    public function find(Request $request, $id)
    {
        $user = $request->get('user_id');
        $document = Document::with('level1')->where('id', 'LIKE', $id)->orWhere('document_store_name', 'LIKE', $id)->first();

        //return $document;

        if (isset($document)) {
            $document->sectors = $document->sectors();
            $document->level4 = $document->level(4);

            return Response::json(
                [
                'response' => 'success',
                'data' => $document,
                ]
            );
        }

        return Response::json(
            [
            'response' => 'error',
            'error' => 'Could not find document',
            ]
        );
    }

    public function findReportDocumentsPerOrganisation($id)
    {
        $documents = Document::where('organisation_id', '=', $id)->where('category', '=', 'SURVEYREPORT')->get();


        return Response::json(
            [
            'response' => 'success',
            'data' => $documents->toArray(),
            ]
        );
    }

    public function findDocumentsPerOrganisationLevels(Request $request)
    {
        $organisation_id = $request->get('organisation_id');
        $level1_type = $request->get('level1_type');
        $level2_type = $request->get('level2_type');
        $level3_type = $request->get('level3_type');
        $level4_type = $request->get('level4_type');
        if (!is_numeric($level4_type)) {

            $level = $this->documentLevels->where('level_name', '=', preg_replace('/-/', ' ', $level4_type))->first();
            $level4_type = $level->level_id;
        }

        $documents = Document::where('organisation_id', '=', $organisation_id)
            ->where('level1_type', '=', $level1_type, 'AND')
            ->where('level2_type', '=', $level2_type, 'AND')
            ->where('level3_type', '=', $level3_type, 'AND')
            ->where('level4_type', '=', $level4_type, 'AND')
            ->where('category', '<>', 'LOSSLESSON', 'AND')
            ->where('category', '<>', 'CLIENTFOLDER')
            ->get();

        foreach ($documents as $doc) {
            $doc->sector;
            $doc->cover;
        }
        return Response::json(
            [
            'response' => 'success',
            'data' => $documents->toArray(),
            ]
        );
    }

    public function findUploadDocumentsPerOrganisation($id)
    {
        $documents = Document::where('organisation_id', '=', $id)
            ->where('level1_type', '=', null)
            ->where('level2_type', '=', null)
            ->where('level3_type', '=', null)
            ->where('level4_type', '=', null)
            ->where(
                function ($q) {
                    $q->where('category', '=', 'CLIENTFOLDER')
                        ->orWhere('category', '=', 'CLIENTFOLDERMINUTES');
                }
            )->get();

        foreach ($documents as $doc) {
            $doc->sector;
            $doc->cover;
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => $documents,
            ]
        );
    }


    /**
     * Store Document after being uploaded to rackspace
     */

    public function store(Request $request)
    {
        $rules = [
            'name' => 'required',
            'description' => 'required',
            'document_title' => 'required',
            'sector' => 'required',
            'user_id' => 'required',
            'type' => 'required',
        ];

        $isPolicyDocument = false;
        $document_id = null;

        if ($request->has('category') && $request->get('category') == 'SURVEYREPORT') {
            unset($rules['sector']);
            unset($rules['description']);
        }


        if ($request->has('document_id')) {
            $document_id = $request->get('document_id');
        }

        if ($request->has('document_type_id')) {
            $isPolicyDocument = true;
            unset($rules['sector']);
            unset($rules['description']);
            unset($rules['user_id']);
            unset($rules['type']);
        }

        if (isset($document_id)) {
            unset($rules['document_title']);
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'failed to create document',
                'errors' => $validator->errors(),
                ]
            );
        } else {
            $data = $request->all();
            if (is_null($data['category'])) { // Set category field to empty for Create New Document
                $data['category'] = '';
            }

            if (!$isPolicyDocument) {
                $document = Document::create($data);
                if (isset($document)) {
                    if ($request->has('category') && ($request->get('category') == 'SURVEYREPORT' || $request->get('category') == 'CLIENTFOLDER') && ($request->get('category') != 'LOSSLESSON')) {

                    } else {
                        foreach (explode(',', $data['sector']) as $sec) {
                            SectorDocument::create(
                                [
                                'document_id' => $document->id,
                                'sector_id' => $sec,
                                ]
                            );
                        }
                    }
                    return Response::json(
                        [
                        'response' => 'success',
                        'message' => 'Document created',
                        'id' => $document->id,
                        ]
                    );
                } else {
                    return Response::json(
                        [
                        'response' => 'error',
                        'message' => 'Failed to create document',
                        ]
                    );
                }
            }


            if ($isPolicyDocument) {

                $data = $request->except(['document', 'document_id']);

                $policy_doc = PolicyDocumentModel::updateOrCreate(['id' => $document_id], $data);

                if (isset($policy_doc)) {
                    return Response::json(
                        [
                        'response' => 'success',
                        'message' => 'Policy document created',
                        'id' => $policy_doc->id,
                        ]
                    );
                }
            }
        }
    }

    /*
     *
     * Find Document
     */

    public function find_policy_document($id, $organisation_id)
    {
        $document = PolicyDocumentModel::get_policy_docs($organisation_id, $id)->first();

        if (isset($document)) {
            return Response::json(['response' => 'success', 'data' => $document], 200);
        }

        return null;
    }

    public function get_policy_docs($id)
    {
        $data = PolicyDocumentModel::get_policy_docs($id);

        if (isset($data)) {
            return Response::json($data, 200);
        }

        return [];
    }

    public function delete_policy_document($document_id)
    {
        $data = PolicyDocumentModel::find($document_id);

        if (isset($data)) {
            $data->deleted = true;

            $result = $data->save();

            if (isset($result)) {
                return Response::json('Document deleted.', 200);
            }
        }

        return Response::json('Failed to delete document type.', 200);
    }

    /**
     * Destroy document
     */
    public function destroy($id)
    {
        $document = Document::find($id);

        if (isset($document)) {
            SectorDocument::where('document_id', '=', $document->id)->delete();
            $document->delete();
            //and delete sector pivots
            return Response::json(
                [
                'response' => 'success',
                'message' => 'Delete Document',
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'error' => 'Could not find document',
                ]
            );
        }
    }

    /**
     * Update Document
     */
    public function update(Request $request)
    {
        $rules = [
            'name' => 'required',
            'description' => 'required',
        ];

        $data = $request->all();

        if (isset($data['category']) && $data['category'] == 'SURVEYREPORT') {
            unset($rules['description']);
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Failed to update document',
                'errors' => $validator->errors(),
                ]
            );
        }

        $docID = $request->get('id');
        $document = Document::find($docID);
        if (isset($document)) {
            $data = $request->all();
            SectorDocument::where('document_id', '=', $document->id)->delete();
            if (isset($data['category']) && $data['category'] != 'SURVEYREPORT') {
                foreach (explode(',', $data['sector']) as $sec) {
                    SectorDocument::create(
                        [
                        'document_id' => $document->id,
                        'sector_id' => $sec,
                        ]
                    );
                }
            } else {
                if (isset($data['sector'])) {
                    foreach (explode(',', $data['sector']) as $sec) {
                        SectorDocument::create(
                            [
                            'document_id' => $document->id,
                            'sector_id' => $sec,
                            ]
                        );
                    }
                }
            }

            if (empty($data['level2_type'])) {
                $data['level2_type'] = null;
            }

            if (empty($data['level3_type'])) {
                $data['level3_type'] = null;
            }

            $document->fill($data);
            if ($document->save()) {
                return Response::json(
                    [
                    'response' => 'success',
                    'message' => 'Document updated',
                    ]
                );
            }

        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'Could not find document',
            ]
        );
    }


    public function featured(Request $request)
    {
        $level_id = $request->get('level_id');
        $organisation = $request->get('organisation_id');

        if (isset($level_id) && isset($organisation)) {
            $level = $this->documentLevels->where('level_id', '=', $level_id)->first();
            $level4 = [];
            if ($level->level == 3) {
                $childs = Documentlevels::where('level', '=', '4')->get();
                //$childs = $level->children();
                //print_r($childs);exit;
                foreach ($childs as $c) {
                    array_push($level4, $c->level_id);
                }

            } elseif ($level->level == 4) {
                $level4 = [$level->id];
            }

            $org = Organisation::where('id', '=', $organisation)->first();

            //print_r($org);exit;

            $sector_id = $org->sector;

            $document = Document::whereIn('level4_type', $level4)
                ->where('featured', '=', 1)
                ->where('level3_type', '=', $level_id)
                ->whereNull('organisation_id')
                ->orderby('updated_at', 'DESC')
                ->get()->take(3);

            foreach ($document as $index => $doc) {
                $sectors = [];
                $doc->sector = $doc->sectors();
                $doc_sectors = $doc->sectors();
                foreach ($doc_sectors as $sector) {
                    array_push($sectors, $sector->id);
                }
                if (!in_array($sector_id, $sectors)) {
                    $document->offsetUnset($index);
                }

            }
            return Response::json(
                [
                'response' => 'success',
                'data' => $document,
                ]
            );
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'Level ID and Organisation required',
            ]
        );

    }

    public function recommended($sector_id, $page = 0, $limit = 10)
    {
        if (isset($sector_id)) {
            $pivots = $this->sectorDocument->where('sector_id', '=', $sector_id)->pluck('document_id')->all();

            $documents = $this->documents->whereIn('id', $pivots)->where('recommended', '=', 1)->orderby(
                'updated_at',
                'DESC'
            )->take($limit)->skip(($page * $limit) - $limit)->get();

            foreach ($documents as $doc) {
                $doc->sectors = $doc->sectors();
                $doc->level4 = $doc->level(4);
            }

            return Response::json(
                [
                'response' => 'success',
                'data' => $documents,
                ]
            );
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'Sector ID required',
            ]
        );

    }

    public function trainingTools($sector_id = 0, $organisation_id = 0)
    {
        $level_id = $this->documentLevels->where('level_name', 'LIKE', 'Training Tools')->first();
        if ($sector_id == 0 && $organisation_id == 0) {
            $documents = $this->documents->where('level' . $level_id->level . '_type', '=', $level_id->level_id)->get();

        } elseif ($sector_id != 0 && $organisation_id == 0) {
            $pivot = $this->sectorDocument->where('sector_id', '=', $sector_id)->pluck('document_id')->all();
            $documents = $this->documents->whereIn('id', $pivot)->where(
                'level' . $level_id->level . '_type', '=',
                $level_id->level_id
            )->where('organisation_id', '=', null)->get();
        } elseif ($sector_id == 0 && $organisation_id != 0) {
            $documents = $this->documents->where(
                'level' . $level_id->level . '_type', '=',
                $level_id->level_id
            )->where('organisation_id', '=', $organisation_id)->get();
        } else {
            $pivot = $this->sectorDocument->where('sector_id', '=', $sector_id)->pluck('document_id')->all();
            $documents = $this->documents->whereIn('id', $pivot)->where(
                'organisation_id', '=',
                $organisation_id
            )->where('level' . $level_id->level . '_type', '=', $level_id->level_id)->get();
        }

        foreach ($documents as $doc) {
            $doc->sectors = $doc->sectors();
            $doc->level2 = $doc->level(2);
            $doc->level3 = $doc->level(3);
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => $documents,
            'total' => count($documents),
            ]
        );
    }

    public function notify_admins($organisation_id, $document_id = 0)
    {
        $admins = PolicyDocumentModel::get_admins_for_notification($organisation_id);
        $document = null;

        try {
            if ($document_id > 0) {
                $document = PolicyDocumentModel::get_policy_docs($organisation_id, $document_id);
            }

            foreach ($admins as $value) {
                //var_dump($value); exit;
                if (isset($document)) {
                    $data = ['admin' => $value, 'doc' => $document];
                    $this->mail->queue(
                        $value['email'], $value['full_name'],
                        'Risk Reduce, Policy Document Notification', 'emails.policydocuments.emailpolicydocument',
                        $data
                    );
                }

                if ($document_id == 0) {
                    $this->mail->queue(
                        $value['email'], $value['full_name'],
                        'Risk Reduce, Policy Document Notification', 'emails.policydocuments.emailpolicyall', $value
                    );
                }
            }

            return Response::json('Emails sent successfully', 200);
        } catch (\Exception $e) {
            return Response::json('Failed to send emails', 200);
        }

        return Response::json('Failed to send emails', 200);
    }
}
