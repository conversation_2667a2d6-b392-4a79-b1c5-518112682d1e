<?php
/**
 * Created by PhpStorm.
 * User: chrisvickers
 * Date: 11/03/15
 * Time: 10:05
 */

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\Document;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;

class LossLessonController extends BaseController
{


    public function __construct(Document $document)
    {
        $this->document = $document;
    }

    public function featured($sector = 0)
    {
        if ($sector == 0) {
            $documents = $this->document->where('category', '=', 'LOSSLESSON')
                ->whereNull('level1_type')
                ->whereNull('level2_type')
                ->whereNull('level3_type')
                ->whereNull('level4_type')
                ->get();
        } else {
            $documents = $this->document->join(
                'sector_document_pivot', 'sector_document_pivot.document_id', '=',
                'documents.id'
            )->where('category', '=', 'LOSSLESSON')
                ->whereNull('level1_type')
                ->whereNull('level2_type')
                ->whereNull('level3_type')
                ->whereNull('level4_type')
                ->where('sector_document_pivot.sector_id', '=', $sector)
                ->where('featured', '=', '1')
                ->whereNull('sector_document_pivot.deleted_at')
                ->orderBy('documents.updated_at', 'desc')
                ->limit(3)->offset(0)
                ->get();
        }

        if ($documents) {
            foreach ($documents as $doc) {
                $doc->cover;
                $doc->sectors = $doc->sectors();
            }
            return Response::json(['response' => 'success', 'data' => $documents]);
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => $documents,
            ]
        );
    }

    public function index($id)
    {
        $documents = $this->document->where('category', '=', 'LOSSLESSON')
            ->whereNull('level1_type')
            ->whereNull('level2_type')
            ->whereNull('level3_type')
            ->whereNull('level4_type')
            ->where('organisation_id', '=', $id)
            ->get();

        if ($documents) {
            foreach ($documents as $doc) {
                $doc->cover;
                $doc->sectors = $doc->sectors();
            }
            return Response::json(['response' => 'success', 'data' => $documents]);
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'No Loss Lesson Documents are available',
            ]
        );

    }

    public function destroy($id, $lossLesson)
    {
        $document = $this->document->where('id', '=', $lossLesson)
            ->where('category', '=', 'LOSSLESSON')
            ->whereNull('level1_type')
            ->whereNull('level2_type')
            ->whereNull('level3_type')
            ->whereNull('level4_type')
            ->where('organisation_id', '=', $id)
            ->first();


        if (isset($document)) {
            $document->delete();
            return Response::json(
                [
                'response' => 'success',
                'data' => 'Loss Lesson Document Deleted',
                ]
            );
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'No Loss Lesson Document Found',
            ]
        );
    }

    public function show($id, $lossLesson)
    {
        $document = $this->document->where('id', '=', $lossLesson)
            ->orWhere('document_title', '=', $lossLesson)
            ->where('category', '=', 'LOSSLESSON')
            ->whereNull('level1_type')
            ->whereNull('level2_type')
            ->whereNull('level3_type')
            ->whereNull('level4_type')
            ->where('organisation_id', '=', $id)
            ->first();
        if (isset($document)) {
            return Response::json(
                [
                'response' => 'success',
                'data' => $document,
                ]
            );
        }
        return Response::json(
            [
            'response' => 'error',
            'message' => 'No Loss Lesson Document Found',
            ]
        );

    }

    public function update(Request $request, $id)
    {
        $rules = [
            'id' => 'required',
            'name' => 'required',
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Failed to update document',
                'errors' => $validator->errors(),
                ]
            );
        }


        $data = $request->except('_token');
        $document = $this->document->where('id', '=', $data['id'])
            ->where('category', '=', 'LOSSLESSON')
            ->whereNull('level1_type')
            ->whereNull('level2_type')
            ->whereNull('level3_type')
            ->whereNull('level4_type')
            ->where('organisation_id', '=', $id)
            ->first();

        if (isset($document)) {

            $document->fill($data);
            $document->save();
            return Response::json(
                [
                'response' => 'success',
                'message' => 'Loss Less Document Updated',
                ]
            );
        }


        return Response::json(
            [
            'response' => 'error',
            'message' => 'No Loss Lesson Document Found',
            ]
        );

    }

    public function all($sector = 0)
    {
        if ($sector == 0) {
            $documents = $this->document->where('category', '=', 'LOSSLESSON')
                ->whereNull('level1_type')
                ->whereNull('level2_type')
                ->whereNull('level3_type')
                ->whereNull('level4_type')
                ->get();
        } else {
            $documents = $this->document->join(
                'sector_document_pivot', 'sector_document_pivot.document_id', '=',
                'documents.id'
            )->where('category', '=', 'LOSSLESSON')
                ->whereNull('level1_type')
                ->whereNull('level2_type')
                ->whereNull('level3_type')
                ->whereNull('level4_type')
                ->where('sector_document_pivot.sector_id', '=', $sector)
                ->whereNull('sector_document_pivot.deleted_at')
                ->get();
        }

        if ($documents) {
            foreach ($documents as $doc) {
                $doc->cover;
                $doc->sectors = $doc->sectors();
            }
            return Response::json(['response' => 'success', 'data' => $documents]);
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => $documents,
            ]
        );
    }

}
