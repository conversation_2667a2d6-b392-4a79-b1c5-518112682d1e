<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\LmsCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class LearningCategoryController extends BaseController
{
    protected $relations = [
        'sectors',
        'coverTypes',
    ];

    /**
     * Store new Category
     */
    public function store(Request $request)
    {
        $validator = Validator::make(
            $request->all(), [
            'name' => 'required',
            ]
        );

        if ($validator->fails()) {
            $response = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        } else {
            $category = LmsCategory::create([
                'organisation_id' => $request->get('organisation', null),
                'name' => $request->get('name'),
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $this->updateRelations(
                $category, [
                'sectors' => $request->get('sector', []),
                'coverTypes' => $request->get('cover_type', []),
            ]);

            if ($category->id) {
                $response = [
                    'response' => 'success',
                    'message' => 'The Category has been created successfully',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The Category has failed to be created',
                ];
            }
        }

        return Response::json($response);
    }

    /**
     * Get All categories
     */
    public function all(Request $request, $page = 1, $limit = 10)
    {
        $query = $request->get('search', '');
        $page = (int)$page;
        $limit = (int)$limit;
        $query = ($query)
            ? '%' . $query . '%'
            : '';

        $categories = ($query)
            ? LmsCategory::where(
                'name', 'LIKE', $query
            )->orWhere(
                'description', 'LIKE', $query
            )
            : LmsCategory::query();

        // get total by filters above (exclude pagimation limits)
        $total = $categories->count();

        // only paginate if a limit is specified (0 = no limit)
        if ($limit > 0) {
            $categories = $categories->take(
                $limit
            )->skip(
                ($page * $limit) - $limit
            );
        }

        $categories = $categories->get();

        foreach ($categories as $category) {
            $this->getRelations($category);
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => $categories,
            'total' => $total,
            ]
        );
    }

    private function getRelations(&$item)
    {
        foreach ($this->relations as $relation) {
            $item->$relation;
            unset($item->{sprintf('%s_id', $relation)});
        }
    }

    private function updateRelations(&$item, $values)
    {
        foreach ($this->relations as $relation) {
            if (isset($values[$relation]) && !empty($values[$relation])) {
                $item->{Str::camel($relation)}()->sync($values[$relation]);
            }
        }
    }

    /**
     * Update Category
     */

    public function update(Request $request, $id)
    {
        if ($id && is_numeric($id)) {
            $validator = Validator::make(
                $request->all(), [
                'name' => 'required',
                ]
            );

            if ($validator->fails()) {
                $response = [
                    'response' => 'error',
                    'errors' => $validator->messages()->toArray(),
                ];
            } else {
                $category = LmsCategory::find(
                    $id
                );

                if ($category->id) {
                    $category->organisation_id = $request->get('organisation_id', null);
                    $category->name = $request->get('name');
                    $category->save();

                    $this->updateRelations(
                        $category, [
                        'sectors' => $request->get('sector', []),
                        'coverTypes' => $request->get('cover_type', []),
                        ]
                    );

                    $response = [
                        'response' => 'success',
                        'message' => 'The Category has been updated successfully',
                    ];
                } else {
                    $response = [
                        'response' => 'error',
                        'message' => 'Invalid Category',
                    ];
                }
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid Category ID',
            ];
        }

        return Response::json($response);
    }

    /**
     * Find Category
     */
    public function show($id)
    {
        if ($id && is_numeric($id)) {
            $category = LmsCategory::find($id);

            if (!$category) {
                $category = LmsCategory::where('id', '=', $id)->first();
            }

            $response = [
                'response' => ($category)
                    ? 'success'
                    : 'error',
            ];

            if ($category) {
                $this->getRelations($category);

                $response['data'] = $category;
            } else {
                $response['message'] = 'The specified Category could not be found';
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid Category ID',
            ];
        }

        return Response::json($response);
    }

    /**
     * Delete Category
     *
     * @param $id
     *
     * @return mixed
     */
    public function destroy($id)
    {
        if ($category = LmsCategory::find($id)) {
            if (LmsCategory::destroy($id)) {
                $this->destroyRelations($id);

                $response = [
                    'response' => 'success',
                    'message' => 'The specified Category was successfully deleted',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The specified Category could not be deleted',
                ];
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'The specified Category could not be found',
            ];
        }

        return Response::json($response);
    }

    private function destroyRelations(&$item)
    {
        foreach ($this->relations as $relation) {
            // $item->{camel_case($relation)}()->delete();
        }
    }

    public function options()
    {
        return Response::json(
            LmsCategory::pluck(
                'name',
                'id'
            )->all()
        );
    }
}
