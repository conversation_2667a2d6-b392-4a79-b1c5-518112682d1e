<?php

namespace App\Http\Controllers\Api\V1\customSectors;

use App\Http\Controllers\Controller;
use App\Models\Cms;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;

class ArticleController extends Controller
{
    public static $do = "Directors & Officers";
    public static $fas = "Fine Art & Specie";
    public static $pi = "Professional Indemnity";

    public function getArticles(Request $request)
    {
        $input = $request->all();
        $sectorName = $input['sector'] ? self::${$input['sector']} : '';
        $isDashboard = (bool) isset($input['is_dashboard']) ? $input['is_dashboard'] : false;
        $limit = 9999;
        $articleCount = 0;
        $workspaceId = config('app.cms.responsible_business.workspace');
        $contentType = config('app.cms.responsible_business.content_type');
        $responsibleBusinessArticles = [];

        $query = json_encode([
            'status' => 'publish',
            'operator' => '=',
        ]);

        $articles = json_decode(Cms::get('workspaces/'.$workspaceId.'/content-types/'.$contentType.'/content-entries?query='.$query))->data;

        if ($articles && !empty($articles)){
            foreach ($articles as $article){
                if($isDashboard){
                    $limit = 4;
                }

                if($article && !empty($article)){

                    $sectors = config('app.cms.responsible_business.sectors');
                    $article_title = config('app.cms.responsible_business.article_title');
                    $content = config('app.cms.responsible_business.article_content');
                    $featured_image = config('app.cms.responsible_business.featured_image');
                    $key_sectors = [];

                    foreach($article->{$sectors} as $info){
                        if (isset($info->name) && ($info->name===$sectorName || $info->name==="Announcement")) {
                            $key_sectors[] = $info->name;
                        }
                    }

                    if (
                        (in_array($sectorName, $key_sectors)) &&
                        (!in_array("Announcement", $key_sectors))
                    ) {
                        $articleCount++;
                        try{
                            $responsibleBusinessArticles[] =  [
                                'article_id' => $article->_id,
                                'name' => $article->name,
                                'featured_image' => $article->{$featured_image}[0]->url,
                                'article_title' => $article->{$article_title},
                                'content' => $article->{$content},
                                'created_at' => $article->created_at,
                                'updated_at' => $article->updated_at,
                            ];
                            if($isDashboard && ($limit == $articleCount)){
                                break;
                            }
                        } catch(\Exception $e) {
                            continue;
                        }
                    }
                }
            }
            return $responsibleBusinessArticles;
        }

        return Response::json([
           'msg' => 'Something went wrong'
        ]);
    }

    public function getArticle($article_id)
    {
        $article_title = config('app.cms.responsible_business.article_title');
        $content = config('app.cms.responsible_business.article_content');
        $featured_image = config('app.cms.responsible_business.featured_image');
        $responsibleBusinessArticle = [];

        try {
            $workspaceId = config('app.cms.responsible_business.workspace');
            $contentType = config('app.cms.responsible_business.content_type');

            $article = json_decode(Cms::get('workspaces/'.$workspaceId.'/content-types/'.$contentType.'/content-entries/'.$article_id));

            if ($article && !empty($article)) {
                $responsibleBusinessArticle = [
                    'article_id' => $article->_id,
                    'name' => $article->name,
                    'featured_image' => $article->{$featured_image}[0]->url,
                    'article_title' => $article->{$article_title},
                    'content' => $article->{$content},
                    'created_at' => $article->created_at,
                    'updated_at' => $article->updated_at,
                ];
            }

            return $responsibleBusinessArticle;

        } catch (\Exception $e) {
            return Response::json([
                'msg' => 'Something went wrong',
                'details' => $e->getMessage()
             ]);
        }
    }
}
