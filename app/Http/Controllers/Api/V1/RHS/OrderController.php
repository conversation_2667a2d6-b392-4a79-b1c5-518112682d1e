<?php

namespace App\Http\Controllers\Api\V1\RHS;

use App\Http\Controllers\BaseController;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\RHS\Document;
use App\Models\RHS\Order;

class OrderController extends BaseController
{
    public function index(Request $request)
    {
        // $orders = Document::whereHas('order', function ($query) {
        //     $query->where('processed', 1);
        // })->orderBy('id', 'DESC');

        $orders = Document::with('order')->orderBy('id', 'DESC');

        if ($request->has('start') && $request->has('end')) {
            $start = Carbon::parse(str_replace('/', '-', $request->get('start')));
            $end = Carbon::parse(str_replace('/', '-', $request->get('end')));
            $orders = $orders->whereBetween('created_at', [$start, $end]);
        }

        $orders = $orders->with('order.products.type.productCategory', 'order.customer');
        if ($request->has('export')) {
            $orders = $orders->get();
            $count = $orders->count();
        } else {
            $page = ($request->get('page'))
                ? $request->get('page')
                : 1;
            $limit = ($request->get('limit'))
                ? $request->get('limit')
                : 10;
            $count = $orders->count();
            $orders = $orders->take($limit)->skip(($page * $limit) - $limit)->get();

        }

        foreach ($orders as &$order) {
            $tempOrder = Order::where('id', $order->order_id)->first();

            if (isset($tempOrder->id)) {
                $customer = $tempOrder->customer;
            }

            $order->organisation_name = $customer->organisation_name ?? null;
        }

        return response()->json(
            [
            'response' => 'success',
            'data' => $orders,
            'total' => $count,
            ]
        );
    }

    public function store()
    {
        //
    }

    public function show($order_id)
    {
        $order = Order::with('customer')
            ->where('id', $order_id)
            ->first();

        if ($order) {
            return response()->json(
                [
                'response' => 'success',
                'data' => $order,
                ]
            );
        }

        return response()->json(
            [
            'response' => 'error',
            'message' => 'Order not found',
            ]
        );
    }

    public function update($order_id)
    {
        //
    }
}
