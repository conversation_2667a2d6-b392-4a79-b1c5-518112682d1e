<?php

namespace App\Http\Controllers\Api\V1\RHS;

use App\Models\Mailqueue;
use App\Models\Organisation;
use App\Http\Controllers\BaseController;
use App\Models\OrganisationPolicy;
use App\Models\PolicyDocumentModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Response;
use App\Models\RHS\Customer;
use App\Models\RHS\CustomerOrder;
use App\Models\RHS\Document;
use App\Models\RHS\Order;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Models\User as OrganisationUser;

class OrganisationController extends BaseController
{
    public function __construct(Mailqueue $mailqueue)
    {
        $this->mail = $mailqueue;
    }

    public function store(Request $request)
    {
        $validator = Validator::make(
            $request->all(), [
            //'email' => 'unique:users|required',
            'name' => 'required|regex:/(^[A-Za-z0-9 ]+$)+/',
            'country' => 'required',
            'first_name' => 'required',
            'last_name' => 'required',
            'manager' => 'required',
            'branch' => 'required',
            'address_line_1' => 'required_if:branch,on',
            'postcode' => 'required_if:branch,on',
            ]
        );

        if ($validator->fails()) {
            return Response::json(
                [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
                ], 200
            );
        }

        $data = $request->except('_token');


        unset($data['login_type']);

        if (isset($data['_rhs'])) {
            $data['rhs_no'] = $request->get('affiliate_ref');
            $data['is_rhs'] = 1;

            $organisationId = Organisation::where(
                [
                'name' => $request->get('name'),
                ]
            )->first();


            if (isset($organisationId->id) && !empty($organisationId->id)) {
                $organisation = $organisationId;
            } else {
                $organisation = Organisation::create($data);
            }

            unset($data['_rhs']);
        }

        $data['safetymedia_url'] = Str::uuid()->toString();

        $sectPolicyNumberFlag = isset($data['sect_policy_no']) && !empty($data['sect_policy_no']);
        $liabPolicyNumberFlag = isset($data['liabs_policy_no']) && !empty($data['liabs_policy_no']);

        // Determine the policy number type (check the "policy_type" table on RR for types)
        $policyTypeId = 3;

        // Create record with SECT type
        if ($sectPolicyNumberFlag) {
            OrganisationPolicy::create(
                [
                'organisation_id' => $organisation->id,
                'policy_type_id' => $policyTypeId,
                'policy_number' => $data['sect_policy_no'],
                'inception_date_of_cover' => $data['sect_inception_date_of_cover'],
                'expiry_date_of_cover' => $data['sect_expiry_date_of_cover'],
                ]
            );
        }

        // Create record with LIABS type
        if ($liabPolicyNumberFlag) {
            OrganisationPolicy::create(
                [
                'organisation_id' => $organisation->id,
                'policy_type_id' => $policyTypeId,
                'policy_number' => $data['liabs_policy_no'],
                'inception_date_of_cover' => $data['liabs_inception_date_of_cover'],
                'expiry_date_of_cover' => $data['liabs_expiry_date_of_cover'],
                ]
            );
        }

        $customer = Customer::find($data['customer_id']);
        $document = Document::where('order_id', $data['order_id'])->first();
        $order = Order::with('customer')->where('id', $data['order_id'])->first();

        $sm_password = uniqid();
        $expiry = time() + (14 * 24 * 60 * 60); // 14 days
        $documentIdentifier = $document->uuid ?? $document->id;
        $hasInsurance = $request->get('has_insurance');

        $data = [
            'email' => $customer->email,
            'password' => Hash::make($request->get('password')),
            'first_name' => $request->get('first_name'),
            'last_name' => $request->get('last_name'),
            'address_line_1' => $request->get('address_line_1'),
            'address_line_2' => $request->get('address_line_2'),
            'postcode' => $request->get('postcode'),
            'country' => $request->get('country'),
            'manager' => $request->get('manager'),
            'branch' => $request->get('branch'),
            'branch_name' => $request->get('branch_name'),
            'organisation_id' => $organisation->id,
            'activation_code' => $request->get('uuid'),
            'activation_code_expires' => $expiry,
            'croner_access' => $request->has('croner_access')
                ? 1
                : 0,
            'safetymedia_access' => $request->has('safetymedia_access')
                ? 1
                : 0,
            'astutis_access' => $request->has('astutis_access')
                ? 1
                : 0,
            'safetymedia_password' => $sm_password,
            'c_live_access' => $request->has('c_live_access')
                ? 1
                : 0,
            'triton_access' => $request->get('triton_access')
                ? 1
                : 0,
            'branch_id' => $request->get('branch_id'),
            'file' => $request->has('file')
                ? $request->get('file')
                : null,
            'original_filename' => $request->has('original_filename')
                ? $request->get('original_filename')
                : null,
            'is_submission_created' => $request->has('is_submission_created')
                ? 1
                : 0,
        ];

        PolicyDocumentModel::create(
            [
            'name' => 'Insurance Policy',
            'description' => 'System Generated',
            'document_title' => 'order_confirmation_#' . $documentIdentifier . '.pdf',
            'document_store_name' => $order->uuid,
            'organisation_id' => $organisation->id,
            'document_type_id' => $policyTypeId,
            ]
        );

        $userId = OrganisationUser::where(
            [
            'email' => $request->get('email'),
            ]
        )->first();

        if (isset($userId->id) && !empty($userId->id)) {
            $user = $userId;
        } else {

            $final_price = 0;

            foreach ($order->products as $product) {
                $final_price += $product->price;
            }

            if ($final_price > 0 && $hasInsurance) {

                $user = OrganisationUser::create($data);

                if ($user->safetymedia_access === '0') {
                    $sm_disabled = '1';
                } else {
                    $sm_disabled = '0';
                }

                if ($user->manager === '1') {
                    $admin_level = 'clientadmin';
                } else {
                    $admin_level = 'user';
                }

                if ($organisation->tpid === '' || is_null($organisation->tpid)) {
                    $sm_account = config('app.sm.org_id_prefix') . $organisation->id;
                } else {
                    $sm_account = $organisation->tpid;
                }

                $this->mail->queue(
                    $user->email, $user->fullName(), 'Risk Reduce, Welcome to Risk Reduce',
                    'emails.auth.welcome', $user
                );
            }
        }

        return Response::json(
            [
            'response' => 'success',
            'message' => 'RHS customer successfully setup',
            //'data' => $user->id,
            'customer' => $customer,
            'order' => $order,
            'document' => $document,
            'document_title' => 'order_confirmation_#' . $documentIdentifier . '.pdf',
            'document_store_name' => $order->uuid,
            //'user' => $user,
            'organisation_id' => $organisation->id,
            ], 200
        );
    }

    public function all($page = null, $limit = null)
    {
        $the_total = 0;

        $organisations = Organisation::where('is_rhs', 1)
            ->whereNotNull('rhs_no');

        if (isset($page, $limit)) {
            $org_count = $organisations->count();

            $organisations = $organisations->take($limit)->skip(($page * $limit) - $limit)->get();
        }

        $organisations = $organisations->orderBy('name', 'asc')->get();

        foreach ($organisations as $org) {
            $org->policy_numbers = $org->policyNumbers();
            $org->user_count = count($org->users);

            if (!isset($org->branch)) {
                $org->branch = $org->libertyBranch();
            }
        }

        $the_total = count(Organisation::all());

        if (isset($org_count)) {
            $the_total = $org_count;
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => $organisations,
            'total' => $the_total,
            ]
        );
    }

    public function show($organisation_id)
    {
        $organisation = Organisation::where('id', $organisation_id)
            ->where('is_rhs', 1)
            ->first();

        if (!$organisation) {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Something went wrong with fetching this RHS organisation',
                ], 200
            );
        }

        $organisation->policy_numbers = $organisation->policyNumbers();
        $organisation->user_count = count($organisation->users);

        if (!isset($organisation->branch)) {
            $organisation->branch = $organisation->libertyBranch();
        }

        $customer = Customer::where('affiliate_ref', $organisation->rhs_no)->first();

        $orders = Order::with('customer', 'documents.order.products')
            ->where('customer_id', $customer->id)
            ->where('processed', 1)
            ->orderBy('id', 'desc');

        $organisation->orders = $orders->get();

        $orderUuids = $orders->pluck('uuid');

        $organisation->documents = PolicyDocumentModel::where('organisation_id', $organisation->id)
            ->whereIn('document_store_name', $orderUuids)
            ->get();

        return Response::json(
            [
            'response' => 'success',
            'data' => $organisation,
            ]
        );
    }

    public function getOrganisationName($organisation_id)
    {
        $organisation = Organisation::where('id', $organisation_id)->first();

        if (!$organisation) {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to find the organisation',
                ], 200
            );
        } else {
            return Response::json(
                [
                'response' => 'success',
                'data' => $organisation,
                ]
            );
        }
    }

    public function getDocumentInformation(Request $request)
    {
        $customer = Customer::find($request->get('customer_id'));
        $document = Document::where('order_id', $request->get('order_id'))->first();
        $order = Order::with('customer')->where('id', $request->get('order_id'))->first();
        $customerOrder = CustomerOrder::where('order_id', $request->get('order_id'))->get();
        $hasInsurance = $request->get('has_insurance');

        $pdf_data = $request->get('pdf_data');

        $final_price = 0;

        foreach ($customerOrder as $product) {
            $final_price += $product->price;
        }

        $attachments = [
            'pdf' => [
                'StringValue' => $pdf_data,
                'DataType' => 'String',
            ],
        ];

        if ($hasInsurance) {
            $this->mail->queue(
                $customer->email, $customer->full_name, 'Purchase Confirmation',
                'emails.rhs.purchase-confirmation', [
                    'order' => $order,
                    'customer' => $customer,
                    'customerOrder' => $customerOrder,
                    'membership_date' => $request->get('membership_date'),
                    'has_insurance' => true,
                ], $attachments
            );
        } else {
            $this->mail->queue(
                $customer->email, $customer->full_name, 'Purchase Confirmation',
                'emails.rhs.purchase-confirmation', [
                    'order' => $order,
                    'customer' => $customer,
                    'membership_date' => $request->get('membership_date'),
                    'has_insurance' => false,
                ]
            );
        }

        return Response::json(
            [
            'response' => 'success',
            'message' => 'RHS Document Generator',
            'customer' => $customer,
            'document' => $document,
            'order' => $order,
            'membership_date' => $request->get('membership_date'),
            'attachments' => $attachments,
            ], 200
        );
    }
}
