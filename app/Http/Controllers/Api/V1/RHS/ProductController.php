<?php

namespace App\Http\Controllers\Api\V1\RHS;

use App\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use App\Models\RHS\Product;
use App\Models\RHS\ProductCategory;

class ProductController extends BaseController
{
    // public function index()
    // {
    //     $products = Product::with('type')->get();

    //     return response()->json([
    //         'response' => 'success',
    //         'data' => $products,
    //         'total' => count($products),
    //     ]);
    // }

    public function categories(Request $request)
    {
        $categories = ProductCategory::productOptions(
            [
            'affiliation' => $request->get('customer_type_id'),
            ]
        );

        return response()->json(
            [
            'response' => 'success',
            'data' => $categories,
            'total' => count($categories),
            ]
        );
    }

    public function descriptions(Request $request)
    {
        $descriptions = Product::getAllProductDescriptions(
            [
            'affiliation' => $request->get('customer_type_id'),
            ]
        );

        return response()->json(
            [
            'response' => 'success',
            'data' => $descriptions,
            'total' => count($descriptions),
            ]
        );
    }

    public function products(Request $request)
    {
        $products = Product::getAllProducts(
            [
            'affiliation' => $request->get('customer_type_id'),
            ]
        );

        return response()->json(
            [
            'response' => 'success',
            'data' => $products,
            'total' => count($products),
            ]
        );
    }
}
