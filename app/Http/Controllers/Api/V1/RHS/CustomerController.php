<?php

namespace App\Http\Controllers\Api\V1\RHS;

use App\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Models\Mailqueue;
use App\Models\RHS\Customer;
use App\Models\RHS\CustomerOrder;
use App\Models\RHS\Document;
use App\Models\RHS\Order;
use App\Models\RHS\Product;

class CustomerController extends BaseController
{

    public function __construct(Mailqueue $mailqueue)
    {
        $this->mail = $mailqueue;
    }

    public function index()
    {
        $customers = Customer::with('type', 'organisation')->get();

        return response()->json(
            [
            'response' => 'success',
            'data' => $customers,
            'total' => count($customers),
            ]
        );
    }

    public function show($customer_id)
    {
        $customer = Customer::with('type', 'orders', 'orders.products')
            ->where('id', $customer_id)
            ->first();

        if ($customer) {
            return response()->json(
                [
                'response' => 'success',
                'data' => $customer,
                ]
            );
        }

        return response()->json(
            [
            'response' => 'error',
            'message' => 'Customer not found',
            ]
        );
    }

    public function getCustomerOrder($order_id)
    {
        $customer = Order::with('customer', 'products', 'documents')
            ->where('id', $order_id)
            ->first();

        if ($customer) {
            return response()->json(
                [
                'response' => 'success',
                'data' => $customer,
                ]
            );
        }

        return response()->json(
            [
            'response' => 'error',
            'message' => 'Customer not found',
            ]
        );
    }

    public function createCustomerOrder($order_data)
    {
        $orderData = json_decode($order_data);
        $customer = Customer::find($orderData->customer_id);

        $uuidString = Str::uuid()->toString();
        $order = $customer->orders()->create(
            [
            'uuid' => $uuidString,
            ]
        );

        Order::where('uuid', $uuidString)
            ->update(
                [
                'customer_id' => $orderData->customer_id,
                'processed' => 1,
                'is_manually_created_rr' => 1,
                ]
            );

        $final_product = Product::setProductCoverage($orderData->products);

        $order->products()->sync($final_product);

        Document::createDocument($order->id, $orderData->customer_id, $customer->customer_type_id);

        $document = Document::where('order_id', $order->id)->first();
        $customers = Customer::with('type')->where('id', $orderData->customer_id)->first();

        foreach ($customer->orders as $order) {
            foreach ($order->products as $product) {
                if ($product->product_type_id == 1 || $product->product_type_id == 2) {
                    $customerOrder = CustomerOrder::where(
                        [
                        'order_id' => $order->id,
                        'product_id' => $product->id,
                        ]
                    )->first();

                    $membership = date('dS', strtotime($customerOrder->coverage_start)) . ' of ' . date(
                        'M Y',
                        strtotime($customerOrder->coverage_start)
                    ) . " To " . date(
                        'dS',
                        strtotime($customerOrder->coverage_end)
                    ) . ' of ' . date(
                        'M Y',
                        strtotime($customerOrder->coverage_end)
                    );
                }
            }
        }

        // Check if has insurance
        $hasInsurance = false;

        foreach ($order->products as $product) {
            if ($product->type->is_insurance == 1) {
                $hasInsurance = true;
            }
        }

        $emailData = [
            'order' => $order,
            'customer' => $customer,
            'membership_date' => isset($membership)
                ? $membership
                : null,
            'has_insurance' => $hasInsurance,
        ];

        $this->mail->queue(
            $customer->email,
            $customer->full_name,
            'Purchase Confirmation',
            'emails.rhs.purchase-confirmation',
            $emailData
        );

        return response()->json(
            [
            'response' => 'success',
            'data' => $orderData->products,
            'document' => $document,
            'customer' => $customers,
            ]
        );
    }

    public function update(Request $request, $customer_id)
    {
        $data = $request->all();

        $products = $data['products'];
        unset($data['products']);

        $customer = Customer::find($customer_id);
        $customer->fill($data);
        $customer->save();
        $order = $customer->orders()->first();
        $order->products()->sync($products);

        return response()->json(
            [
            'response' => 'success',
            ]
        );
    }
}
