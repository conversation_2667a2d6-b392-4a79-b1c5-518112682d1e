<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\OrganisationSubmissionLink;
use Illuminate\Support\Facades\Response;

class OrganisationSubmissionLinksController extends BaseController
{
    public function index()
    {
        $submissionLinks = OrganisationSubmissionLink::query()
            ->with('broker')
            ->orderBy('created_at', 'asc')
            ->get();

        return Response::json(
            [
            'status' => 'success',
            'data' => $submissionLinks,
            ]
        );
    }

    public function store(Request $request)
    {
        $data = $request->except('_token');

        OrganisationSubmissionLink::create(
            $data + [
                'revoked_at' => null,
            ]
        );

        return Response::json(
            [
            'status' => 'success',
            'message' => 'Submission link created.',
            ]
        );
    }

    public function show($id)
    {
        $submissionLink = OrganisationSubmissionLink::query()
            ->where('_id', $id)
            ->with('broker')
            ->first();

        if (!$submissionLink) {
            return Response::json(
                [
                'status' => 'error',
                'message' => 'Submission link not found.',
                ]
            );
        }

        return Response::json(
            [
            'status' => 'success',
            'data' => $submissionLink,
            ]
        );
    }

    public function revoke($id)
    {
        OrganisationSubmissionLink::where('_id', $id)
            ->update([
                'revoked_at' => [
                    "date" => Carbon::now()->toISOString()
                ],
            ]);

        return Response::json(
            [
            'status' => 'success',
            'message' => 'Submission link revoked.',
            ]
        );
    }
}
