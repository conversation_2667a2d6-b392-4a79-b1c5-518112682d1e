<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\CourseImage;
use Illuminate\Support\Facades\Response;

class LearningCourseImageController extends BaseController
{
    /**
     * Delete Course
     *
     * @param $id
     *
     * @return mixed
     */
    public function destroy($type, $image)
    {
        $course_image_types = [
            'featured' => 'Featured Picture',
            'accreditation-logo' => 'Accreditation Logo',
        ];

        $course_image_type = (in_array($type, array_keys($course_image_types)))
            ? $course_image_types[$type]
            : 'Course Image';

        $course_image = CourseImage::where('type', '=', $type)->where('image', '=', $image);

        if ($course_image->first()) {
            if ($course_image->delete()) {
                $response = [
                    'response' => 'success',
                    'message' => sprintf('The specified %s was successfully deleted', $course_image_type),
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => sprintf('The specified %s could not be deleted', $course_image_type),
                ];
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => sprintf('The specified %s could not be found', $course_image_type),
            ];
        }

        return Response::json($response);
    }
}
