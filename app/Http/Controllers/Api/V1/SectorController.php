<?php
/**
 * Created by PhpStorm.
 * User: chrisvickers
 * Date: 09/01/15
 * Time: 11:51
 */

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use App\Models\Sector;

class SectorController extends BaseController
{


    public function all()
    {
        $data = Sector::all();

        if (isset($data)) {
            return Response::json(
                [
                'response' => 'success',
                'data' => $data->toarray(),
                ]
            );
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'Could not return sectors',
            ]
        );

    }

    public function options()
    {
        return Response::json(
            Sector::pluck(
                'handle',
                'id'
            )->all()
        );
    }

}
