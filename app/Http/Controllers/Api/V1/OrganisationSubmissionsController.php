<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\Activity;
use Artisaninweb\SoapWrapper\Facades\SoapWrapper;
use App\Http\Controllers\BaseController;
use App\Models\Cati;
use Exception;
use Illuminate\Support\Facades\Hash;
use App\Models\Mailqueue;
use App\Models\Organisation;
use App\Models\OrganisationPolicy;
use App\Models\OrganisationSubmission;
use App\Models\OrganisationSubmissionLink;
use App\Models\PolicyDocumentModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use App\Models\User;
use Illuminate\Support\Str;

class OrganisationSubmissionsController extends BaseController
{
    public function __construct(Mailqueue $mailqueue)
    {
        $this->mail = $mailqueue;
    }

    public function index()
    {
        $submissions = OrganisationSubmission::query()
            ->where('status', '!=', 'approved')
            ->orderBy('created_at', 'asc')
            ->get();

        foreach ($submissions as &$submission) {
            $submission->link = OrganisationSubmissionLink::query()
                ->where('_id', $submission['submission_link_id'])
                ->with('broker')
                ->first();
        }

        return Response::json(
            [
            'status' => 'success',
            'data' => $submissions,
            ]
        );
    }

    public function store(Request $request)
    {
        $data = $request->except('_token');
        $data['inception_date_of_cover'] = date('Y-m-d H:i:s', $data['inception_date_of_cover']);
        $data['expiry_date_of_cover'] = date('Y-m-d H:i:s', $data['expiry_date_of_cover']);

        $submission = OrganisationSubmission::create(
            $data + [
                'status' => 'pending',
            ]
        );

        $submissionLink = OrganisationSubmissionLink::query()
            ->where('_id', $submission->submission_link_id)
            ->with('broker')
            ->first();

        $submission->submissionUrl = config('app.admin_frontend') . '/do-facilities/pending-submissions/' . $submission->_id;
        $submission->organisationName = $submissionLink->facility_name;

        $this->mail->queue(
            '<EMAIL>', 'New Client Org Submission',
            'Risk Reduce, New Client Org Submission Notification', 'emails.risk-reduce.new-org-submission',
            $submission
        );

        return Response::json(
            [
            'status' => 'success',
            'message' => 'Submission created. We will get in touch with you after we have processed your information.',
            ]
        );
    }

    public function show($id)
    {
        $submission = OrganisationSubmission::query()
            ->where('_id', $id)
            ->with('branch')
            ->first();

        if (!$submission) {
            return Response::json(
                [
                'status' => 'error',
                'message' => 'Submission not found.',
                ]
            );
        }

        $submission->link = OrganisationSubmissionLink::query()
            ->where('_id', $submission['submission_link_id'])
            ->with('broker')
            ->first();

        return Response::json(
            [
            'status' => 'success',
            'data' => $submission,
            ]
        );
    }

    public function status(Request $request, $id, $status)
    {
        $submission = OrganisationSubmission::find($id);

        if (!$submission) {
            return Response::json(
                [
                'status' => 'error',
                'message' => 'Submission not found.',
                ]
            );
        }

        if ($status != 'approved') {
            $submission->delete();

            return Response::json(
                [
                'status' => 'success',
                'message' => 'Submission status rejected.',
                ]
            );
        }

        $orgEmailExists = Organisation::where('email', $submission->email)->first();
        $orgUserExists = User::where('email', $submission->admin['email'])->first();

        if ($orgEmailExists || $orgUserExists) {
            return Response::json(
                [
                'status' => 'error',
                'message' => 'An organisation/user email conflict has occurred.',
                ]
            );
        }

        $submission->update(
            [
            'status' => 'approved',
            ]
        );

        // Create organisation and client user
        $organisation = $this->createOrganisation($submission);
        $user = $this->createUser($request, $submission, $organisation);

        $message = sprintf(
            'Submission status approved. View &nbsp; <strong> %s </strong>\'s organisation record by clicking &nbsp; <a href="/organisation/%s"> here </a>.',
            $organisation->name,
            $organisation->id
        );
        return Response::json(
            [
            'status' => 'success',
            'message' => $message,
            ]
        );
    }

    public function createOrganisation($submission)
    {
        $data = $submission;

        $data->safetymedia_url = substr(Str::uuid()->toString(), 0, 4);

        $submissionLink = OrganisationSubmissionLink::query()
            ->where('_id', $submission->submission_link_id)
            ->with('broker')
            ->first();
        $organisation = Organisation::create(
            [
            'name' => $data->name,
            'email' => $data->email,
            'phone' => $data->phone,
            'description' => $data->description,
            'liberty_branch' => $data->liberty_branch,
            'address_line_1' => $data->address_line_1,
            'address_line_2' => $data->address_line_2,
            'postcode' => $data->postcode,
            'country' => $data->country,
            'logo' => $data->logo
                ?: '',
            'broker_id' => $submissionLink->broker_id
                ?: null,
            'sector' => 3,
            'loss_ratio' => $submissionLink->loss_ratio
                ?: null,
            ]
        );

        // Policy
        OrganisationPolicy::create(
            [
            'organisation_id' => $organisation->id,
            'policy_type_id' => 6,
            'policy_number' => $submission->policy_number,
            'inception_date_of_cover' => $submission->inception_date_of_cover,
            'expiry_date_of_cover' => $submission->expiry_date_of_cover,
            'premium' => $submission->premium,
            'loss_ratio' => $submissionLink->loss_ratio
            ?: null,
            'renewal' => (new \DateTime())->setTimestamp(1),
            ]
        );

        PolicyDocumentModel::updateOrCreate(
            [
            'id' => null,
            ], [
            'name' => '',
            'description' => '',
            'document_title' => '',
            'document_store_name' => '',
            'organisation_id' => $organisation->id,
            'document_type_id' => 3,
            ]
        );

        return $organisation;
    }

    public function createUser(Request $request, $submission, $organisation)
    {
        $data = $submission;

        $userData = [
            'email' => $data->admin['email'],
            'password' => Hash::make(uniqid()),
            'first_name' => $data->admin['first_name'],
            'last_name' => $data->admin['last_name'],
            'address_line_1' => '',
            'address_line_2' => '',
            'postcode' => '',
            'country' => '',
            'manager' => 1,
            'branch' => $data->liberty_branch?: 0,
            'branch_name' => '',
            'organisation_id' => $organisation->id,
            'activation_code' => substr(Str::uuid()->toString(), 0, 4),
            'activation_code_expires' => time() + (14 * 24 * 60 * 60), // 14 days
            'croner_access' => 1,
            'safetymedia_access' => 1,
            'astutis_access' => 0,
            'safetymedia_password' => uniqid(),
            'c_live_access' => 0,
            'triton_access' => 0,
            'branch_id' => $data->liberty_branch?: 0,
            'file' => null,
            'original_filename' => null,
            'is_submission_created' => 0,
            'phone' => $data->admin['phone'],
            'jobtitle' => $data->admin['job_title'],
            'secondary_contact' => 0,
        ];

        if (strtolower($request->get('cati_status')) == 'active' && $request->get('manager')) {
            $organisation = Organisation::find($request->get('organisation_id'));

            if ($organisation->cati_status != 'active' || !$organisation->cati_id) {
                return Response::json(
                    [
                    'response' => 'error',
                    'message' => '<b>User Creation Failed!</b><br/><i>Cati has not been provisioned for this user\'s organisation.</i><br/>If you wish to generate a user account with access to Cati, please enable Cati for this user\'s organisation first.',
                    ], 200
                );
            }

            $userData['cati_status'] = $request->get('cati_status');

            try {
                $cati = new Cati();

                $response = $cati->createClientUser(
                    [
                    'ClientID' => $organisation->cati_id,
                    'Forename' => $userData['first_name'],
                    'Surname' => $userData['last_name'],
                    'EmailAddress' => $userData['email'],
                    'Username' => $userData['email'],
                    ]
                );

                if ($response && $response->StatusCode == 'CU00') {
                    $userData['cati_id'] = $response->UserID;
                } else {
                    return Response::json(
                        [
                        'response' => 'error',
                        'message' => sprintf(
                            $this->catiErrorMessage, $response->StatusCode,
                            $response->StatusDescription
                        ),
                        ]
                    );
                }
            } catch (Exception $e) {
                return Response::json(
                    [
                    'response' => 'error',
                    'message' => sprintf($this->catiErrorMessage, $e->getCode(), $e->getMessage()),
                    ]
                );
            }
        }

        $user = User::create($userData);
        $isProdEnv=(config()->has('app.isProd') && config('app.isProd'));

        // if ($isProdEnv) {
        //     SoapWrapper::add(
        //         function ($service) {
        //             $service
        //             ->wsdl('https://libertymutual.safetylearning.co.uk/api/1.3/UserManagement?wsdl');
        //     });
        // }

        $organisation = Organisation::find($user->organisation_id);

        $departmentName = $organisation->name;

        $sm_disabled = ($user->safetymedia_access == '0')
            ? '1'
            : '0';
        $admin_level = ($user->manager == '1')
            ? 'clientadmin'
            : 'user';

        if ($organisation->tpid == '' || is_null($organisation->tpid)) {
            $sm_account = config('app.sm.org_id_prefix') . $organisation->id;
        } else {
            $sm_account = $organisation->tpid;
        }

        $data1 = [
            'serviceCredentials' => [
                'username' => config('app.sm.username'),
                'password' => config('app.sm.password'),
                'account' => $sm_account,
            ],
            'users' => [
                'item' => [
                    'id' => config('app.sm.user_id_prefix') . $user->id,
                    'username' => $user->email,
                    'password' => $user->safetymedia_password,
                    'forename' => $user->first_name,
                    'surname' => $user->last_name,
                    'email' => $user->email,
                    'disabled' => $sm_disabled,
                    'departmentName' => $departmentName,
                    'preferredLanguage' => 'English',
                    'adminLevel' => $admin_level,
                ],
            ],
        ];

        // Using the added service
        // if ($isProdEnv) {
        //     SoapWrapper::service(
        //         'users', function ($service) use ($data1) {
        //             $add_user_to_sm = $service->call('AddUser', [$data1]);

        //         }
        //     );

        //     SoapWrapper::add(
        //         function ($servicetraining) {
        //             $servicetraining
        //                 ->name('tra')
        //                 ->wsdl('https://libertymutual.safetylearning.co.uk/api/1.3/TrainingManagement?wsdl');
        //         }
        //     );
        // }

        $data2 = [
            'serviceCredentials' => [
                'username' => config('app.sm.username'),
                'password' => config('app.sm.password'),
                'account' => $sm_account,
            ],
            'userTrainingPlans' => [
                'item' => [
                    'userID' => config('app.sm.user_id_prefix') . $user->id,
                    'trainingPlanID' => $sm_account,
                ],
            ],
        ];

        // Using the added service
        
        // if ($isProdEnv) {
        //     SoapWrapper::service(
        //         'tra', function ($servicetraining) use ($data2) {
        //             $add_user_to_tra = $servicetraining->call('AddUserToTrainingPlan', [$data2]);
        //         }
        //     );
        // }

        $this->mail->queue(
            $user->email, $user->fullName(), 'Risk Reduce, Welcome to Risk Reduce',
            'emails.auth.welcome', $user
        );

        if ($user->id) {
            $c_access = User::find($user->id)->croner_access;
            $sm_access = User::find($user->id)->safetymedia_access;

            $sm_disabled = ($user->safetymedia_access == '0')
                ? '1'
                : '0';
            $admin_level = ($user->manager == '1')
                ? 'clientadmin'
                : 'user';

            $this->log(
                $user, 'Create', 'User Created', $request->header('session_ip'),
                $request->header('session_agent')
            );

            // Check for croner differences and record in logs
            if (isset($user->croner_access) && $user->croner_access == $c_access) {
                $this->log(
                    $user, 'Croner Access', $user->croner_access, $request->header('session_ip'),
                    $request->header('session_agent')
                );
            }


            // Check for safety media differences
            if (isset($user->safetymedia_access) && $user->safetymedia_access == $sm_access) {
                $this->log(
                    $user, 'Safety Media Access', $user->safetymedia_access, $request->header('session_ip'),
                    $request->header('session_agent')
                );
            }

            if ($request->has('login_type') && $request->get('login_type') == 'broker-user') {
                $user->org = $user->organisation;
                $user->request_id = $user->id;

                $this->mail->queue(
                    config('app.admin_frontend'), 'Client User Created',
                    'Risk Reduce, Client User Creation Notification', 'emails.users.created', $user
                );
            }

            return Response::json(
                [
                'response' => 'success',
                'message' => 'User created successfully.',
                'data' => $user->id,
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to create user.',
                ], 200
            );
        }
    }

    private function log($user, $action, $description, $session_ip = '127.0.0.1', $session_agent = 'No UserAgent')
    {
        $activity = new Activity();
        $activity->content_id = $user->id;
        $activity->user_id = (string)$user->id;
        $activity->content_type = 'User';
        $activity->action = $action;
        $activity->description = $description;
        $activity->details = 'Username: ' . $user->fullName();
        $activity->ip_address = $session_ip;
        $activity->user_agent = $session_agent;
        $activity->save();
    }
}
