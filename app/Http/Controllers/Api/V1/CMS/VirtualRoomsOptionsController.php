<?php

namespace App\Http\Controllers\Api\V1\CMS;

use App\Http\Controllers\BaseController;
use App\Models\Cms;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;

class VirtualRoomsOptionsController extends BaseController
{
    private $business;

    public function __construct(Request $request)
    {
        $this->business = ($request->has('business') && $request->get('business') === 'lmre')
            ? 'lmre'
            : 'lsm';
    }

    public function options()
    {
        // Get liberty footer from cms
        if (Cache::has($this->business . '_cms_vr_options')) {
            $cmsVrOptions = Cache::get($this->business . '_cms_vr_options');
        } else {
            $cmsVrOptions = json_decode(Cms::get('workspaces/' . $this->getCmsConfig('lets_talk_workspace') . '/content-types/' . $this->getCmsConfig('options_content_type') . '/content-entries?joined=true&query={"status":"publish","operator":"=","locale_id":1}'));
            Cache::put($this->business . '_cms_vr_options', $cmsVrOptions, 1440);
        }

        $optionsArray = [];

        if ($cmsVrOptions && $cmsVrOptions->data) {
            foreach ($cmsVrOptions->data as $entry) {
                $optionsArray[$entry->name] = [
                    'intro' => $entry->{$this->getCmsConfig('options_intro')},
                    'heading' => $entry->{$this->getCmsConfig('options_heading')},
                ];
            }
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => [
                    'options' => $optionsArray,
                ],
            ]
        );
    }

    // wrapper function to get config based on business
    private function getCmsConfig($config)
    {
        return config('app.cms.' . $this->business . '.' . $config);
    }
}
