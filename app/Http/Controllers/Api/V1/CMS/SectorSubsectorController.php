<?php

namespace App\Http\Controllers\Api\V1\CMS;

use App\Http\Controllers\BaseController;
use App\Models\Cms;
use Illuminate\Support\Facades\Response;

class SectorSubsectorController extends BaseController
{

    public function __construct()
    {
    }

    public function sectorsList()
    {
        $data = ['status' => 'draft', 'operator' => '=', 'order' => ['slug' => 'asc']];
        $data = json_encode($data);
        $url = 'workspaces/5d36de5bbf3b21131941e7e2/content-types/5d3f1469bf3b2175ae3b88d9/content-entries?query=' . $data; //on Prod
        $url = 'workspaces/5d36de5bbf3b21131941e7e2/content-types/5d3f1469bf3b2175ae3b88d9/content-entries?query=' . $data; // For testing
        $sectorsData = Cms::get($url);
        if (isset($sectorsData)) {
            return Response::json($sectorsData);
        }
        return false;
    }

    public function subSectorsList($sector)
    {
        $data = ['status' => 'draft', 'operator' => '='];
        $data = [
            'region_1564412684538' => ['slug' => 'uk'],
            'sector_1564412648774' => ['slug' => $sector],
            'operator' => '=',
            'order' => ['slug' => 'asc'],
        ];
        $data = json_encode($data);
        $url = 'workspaces/5d36de5bbf3b21131941e7e2/content-types/5d3edba7bf3b2175613dcfb2/content-entries?joined=true&query=' . $data; //on Prod
        $url = 'workspaces/5d36de5bbf3b21131941e7e2/content-types/5d3edba7bf3b2175613dcfb2/content-entries?joined=true&query=' . $data; // For testing
        //dd($url);
        $subsectorData = Cms::get($url);
        /* echo "<pre>"; */
        $callback = [];
        /* $keys = [];
        $sorted = []; */
        if (isset($subsectorData)) {
            $subSector = json_decode($subsectorData);

            foreach ($subSector->data as $product) {
                if (is_array($product->subsector_1564412664986) && count($product) > 0) {
                    $sector = [
                        'name' => $product->subsector_1564412664986[0]->name,
                        'slug' => $product->subsector_1564412664986[0]->slug,
                    ];
                    if (!in_array($sector, $callback)) {
                        array_push($callback, $sector);
                        /* array_push($keys, $sector['slug']); */
                    }
                }
            }
            /* asort($keys);

            foreach($keys as $key) {
                $selected = array_search($key, array_column($callback, 'slug'));
                array_push($sorted, $callback[$selected]);
            } */
            return Response::json(['data' => $callback]);
        }
        return false;
    }
}
