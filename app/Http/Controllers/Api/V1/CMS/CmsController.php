<?php

namespace App\Http\Controllers\Api\V1\CMS;

use App\Http\Controllers\BaseController;
use App\Models\Cms;

class CmsController extends BaseController
{
    public static $business = 'lsm';

    /**
     * Returns the office of a certain representative base on id provided
     *
     * @param string $personId Id that corresponds to people profile _id
     * @return array
     */
    public function getOfficeDetails(string $personId)
    {
        $url = 'workspaces/' . $this->getCmsConfig('workspace_id') . '/content-types/' . $this->getCmsConfig('people_content_type') . '/content-entries/' . $personId;
        $representative = json_decode(Cms::get($url), true);
        $officeId = $representative[$this->getCmsConfig('people_profile_office')][0]['$oid'];

        $officeUrl = 'workspaces/' . $this->getCmsConfig('workspace_id') . '/content-types/' . config('app.cms.office_info_id') . '/content-entries/' . $officeId;
        $officeDetails = json_decode(Cms::get($officeUrl), true);

        return response()->json($officeDetails[$this->getCmsConfig('people_profile_office_title')]);
    }

    private function getCmsConfig($config)
    {
        return config('app.cms.' . self::$business . '.' . $config);
    }
}
