<?php

namespace App\Http\Controllers\Api\V1\CMS;

use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use App\Models\ClientEnquiries;
use App\Models\CMS\Product;
use App\Models\FileUpload;
use App\Models\Mailqueue;
use Illuminate\Support\Facades\Response;
use Uuid;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ProductController extends BaseController
{

    public function __construct(Mailqueue $mailqueue, FileUpload $fileupload)
    {
        $this->mailqueue = $mailqueue;
        $this->files = $fileupload;
    }

    public function getAllProducts()
    {
        $cacheVariable = "allProductsForRrAdmin";
        if (Cache::has($cacheVariable)) {
            $allProducts = Cache::get($cacheVariable);
        } else {
            $query = json_encode([]);
            $currentRegion = 'uk';
            $products = Product::getProducts(
                $currentRegion, [
                'query' => $query,
                'allow_dupes' => true,
                ]
            );

            $allProducts = [];

            foreach ($products as $product) {
                $productData = (object)[];
                $productData->name = $product->name;
                $productData->slug = $product->slug;
                $productData->sector = isset($product->sector_1564412648774[0]->name)
                    ? $product->sector_1564412648774[0]->name
                    : '';
                $productData->sectorslug = isset($product->sector_1564412648774[0]->slug)
                    ? $product->sector_1564412648774[0]->slug
                    : '';
                $productData->subsector = isset($product->subsector_1564412664986[0]->name)
                    ? $product->subsector_1564412664986[0]->name
                    : '';
                $productData->subsectorslug = isset($product->subsector_1564412664986[0]->slug)
                    ? $product->subsector_1564412664986[0]->slug
                    : '';
                $allProducts[] = $productData;
            }
            Cache::put($cacheVariable, $allProducts, 1440);
        }
        return Response::json($allProducts);
    }

    public function productList($currentRegion, $sector_slug, $slug)
    {
        $queryData = [
            //'status' => 'publish',
            'region_1564412684538' => [
                'slug' => urlencode($currentRegion),
            ],
            'operator' => '=',
        ];

        if (isset($sector_slug)) {
            $queryData['sector_1564412648774'] = [
                'slug' => urlencode($sector_slug),
            ];
        }

        if (isset($slug)) {
            $queryData['subsector_1564412664986'] = [
                'slug' => urlencode($slug),
            ];
        }

        $query = json_encode($queryData);

        //print_r($queryData); exit;

        // $productsData = json_decode(Cms::get('workspaces/'.config('app.cms.workspace_id').'/content-types/'.config('app.cms.products_content_type').'/content-entries?joined=true&query='.$query.'&page=1&limit=99999'));
        $productsData = Product::getProducts(
            $currentRegion, [
            'query' => $query,
            'allow_dupes' => true,
            ]
        );

        $products = [];

        // print_r($productsData); exit;
        foreach ($productsData as &$product) {
            $alreadyExists = false;
            $productSectorSubsectorMatch = false;
            if (!empty($product->sector_1564412648774) 
                && $product->sector_1564412648774[0]->slug == $sector_slug 
                && !empty($product->subsector_1564412664986) 
                && isset($product->subsector_1564412664986[0]->slug) 
                && $product->subsector_1564412664986[0]->slug == $slug
            ) {
                $product->sector_slug = $sector_slug;
                $product->subsector_slug = $slug;
                $productSectorSubsectorMatch = true;
            }
            foreach ($products as $existingProduct) {
                if ($existingProduct->_id == $product->_id) {
                    $alreadyExists = true;
                }
            }
            if (!$alreadyExists && $productSectorSubsectorMatch) {
                $products[] = $product;
            }
        }

        if (!empty($products)) {
            // usort($products, function ($a, $b) {
            //     return $a->product_name_1564400540191 <=> $b->product_name_1564400540191;
            // });


            return [
                'products' => $products,
                'slug' => $slug,
            ];
        }

        return false;
    }

    /**
     * Prepare product for download
     */
    public function prepareProductJson($region_slug, $sector_slug, $subsector_slug, $product_slug)
    {
        $region = $region_slug;
        $product_binder = [];


        $data = [
            'slug' => $product_slug,
        ];

        $queryData = [
            'slug' => urlencode($product_slug),
            'operator' => '=',
        ];

        if (isset($sector_slug)) {
            $queryData['sector_1564412648774'] = [
                'slug' => urlencode($sector_slug),
            ];
        }

        if (isset($subsector_slug)) {
            $queryData['subsector_1564412664986'] = [
                'slug' => urlencode($subsector_slug),
            ];
        }

        if (isset($sector_slug) || isset($subsector_slug)) {
            $queryData['region_1564412684538'] = [
                'slug' => urlencode($region),
            ];
        }

        if (!isset($sector_slug) && !isset($subsector_slug)) {
            $queryData['variation'] = 'default';
            $queryData['locale_id'] = 1;
        }

        $queryData['locale_id'] = 1;

        // get product
        $product_info = Product::searchProducts($region, ['query' => $queryData])[0];
        $product_info->product_slug = $product_slug;
        $product_info->product_name = $product_info->name;
        $product_info->sector_slug = $sector_slug;
        $product_info->subsector_slug = $subsector_slug;
        $product_info->region_slug = $region;
        $product_info->timestamp = Carbon::now('UTC')->toDateTimeString();

        // check against uniqueness
        if (count($product_binder) > 0) {
            $product_check = array_search($product_slug, array_column($product_binder, 'product_slug'));

            if ($sector_slug) {
                $sector_check = array_search($sector_slug, array_column($product_binder, 'sector_slug'));
            }

            if ($subsector_slug) {
                $subsector_check = array_search($subsector_slug, array_column($product_binder, 'subsector_slug'));

                // if array search returns 0 it actually means first key in array
                // check against false explicity
                if ($product_check === false && $sector_check !== false && $subsector_check !== false) {
                    array_push($product_binder, $product_info);
                }
            } elseif ($sector_slug) {
                if ($product_check === false && $sector_check !== false) {
                    array_push($product_binder, $product_info);
                }
            } else {
                if ($product_check === false) {
                    array_push($product_binder, $product_info);
                }
            }
        } else {
            array_push($product_binder, $product_info);
        }

        return Response::json($product_binder);
    }

    public function downloadProduct(Request $request)
    {
        $uses3 = false; // working randonly for some reason
        $info = $request->except('_token');

        $file_key = Str::uuid()->toString();
        $ra_url = config('app.riskappetite.url');
        $fileurl = sprintf('%spdf/rr/%s/lsm', $ra_url, $info['filename']);

        if ($uses3) {
            $file = $this->files->downloadFile($fileurl);
            $cloudname = 'client_products/exports/' . $file_key . '.pdf';
            if ($file && ($this->files->upload_excel($file, $cloudname))) {
                $fileurl = $this->files->link($cloudname, '2 days');
            }
        }

        if (isset($fileurl)) {
            // Send the email to the user who requested the surveys excel sheet download
            $this->mailqueue->queue(
                $info['email'], ($info['first_name'] . ' ' . $info['last_name']),
                'Risk Reduce - ' . $info['product_name'] . ' Product Sheet has been generated ',
                'emails.client.productsheet-download', [
                    'first_name' => $info['first_name'],
                    'last_name' => $info['last_name'],
                    'excel_download_link' => $fileurl,
                    'product_name' => $info['product_name'],
                ]
            );

            $this->logClientEvent($info);

            return Response::json('OK');
        }
    }

    private function logClientEvent($eventinfo)
    {
        $eventinfo = (object)$eventinfo;
        $match = [
            'user_id' => $eventinfo->user_id,
            'organisation_id' => $eventinfo->organisation_id,
            'product_slug' => $eventinfo->product_slug,
        ];
        $data = [
            'product_name' => $eventinfo->product_name,
            'organisation_name' => $eventinfo->organisation_name,
            'user_name' => $eventinfo->user_name,
            'enquiry_date' => $eventinfo->enquiry_date,
        ];
        ClientEnquiries::updateOrCreate($match, $data);
    }
}


