<?php

namespace Api\V1\Cronofy;

use App\Http\Controllers\BaseController;
use App\Models\Cronofy\Cronofy;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\Request;

class CronofyCallbackController extends BaseController
{

    public function __construct()
    {}

    public function callback(Request $request)
    {
        $data   = $request->all();
        $create = Cronofy::create($data);

        return Response::json([
            'status' => 'success',
            'message' => $create,
        ]); 

    }
}
