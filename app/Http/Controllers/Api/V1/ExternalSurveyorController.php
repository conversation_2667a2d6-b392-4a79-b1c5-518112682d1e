<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\Activity;
use App\Models\ApiKey;
use App\Traits\Has2FA;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\Encryption\DecryptException;
use App\Models\ExternalSurveyor;
use Illuminate\Support\Facades\Hash;
use App\Models\Mailqueue;
use App\Models\PreviousPassword;
use App\Traits\ChecksExpiredPassword;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ExternalSurveyorController extends BaseController
{
    use Has2FA, ChecksExpiredPassword;

    public function __construct(Mailqueue $mailqueue)
    {
        $this->mail = $mailqueue;
    }

    /**
     * Authenticate ExternalSurveyor
     *
     * @return mixed
     */
    public function auth(Request $request)
    {
        $email = $request->get('email');
        $password = $request->get('password');

        if ($email != null && $password != null) {
            try {
                $data = [
                    'email' => Crypt::decrypt($email),
                    'password' => Crypt::decrypt($password),
                ];
            } catch (DecryptException $e) {
                return Response::json(
                    [
                    'response' => 'error',
                    'message' => 'External Surveyor login failed',
                    ], 200
                );
            }

            try {
                /** @var LibertyUser $user */
                $user = ExternalSurveyor::where('email', '=', $data['email'])->firstOrFail();
                // banned!
                if ($user->attempts > 4) {
                    return Response::json(
                        [
                            'response' => 'error',
                            'message' => 'Account Temporarily Locked: You have exceeded the maximum number of login attempts for your account. <NAME_EMAIL> to unlock your account.',
                        ]
                    );
                }
            } catch (\Exception $e) {
                return Response::json(
                    [
                        'response' => 'error',
                        'message' => 'Wrong Username or Password.',
                    ]
                );
            }

            if (Auth::guard('external_surveyor')->validate($data)) {
                if ($user->activated != 0 && $this->isPasswordExpired($user)) {
                    return $this->getPasswordExpiredResponse();
                }

                if ($user->activated != 0) {
                    DB::update('update external_surveyors set attempts = 0 where email = ?', [$data['email']]);
                    if (!$user->secret) {
                        // generate a secret key for 2FA
                        $user->generateSecret();
                    }

                    return Response::json(
                        [
                        'response' => 'success',
                        'data' => $user,
                        ], 200
                    );
                } else {
                    return Response::json(
                        [
                        'response' => 'error',
                        'message' => 'The External Surveyor has not been activated',
                        ]
                    );
                }
            }
            DB::update('update external_surveyors set attempts = attempts + 1 where email = ?', [$data['email']]);
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Wrong Username or Password',
                ], 200
            );
        } else {
            DB::update('update external_surveyors set attempts = attempts + 1 where email = ?', [$data['email']]);
            return Response::json(
                [
                'response' => 'error',
                'message' => 'External Surveyor login failed',
                ], 200
            );
        }
    }

    /**
     * Update External Surveyor
     */

    public function update(Request $request)
    {
        $rules = [
            'first_name' => 'required',
            'last_name' => 'required',
            'email' => 'required',
            'phone' => 'required',
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json(
                [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
                ],
                200
            );
        } else {
            $external_surveyor = ExternalSurveyor::find($request->get('id'));

            if (isset($external_surveyor->id)) {
                $external_surveyor->first_name = $request->get('first_name');
                $external_surveyor->last_name = $request->get('last_name');
                $external_surveyor->email = $request->get('email');
                $external_surveyor->phone = $request->get('phone');
                $external_surveyor->external_survey_company_id = $request->get('external_survey_company_id');
                $external_surveyor->role = $request->get('role');
                $external_surveyor->claims_notification = $request->get('claims_notification');
                if (filter_var($request->get('can_pause_notifications', false), FILTER_VALIDATE_BOOLEAN)) {  
                    $external_surveyor->notification_paused_at = filter_var($request->get('notification_paused_at', false), FILTER_VALIDATE_BOOLEAN)
                        ? now()
                        : null;
                }
                $external_surveyor->save();

                return Response::json(
                    [
                    'response' => 'success',
                    'message' => 'The External Surveyor has been updated successfully',
                    ]
                );
            } else {
                return Response::json(
                    [
                    'response' => 'error',
                    'message' => 'Invalid External Surveyor',
                    ]
                );
            }
        }
    }

    /**
     * Get All ExternalSurveyors
     */
    public function all(Request $request, $page = 1, $limit = 10, $companyId = null)
    {
        $query = $request->get('search');
        $query = ($query)
            ? '%' . $query . '%'
            : '';

        $external_surveyors = ($query)
            ? ExternalSurveyor::where(
                function ($q) use ($query) {
                    $q->where('email', 'LIKE', $query)->orWhere('first_name', 'LIKE', $query)->orWhere(
                        'last_name', 'LIKE',
                        $query
                    );
                }
            )
            : ExternalSurveyor::query();

        if ($companyId) {
            $external_surveyors = $external_surveyors->where('external_survey_company_id', $companyId);
        }
        
        $external_surveyors = $external_surveyors->take(
            $limit
        )->skip(
            ($page * $limit) - $limit
        )->with('externalSurveyCompany')->get();

        foreach ($external_surveyors as $external_surveyor) {
            $external_surveyor->external_survey_company;
            unset($external_surveyor->external_survey_company_id);
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => $external_surveyors,
            'total' => count(ExternalSurveyor::all()),
            ]
        );
    }

    /**
     * Authenticate User
     *
     * @return mixed
     */
    public function mobileauth(Request $request)
    {
        if ($request->has('api_key')) {
            $api_key = $request->get('api_key');
            if (count(ApiKey::where('api_key', '=', $api_key)->get()) > 0) {
                return Response::json(
                    [
                    'response' => 'success',
                    'message' => 'logged in',
                    ],
                    200
                );
            } else {
                return Response::json(
                    [
                    'response' => 'error',
                    'message' => 'Unable to login member',
                    ],
                    200
                );
            }
        }
        $email = $request->get('email');
        $password = $request->get('password');

        if ($email != null && $password != null) {
            try {
                $data = [
                    'email' => Crypt::decrypt($email),
                    'password' => Crypt::decrypt($password),
                ];
                // $data = [
                //     'email'     => $email,
                //     'password'     => $password
                // ];
            } catch (DecryptException $e) {
                return Response::json(
                    [
                    'response' => 'error',
                    'message' => 'Unable to login member',
                    ],
                    200
                );
            }


            if (Auth::external_surveyor()->validate($data)) {
                $user = ExternalSurveyor::where('email', '=', $data['email'])->firstOrFail();
                if ($user->activated != 0) {
                    //Audit
                    $this->log(
                        $user, 'Login', 'User Login', $request->header('session_ip'),
                        $request->header('session_agent')
                    );
                    $user_id = $user->id;
                    $key_data = [
                        'user_id' => $user_id,
                        'api_key' => Str::uuid()->toString(),
                        'user_type' => 'external-surveyor',
                    ];
                    ApiKey::create($key_data);
                    $user['api_key'] = $key_data['api_key'];
                    return Response::json(
                        [
                        'response' => 'success',
                        'data' => $user,
                        ],
                        200
                    );

                } else {
                    return Response::json(
                        [
                        'response' => 'error',
                        'message' => 'User is not activated',
                        ]
                    );
                }

            }
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Username or Password were incorrect',
                ],
                200
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Username or Password were incorrect',
                ],
                200
            );
        }

    }

    private function log($user, $action, $description, $session_ip = '127.0.0.1', $session_agent = 'No UserAgent')
    {
        $activity = new Activity();
        $activity->content_id = $user->id;
        $activity->user_id = (string)$user->id;
        $activity->content_type = 'User';
        $activity->action = $action;
        $activity->description = $description;
        $activity->details = 'Username: ' . $user->fullName();
        $activity->ip_address = $session_ip;
        $activity->user_agent = $session_agent;
        $activity->save();
    }

    /**
     * Store new ExternalSurveyor
     */

    public function store(Request $request)
    {
        $validator = Validator::make(
            $request->all(), [
            'first_name' => 'required',
            'last_name' => 'required',
            'email' => 'unique:external_surveyors',
            'phone' => 'unique:external_surveyors',
            ]
        );

        if ($validator->fails()) {
            return Response::json(
                [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
                ], 200
            );
        } else {
            $expiry = time() + (14 * 24 * 60 * 60); //14 days
            $data = [
                'first_name' => $request->get('first_name'),
                'last_name' => $request->get('last_name'),
                'email' => $request->get('email'),
                'phone' => $request->get('phone'),
                'external_survey_company_id' => $request->get('external_survey_company_id'),
                'role' => $request->get('role'),
                'claims_notification' => $request->get('claims_notification'),
                'password' => Hash::make(uniqid()),
                'secret' => $request->get('secret'),
                'activation_code' => Str::uuid()->toString(),
                'activation_code_expires' => $expiry,
            ];

            $external_surveyor = ExternalSurveyor::create($data);

            if ($external_surveyor->id) {
                $external_surveyor->type = 'external-surveyor';

                $this->mail->queue(
                    $external_surveyor->email,
                    $external_surveyor->fullName(),
                    'Risk Reduce, Welcome to Risk Reduce',
                    'emails.authadmin.welcome',
                    $external_surveyor
                );

                return Response::json(
                    [
                    'response' => 'success',
                    'message' => 'The External Surveyor has been created successfully',
                    ],
                    200
                );
            } else {
                return Response::json(
                    [
                    'response' => 'error',
                    'message' => 'Unable to create External Surveyor',
                    ],
                    200
                );
            }
        }
    }

    /**
     * Send Reset Password code
     */

    public function sendResetPasswordCode(Request $request)
    {
        $email = $request->get('email');

        if (isset($email)) {
            $external_surveyor = ExternalSurveyor::where('email', '=', $email)->first();


            if (isset($external_surveyor->id)) {
                //generate reset code
                $hours = config('auth.reminder.expire');
                $expiry = time() + ($hours * 60 * 60);

                $external_surveyor->reset_password_code = Str::uuid()->toString();
                $external_surveyor->reset_password_code_expires = $expiry;
                $external_surveyor->save();

                //Queue:
                $external_surveyor->user_type = 'external-surveyor';
                $this->mail->queue(
                    $external_surveyor->email,
                    $external_surveyor->fullName(),
                    'Risk Reduce - Password reset',
                    'emails.authadmin.passwordChange',
                    $external_surveyor
                );

                return Response::json(
                    [
                    'response' => 'success',
                    'message' => 'External Surveyor has been sent an email with details to reset their password',
                    ]
                );
            }
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'Failed to reset External Surveyor password',
            ]
        );
    }

    /**
     * Reset Password using new password and code to verify user
     */

    public function resetPassword(Request $request)
    {
        $password = $request->get('password');
        $code = $request->get('code');

        if (isset($code) && isset($password)) {
            $User = ExternalSurveyor::where('reset_password_code', '=', $code)->first();

            if (isset($User) && $User->reset_password_code_expires >= time() && $User->reset_password_code == $code) {
                //reset password and reset codes to 0
                $User->password = Hash::make(Crypt::decrypt($password));
                $User->reset_password_code = null;
                $User->reset_password_code_expires = null;
                $User->attempts = 0;
                $User->password_change_updated_at = now();
                $User->save();

                $this->createPreviousPasswordEntry($User);


                return Response::json(
                    [
                    'response' => 'success',
                    'message' => 'User password has been reset',
                    'user_id' => $User->id,
                    ]
                );

            }
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'Could not reset User password',
            ]
        );

    }

    public function generateLink($userID)
    {
        if (isset($userID)) {
            $user = ExternalSurveyor::find($userID);
            if (isset($user)) {
                $user->activation_code = Str::uuid()->toString();
                $user->activation_code_expires = strtotime('+14 days');
                $user->save();
                return Response::json(
                    [
                    'response' => 'success',
                    'data' => $user->toArray(),
                    ]
                );
            }

        }
        return Response::json(
            [
            'response' => 'error',
            'data' => 'Could not change activation timeline',
            ]
        );
    }

    /**
     * Activate a ExternalSurveyor
     */

    public function activate(Request $request)
    {
        $code = $request->get('code', null);
        $email = $request->get('email', null);

        if ($code && $email) {
            $external_surveyor = ExternalSurveyor::where('email', '=', $email)->first();

            if ($external_surveyor && $external_surveyor->activation_code == $code && $external_surveyor->activation_code_expires >= time()) {
                $password = Crypt::decrypt($request->get('password'));
                $external_surveyor->password = Hash::make($password);
                $external_surveyor->activated = true;
                $external_surveyor->activation_code = null;
                $external_surveyor->activation_code_expires = null;
                $external_surveyor->password_change_updated_at = now();
                $external_surveyor->save();

                $this->createPreviousPasswordEntry($external_surveyor);

                return Response::json(
                    [
                    'response' => 'success',
                    'message' => 'External Surveyor activated',
                    ]
                );
            }
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'Could not activate admin',
            ]
        );
    }

    /**
     * Find External Surveyor
     */
    public function show($id)
    {
        if ($id && is_numeric($id)) {
            $external_surveyor = ExternalSurveyor::find($id);

            if (!$external_surveyor) {
                $external_surveyor = ExternalSurveyor::where('id', '=', $id)->first();
            }

            $response = [
                'response' => ($external_surveyor)
                    ? 'success'
                    : 'error',
            ];

            if ($external_surveyor) {
                $external_surveyor->external_survey_company;
                $response['data'] = $external_surveyor;
            } else {
                $response['message'] = 'The specified External Surveyor could not be found';
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid External Surveyor ID',
            ];
        }

        return Response::json($response);
    }

    /**
     * Delete ExternalSurveyor
     *
     * @param $id
     *
     * @return mixed
     */
    public function destroy(Request $request, $id)
    {
        if (is_numeric($id)) {
            $external_surveyor = ExternalSurveyor::find($id);

            if (ExternalSurveyor::destroy($id)) {
                $this->log(
                    $external_surveyor,
                    'Destroy',
                    'ExternalSurveyor Destroyed',
                    $request->header('session_ip'),
                    $request->header('session_agent')
                );

                return Response::json(
                    [
                    'response' => 'success',
                    'message' => 'The External Surveyor has been deleted successfully',
                    ]
                );
            } else {
                return Response::json(
                    [
                    'response' => 'error',
                    'message' => 'The External Surveyor could not be found',
                    ]
                );
            }
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Invalid External Surveyor ID',
                ]
            );
        }
    }

    public function options($role = null)
    {
        $options = ($role)
            ? ExternalSurveyor::where('role', '=', $role)->orderBy('first_name', 'ASC')
            : ExternalSurveyor::query()->orderBy('first_name', 'ASC');

        return Response::json(
            $options->get()->pluck(
                'fullname',
                'id'
            )
        );
    }

    private function createPreviousPasswordEntry(ExternalSurveyor $user)
    {
        PreviousPassword::create([
            'user_id' => $user->id,
            'password' => $user->password,
            'table' => 'external_surveyors',
        ]);
    }
}
