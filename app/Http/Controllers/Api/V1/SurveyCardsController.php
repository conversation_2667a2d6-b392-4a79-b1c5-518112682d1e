<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\SurveyCardCollection;
use App\Models\SurveyCards;
use App\Models\BrokerUser;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Session;

class SurveyCardsController extends Controller
{
    public function index(Request $request)
    {  
        //different column for broker user
        $user_column=$request->get('user_type') == 'broker-user' ? 'broker-column':'column'; 

        $columns = SurveyCards::select($user_column)->groupBy($user_column)->get();

        if ($request->get('column')) {
            $cards = $this->generateCardsPerType($request);
            return $this->collect($cards);
        }

        foreach ($columns as $column) {
            $columnName = $column["_id"][$user_column];
            $data[$columnName] = $this->collect($this->generateCardsPerType($request, $columnName));
        }

        return $data;
    }

    /**
     * @param  Request  $request
     * @return Paginator
     */
    private function generateCardsPerType(Request $request, $kanbanStatus = null): Paginator
    {
        $limit = $request->get('limit') ?? 10;
        $cards=SurveyCards::columns($request);
        if($request->get('user_type') == 'broker-user'){
            $broker=BrokerUser::find($request->get('user_id'));
            $orgIds=$broker->associatedOrganisations();
            $cards = $cards->whereIn('properties.organisation_id', $orgIds);
        }
        return $cards // choose columns
            ->filter($request, $kanbanStatus) // apply filter
            ->sort($request) // apply sorting
            ->simplePaginate($limit); // apply limit
    }

    private function collect(Paginator $cards)
    {
        return (new SurveyCardCollection($cards))->response()->getData(true);
    }
}