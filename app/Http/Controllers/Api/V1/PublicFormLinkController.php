<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use Carbon\Carbon;
use App\Models\PublicFormLink;
use App\Models\PublicFormy;
use Illuminate\Http\Request;

class PublicFormLinkController extends BaseController
{
    public function generateLink($form_id, $org_id)
    {
        $id = PublicFormLink::insertGetId([
            'organisation_id' => $org_id,
            'form_id'         => $form_id,
            'is_active'       => PublicFormLink::DEFAULT_STATUS,
            'domain'          => PublicFormLink::getPublicSubdomain(),
            'uuid'            => PublicFormLink::generateUuid(),
        ]);

        if ($id) {
            $form = PublicFormLink::find($id);
            return response()->json([
                'response' => 'success',
                'data'     => $form,
            ]);
        } else {
            return response()->json([
                'response' => 'error',
                'message'  => 'Unable to create public form link    ',
            ]);
        }
    }

    public function deleteLink($link_id)
    {
        PublicFormLink::where('uuid', '=', $link_id)->delete();
        return response()->json([
            'response' => 'success',
            'message' => 'link has been deleted',
        ]);
    }

    public function index($id)
    {   
        $forms = PublicFormLink::where('form_id', '=', $id)->with('organisation')->get();
        return response()->json([
            'response' => 'success',
            'data'     => $forms->toArray(),
        ]);
    }

    public function show($uuid)
    {
        $link = PublicFormLink::where('uuid', '=', $uuid)->with('publicForm')->first();
        if ($link) {
            return response()->json([
                'response' => 'success',
                'data'     => $link,
            ]);
        }

        return response()->json([
            'response' => 'failed',
        ]);
    }

    public function generateSharedLink(Request $request)
    {
        $data             = $request->except('_token');
        $form_id          = $data['formId'];
        $shared_link_slug = $data['sharedLink_slug'];
        $shared_link_name = $data['sharedLink'];
        $message          = '';

        if (PublicFormy::where('shared_link_slug', $shared_link_slug)->get()->count() <= 0) {
            $form = PublicFormy::find($form_id);
            if (isset($form)) {
                $form->shared_link_slug = $shared_link_slug;
                $form->shared_link_name = $shared_link_name;
                $form->is_active_link   = PublicFormLink::DEFAULT_STATUS;
                $form->domain           = PublicFormLink::getSharedSubdomain();
                $form->updated_at       = Carbon::now()->toDateTimeString();
                if ($form->save()) {
                    return response()->json([
                        'response' => 'success',
                        'data'     => $form,
                    ]);
                } else {
                    $message = 'Unable to generate shared link';
                }
            } else {
                $message = 'Form does not exists';
            }
        } else {
            $message = 'Shared link ' . $shared_link_slug . ' already exists';
        }
        return response()->json([
            'response' => 'error',
            'message' => $message,
        ]);
    }

    public function deleteSharedLink($form_id)
    {
        $form = PublicFormy::find($form_id);
        if (isset($form)) {
            $form->unset('shared_link_slug');
            $form->unset('shared_link_name');
            $form->unset('is_active_link');
            $form->unset('domain');
            $form->updated_at = Carbon::now()->toDateTimeString();
            if ($form->save()) {
                return response()->json([
                    'response' => 'success',
                    'message' => 'link has been deleted',
                ]);
            } else {
                $message = 'Unable to generate shared link';
            }
        } else {
            $message = 'Form does not exists';
        }

        return response()->json([
            'response' => 'error',
            'message'  => $message,
        ]);
    }
}
