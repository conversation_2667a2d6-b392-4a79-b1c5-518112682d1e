<?php

namespace App\Http\Controllers\Api\V1;

use Illuminate\Http\Request;
use App\Http\Controllers\BaseController;
use App\Models\DocumentPolicyType;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;

class DocumentPolicyTypeController extends BaseController
{

    /**
     * [getDocumentTypes description]
     *
     * @return [type] [description]
     */
    public function getDocumentTypes()
    {
        $docTypes = DocumentPolicyType::where('deleted', '0')->get();

        return Response::json(['response' => 'success', 'data' => $docTypes]);
    }

    /**
     * [save_document_policy_type description]
     *
     * @param  integer $id [description]
     * @return [type]      [description]
     */
    public function save_document_policy_type(Request $request, $id = 0)
    {

        $rules = ['title' => 'required'];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'failed to create document',
                'errors' => $validator->errors(),
                ]
            );
        }

        $data = DocumentPolicyType::updateOrCreate(
            ['id' => $id],
            ['title' => $request->get('title'), 'description' => $request->get('desc')]
        );

        if (isset($data)) {
            return Response::json(
                [
                'response' => 'success',
                'message' => 'document type saved.',
                'data' => $data,
                ]
            );
        }

        return null;
    }

    /**
     * [get_policy_type description]
     *
     * @param  [type] $type_id [description]
     * @return [type]          [description]
     */
    public function get_policy_type($type_id)
    {
        $data = DocumentPolicyType::find($type_id);

        if (isset($data)) {
            return Response::json(['response' => 'success', 'data' => $data], 200);
        }

        return null;
    }

    public function delete_type($type_id)
    {
        $data = DocumentPolicyType::find($type_id);

        if (isset($data)) {
            $data->deleted = true;

            $result = $data->save();

            if (isset($result)) {
                return Response::json('Document deleted.', 200);
            }
        }

        return Response::json('Failed to delete document type.', 200);
    }
}
