<?php

namespace App\Http\Controllers\Api\V1\RiskInsights;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;

class BenchmarkController extends Controller
{
    public function index()
    {
        $benchmarkingJson = file_get_contents(
            public_path('templates-league/data/default/dashboard_benchmarking.json')
        );
        return response()->json(json_decode($benchmarkingJson, true));
    }

    public function organisation()
    {
        $organisationJson = file_get_contents(public_path('templates-league/data/default/organisation_benchmarking.json'));
        return response()->json(json_decode($organisationJson, true));
    }
}
