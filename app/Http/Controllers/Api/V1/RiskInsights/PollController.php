<?php

namespace App\Http\Controllers\Api\V1\RiskInsights;

use App\Http\Controllers\Controller;
use App\Models\Organisation;
use App\Models\OrganisationLocations;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class PollController extends Controller
{
    public function poll(Request $request)
    {
        $table = $request->query('table');
        $key = "{$table}_last_updated";

        $start = time();
        $timeout = 120; // Polling timeout in seconds when there are no updates

        // Fetch the initial cache value
        $initialValue = Cache::get($key);
        \Log::info('Initial cache value', [$initialValue]);

        while (true) {
            $current = Cache::get($key);
            \Log::info('Current cache value', [$current]);
            \Log::info('Table', [$key]);

            // Compare the initial value with the current value
            if ($current !== $initialValue) {
                if ($table === 'organisation') {
                    $organisation = Organisation::withoutAppends()->orderBy('name', 'asc')->get();
                    return response()->json([
                        'updated' => true,
                        'organisations' => $organisation,
                        'last_updated' => $current,
                    ]);
                } else if ($table === 'organisation_location') {
                    $orgId = $request->query('org_id');
                    $locationId = $request->query('location_id');
                    \Log::info('OrgId', [$orgId]);
                    \Log::info('LocationId', [$locationId]);
                    $organisationLocations = OrganisationLocations::where('organisation_id', $orgId)->get();
                    
                    $selectedLocation = null;
                    if ($locationId) {
                        $selectedLocation = OrganisationLocations::where('id', $locationId)->first();
                    }

                    return response()->json([
                        'updated' => true,
                        'organisation_locations' => $organisationLocations,
                        'selected_location' => $selectedLocation,
                        'last_updated' => $current,
                    ]);
                }
            }

            if ((time() - $start) > $timeout) {
                \Log::info('Polling timeout reached', ['elapsed' => time() - $start, 'timeout' => $timeout]);
                return response()->json(['updated' => false]);
            }

            usleep(500000);
        }
    }
}
