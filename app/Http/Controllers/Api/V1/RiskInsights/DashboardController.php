<?php

namespace App\Http\Controllers\Api\V1\RiskInsights;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;

class DashboardController extends Controller
{
    public function index()
    {
        $filePath = public_path('templates-league/data/default/dashboard.json');
        return response()->json(json_decode(file_get_contents($filePath), true));
    }

    public function location()
    {
        $dashboardLocation = file_get_contents(public_path('templates-league/data/default/dashboard_location.json'));
        return response()->json(json_decode($dashboardLocation, true));
    }

    public function company()
    {
        $dashboardCompany = file_get_contents(public_path('templates-league/data/default/dashboard_company.json'));
        return response()->json(json_decode($dashboardCompany, true));
    }

    public function risk()
    {
        $riskDashboard = file_get_contents(public_path('templates-league/data/default/risk_dashboard.json'));
        return response()->json(json_decode($riskDashboard, true));
    }
}
