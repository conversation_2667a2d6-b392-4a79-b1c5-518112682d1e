<?php

namespace App\Http\Controllers\Api\V1\RiskInsights;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class PortfolioController extends Controller
{
    public function index()
    {
        return [
            ['sector' => 'Grocery', 'value' => 35],
            ['sector' => 'Fashion', 'value' => 25],
            ['sector' => 'Health & Beauty', 'value' => 24],
            ['sector' => 'Sport', 'value' => 10],
            ['sector' => 'Electrical', 'value' => 6],
        ];
    }
}
