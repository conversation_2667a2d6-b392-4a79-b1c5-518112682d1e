<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use App\Models\Trade;
use App\Models\TradesToTradeGrouping;
use Illuminate\Support\Facades\Validator;

class TradesController extends BaseController
{
    /**
     * Get All Trades
     */
    public function index()
    {
        $trades = Trade::orderBy('name', 'asc')->paginate(20);
        return Response::json(
            [
            'response' => 'success',
            'data' => $trades->toJson(),
            ]
        );
    }

    /**
     * Store new Trade
     */
    public function store(Request $request)
    {
        $validator = Validator::make(
            $request->all(), [
            'name' => 'required',
            'trade_groups' => 'required',
            ]
        );

        if ($validator->fails()) {
            $response = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        } else {

            $trade = Trade::create(
                [
                'name' => $request->get('name'),
                'trade_group_id' => 0
                ]
            );

            if ($trade->id) {

                //grouping
                foreach ($request->get('trade_groups') as $trade_group_id) {
                    TradesToTradeGrouping::create(
                        [
                        'trade_group_id' => $trade_group_id,
                        'trade_id' => $trade->id,
                        ]
                    );
                }

                $response = [
                    'response' => 'success',
                    'message' => 'The trade has been created successfully',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The trade has failed to be created',
                ];
            }
        }

        return Response::json($response);
    }

    /**
     * Update Trade Group
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make(
            $request->all(), [
            'name' => 'required',
            ]
        );

        if ($validator->fails()) {
            $response = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        } else {
            $trade = Trade::find($id);
            if ($trade->id) {
                $trade->name = $request->get('name');
                $trade->trade_group_id = 0;
                $trade->save();

                // delete current groups and add again
                TradesToTradeGrouping::where('trade_id', $id)->delete();
                if ($request->has('trade_groups')) {
                    foreach ($request->get('trade_groups') as $trade_group_id) {
                        TradesToTradeGrouping::create(
                            [
                            'trade_group_id' => $trade_group_id,
                            'trade_id' => $trade->id,
                            ]
                        );
                    }
                }

                $response = [
                    'response' => 'success',
                    'message' => 'The trade has been updated successfully',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'Invalid trade',
                ];
            }
        }
        return Response::json($response);
    }

    /**
     * Find Trade
     */
    public function show($id)
    {
        $trade = Trade::find($id);

        // horrible but hasManyThrough wasnt working
        $trade->trade_groups = TradesToTradeGrouping::where('trade_id', $id)->pluck('trade_group_id')->all();

        $response = [
            'response' => ($trade)
                ? 'success'
                : 'error',
        ];
        if ($trade) {
            $response['data'] = $trade;
        } else {
            $response['message'] = 'The specified trade could not be found';
        }
        return Response::json($response);
    }

    /**
     * Delete Trade
     *
     * @param $id
     *
     * @return mixed
     */
    public function destroy($id)
    {
        if ($trade = Trade::find($id)) {
            if (Trade::destroy($id)) {
                $response = [
                    'response' => 'success',
                    'message' => 'The specified trade was successfully deleted',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The specified trade could not be deleted',
                ];
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'The specified trade could not be found',
            ];
        }

        return Response::json($response);
    }

    public function options($tradeGroup)
    {
        $trades = Trade::select('name', 'id')->orderBy('name', 'asc')->get();

        $trade_output = [];

        foreach ($trades as $trade) {
            $trade_output[(string)$trade->id] = $trade->name;
        }

        return Response::json($trade_output);

        // test
        // return Response::json(Trade::orderBy('name', 'asc')->pluck(
        //     'name',
        //     'id'
        // )->all());
    }
}
