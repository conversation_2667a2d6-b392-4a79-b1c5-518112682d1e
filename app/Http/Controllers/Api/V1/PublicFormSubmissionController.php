<?php

namespace App\Http\Controllers\Api\V1;

use App\Exports\PublicFormSubmissions;
use App\Http\Controllers\BaseController;
use App\Models\BrokerUser;
use Carbon\Carbon;
use App\Models\Documentlevels;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\FileUpload;
use App\Models\FormSubmissionsMapping;
use App\Models\Formy;
use App\Models\FormyDocuments;
use App\Models\FormySubmissions;
use App\Models\LibertyUser;
use App\Models\Mailqueue;
use App\Models\Organisation;
use App\Models\OrganisationContact;
use App\Models\PublicFormLink;
use App\Models\PublicFormy;
use App\Models\PublicFormyDocuments;
use App\Models\PublicFormySubmissions;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use App\Models\User;
use Illuminate\Support\Str;

class PublicFormSubmissionController extends BaseController
{
    const PAGE = 10;

    public function __construct(Mailqueue $queue, FileUpload $fileupload)
    {
        $this->queue = $queue;
        $this->files = $fileupload;
    }

    /**
     * Prepares the data for admin's public submissions table
     * @param  string  $form
     * @return array
     */
    public function index(string $form, Request $request): array
    {
        $data = $request->all();
        $page = $data['page'];
        $offset = ($page * 200);

        $totalRecord = PublicFormySubmissions::select([
            '_id',
            'submission_sequence',
            'verified_at',
            'verified_by',
            'verifier_role',
            'branch-where-accident-happened',
            'organisation_id'
        ])
        ->where('form_id', '=', $form)
        ->where('submitted', '=', '1', 'AND')
        ->count();

        $submissions = PublicFormySubmissions::select([
            '_id',
            'submission_sequence',
            'verified_at',
            'verified_by',
            'verifier_role',
            'branch-where-accident-happened',
            'organisation_id'
        ]);

        if ($totalRecord>=200) {
            $submissions = $submissions->take(200)
                    ->offset($offset)
                    ->where('form_id', '=', $form)
                    ->where('submitted', '=', '1', 'AND')
                    ->orderBy('_id', 'DESC')
                    ->get();
        } else {
            $submissions = $submissions
                ->where('form_id', '=', $form)
                ->where('submitted', '=', '1', 'AND')
                ->orderBy('_id', 'DESC')
                ->get();
        }

        // separate users from admins
        [$userIds, $adminIds] = $submissions->partition(fn($i) => $i->verifier_role === "client");
        $users = $this->getSubmissionUsers($userIds, $submissions);
        $admins = $this->getSubmissionAdmins($adminIds);
        $organisations = $this->getSubmissionOrganisations($submissions);

        foreach ($submissions as $submission) {
            // define the name of the verifier
            $verifiedName = null;
            if (isset($submission->verified_at)) {
                $verifiedBy = $submission->verifier_role === 'admin'
                    ? $admins->first(fn($a) => $a->id === (int)$submission->verified_by)
                    : $users->first(fn($u) => $u->id === (int)$submission->verified_by);

                if ($verifiedBy) {
                    $verifiedName = $verifiedBy->first_name . ' ' . $verifiedBy->last_name;
                }
            }
            $submission->verified_name = $verifiedName ?: '-'; // catch empty string

            // define branch name
            $branchName = null;
            if (isset($submission->{'branch-where-accident-happened'})) {
                $branchName = $users
                    ->first(fn($u) => $u->id === (int)$submission->{'branch-where-accident-happened'})
                    ->branch_name;
            } else {
                if ($submission->organisation_id) {
                    $branchName = $organisations
                        ->first(fn($o) => $o->id == $submission->organisation_id)
                        ->name;
                }
            }
            $submission->branch_name = $branchName ?: '-'; // catch empty string

            // cleanup
            unset($submission->organisation_id);
            unset($submission->verified_by);
            unset($submission->verifier_role);
        }

        return [
            'response' => 'success',
            'data' => json_encode($submissions->toArray()),
            'total' => $totalRecord
        ];
    }

    /**
     * Return involved client users in the submissions
     * @param  Collection  $userIds
     * @param  Collection  $submissions
     * @return Collection  array of client users; empty array if no result
     */
    private function getSubmissionUsers(Collection $userIds, Collection $submissions): Collection
    {
        // get only those users that are involved
        $userIds = $userIds
            ->pluck('verified_by')
            ->unique()
            ->map(fn($i) => (int)$i);

        // get users of submissions with `branch-where-accident-happened` and add to $userIds
        $usersWithBranch = $submissions->filter(fn($s) => isset($s->{'branch-where-accident-happened'}));
        if ($usersWithBranch->isNotEmpty()) {
            // for every user found, check if their user ID already exists in $userIds
            $usersWithBranch->each(function($user) use ($userIds) {
                // add the user's id if not yet in $userIds
                if (!$userIds->contains((int)$user->{'branch-where-accident-happened'})) {
                    $userIds->push((int)$user->{'branch-where-accident-happened'});
                }
            });
        }
        return User::select(['id', 'first_name', 'last_name', 'branch_name'])->whereIn('id', $userIds)->get();
    }

    /**
     * Return involved admin users in the submissions
     * @param  Collection  $adminIds
     * @return Collection  array of admin users; empty array if no result
     */
    private function getSubmissionAdmins(Collection $adminIds): Collection
    {
        return LibertyUser::select(['id', 'first_name', 'last_name'])
            ->whereIn( 'id', $adminIds
                ->pluck('verified_by')
                ->unique()
                ->map(fn($i) => (int)$i)
            )->get();
    }

    /**
     * Return involved organisations in the submissions
     * @param  Collection  $submissions
     * @return Collection  array of organisations; empty array if no result
     */
    private function getSubmissionOrganisations(Collection $submissions): Collection
    {
        return Organisation::select('id', 'name')
            ->whereIn('id', $submissions
                ->pluck('organisation_id')
                ->unique()
                ->map(fn($o) => (int)$o))
            ->get()
            ->makeHidden(['users_count', 'link']);   // remove appended fields from the response
    }

    private function getSubmissionBranchName(PublicFormySubmissions $submission)
    {
        if (isset($submission['branch-where-accident-happened'])) {
            $branch_details            = User::find($submission['branch-where-accident-happened']);
            $submission['branch_name'] = $branch_details['branch_name'];
        } else {
            if (isset($submission['organisation'])) {
                $branch_where_accident_happened = Organisation::where('id', $submission['organisation'])
                    ->first();
                if($branch_where_accident_happened){
                    $submission['branch_name'] = $branch_where_accident_happened;
                }
            }
        }
    }

    /*
     * get incomplete submissions from a user
     */
    public function savedFormsForOrg($organisation_id, $user_id, $manager = 0, $level1 = null, $level3 = null)
    {


        // $level4 = str_replace("*slash*","/",$level4);

        if (!is_null($level1) && !is_null($level3)) {
            $level1 = str_replace("*slash*", "/", $level1);
            // $level2 = str_replace("*slash*","/",$level2);
            $level3 = str_replace("*slash*", "/", $level3);

            $level1_data = Documentlevels::where('level_name', '=', urldecode($level1))->first();
            $level1 = $level1_data->level_id;

            $level3_data = Documentlevels::where('level_name', '=', urldecode($level3))->first();
            $level3 = $level3_data->level_id;

        }

        if ($manager == '1') {
            $data = PublicFormySubmissions::where('organisation_id', '=', $organisation_id)
                ->orWhere('organisation_id', '=', (int)$organisation_id)
                ->where('submitted', '=', '0')->get();
        } else {
            $data = PublicFormySubmissions::where('user_id', '=', $user_id)
                ->orWhere('user_id', '=', (int)$user_id)
                ->where('submitted', '=', '0')->get();
        }

        foreach ($data as $key => $submission) {
            $form = PublicFormy::find($submission->form_id);
            if (isset($form->formType) && $form->formType == "accident-reporting") {
                unset($data[$key]);
            } else {
                if (isset($form->name)) {
                    $submission['form_name'] = $form->name;
                }
                if (isset($form->fileUploads)) {
                    $submission['fileUploads'] = $form->fileUploads;
                }
                if (!is_null($level1) && !is_null($level3)) {
                    if (isset($form->level1_type) && isset($form->level3_type)) {
                        if ($level1 != $form->level1_type || $level3 != $form->level3_type) {
                            unset($data[$key]);
                        }
                    } else {
                        unset($data[$key]);
                    }
                }

            }
        }

        return response()->json([
            'response' => 'success',
            'data'     => json_encode($data->toArray()),
        ]);
    }

    /*
     * get submitted forms from a user
     */
    public function submittedFormsForOrg($organisation_id, $user_id, $manager = 0, $level1 = null, $level3 = null)
    {

        // $level4 = str_replace("*slash*","/",$level4);
        if (!is_null($level1) && !is_null($level3)) {
            $level1      = str_replace("*slash*", "/", $level1);
            $level3      = str_replace("*slash*", "/", $level3);
            $level1_data = Documentlevels::where('level_name', '=', urldecode($level1))->first();
            $level1      = $level1_data->level_id;
            $level3_data = Documentlevels::where('level_name', '=', urldecode($level3))->first();
            $level3      = $level3_data->level_id;

        }

        if ($manager == '1') {
            $data = PublicFormySubmissions::whereIn('organisation_id', [$organisation_id, (int)$organisation_id])
                ->where('submitted', '=', '1')->get();
        } else {
            $data = PublicFormySubmissions::where('user_id', '=', $user_id)->where('submitted', '=', '1')->get();
        }

        foreach ($data as $key => $submission) {
            $form = PublicFormy::find($submission->form_id);
            if (isset($form->formType) && $form->formType == "accident-reporting") {
                unset($data[$key]);
            } else {
                if (isset($form->name)) {
                    $submission['form_name'] = $form->name;
                }
                if (isset($form->fileUploads)) {
                    $submission['fileUploads'] = $form->fileUploads;
                }
                if (!is_null($level1) && !is_null($level3)) {
                    if (isset($form->level1_type) && isset($form->level3_type)) {
                        if ($level1 != $form->level1_type || $level3 != $form->level3_type) {
                            unset($data[$key]);
                        }
                    } else {
                        unset($data[$key]);
                    }
                }
            }
        }

        return response()->json([
            'response' => 'success',
            'data' => json_encode($data->toArray()),
        ]);
    }

    /*
     * get submissions for a form
     */
    public function submissions($form, Request $request)
    {
        $data = PublicFormySubmissions::where('form_id', '=', $form)
            ->where('submitted', '=', '1', 'AND')
            ->paginate(10);

        foreach ($data as $submission) {
            if (isset($submission['branch-where-accident-happened'])) {
                $branch_details            = User::find($submission['branch-where-accident-happened']);
                $submission['branch_name'] = $branch_details['branch_name'];
            } else {
                if (isset($submission['organisation'])) {
                    $queryId = $submission['organisation'];
                    if(is_object($queryId)){
                        $queryId = $queryId->id;
                    }
                    $branch_where_accident_happened = Organisation::where('id', $queryId)
                        ->first();
                    
                    if($branch_where_accident_happened){
                        $branchName = is_object($branch_where_accident_happened) && isset($branch_where_accident_happened->name) ? $branch_where_accident_happened->name : $branch_where_accident_happened;
                        $submission['branch_name'] = $branchName;
                    }
               
                }
            }
        }

        return response()->json([
            'response' => 'success',
            'data'     => json_encode($data->toArray()),
        ]);
    }

    public function submissionsMapping($mapping_id)
    {
        $submission_id = FormSubmissionsMapping::where('mapping_id', $mapping_id)->first(['submission_id']);
        return response()->json([
            'response' => 'success',
            'data'     => $submission_id,
        ]);
    }

    public function modify(Request $request, $submission_id)
    {
        $data       = json_decode($request->get('submission'), true);
        $id         = $data['_id'];
        $submission = PublicFormySubmissions::find($id);
        $submission->fill($data);

        if ($submission->save()) {
            return response()->json([
                'response' => 'success',
                'data'     => (string)$id,
            ]);
        }
    }

    public function exportSubmissions(Request $request, $form)
    {
        \Log::info('exporting submission for form :'.$form);

        $fields_array = $this->getFormFieldsName($form);
        $columns      = ['Organisation', 'submission_id', 'Submitted At', 'Status'];

        foreach ($fields_array[0] as $key => $value) {
            array_push($columns, $value);
        }

        $sdata = PublicFormySubmissions::where('form_id', '=', $form)
            ->where('submitted', '=', '1', 'AND')
            ->get();

        if (count($sdata) == 0) {
            $sdata = FormySubmissions::where('form_id', '=', $form)
                ->where('submitted', '=', '1', 'AND')
                ->get();
        }

        $excelData = [$columns];
        foreach ($sdata as $submission) {
            $data                     = [];
            $organisation             = Organisation::where('id', $submission->organisation_id)->first();
            $data['organisationName'] = $organisation->name;
            $data['submission_id']    = $submission['_id'];
            $data['submitted_at']     = $submission->created_at;
            $data['status']           = isset($submission->verified_at) ? 'Verified' : 'Submitted';
            foreach ($fields_array[0] as $key => $value) {
                if (array_key_exists($key, $fields_array[1])) {
                    $data[$key] = ($submission[$key] != '' && $submission[$key] != "0" && $submission[$key]) != null
                        ? Carbon::createFromTimestamp((double)$submission[$key])->format("Y-m-d")
                        : '';
                } else {
                    // $data[$key]=$submission[$key];
                    // Check if a timestamp
                    if (((string)(int)$submission[$key] === $submission[$key])
                        && ($submission[$key] >= 160000000)
                    ) {
                        $data[$key] = date('Y-m-d H:i:s', $submission[$key]);
                    } elseif (is_array($submission[$key])) {
                        $data[$key] = implode(", ", $submission[$key]);
                    } else {
                        $data[$key] = $submission[$key];
                    }
                }
            }
            $excelData[] = $data;
        }

        // generate the excel sheet for the data
        $excelData = collect($excelData);
        $fileName  = Carbon::now()->format('d-m-Y') . '-submissions-' . uniqid() . '.xlsx';
        Excel::store(
            new PublicFormSubmissions($excelData),  'excel/exports' . '/' . $fileName
        );
        $fullDir = storage_path('app/excel/exports/' . $fileName);

        $excel_key = Str::uuid()->toString();

        // user info for the email
        $info = [
            'first_name' => $request->get('first_name'),
            'last_name'  => $request->get('last_name'),
            'email'      => $request->get('email'),
        ];

        // upload the excel sheet
        if ($this->files->upload_excel($fullDir, 'excel/exports/' . $excel_key . '.xlsx')) {
            $file = $this->files->link('excel/exports/' . $excel_key . '.xlsx', '2 days');

            //Send the email to the user who requested the surveys excel sheet download
            $this->queue->queue(
                $info['email'], ($info['first_name'] . ' ' . $info['last_name']),
                'Risk Reduce - Submission Excel Export Ready', 'emails.surveys.excel-submissions-ready', [
                    'first_name'          => $info['first_name'],
                    'last_name'           => $info['last_name'],
                    'excel_download_link' => $file,
                ]
            );
        }

        return response()->json([
            'response' => 'success',
            'data' => '',
        ]);
    }

    public function getFormFieldsName($form_id)
    {
        $fields_array = [];
        $date_fields  = [];
        $form         = PublicFormy::find($form_id);
        if (isset($form->fields) && !empty($form->fields)) {
            $fields = $form->fields;
        } else {
            $form   = Formy::find($form_id);
            $fields = $form->fields;
        }

        foreach ($fields as $field) {
            foreach ($field as $key => $value) {
                $keyid = '';
                $name  = '';

                foreach ($value as $v) {
                    if (isset($v['name']) && !empty($v['name']) && $v['name'] == 'label') {
                        $name = $v['value'];
                    }

                    if (isset($v['name']) && !empty($v['name']) && $v['name'] == 'name') {
                        $keyid = $v['value'];
                    }

                    if ($keyid != '' && $value != '') {
                        $fields_array[$keyid] = $name;
                        if ($key == 'date') {
                            $date_fields[$keyid] = true;
                        }
                    }
                }
            }
        }
        return [$fields_array, $date_fields];
    }

    /*
    * get submissions for a form for an organisation
    */

    public function store(Request $request)
    {
        $isSharedForm    = $request->get('isSharedForm');
        $form_id         = $request->get('form_id');
        $organisation_id = $request->get('organisation_id');
        $uuid            = $request->get('uuid');
        $sequence        = '0';
        $submission      = json_decode($request->get('submission'), true);

        if ($isSharedForm && (int)$organisation_id > 0 && $form_id != '') {
            $formId         = $form_id;
            $form           = PublicFormy::find($formId);
            $organisationId = $organisation_id;
        } else {
            $form_link      = PublicFormLink::where('uuid', '=', $uuid)->first();
            $form           = PublicFormy::find($form_link->form_id);
            $organisationId = $form_link->organisation_id;
            $formId         = $form_link->form_id;
        }

        $sequence = PublicFormySubmissions::where('organisation_id', '=', $organisationId)
            ->orderBy('submission_sequence', 'desc')
            ->first();

        if (isset($sequence) && isset($sequence->submission_sequence)) {
            $sequence = $sequence->submission_sequence;
        }

        $now                               = date('Y-m-d H:i:s', time());
        $submission['submission_sequence'] = $sequence + 1;
        $submission['form_id']             = $formId;
        $submission['organisation_id']     = $organisationId;
        $submission['created_at']          = $now;
        $submission['is_public']           = 1;
        $submission['form_name']           = $form->name;
        $id = PublicFormySubmissions::insertGetId($submission);
        if ($id) {
            $sub = PublicFormySubmissions::find($id);
            return response()->json([
                'response' => 'success',
                'data'     => (string)$id,
            ]);
        } else {
            return response()->json([
                'response' => 'error',
                'message'  => 'Unable to create form submission',
            ]);
        }
    }

    public function submissionsOrg($form, $organisation_id)
    {
        $data = PublicFormySubmissions::where('form_id', '=', $form)
            ->where('submitted', '=', '1', 'AND')
            ->whereIn('organisation_id', [$organisation_id, (int)$organisation_id], 'AND')
            ->get();

        $branches = User::where('organisation_id', '=', $organisation_id)
            ->where('branch', '=', '1', 'AND')
            ->where('activated', '=', '1', 'AND')
            ->get();

        $branch = [];
        foreach ($branches as $b) {
            $branch[$b['id']] = $b['branch_name'];
        }

        $branch_string = 'branch-where-accident-happened';
        foreach ($data as $submission) {
            if (isset($submission[$branch_string]) && isset($branch[$submission[$branch_string]])) {
                $submission[$branch_string] = $branch[$submission[$branch_string]];
            }

            $date_accident = 'date-of-accident';
            if (isset($submission[$date_accident]) && $submission[$date_accident] != '') {
                $date_of_accident = $submission[$date_accident];
                $submission[$date_accident] = date('d-m-Y', $date_of_accident);
            } else {
                $submission[$date_accident] = 'N/A';
            }
        }

        return response()->json([
            'response' => 'success',
            'data'     => $data,
        ]);
    }

    /**
     * Get Acciedent Reporting Forms
     *
     * @param $organisation_id
     * @param $user_id
     * @param int $manager
     *
     * @return mixed
     */
    public function submittedAccidentFormsForOrg($organisation_id, $user_id, $manager = 0)
    {
        date_default_timezone_set('Europe/London');
        if ($manager == 1) {
            $data = PublicFormySubmissions::where('organisation_id', '=', $organisation_id)
                ->orWhere('organisation_id', '=', (int)$organisation_id)
                ->where('submitted', '=', '1')
                ->get();
        } else {
            $data = PublicFormySubmissions::where('user_id', '=', $user_id)
                ->orWhere('user_id', '=', (int)$user_id)
                ->where('submitted', '=', '1')
                ->get();
        }


        $organisations = Organisation::all();
        $org_array     = [];
        foreach ($organisations as $org) {
            $org_array[$org->id] = $org->name;
        }

        if ($organisation_id == 0) {
            $branches = User::where('branch', '=', '1')->get();
        } else {
            $branches = User::where('branch', '=', '1')->where('organisation_id', '=', $organisation_id)->get();
        }

        $branch_array = [];
        foreach ($branches as $branch) {
            $branch_array[$branch->id] = $branch->branch_name;
        }

        $submission = [];
        foreach ($data as $key => $submission) {
            if ($submission['updated_at'] != '') {
                $updated = $submission['updated_at']->timezone('Europe/London')->format('d/m/Y H:i:s');
            } else {
                $updated = 'N/A';
            }
            if (isset($submission['date-of-accident']) && $submission['date-of-accident'] != '' && is_numeric($submission['date-of-accident'])) {
                $date_of_accident = date('d/m/Y', $submission['date-of-accident']); //->timezone('Europe/London')->format('d/m/Y H:i:s');
            } else {
                $date_of_accident = 'N/A';
            }
            if (isset($submission['claim_submitted_at']) && $submission['claim_submitted_at'] != '' && is_numeric($submission['claim_submitted_at'])) {
                $claim_submitted_at = date(
                    'd/m/Y',
                    $submission['claim_submitted_at']
                );//->timezone('Europe/London')->format('d/m/Y H:i:s');
            } else {
                $claim_submitted_at = 'N/A';
            }
            if (isset($submission['submission_sequence'])) {
                $submission_sequence = $submission['submission_sequence'];
            } else {
                $submission_sequence = '0';
            }
            if (isset($submission['submission_sequence'])) {
                $submission_sequence = $submission['submission_sequence'];
            } else {
                $submission_sequence = '0';
            }
            $form = PublicFormy::find($submission->form_id);
            if (isset($form->formType) && $form->formType != "accident-reporting") {
                unset($data[$key]);
            } else {
                if (isset($form->name)) {
                    $submission['form_name']           = $form->name;
                    $submission['updated']             = $updated;
                    $submission['submission_sequence'] = $submission_sequence;
                    $branch_str                        = 'branch-where-accident-happened';
                    $submission['org_name']            = $org_array[$submission->organisation_id];
                    $submission['updated']             = $updated;
                    $submission['date_of_accident']    = $date_of_accident;
                    $submission['claim_submitted_at']  = $claim_submitted_at;
                    if (isset($branch_array[$submission->$branch_str])) {
                        $submission['branch_name'] = $branch_array[$submission->$branch_str];
                    } else {
                        $submission['branch_name'] = 'N/A';
                    }
                }
            }

        }

        return response()->json([
            'response' => 'success',
            'data'     => json_encode($data->toArray()),
        ]);
    }

    /**
     * Get Acciedent Reporting Forms that resulted in claims
     *
     * @return mixed
     */
    public function submittedAccidentFormsClaims($org_id = 0)
    {
        date_default_timezone_set('Europe/London');
        if ($org_id == 0) {
            $data = PublicFormySubmissions::where('claim', '=', '1')->where('submitted', '=', '1', 'AND')->get();
        } else {
            $data = PublicFormySubmissions::where('claim', '=', '1')->whereIn(
                'organisation_id',
                [$org_id, (int)$org_id], 'AND'
            )->where('submitted', '=', '1', 'AND')->get();
        }

        $organisations = Organisation::all();
        $org_array = [];
        foreach ($organisations as $org) {
            $org_array[$org->id] = $org->name;
        }

        if ($org_id == 0) {
            $branches = User::where('branch', '=', '1')->get();
        } else {
            $branches = User::where('branch', '=', '1')->where('organisation_id', '=', $org_id)->get();
        }

        //print_r($branches);exit;
        $branch_array = [];
        foreach ($branches as $branch) {
            $branch_array[$branch->id] = $branch->branch_name;
        }

        $submission = [];
        foreach ($data as $key => $submission) {
            if ($submission['updated_at'] != '') {
                $updated = $submission['updated_at']->timezone('Europe/London')->format('d/m/Y H:i:s');
            } else {
                $updated = 'N/A';
            }
            if (isset($submission['date-of-accident']) && $submission['date-of-accident'] != '') {
                $date_of_accident = date(
                    'd/m/Y',
                    $submission['date-of-accident']
                );//->timezone('Europe/London')->format('d/m/Y H:i:s');
            } else {
                $date_of_accident = 'N/A';
            }
            if (isset($submission['claim_submitted_at']) && $submission['claim_submitted_at'] != '') {
                $claim_submitted_at = date(
                    'd/m/Y',
                    $submission['claim_submitted_at']
                );//->timezone('Europe/London')->format('d/m/Y H:i:s');
            } else {
                $claim_submitted_at = 'N/A';
            }
            if (isset($submission['submission_sequence'])) {
                $submission_sequence = $submission['submission_sequence'];
            } else {
                $submission_sequence = '0';
            }
            $form = PublicFormy::find($submission->form_id);
            if (isset($form->formType) && $form->formType != "accident-reporting") {

                unset($data[$key]);
            } else {
                if (isset($form->name)) {
                    $branch_str = 'branch-where-accident-happened';
                    $submission['form_name'] = $form->name;
                    $submission['org_name'] = $org_array[$submission->organisation_id];
                    $submission['updated'] = $updated;
                    $submission['submission_sequence'] = $submission_sequence;
                    $submission['date_of_accident'] = $date_of_accident;
                    $submission['claim_submitted_at'] = $claim_submitted_at;
                    if (isset($branch_array[$submission->$branch_str])) {
                        $submission['branch_name'] = $branch_array[$submission->$branch_str];
                    }
                }
            }

        }

        return Response::json(
            [
            'response' => 'success',
            'data' => json_encode($data->toArray()),
            ]
        );
    }

    /*
    * get a form submission
    */

    public function savedAccidentFormsForOrg($organisation_id, $user_id, $manager = 0)
    {
        if ($manager == 1) {
            $data = PublicFormySubmissions::where('user_id', '=', $user_id)->where('submitted', '=', '0')->get();
        } else {
            $data = PublicFormySubmissions::where('user_id', '=', $user_id)->where('submitted', '=', '0')->get();
        }


        foreach ($data as $key => $submission) {
            //print_r($submission['updated_at']);exit;
            if ($submission['updated_at'] != '') {
                $updated = $submission['updated_at']->timezone('Europe/London')->format('d/m/Y H:i:s');
            } else {
                $updated = 'N/A';
            }

            if (isset($submission['submission_sequence'])) {
                $submission_sequence = $submission['submission_sequence'];
            } else {
                $submission_sequence = '0';
            }

            //$updated = $submission->updated_at;
            //$updated = $updated->format('d/m/Y');
            //$updated = \Carbon\Carbon::parse($submission->updated_at)->format('d/m/Y H:i:s');
            $form = PublicFormy::find($submission->form_id);
            if (isset($form)) {
                if ($form->formType != 'accident-reporting') {
                    unset($data[$key]);
                } else {
                    $submission['form_name'] = $form->name;
                    $submission['updated'] = $updated;
                    $submission['submission_sequence'] = $submission_sequence;
                }
            } else {
                unset($data[$key]);
            }

        }

        return Response::json(
            [
            'response' => 'success',
            'data' => json_encode($data->toArray()),
            ]
        );
    }

    /*
     * save a form
     */

    public function show($form)
    {
        $data = PublicFormySubmissions::find($form);
        if (isset($data->organisation)) {
            $organisation = Organisation::find($data->organisation);
            if (isset($organisation->name)) {
                $data->organisation_name = $organisation->name;
            }
        }
        $attachments = PublicFormyDocuments::where('submission_id', '=', $form)->get();

        if (isset($data)) {
            $data->attachments = $attachments;
            if (isset($data['branch-where-accident-happened'])) {
                $branch_details = User::find($data['branch-where-accident-happened']);
                //print_r($branch_details); exit;
                if ($branch_details) {
                    $branch_name = ($branch_details['branch_name'] != ''
                        ? $branch_details['branch_name']
                        : $branch_details['first_name'] . ' ' . $branch_details['last_name']);
                    $data['branch-where-accident-happened-name'] = $branch_name;
                }
            }
            return response()->json([
                'response' => 'success',
                'data' => json_encode($data->toArray()),
            ]);
        }

        return response()->json([
            'response' => 'error',
            'message' => 'Submission does not exist',
        ]);
    }


    /*
    * update a form
    */

    public function update(Request $request)
    {
        $data = $request->all();
        $id = $request->get('_id');
        $submission = PublicFormySubmissions::find($id);


        if ($submission->submitted == '1' || (!isset($submission->claim) && isset($data['claim']) && $data['claim'] == '1')) {
            if (((!isset($submission->claim) && isset($data['claim']) && $data['claim'] == '1')) || ($request->has('isAdmin') && $request->get('isAdmin') == '1' && !isset($submission->claim))) {

            } else {
                return Response::json(
                    [
                    'response' => 'error',
                    'message' => 'Unable to update form submission',
                    ]
                );
            }
        }

        $submission->fill($data);

        if ((isset($data['submitted']) && $data['submitted'] == true) || isset($data['claim'])) {
            //send emails to admin/group users
            $form = PublicFormy::find($submission->form_id);
            //print_r($form); exit;
            if (isset($form)) {
                if ($form->notify_group_user) {
                    //notify all users in organisation with manager
                    $organisation = Organisation::find($form->organisation);
                    if (isset($organisation)) {
                        foreach ($organisation->users as $user) {
                            if ($user->manager && $user->activated) {
                                $user->formName = $form->name;
                                $user->formLink = '/forms/submission/' . $submission->id;
                                $user->linkType = 'client';
                                $user->seq = $submission->submission_sequence;
                                $this->queue->queue(
                                    $user->email, $user->fullName(),
                                    'Risk Reduce - New form submission', 'emails.forms.submitted', $user
                                );
                            }
                        }
                    }
                }

                if ($form->notify_liberty_admin) {
                    $admins = LibertyUser::where('role', 'admin')->get();
                    foreach ($admins as $admin) {
                        if ($admin->activated == '1') {
                            $admin->formName = $form->name;
                            $admin->formLink = '/forms/submission/' . $submission->id;
                            $admin->seq = $submission->submission_sequence;
                            //print_r($admin);exit;
                            $this->queue->queue(
                                $admin->email, $admin->fullName(), 'Risk Reduce - New form submission',
                                'emails.forms.submitted', $admin
                            );
                        }
                    }
                }
                //print_r($form->notifications); exit;
                if ($form->notifications != '') {
                    $emails = explode(',', $form->notifications);
                    foreach ($emails as $email) {
                        $x = [];
                        $x['email'] = $email;
                        $x['formName'] = $form->name;
                        $x['formLink'] = '/forms/submission/' . $submission->id;
                        $x['seq'] = $submission->submission_sequence;
                        // $x['linkType'] = 'client';
                        //print_r($x); exit;
                        $this->queue->queue(
                            $email, $email, 'Risk Reduce - New form submission',
                            'emails.forms.submitted_notification', $x
                        );
                    }
                }
                if (isset($data['claim'])) {
                    $admins = LibertyUser::all();
                    foreach ($admins as $admin) {
                        if ($admin->activated == '1' && $admin->claims_notification == '1') {
                            $admin->formName = $form->name;
                            $admin->formLink = '/forms/submission/' . $submission->id;
                            $admin->seq = $submission->submission_sequence;
                            $this->queue->queue(
                                $admin->email, $admin->fullName(), 'Risk Reduce - New claim submission',
                                'emails.forms.claim', $admin
                            );
                        }
                    }
                }
            }
        }

        if ($submission->save()) {

            return Response::json(
                [
                'response' => 'success',
                'data' => (string)$id,
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to update form submission',
                ]
            );
        }
    }

    /*
    * delete a submission
    */
    public function destroy($submission)
    {
        if (PublicFormySubmissions::destroy($submission)) {
            return response()->json([
                'response' => 'success',
                'message'  => 'Submission Deleted',
            ]);
        }
        return response()->json([
            'response' => 'error',
            'message'  => 'Unable to delete form',
        ]);
    }

    /*
    * update a submission
    */
    public function addDoc(Request $request)
    {
        $formData = $request->except('_token');
        $id = PublicFormyDocuments::insertGetId($formData);
        if ($id) {
            return Response::json(
                [
                'response' => 'success',
                'data' => (string)$id,
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to create form submission',
                ]
            );
        }
    }

    /*
    * retrieve attachment
    */
    public function retrieveAttachment($id)
    {
        $document = PublicFormyDocuments::where('document_store_name', '=', $id)->first();

        if (isset($document)) {
            $submission = PublicFormySubmissions::where('_id', '=', $document->submission_id)->first();
            return Response::json(
                [
                'response' => 'success',
                'data' => $document,
                'submission' => $submission,
                ]
            );
        }

        return Response::json(
            [
            'response' => 'error',
            'error' => 'Could not find document',
            ]
        );
    }

    public function deleteDoc(Request $request)
    {
        $document_store_name = $request->get('document_store_name');
        $document = FormyDocuments::where('document_store_name', '=', $document_store_name)->delete();
        if ($document) {
            return Response::json(
                [
                'response' => 'success',
                'data' => 'document deleted',
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to delete document',
                ]
            );
        }
    }

    public function form($uuid)
    {
        $form = PublicFormLink::where('uuid', '=', $uuid)->with('organisation')->first();
        if (isset($form)) {
            return response()->json([
                'response' => 'success',
                'data'     => $form,
            ]);
        } else {
            return response()->json([
                'response' => 'error',
                'message'  => 'Could not find form',
            ]);
        }
    }

    public function verify($id, $user_id, $user_type)
    {
        $submission = PublicFormySubmissions::where('_id', '=', $id)->first();

        if ($submission) {
            $submission->verified_at = Carbon::now()->toDateTimeString();
            $submission->updated_at = Carbon::now()->toDateTimeString();
            $submission->verified_by = $user_id;
            $submission->verifier_role = $user_type;
            $submission->public_submissions_id = (string)$id;

            $submission->save();

            $this->SendNotificationEmail($submission, $user_id, $user_type);
            // copy submission to protected
            $submission_copy = $submission->toArray();
            unset($submission_copy['_id']);
            unset($submission_copy['recaptcha_response']);
            $new_submission_id = FormySubmissions::insertGetId($submission_copy);

            // copy relevant public_submitted_documents to submitted_documents
            $this->moveAttachments($id, (string)$new_submission_id);

            return Response::json(
                [
                'response' => 'success',
                'data' => $submission,
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Could not find submission',
                ]
            );
        }
    }

    public function SendNotificationEmail($submission, $user_id, $user_type)
    {
        $client_admins = [];

        if ($user_type == 'client') {
            $org_id = User::find($user_id)->organisation_id;
            if (isset($org_id) && $org_id > 0) {
                $users = User::where('organisation_id', '=', $org_id)
                    ->where('activated', 1)
                    ->where('manager', 1)
                    ->get(['id']);

                foreach ($users as $user) {
                    array_push($client_admins, $user->id);
                }
            }
        }

        if ($user_type == 'admin') {
            $org_id = $submission->organisation_id;
            if (isset($org_id) && $org_id > 0) {
                $users = User::where('organisation_id', '=', $org_id)
                    ->where('activated', 1)
                    ->where('manager', 1)
                    ->get(['id']);

                foreach ($users as $user) {
                    array_push($client_admins, $user->id);
                }
            }

        }

        $form = PublicFormy::find($submission->form_id);

        if (isset($form)) {
            if ($form->notify_group_user) {
                //notify all users in organisation with manager
                $formLinks = PublicFormLink::where('form_id', '=', $form->_id)->get();

                foreach ($formLinks as $formLink) {
                    $organisation = Organisation::find($formLink->organisation_id);
                    if (isset($organisation)) {

                        foreach ($organisation->users as $user) {
                            if ($user->manager && $user->activated) {
                                $user->formName = $form->name;
                                $user->formLink = 'submission/preview/' . $submission->_id;
                                $user->customLink = PublicFormLink::PUBLIC_SHARED_FORM_SUBDOMAIN;
                                $user->linkType = 'client';
                                $user->seq = $submission->submission_sequence;

                                if ($user_type == 'client') {
                                    if (in_array($user->id, $client_admins)) {
                                        $this->queue->queue(
                                            $user->email,
                                            $user->fullName(),
                                            'Risk Reduce - New verified form submission',
                                            'emails.forms.verified_notification',
                                            $user
                                        );
                                    }
                                } else {
                                    if ($user_type == 'admin') {
                                        if (in_array($user->id, $client_admins)) {
                                            $this->queue->queue(
                                                $user->email,
                                                $user->fullName(),
                                                'Risk Reduce - New verified form submission',
                                                'emails.forms.verified_notification',
                                                $user
                                            );
                                        }
                                    } else {
                                        $this->queue->queue(
                                            $user->email,
                                            $user->fullName(),
                                            'Risk Reduce - New verified form submission',
                                            'emails.forms.verified_notification',
                                            $user
                                        );
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if ($form->notify_liberty_admin) {
                $admins = LibertyUser::where('role', 'admin')->get();
                foreach ($admins as $admin) {
                    if ($admin->activated == '1') {
                        $admin->formName = $form->name;
                        $admin->formLink = '/forms/submission/' . $submission->_id . '/public';
                        $admin->seq = $submission->submission_sequence;
                        $this->queue->queue(
                            $admin->email,
                            $admin->fullName(),
                            'Risk Reduce - New verified form submission',
                            'emails.forms.verified_notification',
                            $admin
                        );
                    }
                }
            }

            if (isset($form->notify_broker) && $form->notify_broker) {
                $publicFormLinks = PublicFormLink::where('organisation_id', '=', $submission->organisation_id)
                    ->where('form_id', '=', $form->_id)
                    ->get();

                foreach ($publicFormLinks as $publicFormLink) {
                    $orgBrokers = OrganisationContact::with('brokerUser')
                        ->where('organisation_id', $publicFormLink->organisation_id)
                        ->where('type', 'broker-user-contact')->get();
                    if (!empty($orgBrokers)) {
                        foreach ($orgBrokers as $orgBroker) {
                            $firstName = !empty($orgBroker->first_name) ? $orgBroker->first_name : $orgBroker->brokerUser->first_name;
                            $lastName  = !empty($orgBroker->last_name) ? $orgBroker->last_name : $orgBroker->brokerUser->last_name;
                            $fullName  = $firstName . ' ' . $lastName;
                            $orgBroker->first_name = $firstName;
                            $orgBroker->last_name  = $lastName;
                            $orgBroker->formName   = $form->name;
                            $orgBroker->formLink   = '/forms/submission/'.$submission->_id.'/public?user-type=broker-user';
                            $orgBroker->seq        = $submission->submission_sequence;
                            $email = !is_null($orgBroker->email) ? $orgBroker->email : $orgBroker->brokerUser->email;

                            if (!is_null($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
                                $this->queue->queue(
                                    $email,
                                    $fullName,
                                    'Risk Reduce - New verified form submission',
                                    'emails.forms.verified_notification',
                                    $orgBroker
                                );
                            }
                        }
                    }
                }
            }

            if ($form->notifications_admin_users) {
                $admins = LibertyUser::whereIn('email', explode(', ', $form->notifications_admin_users))->get();
                foreach ($admins as $admin) {
                    if ($admin->activated == '1') {
                        $admin->formName = $form->name;
                        $admin->formLink = '/forms/submission/' . $submission->_id . '/public';
                        $admin->seq = $submission->submission_sequence;
                        $this->queue->queue(
                            $admin->email,
                            $admin->fullName(),
                            'Risk Reduce - New verified form submission',
                            'emails.forms.verified_notification',
                            $admin
                        );
                    }
                }
                
                $brokers = BrokerUser::whereIn('email', explode(', ', $form->notifications_admin_users))->get();
                foreach ($brokers as $broker) {
                    $broker->formName = $form->name;
                    $broker->formLink = '/forms/submission/' . $submission->_id . '/public?user-type=broker-user';
                    $broker->seq = $submission->submission_sequence;
                    $this->queue->queue(
                        $broker->email,
                        $broker->fullName(),
                        'Risk Reduce - New verified form submission',
                        'emails.forms.verified_notification',
                        $broker
                    );
                }
            }

            if ($form->notifications_client_users) {
                $users = User::whereIn('email', explode(', ', $form->notifications_client_users))->get();
                foreach ($users as $user) {
                    if ($user->activated == '1') {
                        $user->formName = $form->name;
                        $user->formLink = '/forms/submission/preview/' . $submission->_id;
                        $user->linkType = 'client';
                        $user->seq = $submission->submission_sequence;
                        $this->queue->queue(
                            $user->email,
                            $user->fullName(),
                            'Risk Reduce - New verified form submission',
                            'emails.forms.verified_notification',
                            $user
                        );
                    }
                }
            }
        }
    }

    private function moveAttachments($submission_id, $new_submission_id)
    {
        $publicDocuments = PublicFormyDocuments::where('submission_id', '=', $submission_id)->get();
        foreach ($publicDocuments as $publicDocument) {
            $publicDocumentCopy = [
                    'from_public' => '1',
                    'submission_id' => $new_submission_id,
                ] + $publicDocument->toArray();

            FormyDocuments::insert($publicDocumentCopy);
        }
    }

    private function getVerifierName($submission){
        $user='-';
        if(!isset($submission['verified_by'])){
            return $user;
        }
        if($submission['verifier_role']=='client'){
            $client=User::find((int)$submission['verified_by']);
            $user=$client->first_name . ' ' . $client->last_name;
        }else{
            $admin=LibertyUser::find((int)$submission['verified_by']);
            $user=$admin->first_name . ' ' . $admin->last_name;
        }
        return $user;
    }

    public function ajaxSubmissions($form_id)
    {
        
        $data = PublicFormySubmissions::select([
            '_id',
            'submission_sequence',
            'verified_at',
            'verified_by',
            'verifier_role',
            'branch-where-accident-happened',
            'organisation_id'
        ])
        ->where('form_id', '=', $form_id)
        ->where('submitted', '=', '1')
        ->with(['organisation' => function ($query) {
            $query->select('id', 'name');
        }])
        ->get();

        foreach ($data as $submission) {
            $submission['verified_by']=$this->getVerifierName($submission);
            if (isset($submission['branch-where-accident-happened'])) {
                $branch_details            = User::find($submission['branch-where-accident-happened']);
                $submission['branch_name'] = $branch_details['branch_name'];
            }else{
                $submission['branch_name'] = $submission->organisation->name;
            }

            $submission['verified_at']=isset($submission['verified_at'])?date("Y-m-d", strtotime($submission['verified_at'])):'-';
            unset($submission['organisation']);
            unset($submission['organisation_id']);
        }

        return response()->json([
            'response' => 'success',
            'data'     => $data,
        ]);
    }

}
