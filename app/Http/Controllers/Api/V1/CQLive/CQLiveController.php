<?php

namespace App\Http\Controllers\Api\V1\CQLive;

use App\Http\Controllers\BaseController;
use App\Models\CQLive;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;

class CQLiveController extends BaseController
{

    public function __construct()
    {

    }

    public function getPayload()
    {
        $data = CQLive::all()->sortByDesc('created_at');
        return Response::json($data);
    }

    public function storePayload(Request $request)
    {
        if ($request->header('x-cqlive-api-key') == config('app.cqlive.apikey')
            && $request->header('user') == config('app.cqlive.username')
            && $request->header('password') == config('app.cqlive.password')
        ) {
            $data = $request->except('_token');
            CQLive::create($data);
            return Response::json([
                'response' => 'success',
                'message' => 'data logged successfully',
            ]);
        } else {
            return Response::json('unauthorized', 401);
        }
    }
}


