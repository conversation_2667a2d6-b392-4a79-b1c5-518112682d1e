<?php

namespace App\Http\Controllers\Api\V1\GDPR;

use Illuminate\Http\Request;
use App\Services\GDPR\DataPrivacyService;

class DataPrivacyController
{
    const SEARCH_FIELDS = [
        'first_name',
        'last_name',
        'email'
    ];

    /**
     * index
     *
     * @param  mixed $request
     * @param  DataPrivacyService $dataPrivacyService
     * @return void
     */
    public function index(Request $request, DataPrivacyService $dataPrivacyService)
    {
        $searchParams = $request->only(self::SEARCH_FIELDS);
        if (empty($searchParams)) {
            return response()->json(['message' => 'No data found!']);
        }
        $searchData = $dataPrivacyService->search($searchParams)->toArray();
        return response()->json($searchData);
    }

    /**
     * Delete or Anonymise data based of first_name, last_name and email
     *
     * @param  mixed $request
     * @param  DataPrivacyService $dataPrivacyService
     * @return void
     */
    public function action(Request $request, DataPrivacyService $dataPrivacyService, string $action)
    {
        $searchParams = $request->only(self::SEARCH_FIELDS);
        if (empty($searchParams)) {
            return response()->json(['message' => 'No data found!']);
        }
        $dataPrivacyService->{$action}($searchParams);
        $message = 'Data has been '. $action . 'd.';
        return response()->json(['response' => 'success', 'message' => $message]);
    }
}
