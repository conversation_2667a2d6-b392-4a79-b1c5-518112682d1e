<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\LibertyUser;
use App\Models\Mailqueue;
use App\Models\NotesMention;
use App\Models\Organisation;
use App\Models\OrganisationNote;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\Request;

class OrganisationNoteController extends BaseController
{
    const MENTION_FORMAT = '@<%s id=\"%s\">%s<\/%s>';
    const MENTION_ENCODE_TAG = 'b';

    private $liberty_admins;

    public function __construct(Mailqueue $mailqueue)
    {
        $this->mail = $mailqueue;
    }

    public function all($organisation_id)
    {
        $notes = OrganisationNote::where('organisation_id', $organisation_id)
            ->orderBy('created_at', 'desc')
            ->get();

        return Response::json(
            [
            'response' => 'success',
            'data' => $notes,
            ]
        );
    }

    public function store(Request $request, $organisation_id)
    {
        $data = $request->except('_token');

        // save the note
        $note = OrganisationNote::create(
            $data + [
                'organisation_id' => $organisation_id,
            ]
        );

        if ($note) {
            $this->saveAndNotifyMentions($note->_id, $organisation_id, $data);
        }

        return Response::json(
            [
            'response' => 'success',
            'message' => 'Organisation note has been created successfully',
            ]
        );
    }

    private function saveAndNotifyMentions($note_id, $organisation_id, array $data)
    {
        $mentions = $this->extractMentions($data['content'], 'data-id="', '"');

        if (!empty($mentions)) {
            // skip those who were already notified
            $mentioned = NotesMention::where(
                [
                'note_id' => $note_id,
                'organisation_id' => $organisation_id,
                ]
            )->get()->toArray();

            $mentioned_users = array_column($mentioned, 'user_id');

            foreach ($mentions as $user_id) {
                $user = LibertyUser::find($user_id);
                $note = OrganisationNote::find($note_id);
                $organisation = Organisation::find($organisation_id);

                $email_subject = $note->author . ' has updated a note you were tagged in';
                $title = '<strong>' . $note->author . '</strong> updated a note in the <strong>' . $organisation->name . '</strong> dashboard:';
                if (!in_array($user_id, $mentioned_users)) {
                    $email_subject = $note->author . ' tagged you in ' . $note->subject;
                    $title = '<strong>' . $note->author . '</strong> mentioned you in the <strong>' . $organisation->name . '</strong> dashboard:';

                    //save new mentions
                    NotesMention::create(
                        [
                        'note_id' => $note_id,
                        'user_id' => $user_id,
                        'organisation_id' => $organisation_id,
                        ]
                    );
                }

                //send email
                $email_template = 'emails.risk-reduce.mentions';
                $this->mail->queue(
                    $user->email,
                    $user->first_name . ' ' . $user->last_name,
                    $email_subject,
                    $email_template,
                    [
                        'mentioner' => $note->author,
                        'organisation_name' => $organisation->name,
                        'note_id' => $note_id,
                        'organisation_id' => $organisation_id,
                        'subject' => $note->subject,
                        'title' => $title,
                    ]
                );

            }
        }
    }

    private function extractMentions($str, $start_delimiter, $end_delimiter)
    {
        $contents = [];
        $start_delimiter_length = strlen($start_delimiter);
        $endDelimiterLength = strlen($end_delimiter);
        $start_from = $content_start = $content_end = 0;
        while (false !== ($content_start = strpos($str, $start_delimiter, $start_from))) {
            $content_start += $start_delimiter_length;
            $content_end = strpos($str, $end_delimiter, $content_start);
            if (false === $content_end) {
                break;
            }
            $contents[] = substr($str, $content_start, $content_end - $content_start);
            $start_from = $content_end + $endDelimiterLength;
        }
        return $contents;
    }

    public function show(Request $request, $organisation_id, $note_id)
    {
        $data = $request->except('_token');

        $note = OrganisationNote::where('organisation_id', $organisation_id)
            ->where('_id', $note_id)
            ->first();

        return Response::json(
            [
            'response' => 'success',
            'data' => $note,
            ]
        );
    }

    public function update(Request $request, $organisation_id, $note_id)
    {
        $data = $request->except('_token');

        $note = OrganisationNote::where('organisation_id', $organisation_id)
            ->where('_id', $note_id)
            ->update($data);

        if ($note) {
            $this->saveAndNotifyMentions($note_id, $organisation_id, $data);
        }

        return Response::json(
            [
            'response' => 'success',
            'message' => 'Organisation note has been updated successfully',
            ]
        );
    }

    public function destroy(Request $request, $organisation_id, $note_id)
    {
        $data = $request->except('_token');

        $note = OrganisationNote::where('organisation_id', $organisation_id)
            ->where('_id', $note_id)
            ->delete();

        return Response::json(
            [
            'response' => 'success',
            'message' => 'Organisation note has been deleted successfully',
            ]
        );
    }
}
