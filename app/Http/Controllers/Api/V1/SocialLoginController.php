<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\SocialAccount;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class SocialLoginController extends Controller
{
    public function login(Request $request, string $provider)
    {
        if ($request->has('user')) {
            $loginSource = $request->get('source') ?? 'client';
            $socialUser = $request->get('user');

            // First Find Social Account
            $account = SocialAccount::where([
                'provider_name' => $provider,
                'provider_id' => $socialUser->getId(),
                'source' => $loginSource,
            ])->first();

            if ($account) {
                $account->token = $socialUser->token;
                $account->metadata = $socialUser->user;
                $account->save();

                $user = $account->user;
            } else {
                // Find User
                // TODO: find user depending on the user's role
                /** @var User $user */
                $user = User::where([
                    'email' => $socialUser->getEmail(),
                ])->first();

                // If User not get then create new user
                // TODO: create user depending on the user's role
                if (!$user) {
                    $user = User::create([
                        'email' => $socialUser->getEmail(),
                        'password' => Hash::make(Str::uuid()), // random password
                        // other required fields
                    ]);
                }
                // Create Social Accounts
                SocialAccount::create([
                    'user_id' => $user->id,
                    'provider_id' => $socialUser->getId(),
                    'provider_name' => $provider,
                    'token' => $socialUser->token,
                    'metadata' => $socialUser->user,
                    'source' => $loginSource,
                ]);

                // TODO: Authenticate the user and send response back to Client
            }
        }
        abort('No user found');
    }
}
