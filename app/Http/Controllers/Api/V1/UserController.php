<?php

// test

namespace App\Http\Controllers\Api\V1;

use App\Traits\Has2FA;
use Exception;
use Carbon\Carbon;
use App\Models\Cati;
use App\Models\User;
use App\Models\ExternalSurveyor;
use App\Models\Activity;
use Webpatser\Uuid\Uuid;
use App\Models\Mailqueue;
use App\Models\MgaScheme;
use App\Models\LibertyUser;
use App\Models\Organisation;
use App\Models\UserExtraInfo;
use Illuminate\Http\Request;
use App\Models\OrganisationPolicy;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Crypt;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use Artisaninweb\SoapWrapper\SoapWrapper;
use Illuminate\Support\Facades\Validator;
use Illuminate\Contracts\Encryption\DecryptException;
use App\Jobs\CreateAccessGroupOrganisationJob;
use App\Jobs\CreateAccessGroupPersonJob;
use App\Models\PreviousPassword;
use App\Services\AccessGroup\ArchiveUsersService;
use App\Services\AccessGroup\CreateOrganisationService;
use App\Services\AccessGroup\CreateUserService;
use App\Services\AccessGroup\UpdatePersonService;
use App\Models\Previsico\PrevisicoAssetAccess;
use App\Models\Previsico\PrevisicoAsset;
use App\Helpers\Helper;

class UserController extends BaseController
{
    use Has2FA;

    protected $catiErrorMessage = '<b>Operation Failed.</b><br/>The error code is <b>%s</b>:<i>%s.</i><br/>Please contact support with this error information, link of this page and any other details that can help our investigation.';

    public function __construct(Mailqueue $mailqueue, User $user, Organisation $organisation, SoapWrapper $soapwrapper)
    {
        $this->mail = $mailqueue;
        $this->user = $user;
        $this->organisation = $organisation;
        $this->soapWrapper = $soapwrapper;
    }

    /**
     * Authenticate User
     *
     * @return mixed
     */
    public function auth(Request $request)
    {
        $email = $request->get('email');
        $password = $request->get('password');

        if ($email != null && $password != null) {
            try {
                $data = [
                    'email' => Crypt::decrypt($email),
                    'password' => Crypt::decrypt($password),
                ];
            } catch (DecryptException $e) {
                return Response::json(
                    [
                        'response' => 'error',
                        'message' => 'Unable to login member',
                    ],
                    200
                );
            }

            $user = User::where('email', '=', $data['email'])->first();
            $effectivityDate = Carbon::parse(config('app.password_expired_effectivity_date'));
            if (Auth::attempt($data) && $user) {

                if (Helper::shouldTakeEffect($effectivityDate) && $user?->isPasswordExpired()) {

                    return Response::json(
                        [
                            'response' => 'error',
                            'message' => 'The User password has expired.',
                            'password_expired' => true
                        ]
                    );
                }
    
                $maxAttempts = 5;
                if ($user->login_attempts >= $maxAttempts) {
                    return Response::json(
                        [
                            'response' => 'error',
                            'message' => 'Account Temporarily Locked: You have exceeded the maximum number of login attempts for your account. <NAME_EMAIL> to unlock your account.',
                        ]
                    );
                }

                if ($user->activated != 0) {
                    //Audit
                    $this->log(
                        $user,
                        'Login',
                        'User Login',
                        $request->header('session_ip'),
                        $request->header('session_agent')
                    );

                    if (!$user->secret) {
                        // generate a secret key for 2FA
                        $user->generateSecret();
                    }

                    return Response::json(
                        [
                            'response' => 'success',
                            'data' => $user,
                        ],
                        200
                    );
                } else {
                    return Response::json(
                        [
                            'response' => 'error',
                            'message' => 'User is not activated',
                        ]
                    );
                }
            }
            
            if($user){
                $user->login_attempts = $user->login_attempts + 1;
                $user->save();
            }

            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'Username or Password were incorrect',
                ],
                200
            );
        } else {

            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'Username or Password were incorrect',
                ],
                200
            );
        }
    }

    private function log($user, $action, $description, $session_ip = '127.0.0.1', $session_agent = 'No UserAgent')
    {
        $activity = new Activity();
        $activity->content_id = $user->id;
        $activity->user_id = (string)$user->id;
        $activity->content_type = 'User';
        $activity->action = $action;
        $activity->description = $description;
        $activity->details = 'Username: ' . $user->fullName();
        $activity->ip_address = $session_ip;
        $activity->user_agent = $session_agent;
        $activity->save();
    }

    /**
     * Reset Login Attempt: Liberty Users
     */
    public function resetLogin($id)
    {
        $attempt = LibertyUser::where('id', $id)
            ->update(['attempts' => 0]);

        if ($attempt) {
            return Response::json(
                [
                    'response' => 'success',
                ],
                200
            );
        } else {
            return Response::json(
                [
                    'response' => 'error',
                ],
                200
            );
        }
    }

    /**
     * Update Member
     */

    public function update(Request $request)
    {
        $rules = [
            'email' => 'required',
            'first_name' => 'required',
            'last_name' => 'required',
            'address_line_1' => 'required_if:branch,on',
            'postcode' => 'required_if:branch,on',
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json(
                [
                    'response' => 'error',
                    'errors' => $validator->messages()->toArray(),
                ],
                200
            );
        } else {
            $User = $this->user->find($request->get('id'));

            $c_access = $this->user->find($request->get('id'))->croner_access;
            $sm_access = $this->user->find($request->get('id'))->safetymedia_access;
            $updatedSMAccess = !empty($request->get('safetymedia_access')) ? $request->get('safetymedia_access') : 0;
            $smPassword = $sm_access == 0 && empty($updatedSMAccess) || $sm_access == 1 && empty($updatedSMAccess)
                ? ''
                : uniqid();

            if (isset($User)) {

                if ($request->has('previsico_asset_ids')) {
                    $previsicoAssetAccessIds = $request->get('previsico_asset_ids');
                    $previsicoAssets = PrevisicoAsset::whereIn('id', $previsicoAssetAccessIds)->get();

                    PrevisicoAssetAccess::where('user_id', $User->id)->delete();

                    foreach ($previsicoAssets as $previsicoAsset) {
                        PrevisicoAssetAccess::firstOrNew([
                            'user_id' => $User->id,
                            'previsico_asset_id' => $previsicoAsset->id
                        ])->save();
                    }
                }

                $User->email = $request->get('email');
                $User->first_name = $request->get('first_name');
                $User->last_name = $request->get('last_name');
                $User->address_line_1 = $request->get('address_line_1');
                $User->address_line_2 = $request->get('address_line_2');
                $User->postcode = $request->get('postcode');
                $User->country = $request->get('postcode');
                $User->manager = $request->get('manager');
                $User->branch = $request->get('branch');
                $User->branch_name = $request->get('branch_name');
                $User->croner_access = $request->get('croner_access');
                $User->safetymedia_access = $updatedSMAccess;
                $User->safetymedia_password = $smPassword;
                $User->client_dashboard_access = !empty($request->get('client_dashboard_access')) ? $request->get('client_dashboard_access') : 0;
                $User->astutis_access = !empty($request->get('astutis_access')) ? $request->get('astutis_access') : 0;
                $User->c_live_access = !empty($request->get('c_live_access')) ? $request->get('c_live_access') : 0;
                $User->triton_access = !empty($request->get('triton_access')) ? $request->get('triton_access') : 0;
                $User->activation_code = $User->activation_code;
                $User->activation_code_expires = $User->activation_code_expires;
                $User->file = $request->has('file')
                    ? $request->get('file')
                    : null;
                $User->original_filename = $request->has('original_filename')
                    ? $request->get('original_filename')
                    : null;
                $User->phone = $request->get('phone');
                $User->jobtitle = $request->get('jobtitle');
                $User->has_previsico_access = $request->has('has_previsico_access')
                    ? $request->get('has_previsico_access')
                    : 0;

                $catiOldVal = $User->cati_status;
                $User->cati_status = $request->has('cati_status')
                    ? $request->get('cati_status')
                    : null;
                $User->cati_id = ($request->has('cati_id') || $request->get('cati_id' != ''))
                    ? $request->get('cati_id')
                    : null;

                if (filter_var($request->get('can_pause_notifications', false), FILTER_VALIDATE_BOOLEAN)) {  
                    $User->notification_paused_at = filter_var($request->get('notification_paused_at', false), FILTER_VALIDATE_BOOLEAN)
                        ? now()
                        : null;
                }

                $User->load('organisation');
                try {
                    if ($User->manager) {
                        if (isset($User->cati_status) && $User->cati_status != $catiOldVal) {
                            if (strtolower($User->cati_status) == 'active') {
                                $cati = new Cati();
                                if (!$User->cati_id) {
                                    $response = $cati->createClientUser(
                                        [
                                            "ClientID" => $User->organisation->cati_id,
                                            "Forename" => $User->first_name,
                                            "Surname" => $User->last_name,
                                            "EmailAddress" => $User->email,
                                            "Username" => $User->email,
                                        ]
                                    );

                                    if ($response && $response->StatusCode == 'CU00') {
                                        $User->cati_id = $response->UserID;
                                    } else {
                                        return Response::json(
                                            [
                                                'response' => 'error',
                                                'message' => sprintf(
                                                    $this->catiErrorMessage,
                                                    $response->StatusCode,
                                                    $response->StatusDescription
                                                ),
                                            ]
                                        );
                                    }
                                } else {
                                    $response = $cati->enableClientUser($User->cati_id);
                                    if ($response && $response->StatusCode != 'EU00') {
                                        return Response::json(
                                            [
                                                'response' => 'error',
                                                'message' => sprintf(
                                                    $this->catiErrorMessage,
                                                    $response->StatusCode,
                                                    $response->StatusDescription
                                                ),
                                            ]
                                        );
                                    }
                                }
                            } elseif (strtolower($User->cati_status) == 'suspense') {
                                $cati = new Cati();
                                $response = $cati->suspendClientUser($User->cati_id);
                                if ($response && $response->StatusCode != 'DU00') {
                                    return Response::json(
                                        [
                                            'response' => 'error',
                                            'message' => sprintf(
                                                $this->catiErrorMessage,
                                                $response->StatusCode,
                                                $response->StatusDescription
                                            ),
                                        ]
                                    );
                                }
                            }
                        }
                    } elseif (!$User->manager && $User->cati_id) {
                        $User->cati_status = 'suspense';
                        $cati = new Cati();
                        $response = $cati->suspendClientUser($User->cati_id);
                        if ($response && $response->StatusCode != 'DU00') {
                            return Response::json(
                                [
                                    'response' => 'error',
                                    'message' => sprintf(
                                        $this->catiErrorMessage,
                                        $response->StatusCode,
                                        $response->StatusDescription
                                    ),
                                ]
                            );
                        }
                    } else {
                        $User->cati_status = null;
                    }
                } catch (Exception $e) {
                    return Response::json(
                        [
                            'response' => 'error',
                            'message' => sprintf($this->catiErrorMessage, $e->getCode(), $e->getMessage()),
                        ]
                    );
                }

                if ($User->save()) {
                    $invite = $request->get('send_invite');

                    if (isset($invite)) {
                        if ($User->activation_code_expires >= time()) {
                            $this->mail->queue(
                                $User->email,
                                $User->fullName(),
                                'Risk Reduce, Welcome to Risk Reduce',
                                'emails.auth.welcome',
                                $User
                            );
                        } else {
                            $User->activation_code_expires = time() + (14 * 24 * 60 * 60);
                            $User->save();
                        }
                    }

                    // $this->soapWrapper->add(
                    //     'users',
                    //     function ($service) {
                    //         $service
                    //             ->wsdl('https://libertymutual.safetylearning.co.uk/api/1.3/UserManagement?wsdl');
                    //     }
                    // );

                    //check for croner differences and record in logs
                    if ($User->croner_access != $c_access) {
                        if (!empty($User->croner_access)) {
                            $this->log(
                                $User,
                                'Croner Access',
                                $User->croner_access,
                                $request->header('session_ip'),
                                $request->header('session_agent')
                            );
                        } elseif ($c_access != 0) {
                            $this->log(
                                $User,
                                'Croner Access',
                                0,
                                $request->header('session_ip'),
                                $request->header('session_agent')
                            );
                        }
                    }

                    //Check for safety media differences
                    if ($User->safetymedia_access != $sm_access) {
                        if (!empty($User->safetymedia_access)) {
                            $this->log(
                                $User,
                                'Safety Media Access',
                                $User->safetymedia_access,
                                $request->header('session_ip'),
                                $request->header('session_agent')
                            );
                        } elseif ($sm_access != 0) {
                            $this->log(
                                $User,
                                'Safety Media Access',
                                0,
                                $request->header('session_ip'),
                                $request->header('session_agent')
                            );
                        }
                    }

                    $this->dispatchAccessGroupQueues($User);

                    $organisation = Organisation::find($User->organisation_id);
                    // $departmentName = $organisation->name;

                    // if ($User->safetymedia_access == '0') {
                    //     $sm_disabled = '1';
                    // } else {
                    //     $sm_disabled = '0';
                    // }
                    // if ($User->manager == '1') {
                    //     $admin_level = 'clientadmin';
                    // } else {
                    //     $admin_level = 'user';
                    // }

                    // if ($organisation->tpid == '' || is_null($organisation->tpid)) {
                    //     $sm_account = config('app.sm.org_id_prefix') . $organisation->id;
                    // } else {
                    //     $sm_account = $organisation->tpid;
                    // }

                    // if ($User->tpid == '' || is_null($User->tpid)) {
                    //     $sm_id = config('app.sm.user_id_prefix') . $User->id;
                    // } else {
                    //     $sm_id = $User->tpid;
                    // }

                    // $data = [
                    //     'serviceCredentials' => [
                    //         'username' => config('app.sm.username'),
                    //         'password' => config('app.sm.password'),
                    //         'account' => $sm_account,
                    //     ],
                    //     'users' => [
                    //         'item' =>
                    //         [
                    //             'id' => $sm_id,
                    //             'username' => $User->email,
                    //             'password' => $User->safetymedia_password,
                    //             'forename' => $User->first_name,
                    //             'surname' => $User->last_name,
                    //             'email' => $User->email,
                    //             'disabled' => $sm_disabled,
                    //             'departmentName' => $departmentName,
                    //             'preferredLanguage' => 'English',
                    //             'adminLevel' => $admin_level,
                    //         ],
                    //     ],
                    // ];

                    //print_r($data);exit;

                    // Using the added service
                    // $this->soapWrapper->service('users', function ($service) use ($data) {
                    //     //var_dump($service->getFunctions());
                    //     $add_user_to_sm = $service->call('AddUser', [$data]);
                    // });

                    // Using the added service
                    /*
                    $this->soapWrapper->service(
                        'users', function ($service) use ($data) {
                            //var_dump($service->getFunctions());
                            $add_user_to_sm = $service->call('EditUser', [$data]);
                            //print_r($add_user_to_sm); exit;
                        }
                    );*/

                    // $add_user_to_sm = $this->soapWrapper->call('users.EditUser', [$data]);

                    // $this->soapWrapper->add(
                    //     'tra',
                    //     function ($servicetraining) {
                    //         $servicetraining
                    //             ->wsdl('https://libertymutual.safetylearning.co.uk/api/1.3/TrainingManagement?wsdl');
                    //     }
                    // );

                    // $data2 = [
                    //     'serviceCredentials' => [
                    //         'username' => config('app.sm.username'),
                    //         'password' => config('app.sm.password'),
                    //         'account' => $sm_account,
                    //     ],
                    //     'userTrainingPlans' => [
                    //         'item' =>
                    //         [
                    //             'userID' => $sm_id,
                    //             'trainingPlanID' => $sm_account,
                    //         ],
                    //     ],
                    // ];

                    // Using the added service
                    /*
                    $this->soapWrapper->service(
                        'tra', function ($servicetraining) use ($data2) {
                            //var_dump($service->getFunctions());
                            $add_user_to_tra = $servicetraining->call('AddUserToTrainingPlan', [$data2]);
                        }
                    );*/

                    // $add_user_to_tra = $this->soapWrapper->call('tra.AddUserToTrainingPlan', [$data2]);

                    $this->log(
                        $User,
                        'Update',
                        'User Updated',
                        $request->header('session_ip'),
                        $request->header('session_agent')
                    );
                    return Response::json(
                        [
                            'response' => 'success',
                            'message' => 'User Updated',
                        ]
                    );
                } else {
                    return Response::json(
                        [
                            'response' => 'error',
                            'message' => 'User data could not be saved',
                        ]
                    );
                }
            } else {
                return Response::json(
                    [
                        'response' => 'error',
                        'message' => 'No Valid User',
                    ]
                );
            }
        }
    }

    public function allFiltered(Request $request)
    {
        $query = $request->get('search');
        $limit = $request->get('limit') ?? 10;
        $order = $request->get('order') ?? 'last_name';
        $sort = $request->get('sort') ?? 'ASC';
        $columns = $request->get('columns') ? explode(',', $request->get('columns')) : null;

        $users = $columns ? User::select(...$columns) : User::query();

        if (!empty($query)) {
            $users->where(
                function ($q) use ($query) {
                    $q->where('first_name', 'LIKE', '%' . $query . '%')
                        ->orWhere('last_name', 'LIKE', '%' . $query . '%')
                        ->orWhere('email', 'LIKE', '%' . $query . '%');
                }
            )->orWhereHas('organisation', function ($q) use ($query) {
                $q->where('name', 'LIKE', '%' . $query . '%');
            });
        }

        $users->orderBy($order, $sort)->paginate($limit);

        $users = $users->paginate($limit);
        return Response::json([
            'response' => 'success',
            'data' => $users->items(),
            'total' => $users->total(),
        ]);
    }

    /**
     * Get All Users
     */
    public function all(Request $request, $page = 1, $limit = 10)
    {
        $from_survey = $request->get('from_survey');
        $search      = $request->get('search');
        $total = 0;

        if (isset($search) && $search != '') {
            $users = User::join('organisations', 'users.organisation_id', '=', 'organisations.id')
                ->where('users.email', 'LIKE', '%' . $search . '%')
                ->orWhereRaw('LOWER(TRIM(users.first_name)) LIKE ?', ["%{$search}%"])
                ->orWhereRaw('LOWER(TRIM(users.last_name)) LIKE ?', ["%{$search}%"])
                ->orWhere('organisations.name', 'LIKE', '%' . $search . '%')
                ->orWhereRaw("LOWER(TRIM(CONCAT(TRIM(users.first_name), ' ', TRIM(users.last_name)))) LIKE ?", ["%{$search}%"]);

            if ($from_survey) {
                $users = $users->where('users.is_submission_created', 1);
            }

            $total = $users->count();
            $users = $users->select([
                'users.id',
                'users.first_name',
                'users.last_name',
                'users.branch',
                'users.manager',
                'users.branch_name',
                'users.activated', 
                'organisations.name',
                'users.activation_code',
                'users.email',
                'users.established',
            ])->take($limit)->skip(($page * $limit) - $limit)->get();

            foreach ($users as $key => $user) {
                if (!is_null($user->deleted_at)) {
                    unset($users[$key]);
                }
            }
        } else {
            $users = DB::table('users')->select([
                'users.id',
                'users.first_name',
                'users.last_name',
                'users.branch',
                'users.manager',
                'users.branch_name',
                'users.activated', 'organisations.name',
                'users.activation_code',
                'users.email',
                'users.established',
            ])->join('organisations', 'users.organisation_id', '=', 'organisations.id');

            if ($from_survey) {
                $users = $users->where('is_submission_created', 1);
            }

            $total = $users->count();
            $users = $users->take($limit)->skip(($page * $limit) - $limit)->get();
        }

        return Response::json([
            'response' => 'success',
            'data'     => $users,
            'total'    => $total,
        ]);
    }

    /**
     * Find User
     */
    public function find($userID)
    {
        if ($userID) {
            $user = User::find($userID);
            if (!empty($user)) {
                return Response::json(
                    [
                        'response' => 'success',
                        'data' => $user,
                    ]
                );
            }

            $user = User::where('email', '=', $userID)->first();
            if (!empty($user)) {
                return Response::json(
                    [
                        'response' => 'success',
                        'data' => $user,
                    ]
                );
            }
        }
    }

    /**
     * Reset Login Attempt: External Surveyors
     */
    public function resetLoginExternalUser($id)
    {
        $attempt = ExternalSurveyor::where('id', $id)
            ->update(['attempts' => 0]);

        if ($attempt) {
            return Response::json(
                [
                    'response' => 'success',
                ],
                200
            );
        } else {
            return Response::json(
                [
                    'response' => 'error',
                ],
                200
            );
        }
    }

    public function resetLoginAttempts($id)
    {
        $isResetLoginSuccess = User::where('id', $id)->update(['login_attempts' => 0]);
        if ($isResetLoginSuccess) {
            return response()->json(['response' => 'success']);
        }
        return response()->json(['response' => 'error']);
    }

    /**
     * Store new User
     */

    public function store(Request $request)
    {
        //validate the info
        $rules = [
            'email' => 'unique:users|required',
            'first_name' => 'required',
            'last_name' => 'required',
            'manager' => 'required',
            'branch' => 'required',
            'organisation_id' => 'required',
            'address_line_1' => 'required_if:branch,on',
            'postcode' => 'required_if:branch,on',
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json(
                [
                    'response' => 'error',
                    'errors' => $validator->messages()->toArray(),
                ],
                200
            );
        } else {
            $uuid = Uuid::generate(4);
            $sm_password = $request->get('safetymedia_access') == 1
                ? uniqid()
                : '';
            $expiry = time() + (14 * 24 * 60 * 60); // 14 days
            $data = [
                'email' => $request->get('email'),
                'password' => Hash::make($request->get('password')),
                'first_name' => $request->get('first_name'),
                'last_name' => $request->get('last_name'),
                'address_line_1' => $request->get('address_line_1'),
                'address_line_2' => $request->get('address_line_2'),
                'postcode' => $request->get('postcode'),
                'country' => $request->get('country'),
                'manager' => $request->get('manager'),
                'branch' => $request->get('branch'),
                'branch_name' => $request->get('branch_name'),
                'organisation_id' => $request->get('organisation_id'),
                'activation_code' => $uuid->string,
                'activation_code_expires' => $expiry,
                'client_dashboard_access' => $request->get('client_dashboard_access'),
                'croner_access' => $request->get('croner_access'),
                'safetymedia_access' => $request->get('safetymedia_access'),
                'astutis_access' => $request->get('astutis_access'),
                'safetymedia_password' => $sm_password,
                'c_live_access' => $request->get('c_live_access'),
                'triton_access' => $request->get('triton_access'),
                'branch_id' => $request->get('branch_id'),
                'file' => $request->has('file')
                    ? $request->get('file')
                    : null,
                'original_filename' => $request->has('original_filename')
                    ? $request->get('original_filename')
                    : null,
                'is_submission_created' => $request->has('is_submission_created')
                    ? 1
                    : 0,
                'phone' => $request->has('phone')
                    ? $request->get('phone')
                    : null,
                'jobtitle' => $request->has('jobtitle')
                    ? $request->get('jobtitle')
                    : null,
                'secondary_contact' => $request->has('secondary_contact')
                    ? $request->get('secondary_contact')
                    : 0,
                'has_previsico_access' => $request->has('has_previsico_access')
                ? $request->get('has_previsico_access')
                : 0,
            ];

            if (strtolower($request->get('cati_status')) == 'active' && $request->get('manager')) {
                $organisation = Organisation::find($request->get('organisation_id'));
                if ($organisation->cati_status != 'active' || !$organisation->cati_id) {
                    return Response::json(
                        [
                            'response' => 'error',
                            'message' => '<b>User Creation Failed!</b><br/><i>Cati has not been provisioned for this user\'s organisation.</i><br/>If you wish to generate a user account with access to Cati, please enable Cati for this user\'s organisation first.',
                        ],
                        200
                    );
                }

                $data['cati_status'] = $request->get('cati_status');
                try {
                    $cati = new Cati();
                    $response = $cati->createClientUser(
                        [
                            "ClientID" => $organisation->cati_id,
                            "Forename" => $data['first_name'],
                            "Surname" => $data['last_name'],
                            "EmailAddress" => $data['email'],
                            "Username" => $data['email'],
                        ]
                    );

                    if ($response && $response->StatusCode == 'CU00') {
                        $data['cati_id'] = $response->UserID;
                    } else {
                        return Response::json(
                            [
                                'response' => 'error',
                                'message' => sprintf(
                                    $this->catiErrorMessage,
                                    $response->StatusCode,
                                    $response->StatusDescription
                                ),
                            ]
                        );
                    }
                } catch (Exception $e) {
                    return Response::json(
                        [
                            'response' => 'error',
                            'message' => sprintf($this->catiErrorMessage, $e->getCode(), $e->getMessage()),
                        ]
                    );
                }
            }

            $User = User::create($data);

            if ($request->has('previsico_asset_ids')) {
                $previsicoAssetAccessIds = $request->get('previsico_asset_ids');
                $previsicoAssets = PrevisicoAsset::whereIn('id', $previsicoAssetAccessIds)->get();
                foreach ($previsicoAssets as $previsicoAsset) {
                    PrevisicoAssetAccess::firstOrNew([
                        'user_id' => $User->id,
                        'previsico_asset_id' => $previsicoAsset->id
                    ])->save();
                }
            }

            $this->dispatchAccessGroupQueues($User);

            // $this->soapWrapper->add(
            //     'users',
            //     function ($service) {
            //         $service
            //             ->wsdl('https://libertymutual.safetylearning.co.uk/api/1.3/UserManagement?wsdl');
            //     }
            // );

            $organisation = Organisation::find($User->organisation_id);
            // $departmentName = $organisation->name;
            // $sm_disabled = $User->safetymedia_access == '0'
            //     ? '1'
            //     : '0';
            // $admin_level = $User->manager == '1'
            //     ? 'clientadmin'
            //     : 'user';
            // $sm_account = $organisation->tpid == '' || is_null($organisation->tpid)
            //     ? config('app.sm.org_id_prefix') . $organisation->id
            //     : $organisation->tpid;

            // $data = [
            //     'serviceCredentials' => [
            //         'username' => config('app.sm.username'),
            //         'password' => config('app.sm.password'),
            //         'account' => $sm_account,
            //     ],
            //     'users' => [
            //         'item' =>
            //         [
            //             'id' => config('app.sm.user_id_prefix') . $User->id,
            //             'username' => $User->email,
            //             'password' => $User->safetymedia_password,
            //             'forename' => $User->first_name,
            //             'surname' => $User->last_name,
            //             'email' => $User->email,
            //             'disabled' => $sm_disabled,
            //             'departmentName' => $departmentName,
            //             'preferredLanguage' => 'English',
            //             'adminLevel' => $admin_level,
            //         ],
            //     ],
            // ];

            // Using the added service
            /*
            $this->soapWrapper->service(
                'users', function ($service) use ($data) {
                    //var_dump($service->getFunctions());
                    $add_user_to_sm = $service->call('AddUser', [$data]);

                }
            );*/
            // $add_user_to_sm = $this->soapWrapper->call('users.AddUser', [$data]);

            // $this->soapWrapper->add(
            //     'tra',
            //     function ($servicetraining) {
            //         $servicetraining
            //             ->wsdl('https://libertymutual.safetylearning.co.uk/api/1.3/TrainingManagement?wsdl');
            //     }
            // );

            // $data2 = [
            //     'serviceCredentials' => [
            //         'username' => config('app.sm.username'),
            //         'password' => config('app.sm.password'),
            //         'account' => $sm_account,
            //     ],
            //     'userTrainingPlans' => [
            //         'item' =>
            //         [
            //             'userID' => config('app.sm.user_id_prefix') . $User->id,
            //             'trainingPlanID' => $sm_account,
            //         ],
            //     ],
            // ];

            // Using the added service
            /*
            $this->soapWrapper->service(
                'tra', function ($servicetraining) use ($data2) {
                    $add_user_to_tra = $servicetraining->call('AddUserToTrainingPlan', [$data2]);
                }
            );*/
            // $add_user_to_tra = $this->soapWrapper->call('tra.AddUserToTrainingPlan', [$data2]);

            // UNCOMMENT TO START SENDING EMAILS
            $this->mail->queue(
                $User->email,
                $User->fullName(),
                'Risk Reduce, Welcome to Risk Reduce',
                'emails.auth.welcome',
                $User
            );

            if ($User->id) {
                $c_access = $this->user->find($User->id)->croner_access;
                $sm_access = $this->user->find($User->id)->safetymedia_access;
                $sm_disabled = $User->safetymedia_access == '0'
                    ? '1'
                    : '0';
                $admin_level = $User->manager == '1'
                    ? 'clientadmin'
                    : 'user';

                $this->log(
                    $User,
                    'Create',
                    'User Created',
                    $request->header('session_ip'),
                    $request->header('session_agent')
                );

                //check for croner differences and record in logs
                if ($User->croner_access == $c_access && !empty($User->croner_access)) {
                    $this->log(
                        $User,
                        'Croner Access',
                        $User->croner_access,
                        $request->header('session_ip'),
                        $request->header('session_agent')
                    );
                }

                //Check for safety media differences
                if ($User->safetymedia_access == $sm_access && !empty($User->safetymedia_access)) {
                    $this->log(
                        $User,
                        'Safety Media Access',
                        $User->safetymedia_access,
                        $request->header('session_ip'),
                        $request->header('session_agent')
                    );
                }

                if ($request->has('login_type') && $request->get('login_type') == 'broker-user') {
                    $User->org = $User->organisation;
                    $User->request_id = $User->id;

                    $this->mail->queue(
                        config('app.risk_control'),
                        'Client User Created',
                        'Risk Reduce, Client User Creation Notification',
                        'emails.users.created',
                        $User
                    );
                }

                return Response::json(
                    [
                        'response' => 'success',
                        'message' => 'User successfully created',
                        'data' => $User->id,
                    ]
                );
            } else {
                return Response::json(
                    [
                        'response' => 'error',
                        'message' => 'Unable to create user',
                    ],
                    200
                );
            }
        }
    }

    public function claim()
    {
        $this->mail->claim();
    }

    /**
     * Delete Member
     *
     * @param $id
     *
     * @return mixed
     */
    public function destroy(Request $request)
    {
        $id = $request->get('id');

        if (isset($id)) {
            $User = User::find($id);
            if (isset($User)) {
                // Archive user in Access Group LMS
                (new ArchiveUsersService($User))->archiveUser();

                // $this->soapWrapper->add(
                //     'users',
                //     function ($service) {
                //         $service
                //             ->wsdl('https://libertymutual.safetylearning.co.uk/api/1.3/UserManagement?wsdl');
                //     }
                // );


                $organisation = Organisation::find($User->organisation_id);
                // $departmentName = $organisation->name;

                // if ($User->safetymedia_access == '0') {
                //     $sm_disabled = '1';
                // } else {
                //     $sm_disabled = '0';
                // }
                // if ($User->manager == '1') {
                //     $admin_level = 'clientuser';
                // } else {
                //     $admin_level = 'user';
                // }

                // if ($organisation->tpid == '' || is_null($organisation->tpid)) {
                //     $sm_account = config('app.sm.org_id_prefix') . $organisation->id;
                // } else {
                //     $sm_account = $organisation->tpid;
                // }

                if ($User->cati_status == 'active' && $User->cati_id) {
                    $cati = new Cati();
                    try {
                        $response = $cati->suspendClientUser($User->cati_id);
                        if ($response && $response->StatusCode != 'DU00') {
                            return Response::json(
                                [
                                    'response' => 'error',
                                    'message' => sprintf(
                                        $this->catiErrorMessage,
                                        $response->StatusCode,
                                        $response->StatusDescription
                                    ),
                                ]
                            );
                        }
                    } catch (Exception $e) {
                        return Response::json(
                            [
                                'response' => 'error',
                                'message' => sprintf($this->catiErrorMessage, $e->getCode(), $e->getMessage()),
                            ]
                        );
                    }
                }

                // if ($User->tpid == '' || is_null($User->tpid)) {
                //     $sm_id = config('app.sm.user_id_prefix') . $User->id;
                // } else {
                //     $sm_id = $User->tpid;
                // }

                // $data = [
                //     'serviceCredentials' => [
                //         'username' => config('app.sm.username'),
                //         'password' => config('app.sm.password'),
                //         'account' => $sm_account,
                //     ],
                //     'users' => [
                //         'item' =>
                //         [
                //             'id' => $sm_id,
                //             'username' => $User->email,
                //             'password' => $User->safetymedia_password,
                //             'forename' => $User->first_name,
                //             'surname' => $User->last_name,
                //             'email' => $User->email,
                //             'disabled' => 1,
                //             'departmentName' => $User->departmentName,
                //             'preferredLanguage' => 'English',
                //             'adminLevel' => $admin_level,
                //         ],
                //     ],
                // ];

                // Using the added service
                /*
                $this->soapWrapper->service(
                    'users', function ($service) use ($data) {
                        //var_dump($service->getFunctions());
                        $add_user_to_sm = $service->call('EditUser', [$data]);
                    }
                );
                */
                // $add_user_to_sm = $this->soapWrapper->call('users.EditUser', [$data]);
            }
            if (User::destroy($id)) {
                $this->log(
                    $User,
                    'Destroy',
                    'User Destroyed',
                    $request->header('session_ip'),
                    $request->header('session_agent')
                );
                return Response::json(
                    [
                        'response' => 'success',
                        'message' => 'Member deleted',
                    ],
                    200
                );
            } else {
                return Response::json(
                    [
                        'response' => 'error',
                        'message' => 'Unable to delete member',
                    ],
                    200
                );
            }
        } else {
            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'Unable to delete member',
                ],
                200
            );
        }
    }

    /**
     * Get All Broker Users
     */
    public function broker(Request $request, $id, $page = 1, $limit = 10)
    {
        $from_survey = $request->get('from_survey');
        $search = $request->get('search');

        if (isset($from_survey) && $from_survey != '') {
            $mgas = MgaScheme::where('broker_id', $id)->pluck('id')->all();

            $organisations = Organisation::whereIn('mga_scheme', $mgas)->pluck('id')->all();

            $Users = new User;
            $Users = $Users->whereIn('organisation_id', $organisations);
            if ($from_survey) {
                $Users = $Users->where('is_submission_created', 1);
            }
            $Users = $Users->with('organisation')->take($limit)->skip(($page * $limit) - $limit)->get();
        } else {
            if (isset($search) && $search != '') {
                $mgas = MgaScheme::where('broker_id', $id)->pluck('id')->all();

                $organisations = Organisation::whereIn('mga_scheme', $mgas)
                    ->orWhere('name', 'LIKE', '%' . $search . '%')
                    ->pluck('id')->all();

                $Users = new User;
                $Users = $Users->whereIn('organisation_id', $organisations);
                $Users = $Users->where('email', 'LIKE', '%' . $search . '%')
                    ->orWhere('first_name', 'LIKE', '%' . $search . '%')
                    ->orWhere('last_name', 'LIKE', '%' . $search . '%');
                $Users = $Users->with('organisation')->take($limit)->skip(($page * $limit) - $limit)->get();
            } else {
                $mgas = MgaScheme::where('broker_id', $id)->pluck('id')->all();

                $organisations = Organisation::whereIn('mga_scheme', $mgas)->pluck('id')->all();

                $Users = new User;
                $Users = $Users->whereIn('organisation_id', $organisations);
                $Users = $Users->with('organisation')->take($limit)->skip(($page * $limit) - $limit)->get();
            }
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => $Users,
                'total' => count($Users),
            ]
        );
    }

    /**
     * Send Reset Password code
     */

    public function sendResetPasswordCode(Request $request)
    {
        $email = $request->get('email');
        if (isset($email)) {
            $User = User::where('email', '=', $email)->first();


            if (isset($User)) {
                //generate reset code
                $uuid = Uuid::generate(4);
                $hours = config('auth.reminder.expire');
                $expiry = time() + ($hours * 60 * 60); 

                $User->reset_password_code = $uuid->string;
                $User->reset_password_code_expires = $expiry;
                $User->save();

                $this->log(
                    $User,
                    'Password Reset',
                    'User Reset Password',
                    $request->header('session_ip'),
                    $request->header('session_agent')
                );

                $this->mail->queue(
                    $User->email,
                    $User->Fullname(),
                    'Risk Reduce, Your requested password',
                    'emails.auth.reminderClient',
                    $User
                );

                return Response::json(
                    [
                        'response' => 'success',
                        'message' => 'User has been sent an email with details to reset their password',
                    ]
                );
            }
        }

        return Response::json(
            [
                'response' => 'error',
                'message' => 'Failed to reset User password',
            ]
        );
    }

    /**
     * Reset Password using new password and code to verify user
     */

    public function resetPassword(Request $request)
    {
        $password = $request->get('password');
        $code = $request->get('code');

        if (isset($code) && isset($password)) {
            $User = User::where('reset_password_code', '=', $code)->first();

            if (isset($User) && $User->reset_password_code_expires >= time() && $User->reset_password_code == $code) {

                //reset password and reset codes to 0
                $User->password = Hash::make(Crypt::decrypt($password));
                $User->reset_password_code = null;
                $User->reset_password_code_expires = null;
                $User->password_change_updated_at = now();
                $User->login_attempts = 0;
                $User->save();

                $this->log(
                    $User,
                    'Password Reset Confirm',
                    'User Reset Password Confirmation',
                    $request->header('session_ip'),
                    $request->header('session_agent')
                );

                $this->createPreviousPasswordEntry($User);


                return Response::json(
                    [
                        'response' => 'success',
                        'message' => 'User password has been reset',
                        'user_id' => $User->id
                    ]
                );
            }
        }

        return Response::json(
            [
                'response' => 'error',
                'message' => 'Could not reset User password',
            ]
        );
    }

    /**
     * Activate an User
     */

    public function activate(Request $request)
    {
        $code = $request->get('code');
        $email = $request->get('email');

        if (isset($code) && isset($email)) {
            $User = User::where('email', '=', $email)->first();

            if (isset($User) && $User->activation_code == $code && $User->activation_code_expires >= time()) {
                $password = Crypt::decrypt($request->get('password'));
                $User->password = Hash::make($password);
                $User->activated = true;
                $User->activation_code = null;
                $User->activation_code_expires = null;
                $User->password_change_updated_at = now();

                $User->save();

                $this->log(
                    $User,
                    'Activation',
                    'User Activation',
                    $request->header('session_ip'),
                    $request->header('session_agent')
                );

                $this->createPreviousPasswordEntry($User);

                return Response::json(
                    [
                        'response' => 'success',
                        'message' => 'User activated',
                    ]
                );
            } else {
                return Response::json(
                    [
                        'response' => 'error',
                        'message' => 'Activation code was invalid.',
                    ]
                );
            }
        }

        return Response::json(
            [
                'response' => 'error',
                'message' => 'Could not activate user. Please provide a valid email and activation link',
            ]
        );
    }

    /**
     * Change password
     */

    public function change_password(Request $request)
    {
        $current_password = $request->get('password');
        $new_password = $request->get('new_password');
        $user_id = $request->get('id');

        if (isset($current_password) && isset($new_password)) {
            $User = User::find($user_id);

            if (isset($User) && Hash::check($current_password, $User->password)) {
                //$new_password = Crypt::decrypt($new_password);
                $User->password = Hash::make($new_password);
                $User->password_change_updated_at = now();

                $User->save();

                $this->log(
                    $User,
                    'Change Password',
                    'User Change Password',
                    $request->header('session_ip'),
                    $request->header('session_agent')
                );

                return Response::json(
                    [
                        'response' => 'success',
                        'message' => 'Password changed',
                    ]
                );
            } else {
                return Response::json(
                    [
                        'response' => 'error',
                        'message' => 'Invalid request',
                    ]
                );
            }
        }

        return Response::json(
            [
                'response' => 'error',
                'message' => 'Could not change password',
            ]
        );
    }

    /**
     * Get All Branch Users
     */
    public function branchUsers($org_id = 0)
    {
        if ($org_id == 0) {
            $Users = User::where('branch', '=', true)->get();
        } else {
            $Users = User::where('branch', '=', true)->where('organisation_id', '=', $org_id, 'AND')->get();
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => $Users,
            ]
        );
    }

    public function generateLink($userID)
    {
        if (isset($userID)) {
            $user = User::find($userID);
            if (isset($user)) {
                $code = Uuid::generate(4);
                $user->activation_code = $code->string;
                $user->activation_code_expires = strtotime('+14 days');
                $user->save();
                return Response::json(
                    [
                        'response' => 'success',
                        'data' => $user->toArray(),
                    ]
                );
            } else {
                $user = User::where('email', '=', $userID)->first();
                if (isset($user)) {
                    return Response::json(
                        [
                            'response' => 'success',
                            'data' => $user,
                        ]
                    );
                }
            }
        }
    }

    /**
     * Find User and Org
     */
    public function findUserOrg($userID)
    {
        if (isset($userID)) {
            $user = User::find($userID);
            if (isset($user)) {
                $organisation = Organisation::find($user->organisation_id);
                // print_r($organisation);exit;
                if ($organisation) {
                    return Response::json(
                        [
                            'response' => 'success',
                            'user' => $user,
                            'organisation' => $organisation,
                        ]
                    );
                } else {
                    return Response::json(
                        [
                            'response' => 'success',
                            'user' => $user,
                        ]
                    );
                }
            } else {
                $user = User::where('email', '=', $userID)->first();
                if (isset($user)) {
                    return Response::json(
                        [
                            'response' => 'success',
                            'data' => $user,
                        ]
                    );
                } else {
                    return Response::json(
                        [
                            'response' => 'error',
                            'data' => 'User doesn\'t exist',
                        ]
                    );
                }
            }
        }
    }

    /**
     * Find all branch users within an organisation
     */
    public function findBranchesByOrganisation($organisation_id)
    {
        $users = User::where('organisation_id', '=', $organisation_id)->where(
            'branch',
            '=',
            '1',
            'AND'
        )->where('activated', '=', '1', 'AND')->get();

        return Response::json(
            [
                'response' => 'success',
                'data' => $users->toArray(),
            ]
        );
    }

    /**
     * Find all users within an organisation
     */
    public function findUsersByOrganisation($id)
    {
        $users = User::where('organisation_id', '=', $id)->get();
        foreach ($users as $user) {
            if ($user->branch == 1) {
                $user->parent_branch = $user->branch_name;
            } elseif (is_null($user->branch_id) || $user->branch_id == '') {
                $user->parent_branch = '-';
            } else {
                $pb = User::where('id', $user->branch_id)->select('branch_name')->first();
                if ($pb) {
                    $user->parent_branch = User::where(
                        'id',
                        $user->branch_id
                    )->select('branch_name')->first()->branch_name;
                } else {
                    $user->parent_branch = '-';
                }
            }
        }


        return Response::json(
            [
                'response' => 'success',
                'data' => $users->toArray(),
            ]
        );
    }

    /**
     * sendWelcome email
     */
    public function sendWelcome($userID)
    {
        $User = User::find($userID);
        $User->activation_code = Uuid::generate(4);
        $User->activation_code_expires = strtotime('+14 days');
        $User->save();
        $this->mail->queue(
            $User->email,
            $User->fullName(),
            'Risk Reduce, Welcome to Risk Reduce',
            'emails.auth.welcome',
            $User
        );
        return Response::json(
            [
                'response' => 'success',
            ]
        );
    }

    /**
     * Register a user
     */
    public function register(Request $request)
    {
        $data = $request->get();

        //Striped if - exists
        $policy_number = explode('-', $data['policy_number']) !== false
            ? explode('-', $data['policy_number'])
            : [$data['policy_number']];
        $policy_number = $policy_number[0];
        $expire_date = Carbon::createFromTimestamp($data['expiry_date_of_cover'])->hour(0)->minute(0)->second(0);
        $policy = OrganisationPolicy::where('policy_number', 'LIKE', $policy_number)
            ->where('expiry_date_of_cover', 'LIKE', $expire_date)
            ->first();

        if ($policy != null) {
            $organisation = $this->organisation->find($policy->organisation_id);
        }

        if (isset($organisation)) {
            if (count($organisation->users) == 0) {
                $code = Uuid::generate(4);
                //create user and send invitation email
                $data['activation_code'] = $code->string;
                $data['safetymedia_password'] = uniqid();
                $data['activation_code_expires'] = time() + (2 * 24 * 60 * 60); //2 days
                $data['organisation_id'] = $organisation->id;
                $data['manager'] = true;
                $user = $this->user->create($data);
                if (isset($user)) {
                    // $this->soapWrapper->add(
                    //     function ($service) {
                    //         $service
                    //             ->name('users')
                    //             ->wsdl('https://libertymutual.safetylearning.co.uk/api/1.3/UserManagement?wsdl');
                    //     }
                    // );


                    // $departmentName = $organisation->name;

                    // if ($user->safetymedia_access == '0') {
                    //     $sm_disabled = '1';
                    // } else {
                    //     $sm_disabled = '0';
                    // }
                    // if ($user->manager == '1') {
                    //     $admin_level = 'clientadmin';
                    // } else {
                    //     $admin_level = 'user';
                    // }

                    // if ($organisation->tpid == '' || is_null($organisation->tpid)) {
                    //     $sm_account = config('app.sm.org_id_prefix') . $organisation->id;
                    // } else {
                    //     $sm_account = $organisation->tpid;
                    // }

                    // $data = [
                    //     'serviceCredentials' => [
                    //         'username' => config('app.sm.username'),
                    //         'password' => config('app.sm.password'),
                    //         'account' => $sm_account,
                    //     ],
                    //     'users' => [
                    //         'item' =>
                    //         [
                    //             'id' => config('app.sm.user_id_prefix') . $user->id,
                    //             'username' => $user->email,
                    //             'password' => $user->safetymedia_password,
                    //             'forename' => $user->first_name,
                    //             'surname' => $user->last_name,
                    //             'email' => $user->email,
                    //             'disabled' => $sm_disabled,
                    //             'departmentName' => $departmentName,
                    //             'preferredLanguage' => 'English',
                    //             'adminLevel' => $admin_level,
                    //         ],
                    //     ],
                    // ];

                    // Using the added service
                    // $this->soapWrapper->service(
                    //     'users',
                    //     function ($service) use ($data) {
                    //         //var_dump($service->getFunctions());
                    //         $add_user_to_sm = $service->call('AddUser', [$data]);
                    //     }
                    // );

                    // $this->soapWrapper->add(
                    //     function ($servicetraining) {
                    //         $servicetraining
                    //             ->name('tra')
                    //             ->wsdl('https://libertymutual.safetylearning.co.uk/api/1.3/TrainingManagement?wsdl');
                    //     }
                    // );

                    // $data2 = [
                    //     'serviceCredentials' => [
                    //         'username' => config('app.sm.username'),
                    //         'password' => config('app.sm.password'),
                    //         'account' => $sm_account,
                    //     ],
                    //     'userTrainingPlans' => [
                    //         'item' =>
                    //         [
                    //             'userID' => config('app.sm.user_id_prefix') . $user->id,
                    //             'trainingPlanID' => config('app.sm.org_id_prefix') . $organisation->id,
                    //         ],
                    //     ],
                    // ];

                    // Using the added service
                    // $this->soapWrapper->service(
                    //     'tra',
                    //     function ($servicetraining) use ($data2) {
                    //         //var_dump($service->getFunctions());
                    //         $add_user_to_tra = $servicetraining->call('AddUserToTrainingPlan', [$data2]);
                    //     }
                    // );

                    $this->log(
                        $user,
                        'Register',
                        'User Registered',
                        $request->header('session_ip'),
                        $request->header('session_agent')
                    );

                    $this->mail->queue(
                        $user->email,
                        $user->fullName(),
                        'Risk Reduce, Welcome to Risk Reduce',
                        'emails.auth.welcome',
                        $user
                    );

                    return Response::json(
                        [
                            'response' => 'success',
                            'message' => 'Please check your email for a verification message and follow the link provided to set up your password.',
                        ]
                    );
                }

                return Response::json(
                    [
                        'response' => 'error',
                        'message' => 'There was an error. Please try again.',
                    ]
                );
            }
            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'A user has already signed up to this organisation. Please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>',
                ]
            );
        }

        return Response::json(
            [
                'response' => 'error',
                'message' => 'Your Liberty Risk Reduce registration hasn’t been fully activated yet. Please email <a href="mailto:<EMAIL>"><EMAIL></a> with your company name, policy number and expiry date of cover.
                                We’ll complete the process and confirm back to you shortly.',
            ]
        );
    }

    public function licenses()
    {
        $results = DB::select(
            DB::raw(
                'SELECT organisation_id, SUM(if(croner_access = \'1\', 1, 0)) as croner, SUM(if(safetymedia_access = \'1\', 1, 0)) as safetymedia
                                        FROM users
                                        GROUP BY organisation_id'
            )
        );

        foreach ($results as $key => $org) {
            $results[$key]->organisation = $this->organisation->find($org->organisation_id);
        }

        return Response::json(['response' => 'success', 'data' => $results], 200);
    }

    public function registerNotificationToken(Request $request)
    {
        $data = $request->except('_token');
        $insertRow = Notification::firstOrCreate($data);

        if ($insertRow) {
            return Response::json(['response' => 'success', 'data' => $insertRow], 200);
        }
    }

    public function getPrevisicoUiNotif(Request $request)
    {
        $email = $request->get('email');
        $notification = User::select('has_previsico_alert_ui_notification')
            ->where('email', $email)
            ->first();

        return $notification->has_previsico_alert_ui_notification;
    }

    public function clearPrevisicoUiNotif(Request $request)
    {
        $email = $request->get('email');
        $notification = User::where('email', $email)
            ->update(
                [
                    'has_previsico_alert_ui_notification' => 0,
                ]
            );
    }

    // Dispatch the Access Group create org or create user queue if needed 
    private function dispatchAccessGroupQueues(User $user): void
    {
        // If Access Group LMS Org already exists, it will not create a new one
        $createOrgService = new CreateOrganisationService($user);
        if ($createOrgService->shouldCreate()) {
            CreateAccessGroupOrganisationJob::dispatch($createOrgService);
            return;
        }

        // If Access Group LMS Org already exists, we just need to dispatch the create user
        $createUserService = new CreateUserService($user);
        if ($createUserService->shouldCreate()) {
            CreateAccessGroupPersonJob::dispatch($createUserService);
            return;
        }

        // Update AccessGroup LMS Person details
        (new UpdatePersonService($user))->update();
    }

    public function getMultipleUserDetails(Request $request)
    {
        $libertySubmissionUserIds = $request->all();
        $users = User::select('id', 'first_name', 'last_name')->whereIn('id', $libertySubmissionUserIds)->get();
        return Response::json(['response' => 'success', 'data' => $users], 200);
    }

    public function checkWelcomeModalViewed($userID)
    {
        $userid=(int)$userID;
        $viewed = UserExtraInfo::select('welcome_modal_viewed')
            ->where('user_id', $userid)
            ->count();

        if($viewed == 0 )
        {
            UserExtraInfo::create([
                'user_id' => (int)$userID,
                'welcome_modal_viewed' => true,
            ]);
        }
        return Response::json(['response' => 'success', 'data' => $viewed], 200);
    }

    public function updateTourModalViewed($userID)
    {
        $userid=(int)$userID;
        $data=['user_id' => (int)$userID,'tour_modal_viewed' => true];
        $user = UserExtraInfo::where('user_id', $userid)->first();
        if ($user) {
            $user->update($data);
        } else {
            $user = UserExtraInfo::create($data);
        }
        return Response::json(['response' => 'success', 'data' => $user], 200);
    }

    public function getTourModalViewed($userID)
    {
        $userid=(int)$userID;
        $count = UserExtraInfo::where('user_id', $userid)
        ->where('tour_modal_viewed',true)->count();
       
        return Response::json(['response' => 'success', 'data' => $count], 200);
    }
    
    private function createPreviousPasswordEntry(User $user)
    {
        PreviousPassword::create([
            'user_id' => $user->id,
            'password' => $user->password,
            'table' => 'users',
        ]);
    }
}
