<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\EwDocument;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\Request;

class EwDocumentController extends BaseController
{

    public function update(Request $request)
    {
        $data = $request->all();
        $id = $request->get('id');
        $ew = EwDocument::find($id);

        if (isset($ew)) {
            $ew->name = $request->get('name');
            $ew->description = $request->get('description');

            if ($request->has('file') && $request->has('original_filename')) {
                $ew->file = $request->get('file');
                $ew->original_filename = $request->get('original_filename');
            }

            $ew->authority = $request->has('authority')
                ? 1
                : 0;

            if ($ew->save()) {
                return Response::json(
                    [
                    'response' => 'success',
                    'message' => 'Endorsement document updated',
                    ]
                );
            }

            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to update the endorsement document',
                ]
            );
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'Unable to find endorsement document',
            ]
        );
    }

    public function all()
    {
        $documents = EwDocument::all();

        if (!$documents) {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to get results',
                ]
            );
        }

        $data['documents'] = $documents;

        return Response::json(
            [
            'response' => 'success',
            'message' => 'Results found',
            'data' => $data,
            ]
        );
    }

    public function find($id)
    {
        $ew = EwDocument::find($id);

        if (isset($ew)) {
            return Response::json(
                [
                'response' => 'success',
                'data' => $ew,
                ]
            );
        }

        return Response::json(
            [
            'response' => 'error',
            'error' => 'Unable to locate endorsement document',
            ]
        );
    }

    public function authorise($id)
    {
        $ew = EwDocument::find($id);

        if (isset($ew)) {
            $ew->authority = 1;

            if ($ew->save()) {
                return Response::json(
                    [
                    'response' => 'success',
                    'message' => 'Endorsement document authorized',
                    ]
                );
            }

            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to authorize endorsement document',
                ]
            );
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'Unable to find endorsement document',
            ]
        );
    }

    public function store(Request $request)
    {
        $data = $request->all();
        $ew = EwDocument::create($data);

        if (isset($ew)) {
            return Response::json(
                [
                'response' => 'success',
                'message' => 'Endorsement document created',
                ]
            );
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'Unable to create endorsement document',
            ]
        );
    }

    public function destroy(Request $request, $id)
    {
        $data = $request->all();
        $ew = EwDocument::destroy($id);

        if (isset($ew)) {
            return Response::json(
                [
                'response' => 'success',
                'message' => 'Endorsement document deleted',
                ]
            );
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'Unable to delete endorsement document',
            ]
        );
    }

}
