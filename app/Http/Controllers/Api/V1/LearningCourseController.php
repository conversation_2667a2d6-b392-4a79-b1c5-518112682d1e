<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use Carbon\Carbon;
use App\Models\Course;
use App\Models\CourseCategory;
use App\Models\CourseOrganisation;
use App\Models\CourseUser;
use Illuminate\Support\Facades\DB;
use App\Models\Lesson;
use App\Models\LmsCourseCertificate;
use App\Models\LmsTestResults;
use App\Models\LmsTests;
use App\Models\LmsUserLesson;
use App\Models\Mailqueue;
use App\Models\Organisation;
use App\Models\Pages;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use App\Models\Sector;
use App\Models\SectorCourses;
use App\Models\User;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class LearningCourseController extends BaseController
{

    const SECTOR_TYPE_NOT_FOR_PROFIT = "Not for Profit";

    protected $relations = [
        'organisation',
        'lessons',
        'category',
    ];

    public function __construct(Mailqueue $mailqueue)
    {
        $this->mail = $mailqueue;
    }

    /**
     * Store new Course
     */

    public function store(Request $request)
    {
        $validator = Validator::make(
            $request->all(), [
            'title' => 'required',
            ]
        );

        if ($validator->fails()) {
            $response = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        } else {
            $inputData = $request->all();
            $organisationId = null;
            if ($inputData['src'] === 'organisation' && $request->has('organisation')) {
                $organisationId = $inputData['organisation'];
            } elseif ($inputData['src'] === 'liberty' && $request->has('organisation_id')) {
                $organisationId = $inputData['organisation_id'];
            } elseif ($request->has('owner')) {
                $organisationId = $inputData['owner'];
            }

            $course = Course::create([
                'organisation_id' => $organisationId,
                'title'           => $request->get('title'),
                'description'     => $request->get('description', null),
                'certificate'     => $request->get('certificate', 0),
                'published'       => $request->get('published', 0),
            ]);

            if ($course->id) {
                if ($request->get('src') === 'liberty') {
                    if ($request->has('organisation') && isset($inputData['owner'])) {
                        $organisations = $request->get('organisation');
                        foreach ($organisations as $organisation) {
                            CourseOrganisation::create([
                                'course_id'       => $course->id,
                                'organisation_id' => $organisation,
                            ]);
                        }
                    }

                    if ($request->has('category')) {
                        $categories = $request->get('category');
                        foreach ($categories as $category) {
                            CourseCategory::create(['course_id' => $course->id, 'category_id' => $category]);
                        }
                    }
                }

                $response = [
                    'response' => 'success',
                    'message' => 'The Course has been created successfully',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The Course has failed to be created',
                ];
            }
        }

        return response()->json($response);
    }

    /**
     * Get All Courses
     */
    public function all(
        Request $request,
        $course_type,
        $page = 1,
        $limit = 10,
        $organisationId = null,
        $userId = null,
        $isResponsibleBusiness = false
    ) {
        $page = (int)$page;
        $limit = (int)$limit;
        $query = $request->get('search', '');
        $query = ($query)
            ? '%' . $query . '%'
            : '';
        $courses = ($query)
            ? Course::where('lms_courses.title', 'LIKE', $query)
                ->orWhere('lms_courses.description', 'LIKE', $query)
            : Course::query();

        switch ($course_type) {
        case 'liberty':
            if (is_null($organisationId)) {
                $courses->whereNull('lms_courses.organisation_id');
                $courses->orWhere('lms_courses.organisation_id', '=', '');
            } else {
                $sectorCourses = ($query)
                    ? Course::where('lms_courses.title', 'LIKE', $query)
                        ->orWhere('lms_courses.description', 'LIKE', $query)
                    : Course::query();

                $userCourses = ($query)
                    ? Course::where('lms_courses.title', 'LIKE', $query)
                        ->orWhere('lms_courses.description', 'LIKE', $query)
                    : Course::query();

                $courses->select('lms_courses.*')
                    ->join(
                        'lms_course_organisation as lco', function ($join) use ($organisationId) {
                                $join->on('lms_courses.id', '=', 'lco.course_id');
                                $join->where('lco.organisation_id', '=', (int)$organisationId);
                                $join->whereNull('lco.deleted_at');
                        }
                    )
                    ->where('lms_courses.published', '=', 1)
                    ->whereRaw("(lms_courses.organisation_id = '' OR lms_courses.organisation_id IS NULL)");

                $sectorCourses->select('lms_courses.*')
                    ->join(
                        'sector_courses as sc', function ($join) {
                                $join->on('lms_courses.id', '=', 'sc.course_id');
                                $join->whereNull('sc.deleted_at');
                                $join->where('lms_courses.published', '=', 1);
                        }
                    )
                    ->join(
                        'organisations as org', function ($join) use ($organisationId) {
                                $join->on('sc.sector_id', '=', 'org.sector');
                                $join->where('org.id', '=', (int)$organisationId);
                        }
                    )
                    ->where('lms_courses.published', '=', 1)
                    ->whereRaw("(lms_courses.organisation_id = '' OR lms_courses.organisation_id IS NULL)");

                $userCourses->select('lms_courses.*')
                    ->join(
                        'lms_course_users as lcu', function ($join) {
                                $join->on('lms_courses.id', '=', 'lcu.course_id');
                                $join->whereNull('lcu.deleted_at');
                        }
                    )
                    ->where('lms_courses.published', '=', 1)
                    ->where('lcu.user_id', '=', $userId);

                if ($isResponsibleBusiness) {
                    $sectorCourses->join(
                        'sectors', function ($join) {
                                $join->on('sc.sector_id', '=', 'sectors.id');
                                $join->whereNull('sectors.deleted_at');
                                $join->where('sectors.type', '=', self::SECTOR_TYPE_NOT_FOR_PROFIT);
                        }
                    );
                }
            }

            break;
        case 'organisation':
            $courses->whereNotNull('lms_courses.organisation_id');
            $courses->where('lms_courses.organisation_id', '!=', '');

            if (!is_null($organisationId)) {
                $courses->where('lms_courses.organisation_id', (int)$organisationId);
            }

            break;
        }

        // get total by filters above (exclude pagimation limits)
        $coursesCount = $courses->count();
        $sectorCoursesCount = isset($sectorCourses)
            ? $sectorCourses->count()
            : 0;
        $userCoursesCount = isset($userCourses)
            ? $userCourses->count()
            : 0;
        $total = $coursesCount + $sectorCoursesCount;

        // only paginate if a limit is specified (0 = no limit)
        if ($limit > 0) {
            $courses = $courses->take($limit)
                ->skip(($page * $limit) - $limit);

            if (isset($sectorCourses)) {
                $sectorCourseLimit = $sectorCoursesCount > 0 && $coursesCount < $limit
                    ? ($coursesCount - $limit) * -1
                    : $coursesCount - $limit;
                $sectorCourses = $sectorCourses->take($sectorCourseLimit)
                    ->skip(($page * $sectorCourseLimit) - $sectorCourseLimit);
            }

            if (isset($userCourses)) {
                $userCoursesLimit = $userCoursesCount > 0 && $coursesCount < $limit
                    ? ($coursesCount - $limit) * -1
                    : $coursesCount - $limit;
                $userCourses = $userCourses->take($userCoursesLimit)
                    ->skip(($page * $userCoursesLimit) - $userCoursesLimit);
            }
        }

        $courses = $courses->get();
        if (isset($sectorCourses)) {
            $sectorCourses = $sectorCourses->get();
            $userCourses = $userCourses->get();
            $courses = $courses->merge($sectorCourses)->merge($userCourses);
        }

        foreach ($courses as $course) {
            $this->getRelations($course);
            $featured_image = $course->getFeaturedImageAttribute();
            $course->image = $featured_image;
        }

        return response()->json([
            'response' => 'success',
            'data'     => $courses,
            'total'    => $total,
        ]);
    }

    private function getRelations(&$item)
    {
        foreach ($this->relations as $relation) {
            $item->$relation;
            if ($relation != 'organisation' || ($relation == 'organisation' && $item->organisation_id == null)) {
                unset($item->{sprintf('%s_id', $relation)});
            }
        }
    }

    /**
     * Update certain course
     *
     * @param  int $id
     * @return json
     */
    public function update(Request $request, $id)
    {
        if ($id && is_numeric($id)) {
            $validator = Validator::make(
                $request->all(), [
                'title' => 'required',
                ]
            );

            if ($validator->fails()) {
                $response = [
                    'response' => 'error',
                    'errors' => $validator->messages()->toArray(),
                ];
            } else {
                $course = Course::find($id);
                if ($course->exists()) {
                    $course->title = $request->get('title');
                    $course->description = $request->get('description', null);
                    $course->certificate = $request->get('certificate', 0);
                    $course->published = $request->get('published', 0);

                    if ($request->has('featured_image')) {
                        $course->featured_image = $request->get('featured_image');
                    }

                    $owner = $course->organisation_id;
                    if ($request->has('owner') && $request->get('owner') != '') {
                        $owner = (int)$request->get('owner');
                    }

                    $course->organisation_id = $owner;
                    $categories = $request->has('category')
                        ? $request->get('category')
                        : null;
                    $course->setCategoryAttribute($categories);

                    if ($request->has('accreditation_logo_image')) {
                        $accreditation_logos = $course->getAccreditationLogoImagesAttribute();
                        $newAccreditation_logos = $request->get('accreditation_logo_image');
                        if ((count($accreditation_logos) + count($newAccreditation_logos)) <= 4) {
                            $course->accreditation_logo_image = $request->get('accreditation_logo_image');
                        } else {
                            $response = [
                                'response' => 'error',
                                'message' => 'Limit 4 images per course',
                            ];
                            return response()->json($response);
                        }
                    }

                    $course->save();

                    $response = [
                        'response' => 'success',
                        'message' => 'The Course has been updated successfully',
                    ];
                } else {
                    $response = [
                        'response' => 'error',
                        'message' => 'Invalid Course',
                    ];
                }
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid Course ID',
            ];
        }

        return response()->json($response);
    }

    /**
     * Find Course
     */
    public function show(Request $request, $id)
    {
        if ($id && is_numeric($id)) {
            $course = Course::find($id);

            if (!$course) {
                $course = Course::where('id', '=', $id)->first();
            }

            $response = [
                'response' => ($course)
                    ? 'success'
                    : 'error',
            ];

            if ($course) {
                $this->getRelations($course);
                $featured_image = $course->getFeaturedImageAttribute();
                $course->organisation_owner = $course->organisation_id;
                $course->image = $featured_image;
                $course->hasCertificate = false;
                $course->accreditation_logos = $course->getAccreditationLogoImagesAttribute();
                $lessonsArr = [];

                foreach ($course->lessons as $lesson) {
                    $lessonsArr[] = $lesson->id;
                }

                $user_id = $request->get('user_id');
                $matchThese = ['course_id' => $id, 'user_id' => $user_id];
                if (!is_null($user_id)) {
                    $hasCertificate = LmsCourseCertificate::where($matchThese)->first();
                    if ($hasCertificate) {
                        $course->hasCertificate = true;
                        $course->completeDate = $hasCertificate->created_at->format('d F Y');

                    }
                }

                if ($lessonsArr) {
                    $tests = LmsTests::whereIn('lesson_id', $lessonsArr)->get();
                    if ($tests) {
                        $lessons = $course->lessons;
                        foreach ($lessons as $lesson) {
                            $lesson->hasTest = false;
                            foreach ($tests as $test) {
                                if ($lesson->id == $test->lesson_id) {
                                    $lesson->hasTest = true;
                                }
                            }
                        }
                        $course->lessons = $lessons;
                    }
                }
                $response['data'] = $course;

            } else {
                $response['message'] = 'The specified Course could not be found';
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid Course ID',
            ];
        }

        return response()->json($response);
    }

    /**
     * Delete Course
     *
     * @param $id
     *
     * @return mixed
     */
    public function destroy($id)
    {
        if ($course = Course::find($id)) {
            if (Course::destroy($id)) {
                $response = [
                    'response' => 'success',
                    'message' => 'The specified Course was successfully deleted',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The specified Course could not be deleted',
                ];
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'The specified Course could not be found',
            ];
        }

        return response()->json($response);
    }

    /**
     * Assign course to Users or Categories
     *
     * @param  int
     * @return json
     */
    public function assignCourse(Request $request, $course_id)
    {
        if ($request->isMethod('get')) {
            $users = CourseUser::select('user_id')->where('course_id', $course_id)->get();
            $comma_separated = '';
            foreach ($users as $user) {
                $comma_separated .= $user->user_id;
                $comma_separated .= ',';
            }
            return response()->json(['user_ids' => rtrim($comma_separated, ",")]);
        } else {

            CourseUser::where('course_id', $course_id)->delete();

            if (trim($request->get('users')) != '') {
                $users = explode(',', $request->get('users'));
                $data = [];
                foreach ($users as $user) {
                    $data[] = [
                        'course_id' => $course_id,
                        'user_id' => $user,
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ];
                }

                if (DB::table('lms_course_users')->insert($data)) {
                    return response()->json([
                        'response' => 'success',
                        'message' => 'Course was assigned successfully',
                    ]);
                } else {
                    return response()->json([
                        'response' => 'error',
                        'message' => 'Could not assign course.',
                    ]);
                }
            } else {
                return response()->json([
                    'response' => 'success',
                    'message' => 'Course was unassigned successfully',
                ]);
            }
        }
    }

    public function assignOrganisation(Request $request, $course_id)
    {
        if ($request->isMethod('get')) {
            $course = Course::find($course_id);
            if ($course) {
                $this->getRelations($course);
                $orgs = [];
                foreach ($course->organisation as $organisation) {
                    $orgs[] = $organisation->organisation_id;
                }

                $orgs = implode(", ", $orgs);
                return response()->json(['organisations' => $orgs]);
            }
        } else {
            $course = Course::find($course_id);
            if ($course) {
                $organisations = $request->has('organisations')
                    ? explode(',', $request->get('organisations'))
                    : null;
                $course->setOrganisationAttribute($organisations);
                if ($course->save()) {
                    return response()->json([
                        'response' => 'success',
                        'message' => 'Course was assigned successfully',
                    ]);
                }

                return response()->json([
                    'response' => 'error',
                    'message' => 'Could not assign course.',
                ]);
            } else {
                return response()->json([
                    'response' => 'error',
                    'message' => 'Course not found.',
                ]);
            }
        }

        return null;
    }

    /**
     * Return all undeleted sectors
     *
     * @return json
     */
    public function getAllSectors()
    {
        $sectors = Sector::orderBy('handle', 'asc')->pluck('handle', 'id');
        return response()->json($sectors);
    }

    /**
     * Return all assigned sectors to a certain course
     *
     * @param  int $courseId
     * @return json
     */
    public function assignedSectors($courseId)
    {
        $course = Course::find($courseId);
        if ($course) {
            $this->getRelations($course);
            $sectors = array_map(
                function ($sectorCourse) {
                    return $sectorCourse['sector_id'];
                }, $course->sectorCourses->toArray()
            );

            $sectors = implode(',', $sectors);
            return response()->json(['sectors' => $sectors]);
        }
    }

    /**
     * Assigne all the sectors choosen to the course
     *
     * @return json
     */
    public function assignedNewSectors(Request $request, $courseId)
    {
        $course = Course::find($courseId);
        $hasSectorCourses = SectorCourses::where('course_id', $courseId)->exists();
        if ($course) {
            $sectors = $request->has('sectors') && !empty($request->get('sectors'))
                ? explode(',', $request->get('sectors'))
                : null;
            $result = $course->setSectorsAttribute($sectors);
            if ($result) {
                return response()->json([
                    'response' => 'success',
                    'message' => 'Course was assigned to sector(s) successfully',
                ]);
            } elseif ($hasSectorCourses && empty($sectors)) {
                SectorCourses::where('course_id', $courseId)->delete();
                return response()->json([
                    'response' => 'success',
                    'message' => 'Sector(s) already removed',
                ]);
            }

            return response()->json([
                'response' => 'error',
                'message' => 'Sector(s) could not assign to course.',
            ]);
        }

        return response()->json([
            'response' => 'error',
            'message' => 'Course not found.',
        ]);
    }

    public function view($course_id)
    {
        $course = Course::find($course_id);

        if ($course) {
            $this->getRelations($course);
            $course->image = $course->getFeaturedImageAttribute();
            // get course related images here

            $response = [
                'response' => 'success',
                'message' => 'The course is available.',
                'data' => $course,
            ];
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Something went wrong, no course available.',
            ];
        }

        return response()->json($response);
    }

    public function options()
    {
        return response()->json(Course::pluck('title', 'id'));
    }

    public function createCertificate(Request $request, $course_id)
    {
        $validator = Validator::make(
            $request->all(), [
            'user_id' => 'required',
            ]
        );
        if ($validator->fails()) {
            $response = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        } else {
            $user_id = $request->get('user_id');
            $lessons = Lesson::where('course_id', '=', $course_id)->select('id');
            $qtdLessons = $lessons->count();
            $lessons = $lessons->get();
            $lessonsToFind = [];
            foreach ($lessons as $lesson) {
                array_push($lessonsToFind, $lesson->id);
            }
            $userLessons = LmsUserLesson::where('user_id', '=', $user_id)->whereIn('lesson_id', $lessonsToFind);
            $tests = LmsTests::whereIn('lesson_id', $lessonsToFind)->get();
            foreach ($lessons as $key => $lesson) {
                $lesson->hasTest = false;
                foreach ($tests as $test) {
                    if ($lesson->id == $test->lesson_id) {
                        $lesson->hasTest = true;
                    }
                }
                $lessons[$key] = $lesson;
            }
            $qtdUserLesson = $userLessons->count();
            $userLessons = $userLessons->get();

            $qtdOK = 0;
            if ($qtdUserLesson == $qtdLessons) {
                foreach ($lessons as $lesson) {
                    foreach ($userLessons as $userLesson) {
                        if ($lesson->id == $userLesson->lesson_id) {
                            if ($userLesson->read == '1' && (($lesson->hasTest == true && $userLesson->test == 1) || ($lesson->hasTest == false))) {
                                $qtdOK++;
                            }
                        }
                    }
                }
            }
            $response = [
                'response' => 'error',
            ];
            $matchThese = ['user_id' => $user_id, 'course_id' => $course_id];
            $certificate = LmsCourseCertificate::where($matchThese)->count();
            if ($qtdLessons == $qtdOK && $qtdUserLesson == $qtdOK && $qtdLessons == $qtdUserLesson && $certificate == 0) {

                $course = LmsCourseCertificate::create(
                    [
                    'user_id' => $user_id,
                    'course_id' => $course_id,
                    ]
                );
                $response = [
                    'response' => 'success',
                ];
            }
        }
        return response()->json($response);
    }

    /**
     * Get All Courses assigned to user on library
     */
    public function assignedToUser($user_id, $type)
    {

        $user = User::find($user_id);
        $coursesToFind = [];
        $hasTest = [];
        $courseAssigned = [];
        $lessonProgress = [];
        $userTestResults = [];
        //$coursesUser = CourseUser::where('user_id', '=', $user_id)->select('course_id')->get();
        $coursesUser = CourseUser::get();
        $tests = LmsTests::get();
        $userLesson = LmsUserLesson::where('user_id', '=', $user_id)->get();
        $testResults = LmsTestResults::where('user_id', '=', $user_id)->get();
        foreach ($tests as $test) {
            $hasTest[$test->lesson_id] = $test;
        }
        foreach ($userLesson as $lesson) {
            $lessonProgress[$lesson->lesson_id] = $lesson;
        }
        foreach ($testResults as $testResult) {
            $userTestResults[$testResult->lesson_id] = $testResult;
        }
        $courses = Course::query();
        if ($type != 'library') {
            foreach ($coursesUser as $courseUser) {
                //$courseAssigned[$courseUser->course_id] = true;
                array_push($courseAssigned, $courseUser->course_id);
                if ($courseUser->user_id == $user_id) {
                    array_push($coursesToFind, $courseUser->course_id);
                }
            }
            $courses = $courses->with('organisation')->whereIn('id', $coursesToFind);
        }

        // get total by filters above (exclude pagimation limits)
        $total = $courses->count();

        $courses = $courses->get();

        foreach ($courses as $course) {

            $orgs = [];
            foreach ($course->organisation as $org) {
                array_push($orgs, $org->organisation_id);
            }

            if ($course->organisation_id == null || $course->organisation_id == $user->organisation_id || in_array(
                $user->organisation_id,
                $orgs
            )
            ) {
                $this->getRelations($course);
                $featured_image = $course->getFeaturedImageAttribute();
                $course->image = $featured_image;
                $hasIncomplete = 0;
                $completeLessonCounter = 0;
                foreach ($course->lessons as $lessonKey => $lesson) {
                    $lesson->hasTest = false;
                    if (isset($hasTest[$lesson->id])) {
                        $lesson->hasTest = true;
                    }
                    $lesson->read = 0;
                    $lesson->test = 0;
                    $lesson->page_progress = 0;
                    $lesson->status = 'Pending';
                    if (isset($lessonProgress[$lesson->id])) {
                        $lesson->read = $lessonProgress[$lesson->id]->read;
                        $lesson->test = $lessonProgress[$lesson->id]->test;
                        $lesson->page_progress = $lessonProgress[$lesson->id]->page_progress;
                        if (($lesson->read == 1 && $lesson->test == 0 && $lesson->hasTest == false) || (($lesson->read == 1 && $lesson->test == 1 && $lesson->hasTest == true))) {
                            $lesson->status = 'Complete';
                            $completeLessonCounter++;
                        } else {
                            if ($lesson->read == 1 && $lesson->test != 1 && $lesson->hasTest == true) {
                                $lesson->status = 'Incomplete';
                                $hasIncomplete++;
                            }
                        }
                    }
                }
                $course->progress = 0;
                if ($course->lessons) {
                    $course->progress = round(($completeLessonCounter * 100) / count($course->lessons), 2);
                    if ($course->progress == 0 && $hasIncomplete > 0) {
                        $course->progress = 1;
                    }
                }
            }
        }
        // exit;

        return response()->json([
            'response' => 'success',
            'data'     => $courses,
            'total'    => $total,
        ]);
    }

    public function getUserCourses($user_id)
    {

        if ($user_id != null) {
            $user = User::find($user_id);
            $coursesToFind = [];
            $coursesUser = CourseUser::where('user_id', '=', $user_id)->select('course_id')->get();
            foreach ($coursesUser as $courseUser) {
                array_push($coursesToFind, $courseUser->course_id);
            }
            $coursesAsignOrg = CourseOrganisation::where(
                'organisation_id', '=',
                $user->organisation_id
            )->select('course_id')->get();
            foreach ($coursesAsignOrg as $courseAsignOrg) {
                array_push($coursesToFind, $courseAsignOrg->course_id);
            }
            $coursesBelongOrg = Course::where('organisation_id', '=', $user->organisation_id)->select('id')->get();
            foreach ($coursesBelongOrg as $courseBelongOrg) {
                array_push($coursesToFind, $courseBelongOrg->id);
            }
            $coursesall = Course::where('organisation_id', '=', null)->select('id')->get();
            foreach ($coursesall as $courseall) {
                array_push($coursesToFind, $courseall->id);
            }
            $orgSector = Organisation::where('id', '=', $user->organisation_id)->select('sector')->first();
            $sector_id = $orgSector->sector;
            $sectorcourses = SectorCourses::where('sector_id', '=', $sector_id)->select('course_id')->get();
            foreach ($sectorcourses as $sectorcourse) {
                array_push($coursesToFind, $sectorcourse->course_id);
            }

            $courses = Course::whereIn('id', $coursesToFind)->get();
            return response()->json([
                'response' => 'success',
                'data' => $courses,
            ]);
        }

        return response()->json([
            'response' => 'error',
        ]);
    }

    public function getProgress(Request $request)
    {

        $branches = [];
        $usersInfos = [];
        $coursesFull = [];
        $relUsrCourse = [];
        $isAssigned = [];
        $hasTest = [];
        $tests = LmsTests::get();
        $courses = Course::query();
        $sectorCourses = Course::query();
        $allUsers = User::query();
        $courseUsers = CourseUser::get();
        $userLessons = LmsUserLesson::get();
        $courseOrgs = CourseOrganisation::get();

        if ($request->has('organisation_id')) {
            $allUsers = $allUsers->where('organisation_id', '=', $request->get('organisation_id'));
            $courses = $courses->where('organisation_id', '=', $request->get('organisation_id'));
        }

        if ($courses->count() == 0) {
            $courses = $sectorCourses->whereHas(
                'sectorCourses', function ($query) use ($request) {
                    return $query->where('sector_id', $request->get('sector_id'));
                }
            );
        }

        $courses = $courses->where('published', '=', 1)->get();
        $allUsers = $allUsers->get();
        foreach ($courseUsers as $courseUser) {
            $relUsrCourse[] = ['course_id' => $courseUser->course_id, 'user_id' => $courseUser->user_id];
            $isAssigned[$courseUser->course_id] = true;
        }

        foreach ($allUsers as $userInfo) {
            $usrInfos['id'] = $userInfo->id;
            $usrInfos['name'] = $userInfo->first_name . ' ' . $userInfo->last_name;
            $usrInfos['branch'] = $userInfo->branch_name;
            $usrInfos['organisation'] = $userInfo->organisation_id;
            $usersInfos[$userInfo->id] = $usrInfos;

            if ($userInfo->branch_name != null) {
                $branches[] = ['name' => $userInfo->branch_name, 'organisation_id' => $userInfo->organisation_id];
            }

            foreach ($courseOrgs as $courseOrg) {
                $isAssigned[$courseOrg->course_id] = true;
                if ($courseOrg->organisation_id == $userInfo->organisation_id) {
                    $relUsrCourse[] = ['course_id' => $courseOrg->course_id, 'user_id' => $userInfo->id];
                }
            }

            foreach ($courses as $course) {
                if (!isset($isAssigned[$course->id])) {
                    $relUsrCourse[] = ['course_id' => $course->id, 'user_id' => $userInfo->id];
                }
            }
        }

        $relUsrCourse = array_map("unserialize", array_unique(array_map("serialize", $relUsrCourse)));

        $branches = array_map("unserialize", array_unique(array_map("serialize", $branches)));
        $lessonsIds = [];
        foreach ($userLessons as $userLesson) {
            $lessonsIds[] = $userLesson->lesson_id;
        }

        $results = [];
        $testsResults = LmsTestResults::whereIn('lesson_id', $lessonsIds)->get();
        foreach ($testsResults as $testsResult) {
            $results[$testsResult->lesson_id][$testsResult->user_id] = $testsResult->status;
        }

        $progressLessonUser = [];
        foreach ($userLessons as $usrLssnKey => $userLesson) {
            $userLesson->testStatus = null;
            if (isset($results[$userLesson->lesson_id]) 
                && isset($results[$userLesson->lesson_id][$userLesson->user_id])) {
                $results[$userLesson->lesson_id][$userLesson->user_id];
            }

            $userLessons[$usrLssnKey] = $userLesson;
            $progressLessonUser[$userLesson->lesson_id][$userLesson->user_id] = $userLesson;
        }

        foreach ($tests as $test) {
            $hasTest[$test->lesson_id] = true;
        }
        //create the object with all the informations for each user
        //dd($relUsrCourse);
        foreach ($courses as $course) {

            $this->getRelations($course);
            foreach ($relUsrCourse as $rel) {
                //$currentCourse = $course;
                if ($rel['course_id'] == $course->id) {
                    $currentCourse = [];


                    $currentCourse['user'] = $rel['user_id'];
                    $currentCourse['course'] = $course;

                    array_push($coursesFull, $currentCourse);
                }
            }
        }

        //organise informations to show in the sreen and add the status of each lesson for each user
        $newCourseFull = [];
        foreach ($coursesFull as $line) {
            $newCourse = [];
            $newCourse['user_id'] = $line['user'];
            $newCourse['user_name'] = isset($usersInfos[$line['user']])
                ? $usersInfos[$line['user']]['name']
                : '';
            $newCourse['branch'] = isset($usersInfos[$line['user']])
                ? $usersInfos[$line['user']]['branch']
                : '';
            $newCourse['organisation'] = isset($usersInfos[$line['user']])
                ? $usersInfos[$line['user']]['organisation']
                : '';
            $newCourse['id'] = $line['course']->id;
            $newCourse['title'] = $line['course']->title;
            $newCourse['description'] = $line['course']->description;
            $newCourse['certificate'] = $line['course']->certificate;
            $newCourse['updated_at'] = $line['course']->updated_at;
            $cmpltCnt = 0;
            $failStts = false;
            $inLessons = [];
            foreach ($line['course']->lessons as $lesson) {
                $newLesson = [];
                $newLesson['id'] = $lesson->id;
                $newLesson['title'] = $lesson->title;
                $newLesson['description'] = $lesson->description;
                $newLesson['status'] = $line['user'];
                $newLesson['status'] = 'Pending';
                $newLesson['hasTest'] = isset($hasTest[$lesson->id])
                    ? $hasTest[$lesson->id]
                    : false;
                if (array_key_exists($lesson->id, $progressLessonUser)) {
                    if (array_key_exists($line['user'], $progressLessonUser[$lesson->id])) {
                        $progress = $progressLessonUser[$lesson->id][$line['user']];
                        if ($progress['read'] == 1) {
                            $newLesson['status'] = 'Incomplete';
                            if (($progress['test'] == 0 && $newLesson['hasTest'] == false) || (($progress['test'] == 1) && ($progress['testStatus'] == "Complete"))) {
                                $newLesson['status'] = 'Complete';
                                $cmpltCnt++;
                            } elseif (($progress['test'] == 1) && ($progress['testStatus'] == "Fail")) {
                                $newLesson['status'] = 'Failed';
                                $failStts = true;
                            }
                        }
                    }
                }
                $inLessons[] = $newLesson;
            }

            $returnStatus = 'Pending';
            if ($failStts) {
                $returnStatus = 'Incomplete';
            } elseif (count($line['course']->lessons) == $cmpltCnt) {
                $returnStatus = 'Complete';
            } elseif ((count($line['course']->lessons) > $cmpltCnt) && ($cmpltCnt > 0)) {
                $returnStatus = 'Incomplete';
            }

            $newCourse['status'] = $returnStatus;
            $newCourse['lessons'] = $inLessons;
            $newCourseFull[] = $newCourse;
        }
        $data['courses'] = $newCourseFull;
        $data['branches'] = $branches;
        return Response::json(
            [
            'response' => 'success',
            'data' => $data,
            ]
        );
    }

    public function getReports(Request $request)
    {
        $params = $request->all();
        $usrLsn = [];
        $lsnTst = [];
        $testCnt = [];
        $passRate = [];
        $orgUsers = [];
        $branches = [];
        $testsGroup = [];
        $courseToShow = [];
        $courses = Course::query();
        $sectorCourses = Course::query();
        $allUsers = User::query();
        $sectorAllUsers = User::query();
        $userLessons = LmsUserLesson::query();
        $testResults = LmsTestResults::query();

        if (isset($params['src'])) {
            //dd((int)$params['org']);
            $courses = $courses->where('organisation_id', '=', (int)$params['org']);
            $allUsers = $allUsers->where('organisation_id', '=', (int)$params['org']);
        } else {
            $courses = $courses->where('organisation_id', '=', null);
        }

        if ($courses->count() == 0) {
            $courses = $sectorCourses->whereHas(
                'sectorCourses', function ($query) use ($request) {
                    return $query->where('sector_id', $request->get('ssector_id'));
                }
            );
            $allUsers = $sectorAllUsers->where('id', '=', (int)$params['user_id']);
        }

        $allUsers = $allUsers->get();
        $courses = $courses->where('published', '=', 1)->get();
        //dd($courses);
        foreach ($allUsers as $userInfo) {
            if (isset($params['branch'])) {
                if ($userInfo->branch == $params['branch']) {
                    $orgUsers[] = $userInfo->id;
                }
            }
            if ($userInfo->branch_name != null) {
                $branches[] = [
                    'id' => $userInfo->branch,
                    'name' => $userInfo->branch_name,
                    'organisation_id' => $userInfo->organisation_id,
                ];
            }
        }

        if (isset($params['org']) && !isset($params['branch'])) {
            $users = User::where('organisation_id', '=', $params['org'])->get();
            foreach ($users as $user) {
                $orgUsers[] = $user->id;
            }

        }
        if (isset($params['branch']) || isset($params['org'])) {
            $userLessons = $userLessons->whereIn('user_id', $orgUsers);
            $testResults = $testResults->whereIn('user_id', $orgUsers);
        }
        if (isset($params['from']) && isset($params['to'])) {
            $userLessons = $userLessons->whereBetween('created_at', [$params['from'], $params['to']]);
            $testResults = $testResults->whereBetween('created_at', [$params['from'], $params['to']]);
        }
        $userLessons = $userLessons->get();
        $testResults = $testResults->get();
        $tests = LmsTests::get();


        foreach ($userLessons as $userLesson) {
            $usrLsn[$userLesson->lesson_id][] = $userLesson;
        }
        foreach ($testResults as $testResult) {
            $lsnTst[$testResult->lesson_id][] = $testResult;
            $testCnt[$testResult->lesson_id][$testResult->user_id] = $testResult;
        }
        foreach ($testCnt as $id => $lesson) {
            $rateCnt = 0;
            foreach ($lesson as $user_id => $user) {
                if ($user->status == 'Complete') {
                    $rateCnt++;
                }
            }
            $passRate[$id] = ($rateCnt * 100) / count($lesson);
        }
        foreach ($tests as $test) {
            $testsGroup[$test->lesson_id] = $test;
        }
        foreach ($courses as $courseKey => $course) {
            $this->getRelations($course);

            $go = false;
            if (isset($params['org'])) {
                foreach ($course->organisation as $organisation) {
                    if ($organisation->organisation_id == $params['org']) {
                        $go = true;
                    }
                }
            }
            if (isset($params['src'])) {
                $go = true;
            }
            if (!isset($params['org']) || $go == true) {
                $viewCnt = 0;
                $atmptsCnt = 0;
                $rateAvg = 0;
                $cntToSplit = 0;
                foreach ($course->lessons as $lessonKey => $lesson) {
                    $lesson->views = 0;
                    $lesson->passRate = 0;
                    if (isset($usrLsn[$lesson->id])) {
                        $lesson->views = count($usrLsn[$lesson->id]);
                        $viewCnt += $lesson->views;
                        $cntToSplit++;
                        $lesson->passRate = 100;
                    }
                    $lesson->testAttempts = 0;
                    if (isset($lsnTst[$lesson->id])) {
                        $lesson->testAttempts = count($lsnTst[$lesson->id]);
                        $atmptsCnt += $lesson->testAttempts;
                    }

                    if (isset($testsGroup[$lesson->id])) {
                        if (isset($passRate[$lesson->id])) {

                            $lesson->passRate = $passRate[$lesson->id];
                            $rateAvg += $lesson->passRate;
                        }
                    } else {
                        //$lesson->passRate = 100;
                        $rateAvg += $lesson->passRate;
                    }
                }
                $course->views = $viewCnt;
                $course->testAttempts = $atmptsCnt;
                $course->passRate = 0;
                //dd($cntToSplit);
                if ($cntToSplit > 0) {
                    $number = ($rateAvg / $cntToSplit);
                    $course->passRate = round($number, 2);
                }
                $courseToShow[] = $course;
            }
        }
        $return = ['courses' => $courseToShow, 'branches' => $branches];
        return response()->json([
            'response' => 'success',
            'data' => $return,
        ]);
    }

    public function reminder(Request $request)
    {
        $validator = Validator::make(
            $request->all(), [
                'user_id' => 'required',
            ]);
        if ($validator->fails()) {
            $response = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        } else {
            $User = User::find($request->get('user_id'));
            if ($User) {
                $User['frontend_link'] = '' . config('app.client_frontend') . '/learning/my-assignments';
                $this->mail->queue(
                    $User->email, $User->fullName(), 'Please complete your courses',
                    'emails.learning.reminder', $User
                );
                $response = [
                    'response' => 'success',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'errors' => 'Invalid User',
                ];
            }
        }
        return response()->json($response);
    }

    public function duplicateCourse(Request $request, $course_id)
    {
        $course = Course::find($course_id);
        if (!$course) {
            $course = Course::where('id', '=', $course_id);
            //$newLesson = $lesson->replicate();
            $course = $course->first();
        }

        $response = ['response' => 'error'];
        if ($course) {
            $this->getRelations($course);
            $featured_image = $course->getFeaturedImageAttribute();
            $course->image = $featured_image;
            $course->accreditation_logos = $course->getAccreditationLogoImagesAttribute();
            $organisations = [];
            foreach ($course->organisation as $organisation) {
                $organisations[] = $organisation->organisation_id;
            }
            $categories = [];
            foreach ($course->category as $category) {
                $categories[] = $category->category_id;
            }

            if (isset($course->lessons) && count($course->lessons) > 0) {
                $lessons = [];
                foreach ($course->lessons as $lesson) {
                    $lessons[] = $lesson;
                }
            }
            //return $lessons;
            $newCourse = Course::create([
                'organisation_id' => $request->get('src') == 'organisation'
                    ? $course->organisation_id
                    : null,
                'title' => trim($request->get('title')) == ''
                    ? $course->title
                    : $request->get('title'),
                'description' => $course->description,
                'certificate' => $course->certificate,
                'published' => 0,
            ]);

            if ($newCourse) {
                if ($course->image != null) {
                    $newCourse->featured_image = $course->image;
                }
                if ($course->accreditation_logos != null && count($course->accreditation_logos) > 0) {
                    $newCourse->accreditation_logo_image = $course->accreditation_logos;
                }
                $newCourse->setOrganisationAttribute($organisations);
                $newCourse->setCategoryAttribute($categories);
                if (isset($lessons)) {
                    foreach ($lessons as $lesson) {
                        $lesson->course_id = $newCourse->id;
                        $this->duplicateLesson($newCourse->id, $lesson->id);
                    }
                }

                $newCourse->save();
                return response()->json(['response' => 'success', 'data' => $newCourse]);
            }
        }
        return response()->json(['response' => 'error', 'message' => 'Was not possible duplicate this course']);
    }

    public function duplicateLesson($course_id, $id, $title = null)
    {
        if ($id) {
            $lesson = Lesson::find($id);
            if (!$lesson) {
                $lesson = Lesson::where('id', '=', $id);
                //$newLesson = $lesson->replicate();
                $lesson = $lesson->first();
            }

            $response = ['response' => 'error'];

            if ($lesson) {
                $newLessonTitle = $lesson->title;
                if ($title !== null) {
                    $newLessonTitle = $title;
                }
                $lastPos = Lesson::where('course_id', '=', $lesson->course_id)->select('order')->orderby(
                    'order',
                    'DESC'
                )->first();
                if ($lastPos == null) {
                    $newOrder = 1;
                } else {
                    $newOrder = $lastPos->order + 1;
                }
                $newLesson = Lesson::create(
                    [
                    'title' => $newLessonTitle,
                    'description' => $lesson->description,
                    'course_id' => $course_id,
                    'order' => $newOrder,
                    ]
                );
                if ($newLesson) {
                    $response['response'] = 'success';
                }

                $lesson->test = LmsTests::whereIn('lesson_id', [(string)$lesson->id, (int)$lesson->id])->first();
                $lesson->pages = Pages::whereIn(
                    'lesson_id',
                    [(string)$lesson->id, (int)$lesson->id]
                )->orderby('sort_order', 'ASC')->get();

                if ($lesson->test) {
                    $newTest = LmsTests::create([
                        'lesson_id' => (int)$newLesson->id,
                        'pass_mark' => $lesson->test->pass_mark,
                        'amount_questions' => $lesson->test->amount_questions,
                    ]);

                    if (isset($lesson->test->sections)) {
                        $newTest->sections = $lesson->test->sections;
                        $newTest->save();
                    }
                    $newLesson->test = $newTest;
                }
                $files = [];
                if ($lesson->pages) {
                    $newPages = [];
                    foreach ($lesson->pages as $key => $page) {
                        $newPage = Pages::create([
                            'lesson_id' => $newLesson->id,
                            'title' => 'Copy of ' . $page->title,
                            'sort_order' => $page->sort_order,
                        ]);

                        if (isset($page->sections)) {
                            $newsections = [];
                            foreach ($page->sections as $key => $section) {
                                $newSection = $section;
                                $newSection['id'] = uniqid($page->_id);
                                array_push($newsections, $newSection);
                                if (isset($section['file_name'])) {
                                    array_push($files, $section['file_name']);
                                }
                            }
                            $newPage->sections = $newsections;
                        }
                        $newPage->save();
                        array_push($newPages, $newPage);
                    }
                    $newLesson->pages = $newPages;
                }
                $newLesson->files = $files;
                $data = [];
                //$data['old_Lesson'] = $lesson;
                //$data['new_Lesson'] = $newLesson;
                return $newLesson;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    private function updateRelations(&$item, $values)
    {
        foreach ($this->relations as $relation) {
            if (isset($values[$relation])) {
                $item->{Str::camel($relation)}()->sync($values[$relation]);
            }
        }
    }

    private function destroyRelations(&$item)
    {
        foreach ($this->relations as $relation) {
            $item->{Str::camel($relation)}()->delete();
        }
    }
}
