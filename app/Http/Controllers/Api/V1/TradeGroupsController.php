<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use App\Models\TradeGroup;
use Illuminate\Support\Facades\Validator;

class TradeGroupsController extends BaseController
{
    /**
     * Get All Trade Group
     */
    public function index()
    {
        $trades = TradeGroup::orderBy('name', 'asc')->paginate(20);
        return Response::json(
            [
            'response' => 'success',
            'data' => $trades->toJson(),
            ]
        );
    }

    /**
     * Store new Trade Group
     */
    public function store(Request $request)
    {
        $validator = Validator::make(
            $request->all(), [
            'name' => 'required',
            ]
        );

        if ($validator->fails()) {
            $response = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        } else {
            $tradeGroup = TradeGroup::create(
                [
                'name' => $request->get('name'),
                ]
            );

            if ($tradeGroup->id) {

                $response = [
                    'response' => 'success',
                    'message' => 'The trade group has been created successfully',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The trade group has failed to be created',
                ];
            }
        }

        return Response::json($response);
    }

    /**
     * Find TradeGroup
     */
    public function show($id)
    {
        $tradeGroup = TradeGroup::find($id);
        $response = [
            'response' => ($tradeGroup)
                ? 'success'
                : 'error',
        ];
        if ($tradeGroup) {
            $response['data'] = $tradeGroup;
        } else {
            $response['message'] = 'The specified trade group could not be found';
        }
        return Response::json($response);
    }

    /**
     * Delete TradeGroup
     *
     * @param $id
     *
     * @return mixed
     */
    public function destroy($id)
    {
        if ($tradeGroup = TradeGroup::find($id)) {
            if (TradeGroup::destroy($id)) {
                $response = [
                    'response' => 'success',
                    'message' => 'The specified trade group was successfully deleted',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The specified trade group could not be deleted',
                ];
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'The specified trade group could not be found',
            ];
        }

        return Response::json($response);
    }

    public function defaultTradeGroup($id)
    {
        DB::update('update trade_groups set `default` = 0');
        $tradeGroup = TradeGroup::find($id);
        $tradeGroup->default = 1;
        $tradeGroup->save();
        $response = [
            'response' => 'success',
            'message' => 'Default trade group updated',
        ];
        return Response::json($response);
    }

    /**
     * Update Trade Group
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make(
            $request->all(), [
            'name' => 'required',
            ]
        );

        if ($validator->fails()) {
            $response = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        } else {
            $tradeGroup = TradeGroup::find($id);
            if ($tradeGroup->id) {
                $tradeGroup->name = $request->get('name');
                $tradeGroup->save();

                $response = [
                    'response' => 'success',
                    'message' => 'The trade group has been updated successfully',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'Invalid trade group',
                ];
            }
        }
        return Response::json($response);
    }

    public function options()
    {
        $trade_groups = TradeGroup::select('name', 'id')->orderBy('name', 'asc')->get();

        $trade_group_output = [];

        foreach ($trade_groups as $trade) {
            $trade_group_output[(string)$trade->id] = $trade->name;
        }

        return Response::json($trade_group_output);

        // return Response::json(TradeGroup::orderBy('name', 'asc')->pluck(
        //     'name',
        //     'id'
        // )->all());
    }
}
