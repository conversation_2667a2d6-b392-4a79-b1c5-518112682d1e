<?php

namespace App\Http\Controllers\Api\V1\LetsTalk;

use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use App\Models\LetsTalk\SocialRoom;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use App\Models\LetsTalk\UpcomingSchedule;
use App\Models\LetsTalk\CmsLibertyRepresentative;

class SocialCustomerRoomController extends BaseController
{

    private $business;

    public function __construct(Request $request)
    {
        $this->business = ($request->has('business') && $request->get('business') === 'lmre')
            ? 'lmre'
            : 'lsm';

        $this->libReps = CmsLibertyRepresentative::fetchAllFromCacheOrCms();

        $personId = $request->get('person_id');
        $this->authUserCms = $this->libReps[$personId] ?? [];
        $this->authUserTz = $this->authUserCms['office_timezone'] ?? 'Europe/London';
    }

    public function getAll(Request $request)
    {
        $data = $request->all();
        $customerEmail = $data['customer_email'];

        $socialRooms = SocialRoom::with('socialRoomType')
            ->where('lt_social_room_type_id', 5)
            ->where('is_community', 0)
            ->where('status', 'approved')
            ->with(['socialRoomParticipants.broker', 'socialRoomSchedule'])
            ->whereHas(
                'socialRoomParticipants',
                function ($q) use ($customerEmail) {
                    $q->where('external_user_email', $customerEmail);
                }
            )
            ->get();

        $creatorCms = $this->authUserCms;
        foreach ($socialRooms as $socialRoom) {
            if (isset($socialRoom->socialRoomSchedule) && $socialRoom->socialRoomSchedule) {
                if (isset($socialRoom->cms)) {
                    $creatorCms = $socialRoom->cms;
                }
                $socialRoomsWithCMS = SocialRoom::setCmsData([$socialRoom], false, 'socialRoomParticipants');
                $tmpData = UpcomingSchedule::generateWithBuffer($socialRoomsWithCMS, $creatorCms);
                $upcomingSchedules = $tmpData['upcomingSchedules'];
            } else {
                $upcomingSchedules = [];
            }
            $socialRoom->upcoming_schedules = $upcomingSchedules;

            if (isset($socialRoom->socialRoomParticipants)) {
                $participant = Arr::first(
                    $socialRoom->socialRoomParticipants,
                    function ($participant, $key) {
                        return isset($participant->broker);
                    }
                );
                if (isset($participant)) {
                    $socialRoom->brokerCompany = $participant->broker;
                    $socialRoom->brokerCompanyLogo = $participant->broker->link;
                }
            }
        }

        $socialRooms = SocialRoom::setCmsData($socialRooms, true, 'socialRoomParticipants');

        $socialRooms = SocialRoom::orderUpcomingMeetings($socialRooms);

        return Response::json(
            [
                'status' => 'success',
                'data' => [
                    'rooms' => $socialRooms,
                ],
            ]
        );
    }
}
