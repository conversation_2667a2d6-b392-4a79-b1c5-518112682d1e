<?php

namespace App\Http\Controllers\Api\V1\LetsTalk;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use App\Models\LetsTalk\ResourceExternalContact;
use Illuminate\Validation\Validator as ValidatorType;

class ExternalContactsController extends BaseController
{
    const VALID_MOBILE_NUMBER_REGEX = '/^[+][1-9][0-9]{6,14}/';

    public function index(Request $request)
    {
        // search name query
        $name = $request->get('name');
        //return all data
        if ($name == 'all') {
            $resourceContact = ResourceExternalContact::get();
            return Response::json(
                [
                    'response' => 'success',
                    'data' => $resourceContact,
                ]
            );
        }
        if ($name) {
            Log::info('ResourceExternalContact $name:' . $name);
            $resourceContact = ResourceExternalContact::whereNameLike($name)->orderBy('created_at')->get();
        } else {
            $resourceContact = ResourceExternalContact::where('deleted_at', null)->get();
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => $resourceContact,
            ]
        );
    }

    public function store(Request $request)
    {
        $data = $request->all();

        $validator = $this->validateContact($data, 'store');

        if ($validator->fails()) {
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        }

        if (!isset($data['email']) && isset($errorResponse)) {
            return Response::json($errorResponse);
        }

        // Ensure email is unique
        $existingContact = ResourceExternalContact::email($data['email'])->first();
        if ($existingContact) {
            // if has validation errors on the previous validation
            if (isset($errorResponse)) {
                $errorResponse['errors']['email'] = ['The email already exists.'];
                return Response::json($errorResponse);
            }
            $errorResponse = [
                'response' => 'error',
                'errors' => [
                    'email' => ['The email already exists.'],
                ],
            ];
        }

        if (isset($errorResponse)) {
            return Response::json($errorResponse);
        }

        $externalParticipant = ResourceExternalContact::create(
            [
                'business' => ($request->has('business') && $request->get('business') === 'lmre')
                    ? 'lmre'
                    : 'lsm',
                'name' => $data['name'],
                'mobile_number' => $data['mobile_number'],
                'company' => (isset($data['company']) && !empty($data['company']))
                    ? $data['company']
                    : '',
                'email' => $data['email'],
            ]
        );

        return Response::json(
            [
                'response' => 'success',
                'data' => $externalParticipant,
                'message' => 'Contact has been successfully added.',
            ]
        );
    }

    private function validateContact($data, $action): ValidatorType
    {
        $rules = [
            'name' => 'required',
            //'company' => 'required',
            'email' => 'required|email',
            'mobile_number' => 'required|regex:' . self::VALID_MOBILE_NUMBER_REGEX,
        ];

        // On update, we don't want to validate email, cause it cannot be updated
        if ($action === 'update') {
            unset($rules['email']);
        }

        $messages = [
            'name.required' => 'Please enter Name.',
            //'company.required' => 'Please enter Company name.',
            'email.required' => 'Please enter Email.',
            'mobile_number.required' => 'Please enter Mobile Phone in a valid format.',
            'mobile_number.regex' => 'Please enter Mobile Phone in a valid format.',
        ];

        return Validator::make($data, $rules, $messages);
    }

    public function show($id)
    {
        $externalParticipant = ResourceExternalContact::find($id);

        if (!$externalParticipant) {
            return Response::json(
                [
                    'response' => 'error',
                    'errors' => 'External contact not found',
                ],
                404
            );
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => $externalParticipant,
            ]
        );
    }

    public function update(Request $request, $id)
    {
        $externalParticipant = ResourceExternalContact::find($id);
        if (!$externalParticipant) {
            return Response::json(
                [
                    'response' => 'error',
                    'errors' => 'External contact not found',
                ],
                404
            );
        }

        $data = $request->all();

        $validator = $this->validateContact($data, 'update');

        if ($validator->fails()) {
            return Response::json(
                [
                    'response' => 'error',
                    'errors' => $validator->messages()->toArray(),
                ],
                422
            );
        }

        $externalParticipant->update(
            [
                'name' => $data['name'],
                'mobile_number' => $data['mobile_number'],
                'company' => $data['company'],
            ]
        );

        return Response::json(
            [
                'response' => 'success',
                'data' => $externalParticipant,
                'message' => 'Contact has been successfully updated.',
            ]
        );
    }

    public function destroy($id)
    {
        $externalParticipant = ResourceExternalContact::find($id);
        if (!$externalParticipant) {
            return Response::json(
                [
                    'response' => 'error',
                    'errors' => 'External contact not found',
                ],
                404
            );
        }

        $externalParticipant->delete();

        return Response::json(
            [
                'response' => 'success',
                'message' => 'Contact has been successfully deleted.',
            ]
        );
    }
}
