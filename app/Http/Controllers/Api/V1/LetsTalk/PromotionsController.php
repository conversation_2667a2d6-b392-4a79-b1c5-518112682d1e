<?php

namespace App\Http\Controllers\Api\V1\LetsTalk;

use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use App\Models\Cms;

class PromotionsController extends BaseController
{
    public function index()
    {
        $letsTalkWorkspace = config('app.cms.lsm.lets_talk_workspace');
        $lsmVideosContentType = config('app.cms.lsm.promotion_videos_content_type');

        $query = json_encode(
            [
                'order' => [
                    config('app.cms.lsm.promotion_video_fields.updated_at') => 'desc',
                ],
                'status' => 'publish',
                'operator' => '=',
            ]
        );

        $videos = json_decode(
            Cms::get(
                "workspaces/{$letsTalkWorkspace}/content-types/{$lsmVideosContentType}/content-entries?query={$query}"
            )
        )->data;

        if (!empty($videos)) {
            $field = config('app.cms.lsm.promotion_video_fields.video_url');
            $video = current($videos);
            return Response::json(
                [
                    'status' => 'success',
                    'video' => [
                        'url' => $video->{$field}
                            ?: '',
                    ],
                ]
            );
        }

        return Response::json(
            [
                'status' => 'failed',
                'message' => 'No video found.',
            ]
        );
    }
}
