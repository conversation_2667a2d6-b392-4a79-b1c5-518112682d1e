<?php


namespace App\Http\Controllers\Api\V1\LetsTalk;

use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\LetsTalk\SocialRoom;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use App\Models\LetsTalk\UpcomingSchedule;
use Illuminate\Support\Facades\Validator;
use App\Models\LetsTalk\SocialRoomSchedule;
use Illuminate\Database\Eloquent\Collection;
use App\Models\LetsTalk\SocialRoomParticipant;
use App\Models\LetsTalk\CmsLibertyRepresentative;
use App\Models\LetsTalk\SocialRoomScheduleUpdate;
use App\Models\LetsTalk\SocialRoomSmsNotification;
use App\Models\LetsTalk\SocialRoomEmailNotification;

class SocialTeamMeetingController extends BaseController
{
    const VALID_MOBILE_NUMBER_REGEX = '/^[+][1-9][0-9]{6,14}/';
    private $business;

    public function __construct(Request $request)
    {
        $this->business = ($request->has('business') && $request->get('business') === 'lmre')
            ? 'lmre'
            : 'lsm';

        $this->libReps = CmsLibertyRepresentative::fetchAllFromCacheOrCms();

        $personId = $request->get('person_id');
        $this->authUserCms = $this->libReps[$personId] ?? [];
        $this->authUserTz = $this->authUserCms['office_timezone'] ?? 'Europe/London';
    }

    public function index()
    {
    }

    public function getRepresentatives()
    {
        $libReps = $this->libReps;
        return Response::json(
            [
                'response' => 'success',
                'data' => array_values($libReps),
            ]
        );
    }

    public function store(Request $request)
    {
        $data = $request->all();

        // \Log::info($data);

        $libReps = $this->libReps;
        // dd($libReps);

        $data['lt_social_room_type_id'] = 5; //hardocded for now

        $errorResponse = $this->validateStore($data, $libReps);
        if ($errorResponse instanceof JsonResponse) {
            return $errorResponse;
        }

        $libRepCreator = $libReps[$data['created_by_id']];
        $libRepParticipants = [];
        foreach ($data as $key => $value) {
            if (strpos($key, 'person_id') !== false) {
                if ($value != "") {
                    $libRepParticipants[] = $libReps[$value];
                }
            }
        }


        //field name
        $data['room_code'] = SocialRoom::roomCodeGenerator();
        $data['status'] = SocialRoom::STATUS_APPROVED;

        $socialRoom = SocialRoom::create($data);
        $socialRoom->is_team_meeting_edit = false;

        // Convert start_date & end_date to London tz from creator tz
        $data['start_date'] = Carbon::parse(
            $data['start_date'],
            $libRepCreator['office_timezone']
        )->setTimezone("Europe/London")->format('Y-m-d H:i:s');
        $data['end_date'] = Carbon::parse(
            $data['end_date'],
            $libRepCreator['office_timezone']
        )->setTimezone("Europe/London")->format('Y-m-d H:i:s');
        $data['end_after'] = $data['end_after_frequency'] . ' ' . $data['end_after_frequency_type'];
        $data['valid_until'] = SocialRoomSchedule::getValidUntil($data);


        $socialRoomSchedule = new SocialRoomSchedule($data);
        SocialRoomScheduleUpdate::create(
            [
                'room_id' => $socialRoom->id,
                'room_type' => $socialRoom->lt_social_room_type_id,
                'room_code' => $data['room_code'],
            ]
        );

        $socialRoom->socialRoomSchedule()->save($socialRoomSchedule);

        $libRepCreator['person_id'] = $data['created_by_id'];


        $libRepParticipants[] = $libRepCreator;

        // dd($participants);

        foreach ($libRepParticipants as $key => $participant) {
            $participant['person_id'] = $participant['person_id'];
            $participant['user_name'] = $participant['name'];
            $participant['user_code'] = md5(Hash::make(substr(Str::uuid()->toString(), 0, 4)));
            $participant['role'] = $data['role'];

            if ($libRepCreator['person_id'] === $participant['person_id']) {
                $libRepCreator['user_code'] = $participant['user_code'];
                $libRepCreator['user_name'] = $participant['user_name'];
            }

            if ($libRepParticipants[$key]['person_id'] === $participant['person_id']) {
                $libRepParticipants[$key]['user_code'] = $participant['user_code'];
                $libRepParticipants[$key]['user_name'] = $participant['user_name'];
            }

            $socialRoomParticipant = new SocialRoomParticipant($participant);
            $socialRoom->socialRoomParticipants()->save($socialRoomParticipant);
        }

        // Create external guest participants
        $newExternalParticipants = $this->createExternalParticipants($data, $socialRoom);
        Log::info($newExternalParticipants);

        $socialRoom = SocialRoom::setCmsData(new Collection([$socialRoom]), false, 'socialRoomParticipants')->first();

        $personId = $libRepCreator['person_id'];
        $roomCode = $data['room_code'];
        $socialRooms = SocialRoom::teamMeetings($personId)
            ->with('socialRoomSchedule')
            ->with(['socialRoomParticipants'])
            ->with('deletedScheduleEntries')
            ->where('room_code', $roomCode)
            ->get();

        if (isset($socialRooms->first()->socialRoomSchedule) && $socialRooms->first()->socialRoomSchedule) {
            $socialRooms = SocialRoom::setCmsData($socialRooms, false, 'socialRoomParticipants');
            $tmpData = UpcomingSchedule::generateWithBuffer($socialRooms, $this->authUserCms);
            $upcomingSchedules = $tmpData['upcomingSchedules'];
        } else {
            $upcomingSchedules = [];
        }

        if (!$socialRoomSchedule->isPermanent()) {
            // send email notification here
            SocialRoomEmailNotification::sendTeamMeetingTemplate1(
                [
                    'room' => $socialRoom,
                    'schedule' => $socialRoomSchedule,
                    'participants' => $libRepParticipants,
                    'externalParticipants' => $newExternalParticipants,
                    'creator' => $libRepCreator,
                    'frequency_info' => $data,
                    'upcoming_schedules' => $upcomingSchedules,
                    'externalAttendees' => SocialRoomParticipant::where('lt_social_room_id', $socialRoom->id)->where(
                        'role',
                        'external-guest'
                    )->get(),
                ]
            );
            $sendSms = new SocialRoomSmsNotification;
            $sendSms->sendSmsTemplate1(
                $socialRoom,
                $libRepCreator,
                $libRepParticipants,
                $newExternalParticipants,
                $socialRoomSchedule,
                true
            );
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => [
                    'room' => $socialRoom,
                    'schedule' => $socialRoomSchedule,
                    'participant' => $libRepParticipants,
                    'externalParticipants' => $newExternalParticipants,
                    'creator' => $libRepCreator,
                ],
                'message' => 'Room has been successfully created.',
            ]
        );
    }

    private function validateStore($data, $libReps)
    {
        $validator = $this->_validate($data, 'store');

        if ($validator->fails()) {
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        }

        if ($data['created_by_id'] && !isset($libReps[$data['created_by_id']])) {
            $validator->errors()->add('created_by_id', 'Created by id does not exists.');
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        }

        // if(isset($data['person_id']) && !isset($libReps[$data['person_id']])) {
        //     $validator->errors()->add('person_id', 'Participant does not exists.');
        //     $errorResponse = [
        //         'response' => 'error',
        //         'errors' => $validator->messages()->toArray(),
        //     ];
        // }

        if (isset($errorResponse)) {
            return Response::json($errorResponse);
        }

        return [];
    }

    private function _validate($data, $action)
    {
        if ($action == 'store') {
            $rules = [
                'created_by_id' => 'required',
                'duration_type' => 'required',
                'frequency' => 'required|integer',
                'frequency_type' => 'required',
                'start_date' => 'required',
                'end_date' => 'required',
                'external_users' => 'array',
            ];

            // // On update, we don't want to validate email, cause it cannot be updated
            // if($action === 'update') {
            //     unset($rules['email']);
            // }

            // TODO: Add appropriate error messages here
            // TODO: Add appropriate error messages here
            // TODO: Add appropriate error messages here
            $messages = [
                // 'name.required' => 'Please enter Name.',
                // 'email.required' => 'Please enter Email.',
                // 'mobile_number.required' => 'Please enter Mobile Phone in a valid format.',
                // 'mobile_number.regex' => 'Please enter Mobile Phone in a valid format.',
            ];

            // Validate external user/external participants
            if (isset($data['external_users']) && !empty($data['external_users'])) {
                foreach ($data['external_users'] as $key => $externalUser) {
                    $rules['external_users.' . $key . '.external_user'] = 'required';
                    $rules['external_users.' . $key . '.external_user_email'] = 'required|email';
                    $rules['external_users.' . $key . '.external_user_mobile'] = 'required|regex:' . self::VALID_MOBILE_NUMBER_REGEX;

                    $messages['external_users.' . $key . '.external_user.required'] = 'Please enter Name.';
                    $messages['external_users.' . $key . '.external_user_email.required'] = 'Please enter Email in a valid format.';
                    $messages['external_users.' . $key . '.external_user_email.email'] = 'Please enter Email in a valid format.';

                    $messages['external_users.' . $key . '.external_user_mobile.required'] = 'Please enter Mobile Phone in a valid format.';
                    $messages['external_users.' . $key . '.external_user_mobile.regex'] = 'Please enter Mobile Phone in a valid format.';
                }
            }
        } else {
            $rules = [
                'external_users' => 'array',
            ];

            $messages = [];

            if (isset($data['external_users']) && !empty($data['external_users'])) {
                foreach ($data['external_users'] as $key => $externalUser) {
                    $rules['external_users.' . $key . '.external_user'] = 'required';
                    $rules['external_users.' . $key . '.external_user_email'] = 'required|email';
                    $rules['external_users.' . $key . '.external_user_mobile'] = 'required|regex:' . self::VALID_MOBILE_NUMBER_REGEX;

                    $messages['external_users.' . $key . '.external_user.required'] = 'Please enter Name.';
                    $messages['external_users.' . $key . '.external_user_email.required'] = 'Please enter Email in a valid format.';
                    $messages['external_users.' . $key . '.external_user_email.email'] = 'Please enter Email in a valid format.';

                    $messages['external_users.' . $key . '.external_user_mobile.required'] = 'Please enter Mobile Phone in a valid format.';
                    $messages['external_users.' . $key . '.external_user_mobile.regex'] = 'Please enter Mobile Phone in a valid format.';
                }
            }
        }

        return Validator::make($data, $rules, $messages);
    }

    private function createExternalParticipants($data, $socialRoom)
    {
        if (!isset($data['external_users']) || empty($data['external_users'])) {
            return [];
        }

        $externalParticipants = $data['external_users'];
        foreach ($externalParticipants as $key => $participant) {
            $externalParticipants[$key]['user_name'] = 'n/a';
            $externalParticipants[$key]['person_id'] = uniqid();
            $externalParticipants[$key]['mobile'] = 'n/a';
            $externalParticipants[$key]['role'] = 'external-guest';
            $externalParticipants[$key]['user_code'] = SocialRoomParticipant::roomCodeGenerator();

            if (isset($data['broker_id'])) {
                $externalParticipants[$key]['broker_id'] = $data['broker_id'];
                $externalParticipants[$key]['external_user_company'] = null;
            } else {
                $externalParticipants[$key]['external_user_company'] = $data['external_user_company'];
            }

            $socialRoomParticipant = new SocialRoomParticipant($externalParticipants[$key]);
            $socialRoom->socialRoomParticipants()->save($socialRoomParticipant);
        }

        return $externalParticipants;
    }

    private function setCmsData($participants_data, $is_array = true)
    {
        $cms = CmsLibertyRepresentative::fetchAllFromCacheOrCms();

        // Assign CMS data
        foreach ($participants_data as $key => $socialRoomParticipant) {
            $room_id = $socialRoomParticipant['id'];
            foreach ($socialRoomParticipant->social_room_participants as $key2 => $participant) {
                $person_id = ($is_array)
                    ? $participant->person_id
                    : $participant['person_id'];
                $role = ($is_array)
                    ? $participant->role
                    : $participant['role'];

                if (isset($participants_data[$key]) && isset($participants_data[$key]->social_room_participants[$key2]) && $role != 'external-guest') {
                    $participants_data[$key]->social_room_participants[$key2]->cms = $cms[$person_id];
                } else {
                    $participants_data[$key]->social_room_participants[$key2]->cms = [];
                }
            }
        }

        return $participants_data;
    }

    public function show(Request $request, $roomCode)
    {
        $personId = $request->get('person_id');
        $email = $request->get('email');
        $isCustomer = $request->get('is_customer');
        $userCode = $request->get('user_code');
        $includePolls = $request->get('include_polls');
        $includeActivePoll = true;

        $role = 'virtual-rooms';
        if (!empty($userCode) && empty($personId)) {
            // Use user_code to get needed participant data
            $participant = SocialRoomParticipant::where('user_code', $userCode)->first();

            $role = $participant->role;
            $personId = $participant->person_id;

            // Empty email passed.
            // Attempt to used email address in participant's external_user_email
            // If role is external-guest
            if (empty($email) && $role == 'external_guest') {
                $email = $participant->external_user_email;
            }
        } else {
            $checkParticipant = SocialRoomParticipant::where('person_id', $personId)->first();

            if ($checkParticipant) {
                $role = $checkParticipant->role;
            }
        }

        $userCms = $this->authUserCms;

        if ($isCustomer) {
            $role = 'external-guest';
            $socialRooms = SocialRoom::customerTeamMeetings($email)
                ->with('socialRoomSchedule')
                ->with(['socialRoomParticipants.broker'])
                ->with('deletedScheduleEntries')
                ->with('modifiedScheduleEntries')
                ->where('room_code', $roomCode);

            if (!empty($includePolls || $includeActivePoll)) {
                $socialRooms->with('polls');
            }

            $socialRooms = $socialRooms->get();

            if ($socialRooms->first()) {
                $userCms = $this->libReps[$socialRooms->first()->created_by_id];
            }
        } else {
            $socialRooms = SocialRoom::teamMeetings($personId)
                ->with('socialRoomSchedule')
                ->with(['socialRoomParticipants.broker'])
                ->with('deletedScheduleEntries')
                ->with('modifiedScheduleEntries')
                ->where('room_code', $roomCode);

            if (!empty($includePolls || $includeActivePoll)) {
                $socialRooms->with('polls');
            }

            $socialRooms = $socialRooms->get();
        }

        $socialRooms = SocialRoom::setCmsData($socialRooms, false, 'socialRoomParticipants');
        if (isset($socialRooms->first()->socialRoomSchedule) && $socialRooms->first()->socialRoomSchedule) {
            $socialRoomsWithCMS = SocialRoom::setCmsData($socialRooms, false, 'socialRoomParticipants');
            $tmpData = UpcomingSchedule::generateWithBuffer($socialRoomsWithCMS, $userCms);
            $upcomingSchedules = $tmpData['upcomingSchedules'];
        } else {
            $upcomingSchedules = [];
        }

        $brokerCompany = [];
        $brokerCompanyLogo = '';
        if ($socialRooms->first() && $socialRooms->first()->socialRoomParticipants) {
            $participant = Arr::first(
                $socialRooms->first()->socialRoomParticipants,
                function ($participant, $key) {
                    return isset($participant->broker);
                }
            );
            if (isset($participant->broker)) {
                $brokerCompany = $participant->broker;
                $brokerCompanyLogo = $brokerCompany->link;
            }
        }

        $joinRoomLink = '';
        if ($socialRooms->first()) {
            $participantQuery = SocialRoomParticipant::where('lt_social_room_id', $socialRooms->first()->id);
            if ($role == 'external-guest') {
                $participantQuery->where('external_user_email', $email);
            } else {
                $participantQuery->where('person_id', $personId);
            }
            $participant = $participantQuery->first();
            if (!(empty($participant))) //$joinRoomLink = SocialRoomParticipant::generateLink($participant->user_code);
            {
                $joinRoomLink = SocialRoomParticipant::generateWaitingRoomCommonLink($roomCode);
            }
        }

        //$socialRooms = $this->setCmsData($socialRooms, true);
        $socialRoom = $socialRooms->first();

        if (empty($socialRoom)) {
            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'Social room not found!',
                ],
                404
            );
        } else {

            // Access property to load
            if (!empty($includeActivePoll)) {
                $socialRoom->active_poll = $socialRoom->active_poll;
            }

            return Response::json(
                [
                    'response' => 'success',
                    'data' => [
                        'room' => $socialRoom,
                        'upcoming_schedules' => $upcomingSchedules,
                        'join_room_link' => $joinRoomLink,
                        'broker_company_logo' => $brokerCompanyLogo,
                    ],
                ]
            );
        }
    }

    public function destroy($id)
    {
    }

    // Get list of teams the person_id has catchups with

    public function getUpcomingTeamMeetings(Request $request)
    {
        $personId = $request->get('person_id'); //creator

        $carbonNow = Carbon::now();
        $socialRooms = SocialRoom::teamMeetings($personId)
            ->with(['socialRoomSchedule'])
            ->has('socialRoomSchedule')
            ->with('deletedScheduleEntries')
            ->with('modifiedScheduleEntries')
            ->get();


        // return Response::json([
        //     'response' => 'success',
        //     'data' => $socialRooms,
        // ]);
        $socialRooms = SocialRoom::setCmsData($socialRooms, false, 'socialRoomParticipants');

        $permanentRooms = new Collection([]);
        $nonPermanentRooms = new Collection([]);
        $socialRooms->each(
            function ($room) use ($permanentRooms, $nonPermanentRooms) {
                $room->room = $room->room_code;
                if ($room->socialRoomSchedule->isPermanent()) {
                    $room->is_permanent = true;
                    if (isset($room->cms['user_name'])) {
                        $permanentRooms->push($room);
                    }
                } else {
                    $nonPermanentRooms->push($room);
                }
            }
        );

        if ($request->has('no_limit')) {
            $tmpData = UpcomingSchedule::generateNoLimitOnTotal(
                $nonPermanentRooms,
                $this->authUserCms,
                $buffer = 0,
                $maxSchedPerRoom = 1
            );
        } else {
            $tmpData = UpcomingSchedule::generate($nonPermanentRooms, $this->authUserCms);
        }

        $upcomingSchedules = $tmpData['upcomingSchedules'];
        $rooms = $tmpData['rooms'];

        return Response::json(
            [
                'response' => 'success',
                'data' => $upcomingSchedules,
                'rooms' => $rooms,
                'permanent_rooms' => $permanentRooms,
            ]
        );
    }

    public function getTeams(Request $request)
    {
        $personId = $request->get('person_id'); //creator
        $socialRooms = SocialRoom::teamMeetings($personId)->orderBy('name')->get();
        $socialRooms = SocialRoom::setCmsData($socialRooms, false, 'socialRoomParticipants');

        return Response::json(
            [
                'response' => 'success',
                'data' => $socialRooms,
            ]
        );
    }

    public function deleteSpace($socialRoomId)
    {
        // $socialRoom = SocialRoom::with(['socialRoomParticipants', 'socialRoomSchedule'])->find($socialRoomId);
        // if(!$socialRoom) {
        //     return Response::json(['response' => 'error', 'message' => 'Unable to delete. Space not found.'], 400);
        // }

        // $socialRoom->is_cancelled_schedule = false;
        // SocialRoomEmailNotification::catchUpTemplate3($socialRoom->toArray());

        // $socialRoom->delete();

        // return Response::json(['response' => 'success', 'message' => 'Space deleted.']);
    }

    // validate store method


    public function deleteSchedule()
    {
        // $socialRoomId = $request->get('lt_social_room_id');
        // $scheduleId = $request->get('schedule_id');
        // $scheduleDate = $request->get('date');

        // $socialRoom = $socialRoom = SocialRoom::with(['socialRoomParticipants', 'socialRoomSchedule'])->find($socialRoomId);
        // if (!$socialRoom) {
        //     return Response::json(['response' => 'error', 'message' => 'Unable to delete. Space not found.'], 400);
        // }

        // // If schedule date not provided, we will delete the whole schedule
        // if (empty($scheduleDate)) {
        //     $socialRoomSchedule = SocialRoomSchedule::find($scheduleId);
        //     if (!$socialRoomSchedule) {
        //         return Response::json(['response' => 'error', 'message' => 'Unable to delete. Space not found.'], 400);
        //     }

        //     $socialRoom->cancelled_schedule = true;
        //     SocialRoomEmailNotification::catchUpTemplate3($socialRoom->toArray());

        //     $socialRoomSchedule->delete();

        //     return Response::json(['response' => 'success', 'message' => 'Space schedule deleted.']);
        // }

        // $socialRoom = SocialRoomCancelledSchedule::create($request->all());

        // $socialRoom->cancelled_schedule = false;
        // SocialRoomEmailNotification::catchUpTemplate3($socialRoom->toArray());

        // return Response::json(['response' => 'success', 'message' => 'Space schedule deleted.']);
    }

    public function getSocialRoomWithId($room_id)
    {
        $social_room = SocialRoom::where('id', $room_id)->with(['socialRoomParticipants.broker'])->first();
        $social_room = SocialRoom::setCmsData(new Collection([$social_room]), false, 'socialRoomParticipants')->first();

        $brokerCompany = [];
        if ($social_room->socialRoomParticipants) {
            $participant = Arr::first(
                $social_room->socialRoomParticipants,
                function ($participant, $key) {
                    return isset($participant->broker);
                }
            );
            if (isset($participant->broker)) {
                $brokerCompany = $participant->broker;
            }
        }
        $social_room->company_name = isset($brokerCompany->name)
            ? $brokerCompany->name
            : "";
        return Response::json($social_room);
    }

    public function editRoomTitle(Request $request, $id)
    {
        $data        = $request->all();
        $socialRoom = SocialRoom::where('id', $id)
            ->with('socialRoomSchedule')
            ->first();
        $socialRoom->name = $data['room_name'];
        $socialRoom->save();

        return Response::json($socialRoom);
    }

    public function editTeamMeeting(Request $request, $id)
    {
        $data = $request->all();
        $social_room = SocialRoom::where('id', $id)->first();
        $old_room_name = $social_room->name;
        $social_room->name = $data['room_name'];
        $social_room->save();
        $libReps = $this->libReps;
        $now = Carbon::now();
        $hasNewParticipant = false;
        $hasRemovedParticipant = false;
        $updatedParticipant = [];
        $newParticipantsData = [];
        $libRepCreator = $libReps[$social_room->created_by_id];

        // $socialRoomSchedule = SocialRoomSchedule::where('valid_until', '>=', $now)->where('lt_social_room_id', $social_room->id)->first();
        $socialRoomSchedule = $social_room->socialRoomSchedule;
        if ($social_room->socialRoomSchedule) {
            $socialRooms = new Collection([$social_room]);
            $tmpData = UpcomingSchedule::generateWithBuffer($socialRooms, $this->authUserCms);
            $upcomingSchedules = $tmpData['upcomingSchedules'];
            if (is_object($upcomingSchedules)) {
                $social_room->upcoming_schedules_count = $upcomingSchedules->count();
            } else {
                $social_room->upcoming_schedules_count = count($upcomingSchedules);
            }
        }
        $social_room->duration_type = $socialRoomSchedule->duration_type;
        $social_room->frequency_type = $socialRoomSchedule->frequency_type;
        $social_room->frequency = $socialRoomSchedule->frequency;
        $social_room->valid_until = $socialRoomSchedule->valid_until;
        $social_room->sequence = (int)$socialRoomSchedule->ics_sequence + 1;

        SocialRoomSchedule::where(
            'lt_social_room_id',
            $social_room->id
        )->update(['ics_sequence' => (int)$socialRoomSchedule->ics_sequence + 1]);

        $errorResponse = $this->validateEdit($data);
        if ($errorResponse instanceof JsonResponse) {
            Log::info($errorResponse);
            return $errorResponse;
        }

        foreach ($data as $key => $value) {
            if (strpos($key, 'person_id') !== false && !empty($value)) {
                $social_room_participant = SocialRoomParticipant::where('lt_social_room_id', $id)->where(
                    'person_id',
                    $value
                )->first();
                if ($social_room_participant) {
                    $socialRoom = SocialRoom::with(['socialRoomSchedule'])->find($social_room->id);
                    $socialRoomSchedule = SocialRoomSchedule::where(
                        'valid_until',
                        '>=',
                        $now
                    )->where('lt_social_room_id', $social_room->id)->first();
                    if ($socialRoom->socialRoomSchedule) {
                        $socialRooms = new Collection([$socialRoom]);
                        $tmpData = UpcomingSchedule::generateWithBuffer($socialRooms, $this->authUserCms);
                        $upcomingSchedules = $tmpData['upcomingSchedules'];
                        if (is_object($upcomingSchedules)) {
                            $socialRoom->upcoming_schedules_count = $upcomingSchedules->count();
                        } else {
                            $socialRoom->upcoming_schedules_count = count($upcomingSchedules);
                        }
                    }
                    if (strpos($key, 'remove') !== false) { //remove social participant
                        $socialRoom->social_room_participants = $social_room_participant;
                        $socialRoom->is_team_meeting = true;
                        $socialRoom->removed_cms = $libReps[$socialRoom->social_room_participants->person_id];
                        $socialRoom['removed_participant'] = [$social_room_participant];

                        $socialRoom->is_team_meeting_edit = true;
                        // return \Response::json($socialRoom);
                        if (isset($socialRoomSchedule) && isset($socialRoom->upcoming_schedules_count) && $socialRoom->upcoming_schedules_count > 0) { // send only if there is an upcoming schedule
                            SocialRoomEmailNotification::catchUpTemplate3($socialRoom->toArray(), $libRepCreator);
                        }
                        $social_room_participant->delete();
                        $hasRemovedParticipant = true;
                    } else {
                        continue;
                    }
                } else {
                    $socialRoom = SocialRoom::with(['socialRoomSchedule'])->find($social_room->id);
                    $newParticipant = $libReps[$value];
                    $user_code = md5(Hash::make(substr(Str::uuid()->toString(), 0, 4)));
                    $hasNewParticipant = true;
                    SocialRoomParticipant::create(
                        [
                            'lt_social_room_id' => $id,
                            'user_name' => $newParticipant['name'],
                            'role' => 'virtual-rooms',
                            'person_id' => $value,
                            'user_code' => $user_code,
                        ]
                    );

                    $newParticipant['user_code'] = $user_code;
                    $newParticipant['user_name'] = $newParticipant['name'];
                    $newParticipantsData[] = $newParticipant;

                    $updatedParticipant[$value] = $value;
                }
            }
        }

        $social_room_participants = SocialRoomParticipant::where('lt_social_room_id', $id)->get();
        $liberty_participants = [];
        foreach ($social_room_participants as $participant) {
            if (isset($libReps[$participant->person_id])) {
                $liberty_participants[] = $libReps[$participant->person_id];
            }
        }

        $removedExternalParticipants = $this->removeExternalParticipants($data, $social_room);
        $hasRemovedExternalParticipant = count($removedExternalParticipants) > 0;

        if (!empty($removedExternalParticipants)) {
            $socialRoom = $social_room;

            if ($socialRoom->socialRoomSchedule) {
                $socialRooms = new Collection([$socialRoom]);
                $tmpData = UpcomingSchedule::generateWithBuffer($socialRooms, $this->authUserCms);
                $upcomingSchedules = $tmpData['upcomingSchedules'];
                if (is_object($upcomingSchedules)) {
                    $socialRoom->upcoming_schedules_count = $upcomingSchedules->count();
                } else {
                    $socialRoom->upcoming_schedules_count = count($upcomingSchedules);
                }
            }

            $socialRoom->is_team_meeting = true;
            $socialRoom->is_team_meeting_edit = true;
            $socialRoom->removed_participant = $removedExternalParticipants;
            if (isset($socialRoomSchedule) && isset($socialRoom->upcoming_schedules_count) && $socialRoom->upcoming_schedules_count > 0) { // send only if there is an upcoming schedule
                SocialRoomEmailNotification::catchUpTemplate3($socialRoom->toArray(), $libRepCreator);
            }
        }

        $newExternalParticipants = $this->createExternalParticipants($data, $social_room);
        $newExternalParticipantUserCodes = [];
        foreach ($newExternalParticipants as $key => $newExternalParticipant) {
            $newExternalParticipantUserCodes[] = $newExternalParticipant['user_code'];
        }
        $hasNewExternalParticipants = !empty($newExternalParticipants);

        $personId = $social_room->created_by_id;
        $roomCode = $social_room->room_code;
        $socialRooms = SocialRoom::teamMeetings($personId)
            ->with('socialRoomSchedule')
            ->with(['socialRoomParticipants'])
            ->with('deletedScheduleEntries')
            ->where('room_code', $roomCode)
            ->get();

        if (isset($socialRooms->first()->socialRoomSchedule) && $socialRooms->first()->socialRoomSchedule) {
            $socialRooms = SocialRoom::setCmsData($socialRooms, false, 'socialRoomParticipants');
            $tmpData = UpcomingSchedule::generateWithBuffer($socialRooms, $this->authUserCms);
            $upcomingSchedules = $tmpData['upcomingSchedules'];
        } else {
            $upcomingSchedules = [];
        }

        if (isset($socialRoomSchedule) && isset($social_room->upcoming_schedules_count) && $social_room->upcoming_schedules_count > 0) { // send only if there is an upcoming schedule
            if (($newParticipantsData && !empty($newParticipantsData)) || $hasNewExternalParticipants == true) { //send email to newly invited participants
                SocialRoomEmailNotification::sendTeamMeetingTemplate1(
                    [
                        'room' => $social_room,
                        'creator' => $libRepCreator,
                        'externalParticipants' => $newExternalParticipants,
                        'schedule' => $socialRoomSchedule,
                        'participants' => $newParticipantsData,
                        'frequency_info' => $social_room,
                        'attendees' => $liberty_participants,
                        'externalAttendees' => SocialRoomParticipant::where('lt_social_room_id', $id)->where(
                            'role',
                            'external-guest'
                        )->get(),
                    ]
                );

                $sendSms = new SocialRoomSmsNotification;
                $sendSms->sendSmsTemplate1(
                    $social_room,
                    $libRepCreator,
                    $newParticipantsData,
                    $newExternalParticipants,
                    $socialRoomSchedule,
                    true
                );
            }
        }

        if (isset($socialRoomSchedule) && isset($social_room->upcoming_schedules_count) && $social_room->upcoming_schedules_count > 0) {
            if ($old_room_name != $data['room_name'] || $hasNewParticipant == true || $hasRemovedParticipant == true || $hasNewExternalParticipants == true || $hasRemovedExternalParticipant == true) {
                SocialRoomEmailNotification::teamMeetingTemplate5(
                    [
                        'room' => $social_room,
                        'old_room_name' => $old_room_name,
                        'new_room_name' => $data['room_name'],
                        'participants' => $liberty_participants,
                        'externalAttendees' => SocialRoomParticipant::where('lt_social_room_id', $id)->where(
                            'role',
                            'external-guest'
                        )->get(),
                        'externalParticipants' => SocialRoomParticipant::where('lt_social_room_id', $id)->where(
                            'role',
                            'external-guest'
                        )->whereNotIn('user_code', $newExternalParticipantUserCodes)->get(),
                        'skip_send' => $updatedParticipant,
                        'schedule' => $socialRoomSchedule,
                        'creator' => $libRepCreator,
                    ]
                );
            }
        }

        return Response::json($social_room);
    }

    public function update($id)
    {
    }

    private function validateEdit($data)
    {
        $validator = $this->_validate($data, 'edit');

        if ($validator->fails()) {
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        }

        if (isset($errorResponse)) {
            return Response::json($errorResponse);
        }

        return [];
    }

    private function removeExternalParticipants($data, $socialRoom)
    {
        $removedParticipants = [];
        foreach ($data as $key => $value) {
            if (strpos($key, 'remove_external_participant_id') !== false && !empty($value)) {
                $social_room_participant = SocialRoomParticipant::where(
                    'lt_social_room_id',
                    $socialRoom->id
                )->where('id', $value)->first();
                $removedParticipants[] = $social_room_participant;
                $social_room_participant->delete();
            }
        }
        return $removedParticipants;
    }
}
