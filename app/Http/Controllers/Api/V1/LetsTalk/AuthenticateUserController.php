<?php


namespace App\Http\Controllers\Api\V1\LetsTalk;

use App\Http\Controllers\BaseController;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use App\Models\LetsTalk\PasswordlessLog;
use App\Models\LetsTalk\PasswordlessToken;
use App\Models\LetsTalk\SocialRoom;
use App\Models\LetsTalk\SocialRoomParticipant;
use App\Models\LetsTalk\SocialRoomSmsNotification;
use App\Models\LibertyUser;
use App\Models\Mailqueue;
use App\Models\Cms;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class AuthenticateUserController extends BaseController
{
    public function __construct(Mailqueue $mailqueue)
    {
        $this->mail = $mailqueue;
    }

    public function authenticate(Request $request)
    {
        $data = $request->all();

        $token = PasswordlessToken::where('token', $data['token'])
            ->where('token_expiry', '>=', date('Y-m-d H:i:s'))
            ->first();

        // liberty_user_id greater than zero(0) is an existing liberty representative
        if (!empty($token) && $token->liberty_user_id > 0) {

            $user = LibertyUser::where('id', $token->liberty_user_id)->first();

            $user['person_id'] = $token->person_id;
            $user['email'] = $user->email;

            //update row
            PasswordlessToken::where('token', $data['token'])->update(
                [
                'has_logged_in' => 1,
                ]
            );

            if (in_array($user->person_id, config('app.cyber_virtual_rooms.can_access'))){
                Session::put('can-access-cyber-vr', true);
            }

            return Response::json(
                [
                'response' => 'success',
                'message' => 'Permission granted and can be authenticated',
                'socials_user' => $user,
                'socials_customer' => false,
                ]
            );
        }

        // liberty_user_id equal to zero(0) is a customer
        if (!empty($token) && $token->liberty_user_id === 0) {
            return Response::json(
                [
                'response' => 'success',
                'message' => 'Permission granted and can be authenticated',
                'socials_user' => false,
                'socials_customer' => $token,
                ]
            );
        }

        $token = PasswordlessToken::where('token', $data['token'])->first();

        if (!empty($token) && $token->liberty_user_id > 0) {
            return Response::json(
                [
                'response' => 'fail',
                'message' => '<i class="icon icon-alert-triangle icon-md mr-2"></i><b>ACCESS DENIED, the token maybe expired or the link is invalid.</b>',
                'socials_user' => true,
                'socials_customer' => false,
                ]
            );
        }

        if (!empty($token) && $token->liberty_user_id === 0) {
            return Response::json(
                [
                'response' => 'fail',
                'message' => '<i class="icon icon-alert-triangle icon-md mr-2"></i><b>ACCESS DENIED, the token maybe expired or the link is invalid.</b>',
                'socials_user' => false,
                'socials_customer' => true,
                ]
            );
        }
    }

    public function generateLink(Request $request)
    {
        $userVerified = false;
        $person_id = '';
        $bussiness = 'lsm';
        $data = $request->all();
        $email_input = $data['email'];

        $user = LibertyUser::where('email', $data['email'])->first();

        if (!$user) {
            $user = LibertyUser::where('email', strtolower($data['email']))->first();
        }

        $userDetail = (object)[];

        if (isset($user->email) && !empty($user->email)) {
            $libertyRepresenativesCms = json_decode(
                Cms::get(
                    'workspaces/' . $this->getCmsConfig(
                        'lsm',
                        'lets_talk_workspace'
                    ) . '/content-types/' . $this->getCmsConfig(
                        'lsm',
                        'liberty_representatives_content_type'
                    ) . '/content-entries?joined=true&query={"status":"publish","operator":"="}'
                )
            )->data[0]->{$this->getCmsConfig(
                'lsm',
                'liberty_representatives'
            )};

            foreach ($libertyRepresenativesCms as $key => $value) {
                if (!is_null($value) && strtolower(
                    $value->{$this->getCmsConfig(
                        'lsm',
                        'liberty_representative_email'
                    )}
                ) === strtolower($user->email) 
                    && $value->status != 'draft' 
                    && $value->{$this->getCmsConfig('lsm', 'liberty_representative_mobile_number')} != ''
                ) {
                    $business = 'lsm';
                    $userVerified = true;
                    $person_id = $value->_id;
                    $userDetail->mobileNo = $value->{$this->getCmsConfig(
                        'lsm',
                        'liberty_representative_mobile_number'
                    )};
                    $userDetail->name = $value->name;
                }
            }

            // check lmre data of no lsm data has been identified
            if (!$userVerified) {
                $libertyRepresenativesCms = json_decode(
                    Cms::get(
                        'workspaces/' . $this->getCmsConfig(
                            'lmre',
                            'lets_talk_workspace'
                        ) . '/content-types/' . $this->getCmsConfig(
                            'lmre',
                            'liberty_representatives_content_type'
                        ) . '/content-entries?joined=true&query={"status":"publish","operator":"="}'
                    )
                )->data[0]->{$this->getCmsConfig(
                    'lmre',
                    'liberty_representatives'
                )};

                foreach ($libertyRepresenativesCms as $key => $value) {
                    if (!is_null($value) && strtolower(
                        $value->{$this->getCmsConfig(
                            'lmre',
                            'liberty_representative_email'
                        )}
                    ) === strtolower($user->email) 
                        && $value->status != 'draft' 
                        && $value->{$this->getCmsConfig('lmre', 'liberty_representative_mobile_number')} != ''
                    ) {
                        $business = 'lmre';
                        $userVerified = true;
                        $person_id = $value->_id;
                        $userDetail->mobileNo = $value->{$this->getCmsConfig(
                            'lmre',
                            'liberty_representative_mobile_number'
                        )};
                        $userDetail->name = $value->name;
                    }
                }
            }


        }


        // If user exists then formulate the link end send an email notification
        if (!empty($user) && $userVerified) {

            // handle throttling issue max of 10 active emails only
            $tokenCont = PasswordlessToken::where('liberty_user_id', $user->id)
                ->where('token_expiry', '>=', date('Y-m-d H:i:s'))
                ->count();

            if ($tokenCont >= 10) {
                return Response::json(
                    [
                    'response' => 'fail',
                    'message' => 'You have reached maximum magic link request, please try again later',
                    ]
                );
            }

            $passwordless_token = $this->generateEmailLink($user, $person_id);
            $viewParameters = [
                'token' => rawurlencode($passwordless_token->token),
                'liberty_representative' => $user->first_name . ' ' . $user->last_name,
                'business' => $business,
            ];

            $subject = 'Here is your magic link - Virtual Rooms for staff';
            $recipientEmail = $user->email;
            $recipientName = $user->first_name . ' ' . $user->last_name;
            $viewParameters['user_type'] = 'staff';
            $viewParameters['title'] = 'Here is your magic link';
            $viewParameters['is_customer'] = false;
            $viewParameters['message'] = "<h1 style='color: #1e325f; font-family: Arial, Helvetica, sans-serif;'>Dear " . $recipientName . ",</h1>
                                                            <p style='font-size: 16px; color: #5c6064; line-height: 22px; text-align: left; font-family: Arial, Helvetica, sans-serif; margin: 20px 0;' align='left'>
                                                                To access your Virtual Rooms for staff dashboard, please click the button below.
                                                                <br/>
                                                                <br/>
                                                                Access Virtual Rooms for staff dashboard
                                                            </p>";


            // Send SMS with magic link
            $userDetail->token = config('app.admin_frontend') . '/virtual-rooms/authenticate/' . $viewParameters['token'] . '?business=' . $viewParameters['business'];
            $sendSms = new SocialRoomSmsNotification;
            $validateMobile = $sendSms->sendSmsTemplate0($userDetail);

            // if (!$validateMobile) {
            //     $this->failedAttemptLog($email_input);

            //     return Response::json(
            //         [
            //         'response' => 'fail',
            //         'message' => '<i class="icon icon-alert-triangle icon-md mr-2"></i><b>ACCESS DENIED, Invalid email address.',
            //         ]
            //     );
            // }

            //Send Email
            $this->mail->queue(
                $recipientEmail,
                $recipientName,
                $subject,
                "emails.lets-talk-generic.passwordless-notification",
                $viewParameters,
                null
            );
        } else {

            // Log all failed attempt
            $this->failedAttemptLog($email_input);

            return Response::json(
                [
                'response' => 'fail',
                'message' => '<i class="icon icon-alert-triangle icon-md mr-2"></i><b>ACCESS DENIED, Invalid email address.',
                ]
            );
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => $user,
            ]
        );
    }

    private function getCmsConfig($business, $config)
    {
        return config('app.cms.' . $business . '.' . $config);
    }

    private function generateEmailLink($user, $person_id)
    {
        $token = md5(Hash::make(substr(Str::uuid()->toString(), 0, 4)));
        $liberty_user_id = $user->id;
        $token_expiry = Carbon::now()->addHours(24);

        $passwordless_token = PasswordlessToken::create(
            [
            'liberty_user_id' => $liberty_user_id,
            'person_id' => $person_id,
            'token' => $token,
            'token_expiry' => $token_expiry,
            ]
        );

        return $passwordless_token;
    }

    private function failedAttemptLog($email)
    {
        $email = strtolower($email);

        // restore if any existing
        PasswordlessLog::withTrashed()
            ->where('email', $email)
            ->restore();

        $previous_attempts = PasswordlessLog::where('email', $email)->first();

        if ($previous_attempts) {
            PasswordlessLog::where('email', $email)
                ->update(
                    [
                    'login_attempt' => $previous_attempts->login_attempt + 1,
                    ]
                );
        } else {
            PasswordlessLog::create(
                [
                'email' => $email,
                'login_attempt' => 1,
                ]
            );
        }


    }

    public function customerGenerateLink(Request $request)
    {
        $userVerified = false;
        $person_id = '';
        $business = 'lsm';
        $data = $request->all();
        $email = $data['email'];

        // Check the email if connected/invited to any meeting that is not expired more than or equal to 24 hours
        $guest_count = SocialRoom::whereHas(
            'socialRoomParticipants', function ($query) use ($email) {
                return $query->where('role', 'external-guest')
                    ->whereRaw('LOWER(external_user_email) = ?', [(string)strtolower($email)]);
            }
        )->whereHas(
            'socialRoomSchedule', function ($query) {
                    return $query->whereDate(
                        'valid_until', '>=',
                        date('Y-m-d H:i:s', strtotime(Carbon::now()->subDays(1)))
                    );
            }
        )
            ->get();

        // Customer found
        if (count($guest_count) > 0) {
            $external_guest = SocialRoomParticipant::where('role', 'external-guest')
                ->whereRaw('LOWER(external_user_email) = ?', [(string)strtolower($email)])
                ->first();

            $passwordless_token = $this->customerGenerateEmailLink($external_guest);

            $viewParameters = [
                'token' => rawurlencode($passwordless_token->token),
                'liberty_representative' => $external_guest->external_user,
                'business' => $business,
            ];

            $subject = 'Here is your magic link';
            $recipientEmail = $external_guest->external_user_email;
            $recipientName = $external_guest->external_user;
            $viewParameters['user_type'] = 'customers';
            $viewParameters['is_customer'] = true;
            $viewParameters['title'] = 'Here is your magic link';
            $viewParameters['message'] =
                "<h1 style='color: #1e325f; font-family: Arial, Helvetica, sans-serif;'>Dear " . $recipientName . ",</h1>
                <p style='font-size: 16px; color: #5c6064; line-height: 22px; text-align: left; font-family: Arial, Helvetica, sans-serif; margin: 20px 0;' align='left'>
                    To access your Virtual Rooms for customers dashboard, please click the button below.
                    <br/>
                    <br/>
                    Access Virtual Rooms dashboard
                </p>";

            //Send Email
            $this->mail->queue(
                $recipientEmail,
                $recipientName,
                $subject,
                "emails.lets-talk-generic.passwordless-notification",
                $viewParameters,
                null
            );

            return Response::json(
                [
                'response' => 'success',
                'count' => ($external_guest ? 1 : 0),
                ]
            );
        }

        // Log all failed attempt
        $this->failedAttemptLog($email);

        return Response::json(
            [
            'response' => 'fail',
            ]
        );
    }

    private function customerGenerateEmailLink($external_guest)
    {
        $token = md5(Hash::make(substr(Str::uuid()->toString(), 0, 4)));
        $liberty_user_id = 0;
        $token_expiry = Carbon::now()->addHours(24);

        $passwordless_token = PasswordlessToken::create(
            [
            'liberty_user_id' => $liberty_user_id,
            'person_id' => $external_guest->person_id,
            'token' => $token,
            'token_expiry' => $token_expiry,
            'customer_email' => $external_guest->external_user_email,
            ]
        );

        return $passwordless_token;
    }

    public function getLoginAttemptLogs()
    {
        $logs = PasswordlessLog::get();
        return Response::json(
            [
            'response' => $logs,
            ]
        );
    }

    public function deleteLoginAttemptLog(Request $request)
    {
        $data = $request->all();
        PasswordlessLog::where('id', $data['delete_id'])
            ->delete();
    }
}