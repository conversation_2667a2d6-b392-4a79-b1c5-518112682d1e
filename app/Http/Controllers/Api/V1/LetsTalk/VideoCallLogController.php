<?php


namespace App\Http\Controllers\Api\V1\LetsTalk;

use Illuminate\Http\Request;
use App\Models\LetsTalk\VideoLog;
use Illuminate\Support\Facades\Log;
use App\Models\LetsTalk\VideoLogData;
use App\Models\LetsTalk\VideoCallRoom;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use App\Models\LetsTalk\VideoCallParticipant;

class VideoCallLogController extends BaseController
{
    public function log(Request $request)
    {
        $data = $request->all();

        if (isset($data['SmsSid'])) {
            $log = VideoLogData::getModel(VideoLog::TYPE_SMS, $data['SmsSid']);

            $participant = VideoCallParticipant::where('mobile_number', $data['To'])
                ->where('room_id', $log->room_id)
                ->first();

            VideoLogData::create(
                [
                    'log_id' => $log->_id,
                    'sms_id' => $data['SmsSid'],
                    'human_readable' => 'Message has been ' . $data['MessageStatus'] . ' to ' . $participant->user_name,
                    'data' => $data,
                ]
            );
        } else {
            $log = VideoLogData::getModel(VideoLog::TYPE_CALL, $data['RoomSid']);

            /**
             * @var VideoCallRoom $room 
             */
            $room = VideoCallRoom::where('name', $data['RoomName'])->first();

            if (!$log) {
                if (!$room) {
                    Log::error('Cannot create log for ' . $data['RoomName'] . ' with RoomSid="' . $data['RoomSid'] . '"');
                    Log::error($data);
                }
                if (isset($room->_id)) {
                    $log = VideoLog::create(
                        [
                            'type' => VideoLog::TYPE_CALL,
                            'room_sid' => $data['RoomSid'],
                            'room_id' => $room->_id,
                        ]
                    );
                }
            }

            if (isset($data['TrackKind']) && $data['TrackKind'] == "video") {
                $numberOfActiveStreams = isset($room['active_video_streams'])
                    ? $room['active_video_streams']
                    : 0;
                if ($data['StatusCallbackEvent'] == "track-removed" && $numberOfActiveStreams != 0) {
                    $numberOfActiveStreams = (int)$numberOfActiveStreams - 1;
                } elseif ($data['StatusCallbackEvent'] == "track-added") {
                    $numberOfActiveStreams = (int)$numberOfActiveStreams + 1;
                }
                VideoCallRoom::where(
                    'name',
                    $data['RoomName']
                )->update(['active_video_streams' => $numberOfActiveStreams]);
            }

            if ($data['StatusCallbackEvent'] == "participant-connected") {
                $numberOfActiveParticipants = isset($room['active_participants'])
                    ? $room['active_participants']
                    : 0;

                $numberOfActiveParticipants = (int)$numberOfActiveParticipants + 1;

                VideoCallRoom::where(
                    'name',
                    $data['RoomName']
                )->update(['active_participants' => $numberOfActiveParticipants]);
            }

            if ($data['StatusCallbackEvent'] == "participant-disconnected") {

                $numberOfActiveParticipants = isset($room['active_participants'])
                    ? $room['active_participants']
                    : 0;
                $numberOfActiveStreams = isset($room['active_video_streams'])
                    ? $room['active_video_streams']
                    : 0;

                if ($room['active_participants'] != 0) {
                    $numberOfActiveParticipants = (int)$numberOfActiveParticipants - 1;
                }

                if ($numberOfActiveStreams != 0) {
                    $numberOfActiveStreams = (int)$numberOfActiveStreams - 1;
                }

                VideoCallRoom::where('name', $data['RoomName'])->update(
                    [
                        'active_participants' => $numberOfActiveParticipants,
                        'active_video_streams' => $numberOfActiveStreams,
                    ]
                );
            }


            if (isset($data['ParticipantIdentity'])) {
                $participantIdentity = $data['ParticipantIdentity'];
            }
            $humanReadableLog = '';
            switch ($data['StatusCallbackEvent']) {
                case 'room-created':
                    $humanReadableLog = 'Room has been created.';
                    VideoCallRoom::where('name', $data['RoomName'])->update(
                        [
                            'active_participants' => 0,
                            'active_video_streams' => 0,
                        ]
                    );
                    $room->roomCreated();
                    break;
                case 'participant-connected':
                    $humanReadableLog = "$participantIdentity has entered the room.";
                    break;
                case 'participant-disconnected':
                    $humanReadableLog = $participantIdentity . ' has disconnected to the call after ' . $data['ParticipantDuration'] . ' seconds.';
                    break;
                case 'recording-started':
                    $humanReadableLog = $participantIdentity . ' has started the recording of the call.';
                    break;
                case 'recording-completed':
                    $humanReadableLog = $participantIdentity . ' has finished recording the call.';
                    break;
                case 'recording-failed':
                    $humanReadableLog = "Recording initiated by $participantIdentity has failed.";
                    break;
                case 'track-enabled':
                case 'track-added':
                    $humanReadableLog = $participantIdentity . ' has enabled the ' . $data['TrackKind'] . '.';
                    break;
                case 'track-disabled':
                case 'track-removed':
                    $humanReadableLog = $participantIdentity . ' has disabled the ' . $data['TrackKind'] . '.';
                    break;
                case 'room-ended':
                    $humanReadableLog = 'All participants has left the room after ' . $data['RoomDuration'] . ' seconds.';
                    VideoCallRoom::where('name', $data['RoomName'])->update(
                        [
                            'active_participants' => 0,
                            'active_video_streams' => 0,
                        ]
                    );
                    $room->roomCompleted();
                    break;
            }

            VideoLogData::create(
                [
                    'log_id' => $log->_id,
                    'room_sid' => $data['RoomSid'],
                    'human_readable' => $humanReadableLog,
                    'sequence_number' => $data['SequenceNumber'],
                    'data' => $data,
                ]
            );
        }
    }

    public function downloadLog(Request $request)
    {
        $roomCode = $request->get('code');

        $room = VideoCallRoom::where('name', $roomCode)
            ->with(['logs', 'logs.logData'])
            ->first();
        return Response::json(
            [
                'response' => 'success',
                'data' => ['rooms' => $room],
            ]
        );
    }
}
