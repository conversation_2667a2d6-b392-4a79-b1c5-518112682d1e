<?php


namespace App\Http\Controllers\Api\V1\LetsTalk;

use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\LetsTalk\SocialRoom;
use Illuminate\Support\Facades\Hash;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use App\Models\LetsTalk\UpcomingSchedule;
use Illuminate\Support\Facades\Validator;
use App\Models\LetsTalk\SocialRoomSchedule;
use Illuminate\Database\Eloquent\Collection;
use App\Models\LetsTalk\SocialRoomParticipant;
use App\Models\LetsTalk\CmsLibertyRepresentative;
use App\Models\LetsTalk\SocialRoomScheduleUpdate;
use App\Models\LetsTalk\SocialRoomSmsNotification;
use App\Models\LetsTalk\SocialRoomCancelledSchedule;
use App\Models\LetsTalk\SocialRoomEmailNotification;
use Illuminate\Validation\Validator as ValidatorType;

class SocialCatchUpController extends BaseController
{
    private $business;

    public function __construct(Request $request)
    {
        $this->business = ($request->has('business') && $request->get('business') === 'lmre')
            ? 'lmre'
            : 'lsm';

        $this->libReps = CmsLibertyRepresentative::fetchAllFromCacheOrCms();

        $personId = $request->get('person_id');
        $this->authUserCms = $this->libReps[$personId] ?? [];
        $this->authUserTz = $this->authUserCms['office_timezone'] ?? 'Europe/London';
    }

    public function index()
    {
    }

    public function getRepresentatives(Request $request)
    {
        $libReps = $this->libReps;
        if ($request->get('person_id')) {
            unset($libReps[$request->get('person_id')]);
        }
        return Response::json(
            [
                'response' => 'success',
                'data' => array_values($libReps),
            ]
        );
    }

    public function store(Request $request)
    {
        $data = $request->all();

        $libReps = $this->libReps;

        $data['lt_social_room_type_id'] = 4; //hardocded for now

        $errorResponse = $this->validateStore($data, $libReps);
        if ($errorResponse instanceof JsonResponse) {
            return $errorResponse;
        }

        $libRepCreator = $libReps[$data['created_by_id']];

        // Convert start_date & end_date to London tz from creator tz
        $data['start_date'] = Carbon::parse(
            $data['start_date'],
            $libRepCreator['office_timezone']
        )->setTimezone("Europe/London")->format('Y-m-d H:i:s');
        $data['end_date'] = Carbon::parse(
            $data['end_date'],
            $libRepCreator['office_timezone']
        )->setTimezone("Europe/London")->format('Y-m-d H:i:s');

        $libRepParticipant = $libReps[$data['person_id']];
        // $data['name'] = '1:1 Catchup with ' . $libRepParticipant['name'];
        $data['valid_until'] = SocialRoomSchedule::getValidUntil($data);
        // $data['room_code'] = md5($data['name']);
        $data['room_code'] = SocialRoom::roomCodeGenerator();
        $data['status'] = SocialRoom::STATUS_APPROVED;
        $data['end_after'] = $data['end_after_frequency'] . ' ' . $data['end_after_frequency_type'];

        $socialRoom = SocialRoom::create($data);
        $social_room_id = $socialRoom->id;

        $socialRoomSchedule = new SocialRoomSchedule($data);
        $socialRoom->socialRoomSchedule()->save($socialRoomSchedule);

        SocialRoomScheduleUpdate::create(
            [
                'room_id' => $social_room_id,
                'room_type' => $data['lt_social_room_type_id'],
                'room_code' => $data['room_code'],
            ]
        );

        $libRepCreator['person_id'] = $data['created_by_id'];
        $libRepParticipant['person_id'] = $data['person_id'];
        $participants = [
            $libRepCreator,
            $libRepParticipant,
        ];
        // dd($participants);
        foreach ($participants as $participant) {
            $participant['person_id'] = $participant['person_id'];
            $participant['user_name'] = $participant['name'];
            $participant['user_code'] = md5(Hash::make(substr(Str::uuid()->toString(), 0, 4)));
            $participant['role'] = $data['role'];

            if ($libRepCreator['person_id'] === $participant['person_id']) {
                $libRepCreator['user_code'] = $participant['user_code'];
                $libRepCreator['user_name'] = $participant['user_name'];
            }
            if ($libRepParticipant['person_id'] === $participant['person_id']) {
                $libRepParticipant['user_code'] = $participant['user_code'];
                $libRepParticipant['user_name'] = $participant['user_name'];
            }

            $socialRoomParticipant = new SocialRoomParticipant($participant);
            $socialRoom->socialRoomParticipants()->save($socialRoomParticipant);
        }

        // return Response::json([
        //     'room' => $socialRoom,
        //     'schedule' => $socialRoomSchedule,
        //     'participant' => $libRepParticipant,
        //     'creator' => $libRepCreator
        // ]);

        // send email notification here
        //$getSocialRoomParticipants = SocialRoomParticipant::where('lt_social_room_id', $socialRoom->id)->get();
        $getSocialRoomParticipants = SocialRoomParticipant::where('lt_social_room_id', $social_room_id)->get();

        $finalSocialRoom = SocialRoom::where('id', $social_room_id)->first();

        $room_participants = [];
        foreach ($getSocialRoomParticipants as $key => $individualParticipant) {
            $room_participants[$key] = $this->libReps[$individualParticipant->person_id];
            $room_participants[$key]['user_code'] = $individualParticipant->user_code;
            $room_participants[$key]['user_name'] = $individualParticipant->user_name;
        }

        if (!$socialRoomSchedule->isPermanent()) {
            $sendSms = new SocialRoomSmsNotification;
            $sendSms->sendSmsTemplate1($socialRoom, $libRepCreator, $room_participants, [], $socialRoomSchedule);
        }

        $personId = $data['created_by_id'];
        $roomCode = $data['room_code'];
        $socialRooms = SocialRoom::myCatchups($personId)
            ->with('socialRoomSchedule')
            ->with(['socialRoomParticipants'])
            ->with('deletedScheduleEntries')
            ->where('room_code', $roomCode)
            ->get();

        if (isset($socialRooms->first()->socialRoomSchedule) && $socialRooms->first()->socialRoomSchedule) {
            $socialRooms = SocialRoom::setCmsData($socialRooms, false, 'socialRoomParticipants');
            $tmpData = UpcomingSchedule::generateWithBuffer($socialRooms, $this->authUserCms);
            $upcomingSchedules = $tmpData['upcomingSchedules'];
        } else {
            $upcomingSchedules = [];
        }


        if (!$socialRoomSchedule->isPermanent()) {
            SocialRoomEmailNotification::sendTemplate1(
                [
                    'room' => $finalSocialRoom,
                    'schedule' => $socialRoomSchedule,
                    'participant' => $libRepParticipant,
                    'creator' => $libRepCreator,
                    'frequency_info' => $data,
                    'upcoming_schedules' => $upcomingSchedules,
                    'externalParticipants' => [],
                ]
            );
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => [
                    'room' => $finalSocialRoom,
                    'schedule' => $socialRoomSchedule,
                    'participant' => $libRepParticipant,
                    'creator' => $libRepCreator,
                ],
                'message' => 'Room has been successfully created.',
            ]
        );
    }

    private function validateStore($data, $libReps)
    {
        $validator = $this->_validate($data, 'store');

        if ($validator->fails()) {
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        }

        if ($data['created_by_id'] && !isset($libReps[$data['created_by_id']])) {
            $validator->errors()->add('created_by_id', 'Created by id does not exists.');
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        }

        if (isset($data['person_id']) && !isset($libReps[$data['person_id']])) {
            $validator->errors()->add('person_id', 'Participant does not exists.');
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        }

        if (isset($errorResponse)) {
            return Response::json($errorResponse);
        }

        return [];
    }

    private function _validate($data, $action): ValidatorType
    {
        $rules = [
            'name' => 'required',
            'created_by_id' => 'required',
            'person_id' => 'required',
            'duration_type' => 'required',
            'frequency' => 'required|integer',
            'frequency_type' => 'required',
            'start_date' => 'required',
            'end_date' => 'required',
        ];

        // // On update, we don't want to validate email, cause it cannot be updated
        // if($action === 'update') {
        //     unset($rules['email']);
        // }

        // TODO: Add appropriate error messages here
        // TODO: Add appropriate error messages here
        // TODO: Add appropriate error messages here
        $messages = [
            // 'name.required' => 'Please enter Name.',
            // 'email.required' => 'Please enter Email.',
            // 'mobile_number.required' => 'Please enter Mobile Phone in a valid format.',
            // 'mobile_number.regex' => 'Please enter Mobile Phone in a valid format.',
        ];

        return Validator::make($data, $rules, $messages);
    }

    public function show(Request $request, $roomCode)
    {
        $personId = $request->get('person_id');
        $userCode = $request->get('user_code');

        // Set person id based on user code
        if (!empty($userCode) && empty($personId)) {
            $participant = SocialRoomParticipant::where('user_code', $userCode)->first();
            $personId = $participant->person_id;
        }

        $libReps = $this->libReps;

        $socialRooms = SocialRoom::myCatchups($personId)
            ->with('socialRoomSchedule')
            ->with('deletedScheduleEntries')
            ->with('modifiedScheduleEntries')
            ->where('room_code', $roomCode)
            ->get();


        if ($socialRooms->isEmpty()) {
            return Response::json(
                [
                    'response' => 'fail',
                    'message' => 'Room not found.',
                ],
                404
            );
        }

        $joinRoomLink = '';
        if ($socialRooms->first()) {
            $participant = SocialRoomParticipant::where('lt_social_room_id', $socialRooms->first()->id)
                ->where('person_id', $personId)->first();
            //$joinRoomLink = SocialRoomParticipant::generateLink($participant->user_code);
            $joinRoomLink = SocialRoomParticipant::generateWaitingRoomCommonLink($roomCode);
        }

        $socialRooms = SocialRoom::setCmsData($socialRooms, false, 'socialRoomParticipants');
        if (isset($socialRooms->first()->socialRoomSchedule) && $socialRooms->first()->socialRoomSchedule) {
            $tmpData = UpcomingSchedule::generateWithBuffer($socialRooms, $this->authUserCms);
            $upcomingSchedules = $tmpData['upcomingSchedules'];
        } else {
            $upcomingSchedules = [];
        }

        $socialRooms->first()->socialRoomParticipants->push($participant);
        $socialRooms = SocialRoom::setCmsData($socialRooms, false, 'socialRoomParticipants');

        if (empty($socialRooms->first())) {
            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'Social room not found!',
                ]
            );
        } else {
            return Response::json(
                [
                    'response' => 'success',
                    'data' => [
                        'room' => $socialRooms->first(),
                        'upcoming_schedules' => $upcomingSchedules,
                        'join_room_link' => $joinRoomLink,
                    ],
                ]
            );
        }
    }

    public function deleteSpace($socialRoomId)
    {
        $socialRoom = SocialRoom::with(['socialRoomParticipants', 'socialRoomSchedule'])->find($socialRoomId);
        if (!$socialRoom) {
            return Response::json(['response' => 'error', 'message' => 'Unable to delete. Space not found.'], 400);
        }

        $socialRoom = SocialRoom::setCmsData(new Collection([$socialRoom]), false, 'socialRoomParticipants')->first();

        $upcomingSchedules = [];
        $socialRoom->upcoming_schedules_count = count($upcomingSchedules);
        if ($socialRoom->socialRoomSchedule) {
            $socialRooms = new Collection([$socialRoom]);
            $tmpData = UpcomingSchedule::generateWithBuffer($socialRooms, $this->authUserCms);
            $upcomingSchedules = $tmpData['upcomingSchedules'];
            if (is_object($upcomingSchedules)) {
                $socialRoom->upcoming_schedules_count = $upcomingSchedules->count();
            } else {
                $socialRoom->upcoming_schedules_count = count($upcomingSchedules);
            }
        }

        $creator = $this->libReps[$socialRoom->created_by_id];

        // Only send notification if has upcoming schedule
        if (
            isset($socialRoom->socialRoomSchedule)
            && !empty($socialRoom->socialRoomSchedule)
            && !empty($upcomingSchedules)
            && !$socialRoom->socialRoomSchedule->isPermanent()
        ) {
            SocialRoomEmailNotification::catchUpTemplate3($socialRoom->toArray(), $creator);
        }

        $socialRoom->delete();

        return Response::json(['response' => 'success', 'message' => 'Space deleted.']);
    }

    public function deleteSchedule(Request $request)
    {
        $socialRoomId = $request->get('lt_social_room_id');
        $scheduleId = $request->get('schedule_id');
        $scheduleDate = $request->get('date');

        $socialRoom = $socialRoom = SocialRoom::with(
            [
                'socialRoomParticipants',
                'socialRoomSchedule',
            ]
        )->find($socialRoomId);

        if (!$socialRoom) {
            return Response::json(['response' => 'error', 'message' => 'Unable to delete. Space not found.'], 400);
        }

        $socialRoom = SocialRoom::setCmsData(new Collection([$socialRoom]), false, 'socialRoomParticipants')->first();
        $creator = $this->libReps[$socialRoom->created_by_id];
        $upcomingSchedules = [];
        $socialRoom->upcoming_schedules_count = count($upcomingSchedules);
        if ($socialRoom->socialRoomSchedule) {
            $socialRooms = new Collection([$socialRoom]);
            $tmpData = UpcomingSchedule::generateWithBuffer($socialRooms, $this->authUserCms);
            $upcomingSchedules = $tmpData['upcomingSchedules'];
            if (is_object($upcomingSchedules)) {
                $socialRoom->upcoming_schedules_count = $upcomingSchedules->count();
            } else {
                $socialRoom->upcoming_schedules_count = count($upcomingSchedules);
            }
        }

        // If schedule date not provided, we will delete the whole schedule
        if (empty($scheduleDate)) {
            $socialRoomSchedule = SocialRoomSchedule::find($scheduleId);
            if (!$socialRoomSchedule) {
                return Response::json(['response' => 'error', 'message' => 'Unable to delete. Space not found.'], 400);
            }

            $socialRoom = SocialRoom::setCmsData(
                new Collection([$socialRoom]),
                false,
                'socialRoomParticipants'
            )->first();

            if (!$socialRoomSchedule->isPermanent()) {
                SocialRoomEmailNotification::catchUpTemplate3($socialRoom->toArray(), $creator);
            }


            $socialRoomSchedule->delete();

            $newRoomCode = SocialRoom::roomCodeGenerator();

            $socialRoom->room_code = $newRoomCode;

            $updateRoomCode = SocialRoom::where('id', '=', $socialRoomId)->update(['room_code' => $newRoomCode]);

            $deleteDeletedSchedules = SocialRoomCancelledSchedule::where(
                'lt_social_room_id',
                '=',
                $socialRoomId
            )->delete();

            return Response::json(
                [
                    'response' => 'success',
                    'message' => 'Space schedule deleted.',
                    'room' => $socialRoom,
                ]
            );
        }

        // Convert deleted schedule to London before saving, it's from user tz
        $scheduleDateCarbon = Carbon::parse($scheduleDate, $this->authUserTz)->setTimezone("Europe/London");
        $socialRoomCancelledSchedule = SocialRoomCancelledSchedule::create(
            [
                'lt_social_room_id' => $socialRoomId,
                'lt_social_room_schedule_id' => $scheduleId,
                'date' => $scheduleDateCarbon,
            ]
        );

        SocialRoomEmailNotification::catchUpTemplate3($socialRoom->toArray(), $creator, $scheduleDateCarbon);

        return Response::json(['response' => 'success', 'message' => 'Space schedule deleted.', 'room' => $socialRoom]);
    }

    public function update($id)
    {
    }

    public function SMSCron()
    {
        $schedules = $this->getInstantScheduleToSendSMS();
        foreach ($schedules as $key => $schedule) {
            $socialRoom = $schedule;
            $libRepParticipants = $schedule->socialRoomParticipants;
            $socialRoomSchedule = $schedule->socialRoomSchedule;
            $sendSms = new SocialRoomSmsNotification;
            if ($schedule->lt_social_room_type_id == 4) {
                $sendSms->sendSmsTemplate2(
                    $socialRoom,
                    $libRepParticipants,
                    $socialRoomSchedule,
                    $schedule->lt_social_room_type_id == 5
                );
            }
        }
    }

    // Get list of colleagues the person_id has catchups with

    public function getInstantScheduleToSendSMS()
    {
        $minsBefore = -5; //5 mins before
        $libReps = $this->libReps;
        $carbonNow = Carbon::now();

        //Get All Schedules with valid date before 5 mins
        $socialRooms = SocialRoom::whereIn('lt_social_room_type_id', [4, 5])
            ->with('socialRoomSchedule')
            ->whereHas(
                'socialRoomSchedule',
                function ($query) use ($carbonNow) {
                    $query->where('valid_until', '>=', $carbonNow)
                        ->where('start_date', '<=', $carbonNow);
                }
            )
            ->with('socialRoomParticipants')
            ->get();

        $SmsReady = [];

        //Filter schedules

        foreach ($socialRooms as $key => $socialRoom) {
            $start_date = Carbon::Parse($socialRoom->socialRoomSchedule->start_date);
            $scheduledDayofWeek = $start_date->dayOfWeek;

            if ($start_date >= $carbonNow->addMinutes($minsBefore)) {
                $schedule = $socialRoom->socialRoomSchedule;
                switch ($schedule->frequency_type) {
                    case 'day': //every day
                        $SmsReady[] = $socialRoom;
                        break;
                    case 'week':
                        if ($carbonNow->dayOfWeek == $scheduledDayofWeek) {
                            if (($schedule->frequency == 1) || ($schedule->frequency == 2 && ($carbonNow->weekOfMonth % 2 == 1))) {
                                $SmsReady[] = $socialRoom;
                            }
                        }
                        break;
                    case 'month':
                        if ($schedule->frequency == 1 && $carbonNow->weekOfMonth == 1 && $carbonNow->dayOfWeek == $scheduledDayofWeek) {
                            $SmsReady[] = $socialRoom;
                        }
                        break;
                    case 'quarter':
                        if ($schedule->frequency == 1 && $carbonNow->weekOfMonth == 1 && $carbonNow->dayOfWeek == $scheduledDayofWeek && $carbonNow->month % 3 == 0) {
                            $SmsReady[] = $socialRoom;
                        }
                        break;
                    default:
                        break;
                }
            }
        }
        return $SmsReady;
    }

    // validate store method

    public function getUpcomingCatchups(Request $request)
    {
        $personId = $request->get('person_id'); //creator
        $carbonNow = Carbon::now();
        $query = SocialRoom::myCatchups($personId)
            ->with('socialRoomSchedule')
            ->with('deletedScheduleEntries')
            ->with('modifiedScheduleEntries')
            ->has('socialRoomSchedule');

        $withPersonCms = [];
        if ($request->has('with_person_id')) {
            $withPersonId = $request->get('with_person_id');
            $query->whereHas(
                'socialRoomParticipants',
                function ($query) use ($personId, $withPersonId) {
                    $query->where('person_id', '=', $withPersonId);
                }
            );

            $libReps = CmsLibertyRepresentative::fetchAllFromCacheOrCms();
            if (isset($libReps[$withPersonId])) {
                $withPersonCms = $libReps[$withPersonId];
            }
        }

        $socialRooms = $query->get();

        $socialRooms = SocialRoom::setCmsData($socialRooms, false, 'socialRoomParticipants');

        $permanentRooms = new Collection([]);
        $nonPermanentRooms = new Collection([]);
        $socialRooms->each(
            function ($room) use ($permanentRooms, $nonPermanentRooms) {
                $room->room = $room->room_code;
                if ($room->socialRoomSchedule->isPermanent()) {
                    $room->is_permanent = true;
                    if (isset($room->cms['user_name'])) {
                        $permanentRooms->push($room);
                    }
                } else {
                    $nonPermanentRooms->push($room);
                }
            }
        );

        if ($request->has('with_person_id')) {
            $tmpData = UpcomingSchedule::generateNoLimitOnTotal(
                $nonPermanentRooms,
                $this->authUserCms,
                $buffer = 0,
                $maxSchedPerRoom = 1
            );
        } else {
            $tmpData = UpcomingSchedule::generate($nonPermanentRooms, $this->authUserCms);
        }

        $upcomingSchedules = $tmpData['upcomingSchedules'];
        $rooms = $tmpData['rooms'];

        return Response::json(
            [
                'permanent_rooms' => $permanentRooms,
                'response' => 'success',
                'data' => $upcomingSchedules,
                'rooms' => $rooms,
                'with_person_cms' => $withPersonCms,
            ]
        );
    }

    public function getColleagues(Request $request)
    {
        $personId = $request->get('person_id'); //creator
        $socialRooms = SocialRoom::myCatchups($personId)
            ->orderBy('name')->get();

        $socialRooms = SocialRoom::setCmsData($socialRooms, false, 'socialRoomParticipants');

        return Response::json(
            [
                'response' => 'success',
                'data' => $socialRooms,
            ]
        );
    }
}
