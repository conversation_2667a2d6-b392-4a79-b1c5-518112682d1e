<?php

namespace App\Http\Controllers\Api\V1\LetsTalk;

use App\Http\Controllers\BaseController;
use App\Models\FileUpload;
use Illuminate\Support\Facades\Cache;
use App\Models\LetsTalk\CmsLibertyRepresentative;
use App\Models\LetsTalk\SocialRoomPin;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\Request;
use App\Models\Cms;
use Illuminate\Support\Arr;

class AnnouncementController extends BaseController
{
    private $files;

    public function __construct(FileUpload $fileupload)
    {
        $this->files = $fileupload;
    }

    public function getAll(Request $request)
    {
        $input = $request->all();

        $promoted = null;

        $personId = $input['person_id'];
        $page = isset($input['page'])
            ? intval($input['page'])
            : 1;
        $limit = isset($input['limit'])
            ? intval($input['limit'])
            : 99999;

        $letsTalkWorkspace = config('app.cms.lsm.lets_talk_workspace');
        $announcementsContentType = config('app.cms.lsm.announcements_content_type');

        $cached = isset($input['type']) && $input['type'] == 'cached'
            ? "yes"
            : "no";

        $categories = $this->getPersonCategories($personId, $cached);

        $filteredAnnouncements = [];

        $force_recache = $cached === "no";
        $announcements = $this->fetchAllAnnouncementsFromCacheOrCms($force_recache);

        $filteredAnnouncements = array_values(
            array_filter(
                $announcements, function ($announcement) use ($categories) {
                    return in_array($announcement->{config('app.cms.lsm.announcement.category')}[0]->name, $categories);
                }
            )
        );

        usort(
            $filteredAnnouncements, function ($a, $b) {
                $a = date('Y-m-d', strtotime($a->{config('app.cms.lsm.announcement.publish_date')}));
                $b = date('Y-m-d', strtotime($b->{config('app.cms.lsm.announcement.publish_date')}));

                if ($a == $b) {
                    return 0;
                }
                return ($a > $b)
                ? -1
                : 1;
            }
        );

        if ($request->has('promoted')) {
            $found = Arr::first(
                $filteredAnnouncements, function ($announcement, $key) {
                    return isset($announcement->{config('app.cms.lsm.announcement.super_promotion')}[0]);
                }
            );
            if ($found) {
                $promoted = $found;
            }
        }

        $pagedAnnouncements = array_chunk($filteredAnnouncements, $limit, true);
        $totalPages = count($pagedAnnouncements);
        $pageIndex = $page - 1 < 0
            ? 0
            : $page - 1;
        $result = isset($pagedAnnouncements[$pageIndex])
            ? $pagedAnnouncements[$pageIndex]
            : $pagedAnnouncements[0];

        $result = array_map(
            function ($announcement) {
                if (isset($announcement->{config('app.cms.lsm.announcement.video_recording')}->video_type)) {
                    $bucket = $announcement->{config('app.cms.lsm.announcement.video_recording')}->video_type === 'record'
                    ? 'vr_bucket'
                    : 'cms_bucket';
                    $video = $announcement->{config('app.cms.lsm.announcement.video_recording')};
                    $announcement->{config('app.cms.lsm.announcement.video_recording')}->link = $this->files->link(
                        'video-record/' . $video->cloudname . '/' . $video->filename,
                        '10 minutes', $bucket
                    );
                }
                $this->replaceKeys($announcement);
                return $announcement;
            }, $result
        );


        return Response::json(
            [
            'status' => 'success',
            'total_pages' => $totalPages,
            'data' => $result,
            'promoted' => $promoted,
            ]
        );
    }

    private function getPersonCategories($id, $cached = "no")
    {
        $categories = [];

        if (Cache::has('globalCategoryNames') && $cached == "yes") {
            $globalCategoryNames = Cache::get('globalCategoryNames');
        } else {
            $letsTalkWorkspace = config('app.cms.lsm.lets_talk_workspace');
            $announcementCategoriesContentType = config('app.cms.lsm.announcement_categories_content_type');

            if (Cache::has('globalCategories') && $cached == "yes") {
                $globalCategories = Cache::get('globalCategories');
            } else {
                $globalCategories = json_decode(Cms::get('workspaces/' . $letsTalkWorkspace . '/content-types/' . $announcementCategoriesContentType . '/content-entries'))->data;
                Cache::forever('globalCategories', $globalCategories);
            }

            $filteredGlobalCategories = array_filter(
                $globalCategories, function ($category) {
                    return isset($category->{config('app.cms.lsm.announcement_category')}[0]);
                }
            );
            $globalCategoryNames = array_map(
                function ($category) {
                    return $category->name;
                }, $filteredGlobalCategories
            );
            Cache::forever('globalCategoryNames', $globalCategoryNames);
        }

        $categories = array_merge($categories, $globalCategoryNames);

        $pins = SocialRoomPin::where('person_id', $id)
            ->with('socialRoom')
            ->whereHas(
                'socialRoom', function ($query) {
                    $query->where('is_community', 1);
                }
            )
            ->get();

        $pinnedRoomNames = array_map(
            function ($pin) {
                return $pin->social_room->name;
            }, json_decode($pins)
        );

        $categories = array_merge($categories, $pinnedRoomNames);

        return $categories;
    }

    public function fetchAllAnnouncementsFromCacheOrCms($force_recache = false)
    {
        if (Cache::has('social_room_announcements') && !$force_recache) {
            return Cache::get('social_room_announcements');
        }

        $letsTalkWorkspace = config('app.cms.lsm.lets_talk_workspace');
        $announcementsContentType = config('app.cms.lsm.announcements_content_type');

        $query = json_encode(
            [
            'order' => [
                config('app.cms.lsm.announcement.publish_date') => 'desc',
            ],
            'status' => 'publish',
            'operator' => '=',
            ]
        );

        $announcements = json_decode(Cms::get('workspaces/' . $letsTalkWorkspace . '/content-types/' . $announcementsContentType . '/content-entries?query=' . $query))->data;

        Cache::put('social_room_announcements', $announcements, 1440);

        return $announcements;
    }

    private function replaceKeys($announcement)
    {
        $announcement->title = $announcement->{config('app.cms.lsm.announcement.title')};
        unset($announcement->{config('app.cms.lsm.announcement.title')});

        $announcement->category = $announcement->{config('app.cms.lsm.announcement.category')};
        unset($announcement->{config('app.cms.lsm.announcement.category')});

        $announcement->publish_date = $announcement->{config('app.cms.lsm.announcement.publish_date')};
        unset($announcement->{config('app.cms.lsm.announcement.publish_date')});

        $announcement->author = $announcement->{config('app.cms.lsm.announcement.author')};
        unset($announcement->{config('app.cms.lsm.announcement.author')});

        $announcement->video_recording = isset($announcement->{config('app.cms.lsm.announcement.video_recording')})
            ? $announcement->{config('app.cms.lsm.announcement.video_recording')}
            : '';
        unset($announcement->{config('app.cms.lsm.announcement.video_recording')});

        $announcement->content = $announcement->{config('app.cms.lsm.announcement.content')};
        unset($announcement->{config('app.cms.lsm.announcement.content')});

        $announcement->super_promotion = $announcement->{config('app.cms.lsm.announcement.super_promotion')};
        unset($announcement->{config('app.cms.lsm.announcement.super_promotion')});

        $announcement->feature_image = $announcement->{config('app.cms.lsm.announcement.feature_image')};
        unset($announcement->{config('app.cms.lsm.announcement.feature_image')});

        if (isset($announcement->{config('app.cms.lsm.announcement.video_recording_thumbnail')}) 
            && !empty($announcement->{config('app.cms.lsm.announcement.video_recording_thumbnail')})
        ) {
            $announcement->video_recording_thumbnail = $announcement->{config('app.cms.lsm.announcement.video_recording_thumbnail')};
            unset($announcement->{config('app.cms.lsm.announcement.video_recording_thumbnail')});
        }

        $announcement->call_to_action = $announcement->{config('app.cms.lsm.announcement.call_to_action')};
        unset($announcement->{config('app.cms.lsm.announcement.call_to_action')});

        $announcement->button_label = $announcement->{config('app.cms.lsm.announcement.button_label')};
        unset($announcement->{config('app.cms.lsm.announcement.button_label')});

        $announcement->button_url = $announcement->{config('app.cms.lsm.announcement.button_url')};
        unset($announcement->{config('app.cms.lsm.announcement.button_url')});

        if (isset($announcement->{config('app.cms.lsm.announcement.button_action')}) 
            && !empty($announcement->{config('app.cms.lsm.announcement.button_action')})
        ) {
            $announcement->button_action = $announcement->{config('app.cms.lsm.announcement.button_action')};
            unset($announcement->{config('app.cms.lsm.announcement.button_action')});
        }
    }

    public function getAllAnnouncements(Request $request)
    {
        $input = $request->all();

        $promoted = null;

        $page = isset($input['page'])
            ? intval($input['page'])
            : 1;
        $limit = isset($input['limit'])
            ? intval($input['limit'])
            : 99999;

        $letsTalkWorkspace = config('app.cms.lsm.lets_talk_workspace');
        $announcementsContentType = config('app.cms.lsm.announcements_content_type');

        $cached = isset($input['type']) && $input['type'] == 'cached'
            ? "yes"
            : "no";

        $query = json_encode(
            [
            'order' => [
                config('app.cms.lsm.announcement.publish_date') => 'desc',
            ],
            'status' => 'publish',
            'operator' => '=',
            ]
        );

        if (Cache::has('social_room_announcements') && $cached === "yes") {
            $announcements = Cache::get('social_room_announcements');
        } else {
            $announcements = json_decode(Cms::get('workspaces/' . $letsTalkWorkspace . '/content-types/' . $announcementsContentType . '/content-entries?query=' . $query))->data;
            Cache::put('social_room_announcements', $announcements, 1440);
        }

        if ($request->has('promoted')) {
            $found = Arr::first(
                $announcements, function ($announcement, $key) {
                    return isset($announcement->{config('app.cms.lsm.announcement.super_promotion')}[0]);
                }
            );
            if ($found) {
                $promoted = $found;
            }
        }

        $pagedAnnouncements = array_chunk($announcements, $limit, true);
        $totalPages = count($pagedAnnouncements);
        $pageIndex = $page - 1 < 0
            ? 0
            : $page - 1;
        $result = isset($pagedAnnouncements[$pageIndex])
            ? $pagedAnnouncements[$pageIndex]
            : $pagedAnnouncements[0];

        $result = array_map(
            function ($announcement) {
                if (isset($announcement->{config('app.cms.lsm.announcement.video_recording')}->video_type)) {
                    $bucket = $announcement->{config('app.cms.lsm.announcement.video_recording')}->video_type === 'record'
                    ? 'vr_bucket'
                    : 'cms_bucket';
                    $video = $announcement->{config('app.cms.lsm.announcement.video_recording')};
                    $announcement->{config('app.cms.lsm.announcement.video_recording')}->link = $this->files->link(
                        'video-record/' . $video->cloudname . '/' . $video->filename,
                        '10 minutes', $bucket
                    );
                }
                $this->replaceKeys($announcement);
                return $announcement;
            }, $result
        );


        return Response::json(
            [
            'status' => 'success',
            'total_pages' => $totalPages,
            'data' => $result,
            'promoted' => $promoted,
            ]
        );
    }

    public function isEditor(Request $request)
    {
        $input = $request->all();

        $letsTalkWorkspace = config('app.cms.lsm.lets_talk_workspace');
        $lsmStaffContentType = config('app.cms.lsm.liberty_representatives_content_type');
        $announcementEditorsContentEntry = config('app.cms.lsm.announcement_editors_content_entry');

        $cms = CmsLibertyRepresentative::fetchAllFromCacheOrCms();
        $get_editors = json_decode(Cms::get('workspaces/' . $letsTalkWorkspace . '/content-types/' . $lsmStaffContentType . '/content-entries/' . $announcementEditorsContentEntry));
        if ($get_editors) {
            $editors_id = $get_editors->{config('app.cms.lsm.announcement_editors_representative')};
            $editor_details = [];
            $ctr = 0;
            foreach ($editors_id as $key => $value) {
                foreach ($value as $id) {
                    if (isset($cms[$id])) {
                        $editor_details[] = $cms[$id];
                        $editor_details[$ctr]['id'] = $id;
                        $ctr++;
                    }
                }
            }
        }

        $editorsEmail = array_map(
            function ($editor) {
                return $editor['email'];
            }, $editor_details
        );

        $isEditor = in_array($input['email'], $editorsEmail);

        if ($isEditor) {
            return Response::json(
                [
                'status' => 'success',
                'data' => [
                    'url' => 'https://www.libertycms.dev/workspaces/5eecb6d5476ea2243c5813b2/content-types/5fbe63a5476ea239d2545d82/content-entries/create',
                ],
                ]
            );
        } else {
            return Response::json(
                [
                'status' => 'success',
                'data' => [
                    'url' => null,
                ],
                ]
            );
        }
    }

    public function show(Request $request, $id)
    {
        $input = $request->all();
        $letsTalkWorkspace = config('app.cms.lsm.lets_talk_workspace');
        $announcementsContentType = config('app.cms.lsm.announcements_content_type');

        $force_recache = $request->has('force_recache');
        $cached = !$force_recache
            ? "yes"
            : "no";

        $announcements = $this->fetchAllAnnouncementsFromCacheOrCms($force_recache);
        $announcement = Arr::first(
            $announcements, function ($announcement, $key) use ($id) {
                return $announcement->_id === $id;
            }
        );

        if (isset($announcement->{config('app.cms.lsm.announcement.video_recording')}->video_type)) {
            $bucket = $announcement->{config('app.cms.lsm.announcement.video_recording')}->video_type === 'record'
                ? 'vr_bucket'
                : 'cms_bucket';
            $video = $announcement->{config('app.cms.lsm.announcement.video_recording')};
            $announcement->{config('app.cms.lsm.announcement.video_recording')}->link = $this->files->link(
                'video-record/' . $video->cloudname . '/' . $video->filename,
                '10 minutes', $bucket
            );
            $announcement->{config('app.cms.lsm.announcement.video_recording')}->poster = $this->files->link(
                'video-record/' . $video->cloudname . '/' . $video->filename . '-00001.png',
                '10 minutes', $bucket
            );
        }

        $promoted = null;
        // Only get super promoted announcement if the currently viewed announcement is not super promoted
        if (!isset($announcement->{config('app.cms.lsm.announcement.super_promotion')}[0])) {
            $categories = $this->getPersonCategories($input['person_id'], $cached);
            $promoted = $this->getPromoted($announcements, $categories);
        }

        $this->replaceKeys($announcement);

        return Response::json(
            [
            'status' => 'success',
            'data' => [
                'promoted' => !empty($promoted)
                    ? $promoted
                    : null,
                'announcement' => $announcement,
                'author' => $announcement->author,
            ],
            ]
        );
    }

    private function getPromoted($announcements, $categories)
    {
        $announcement = Arr::first(
            $announcements, function ($announcement, $key) use ($categories) {
                return isset($announcement->{config('app.cms.lsm.announcement.super_promotion')}[0])
                && in_array($announcement->{config('app.cms.lsm.announcement.category')}[0]->name, $categories);
            }
        );

        if (isset($announcement->{config('app.cms.lsm.announcement.video_recording')}->video_type)) {
            $bucket = $announcement->{config('app.cms.lsm.announcement.video_recording')}->video_type === 'record'
                ? 'vr_bucket'
                : 'cms_bucket';
            $video = $announcement->{config('app.cms.lsm.announcement.video_recording')};
            $announcement->{config('app.cms.lsm.announcement.video_recording')}->link = $this->files->link(
                'video-record/' . $video->cloudname . '/' . $video->filename,
                '10 minutes', $bucket
            );
            $announcement->{config('app.cms.lsm.announcement.video_recording')}->image = $this->files->link(
                'video-record/' . $video->cloudname . '/' . $video->filename . '-00001.png',
                '10 minutes', $bucket
            );
        }

        if ($announcement) {
            $this->replaceKeys($announcement);
        }
        return $announcement;
    }

    public function related(Request $request, $id)
    {
        $letsTalkWorkspace = config('app.cms.lsm.lets_talk_workspace');
        $announcementsContentType = config('app.cms.lsm.announcements_content_type');
        $announcementCategoriesContentType = config('app.cms.lsm.announcement_categories_content_type');

        $announcements = $this->fetchAllAnnouncementsFromCacheOrCms();
        $announcement = Arr::first(
            $announcements, function ($announcement, $key) use ($id) {
                return $announcement->_id === $id;
            }
        );
        $category = $announcement->{config('app.cms.lsm.announcement.category')}[0];
        $categoryName = $category->name;

        $promotedId = $request->has('promoted_id')
            ? $request->get('promoted_id')
            : null;
        $limit = $request->has('limit')
            ? $request->get('limit')
            : 5;
        $relatedAnnouncements = [];
        foreach ($announcements as $key => $announcement) {
            if (count($relatedAnnouncements) === (int)$limit) {
                break;
            }

            $relatedCategory = isset($announcement->{config('app.cms.lsm.announcement.category')}[0])
                ? $announcement->{config('app.cms.lsm.announcement.category')}[0]
                : [];

            if (empty($relatedCategory) || $announcement->_id === $promotedId) {
                continue;
            }

            if ($relatedCategory->name === $categoryName) {
                if (isset($announcement->{config('app.cms.lsm.announcement.video_recording')}->video_type)) {
                    $bucket = $announcement->{config('app.cms.lsm.announcement.video_recording')}->video_type === 'record'
                        ? 'vr_bucket'
                        : 'cms_bucket';
                    $video = $announcement->{config('app.cms.lsm.announcement.video_recording')};
                    $announcement->{config('app.cms.lsm.announcement.video_recording')}->link = $this->files->link(
                        'video-record/' . $video->cloudname . '/' . $video->filename,
                        '10 minutes', $bucket
                    );
                }
                $this->replaceKeys($announcement);
                $relatedAnnouncements[] = $announcement;
            }
        }

        return Response::json(
            [
            'status' => 'success',
            'count' => count($relatedAnnouncements),
            'data' => $relatedAnnouncements,
            ]
        );
    }

    public function promoted(Request $request)
    {
        $input = $request->all();

        $letsTalkWorkspace = config('app.cms.lsm.lets_talk_workspace');
        $announcementsContentType = config('app.cms.lsm.announcements_content_type');

        $cached = isset($input['type']) && $input['type'] == 'cached'
            ? "yes"
            : "no";

        $categories = $this->getPersonCategories($input['person_id'], $cached);

        $force_recache = $cached === "no";
        $announcements = $this->fetchAllAnnouncementsFromCacheOrCms($force_recache);

        $announcement = Arr::first(
            $announcements, function ($announcement, $key) use ($categories) {
                return isset($announcement->{config('app.cms.lsm.announcement.super_promotion')}[0])
                && in_array($announcement->{config('app.cms.lsm.announcement.category')}[0]->name, $categories);
            }
        );


        // $filteredAnnouncements = array_values(array_filter($announcements, function ($announcement) use ($categories) {
        //     return in_array($announcement->{config('app.cms.lsm.announcement.category')}[0]->name, $categories);
        // }));
        // $announcement = current(array_filter($filteredAnnouncements, function ($announcement) {
        //     return isset($announcement->{config('app.cms.lsm.announcement.super_promotion')}[0]);
        // }));

        if (isset($announcement->{config('app.cms.lsm.announcement.video_recording')}->video_type)) {
            $bucket = $announcement->{config('app.cms.lsm.announcement.video_recording')}->video_type === 'record'
                ? 'vr_bucket'
                : 'cms_bucket';
            $video = $announcement->{config('app.cms.lsm.announcement.video_recording')};
            $announcement->{config('app.cms.lsm.announcement.video_recording')}->link = $this->files->link(
                'video-record/' . $video->cloudname . '/' . $video->filename,
                '10 minutes', $bucket
            );
            $announcement->{config('app.cms.lsm.announcement.video_recording')}->image = $this->files->link(
                'video-record/' . $video->cloudname . '/' . $video->filename . '-00001.png',
                '10 minutes', $bucket
            );

        }

        if ($announcement) {
            $this->replaceKeys($announcement);
            return Response::json(
                [
                'status' => 'success',
                'data' => $announcement,
                ]
            );
        } else {
            return Response::json(
                [
                'status' => 'success',
                'data' => null,
                ]
            );
        }
    }

    public function category($id)
    {
        $letsTalkWorkspace = config('app.cms.lsm.lets_talk_workspace');
        $announcementCategoriesContentType = config('app.cms.lsm.announcement_categories_content_type');

        $category = json_decode(Cms::get('workspaces/' . $letsTalkWorkspace . '/content-types/' . $announcementCategoriesContentType . '/content-entries/' . $id));

        return Response::json(
            [
            'status' => 'success',
            'data' => $category,
            ]
        );
    }

    public function categories()
    {
        $letsTalkWorkspace = config('app.cms.lsm.lets_talk_workspace');
        $announcementCategoriesContentType = config('app.cms.lsm.announcement_categories_content_type');

        $categories = json_decode(Cms::get('workspaces/' . $letsTalkWorkspace . '/content-types/' . $announcementCategoriesContentType . '/content-entries'))->data;

        return Response::json(
            [
            'status' => 'success',
            'data' => $categories,
            ]
        );
    }
}
