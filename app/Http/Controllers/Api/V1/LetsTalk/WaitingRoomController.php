<?php


namespace App\Http\Controllers\Api\V1\LetsTalk;

use App\Models\Cms;
use App\Models\LibertyUser;
use Illuminate\Http\Request;
use App\Models\LetsTalk\SocialRoom;
use App\Models\LetsTalk\VideoCallRoom;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use App\Models\LetsTalk\VideoCallParticipant;
use App\Models\LetsTalk\SocialRoomParticipant;

class WaitingRoomController extends BaseController
{
    public function roomFinder(Request $request)
    {
        $data = $request->all();

        $social_room = SocialRoomParticipant::with('socialRoom')
            ->where('user_code', $data['user_code'])
            ->first();

        $public_room = VideoCallParticipant::with('room')
            ->where('user_code', $data['user_code'])->first();

        return Response::json(
            [
                'status' => 'valid room',
                'room_code' => (isset($social_room->social_room->room_code) && !empty($social_room->social_room->room_code))
                    ? $social_room->social_room->room_code
                    : $public_room->room->name,
                'message' => 'Room code found',
            ]
        );
    }

    public function roomCheck(Request $request)
    {
        $data = $request->all();
        $room_type = 'invalid';

        // Check Social room if the room exist
        $social_room_exists = SocialRoom::where('room_code', $data['room_code'])->exists();

        $public_room_exists = VideoCallRoom::where('name', $data['room_code'])->first(); //exist does not support exists

        if ($social_room_exists || $public_room_exists) {

            $room_type = ($social_room_exists)
                ? 'social'
                : 'public';

            return Response::json(
                [
                    'status' => 'success',
                    'response' => null,
                    'room_type' => $room_type,
                    'message' => 'The room has been identified',
                ]
            );
        } else {
            return Response::json(
                [
                    'status' => 'invalid room',
                    'response' => null,
                    'room_type' => $room_type,
                    'message' => 'Unable to locate the room or the link could be expired/incorrect',
                ]
            );
        }
    }

    public function identityCheck(Request $request)
    {
        $userVerified = false;
        $person_id = '';
        $name = '';
        $business = 'lsm';
        $data = $request->all();
        $room_code = $data['room_code'];
        $email = $data['email'];
        $name = $data['name'];
        $room_type = $data['room_type'];
        $details = [];
        $libertyRepresenativesCms = [];


        $user = LibertyUser::where('email', $data['email'])->first();

        if (!$user) {
            $user = LibertyUser::where('email', strtolower($data['email']))->first();
        }

        if ($user) {
            $cms_query = Cms::get(
                'workspaces/' . $this->getCmsConfig(
                    'lsm',
                    'lets_talk_workspace'
                ) . '/content-types/' . $this->getCmsConfig(
                    'lsm',
                    'liberty_representatives_content_type'
                ) . '/content-entries?joined=true&query={"status":"publish","operator":"="}'
            );

            // determine if a valid json query
            $is_json = $this->isJson($cms_query);

            if ($is_json && isset(json_decode($cms_query)->data[0]) && !empty(json_decode($cms_query)->data[0])) {
                $libertyRepresenativesCms = json_decode($cms_query)->data[0]->{$this->getCmsConfig(
                    'lsm',
                    'liberty_representatives'
                )};

                if (count($libertyRepresenativesCms) > 0) {
                    foreach ($libertyRepresenativesCms as $key => $value) {
                        if (
                            strtolower(
                                data_get($value, $this->getCmsConfig('lsm', 'liberty_representative_email'))
                            ) === strtolower($user->email)
                        ) {
                            $business = 'lsm';
                            $userVerified = true;
                            $person_id = $value->_id;
                            $details = [
                                'name' => $value->name,
                                'email' => $user->email,
                                'mobile' => $value->{$this->getCmsConfig(
                                    'lsm',
                                    'liberty_representative_mobile_number'
                                )},
                                'profile_picture' => $value->{$this->getCmsConfig(
                                    'lsm',
                                    'liberty_representative_profile_picture'
                                )},
                                'job_title' => $value->{$this->getCmsConfig('lsm', 'liberty_representative_job_title')},
                            ];
                        }
                    }
                }
            }

            // check lmre data of no lsm data has been identified
            if (!$userVerified) {
                $cms_query = Cms::get(
                    'workspaces/' . $this->getCmsConfig(
                        'lmre',
                        'lets_talk_workspace'
                    ) . '/content-types/' . $this->getCmsConfig(
                        'lmre',
                        'liberty_representatives_content_type'
                    ) . '/content-entries?joined=true&query={"status":"publish","operator":"="}'
                );

                $is_json = $this->isJson($cms_query);

                if ($is_json && isset(json_decode($cms_query)->data[0]) && !empty(json_decode($cms_query)->data[0])) {
                    $libertyRepresenativesCms = json_decode($cms_query)->data[0]->{$this->getCmsConfig(
                        'lmre',
                        'liberty_representatives'
                    )};

                    if (count($libertyRepresenativesCms) > 0) {
                        foreach ($libertyRepresenativesCms as $key => $value) {
                            if (
                                strtolower(
                                    data_get($value, $this->getCmsConfig('lmre', 'liberty_representative_email'))
                                ) === strtolower($user->email)
                            ) {
                                $business = 'lmre';
                                $userVerified = true;
                                $person_id = $value->_id;
                            }
                        }
                    }
                }
            }
        }


        // if vr-social rooms
        if ($room_type === 'social') {
            $code = SocialRoomParticipant::generateUserCode();
            $generated_link = SocialRoomParticipant::generateCommonLink($code, $room_code);
            $room = SocialRoom::where('room_code', $room_code)->first();

            $tempLink = explode('/', $generated_link);
            if (count($tempLink) === 6) { // check length of the url if 6 = missing room_code
                $generated_link = $generated_link . '/' . $room_code;
            }

            $generated_link = $this->socialRoomLink(
                $email,
                $name,
                $room,
                $userVerified,
                $person_id,
                $room_code,
                $code,
                $generated_link,
                $business
            );

            return Response::json(
                [
                    'status' => 'identity verified',
                    'generated_link' => $generated_link,
                    'message' => 'The identity has been verified and participant can join the meeting',
                ]
            );
        }


        // if vr-public rooms
        if ($room_type === 'public') {
            $code = VideoCallParticipant::generateUserCode();


            $generated_link = VideoCallParticipant::generateCommonLink($code, $room_code);

            $room = VideoCallRoom::where('name', $room_code)->first();


            $generated_link = $this->publicRoomLink(
                $email,
                $name,
                $room,
                $userVerified,
                $person_id,
                $room_code,
                $code,
                $generated_link,
                $details
            );


            return Response::json(
                [
                    'status' => 'identity verified',
                    'generated_link' => $generated_link,
                    'message' => 'The identity has been verified and participant can join the meeting',
                ]
            );
        }

        return Response::json(
            [
                'status' => 'verification failed',
                'message' => 'Ooops... Something went wrong',
            ]
        );
    }

    // Check participant identity/role and generate link

    private function getCmsConfig($business, $config)
    {
        return config('app.cms.' . $business . '.' . $config);
    }

    private function isJson($string)
    {
        json_decode($string);
        return (json_last_error() == JSON_ERROR_NONE);
    }

    private function socialRoomLink(
        $email,
        $name,
        $room,
        $userVerified,
        $person_id,
        $room_code,
        $code,
        $generated_link,
        $business
    ) {
        // Re-use the participants record if participant exist

        if ($userVerified) //
        {
            //$cms = CmsLibertyRepresentative::fetchAllFromCacheOrCms();
            //$person = $cms[$person_id];

            $participant = SocialRoomParticipant::where('person_id', $person_id)
                ->whereNotIn('role', ['librep_guest'])
                ->where('lt_social_room_id', $room->id)
                //->where('user_name', $name)
                ->first();

            if ($participant) {
                $generated_link = SocialRoomParticipant::generateCommonLink($participant->user_code, $room_code);
                return $generated_link;
            }

            $participant = SocialRoomParticipant::where('person_id', $person_id)
                ->where('lt_social_room_id', $room->id)
                //->where('user_name', $name)
                ->first();

            if ($participant) {
                $generated_link = SocialRoomParticipant::generateCommonLink($participant->user_code, $room_code);
            } else {
                SocialRoomParticipant::create(
                    [
                        'lt_social_room_id' => $room->id,
                        'user_name' => $name,
                        'role' => 'librep_guest',
                        'person_id' => (isset($person_id))
                            ? $person_id
                            : 'n/a',
                        'user_code' => $code,
                        'business' => $business,
                    ]
                );
            }

            return $generated_link;
        } else { // participant is entering as a guest

            $participant = SocialRoomParticipant::where(
                function ($query) use ($email, $name, $room) {
                    $query->where('lt_social_room_id', $room->id)
                        ->whereRaw('LOWER(external_user_email) = ?', [(string)strtolower($email)]);
                }
            )
                ->orWhere(
                    function ($query) use ($name, $room, $email) {
                        $query->where('lt_social_room_id', $room->id)
                            ->whereRaw('LOWER(external_user_email) = ?', [(string)strtolower($email)])
                            ->where('role', 'external-guest');
                    }
                )
                ->first();

            if ($participant) {
                $generated_link = SocialRoomParticipant::generateCommonLink($participant->user_code, $room_code);
            } else {
                SocialRoomParticipant::create(
                    [
                        'lt_social_room_id' => $room->id,
                        'user_name' => 'n/a',
                        'role' => 'guest_clients',
                        'person_id' => uniqid(),
                        'external_user' => $name,
                        'external_user_email' => $email,
                        'user_code' => $code,
                        'business' => $business,
                    ]
                );
            }

            return $generated_link;
        }
    }

    private function publicRoomLink(
        $email,
        $name,
        $room,
        $userVerified,
        $person_id,
        $room_code,
        $code,
        $generated_link,
        $details
    ) {


        // Re-use the participants record if participant exist
        $participant = VideoCallParticipant::where('room_id', $room->_id)
            ->where('email', 'regexp', '/^' . preg_quote($email) . '$/i')
            ->first();

        if ($userVerified) {
            //$cms = CmsLibertyRepresentative::fetchAllFromCacheOrCms();
            //$person = $cms[$person_id];

            if ($participant) {
                VideoCallParticipant::where('room_id', $room->_id)
                    ->where('email', 'regexp', '/^' . preg_quote($email) . '$/i')
                    ->where('user_code', $participant->user_code)
                    ->update(
                        [
                            'user_name' => $name,
                        ]
                    );

                $generated_link = VideoCallParticipant::generateCommonLink($participant->user_code, $room_code);
            } else {

                VideoCallParticipant::create(
                    [
                        'room_id' => $room->_id,
                        'user_name' => $name,
                        'user_code' => $code,
                        'email' => $email,
                        'mobile_number' => (isset($details['mobile']))
                            ? $details['mobile']
                            : '',
                        'representative_job_title' => (isset($details['job_title']))
                            ? $details['job_title']
                            : '',
                        'role' => 'liberty_representative',
                        'details' => $details,
                    ]
                );
            }

            return $generated_link;
        } else { // participant is entering as a guest

            if ($participant) {

                VideoCallParticipant::where('room_id', $room->_id)
                    ->where('email', 'regexp', '/^' . preg_quote($email) . '$/i')
                    ->where('user_code', $participant->user_code)
                    ->update(
                        [
                            'user_name' => $name,
                        ]
                    );

                $generated_link = VideoCallParticipant::generateCommonLink($participant->user_code, $room_code);
            } else {

                VideoCallParticipant::create(
                    [
                        'room_id' => $room->_id,
                        'user_name' => $name,
                        'user_code' => $code,
                        'email' => $email,
                        'role' => 'client',
                    ]
                );
            }

            return $generated_link;
        }
    }

    public function businessCheck(Request $request)
    {
        $data = $request->all();
        $email = $data['email'];
        $room_code = $data['room_code'];
        $userVerified = false;
        $business = 'lsm';
        $user = LibertyUser::where('email', $data['email'])->first();

        // Determin the user by room, this will be needed by guest users
        $public_room = VideoCallRoom::where('name', $room_code)->first();

        if ($public_room) {
            $business = $public_room->business;
        }

        // Determine the user by liberty representative
        if ($user) {
            $libertyRepresenativesCms = json_decode(
                Cms::get(
                    'workspaces/' . $this->getCmsConfig(
                        'lsm',
                        'lets_talk_workspace'
                    ) . '/content-types/' . $this->getCmsConfig(
                        'lsm',
                        'liberty_representatives_content_type'
                    ) . '/content-entries?joined=true&query={"status":"publish","operator":"="}'
                )
            )->data[0]->{$this->getCmsConfig(
                'lsm',
                'liberty_representatives'
            )};

            foreach ($libertyRepresenativesCms as $key => $value) {
                if (
                    strtolower(
                        data_get($value, $this->getCmsConfig('lsm', 'liberty_representative_email'))
                    ) === strtolower($user->email)
                ) {
                    $business = 'lsm';
                    $userVerified = true;
                }
            }

            // check schedule room updates
            // $social_room = SocialRoom::where('room_code', $data['room_code'])->first();
            // $social_room_id = $social_room->id;

            // $room = SocialRoomScheduleUpdate::where('room_id', $social_room_id)
            //     ->orderBy('id', 'DESC')
            //     ->first();

            // if (isset($room->room_code) && !empty($room->room_code)) {
            //     $room_code = $room->room_code;
            // } else {
            //     $room_code = $data['room_code'];
            // }

            // check lmre data of no lsm data has been identified
            if (!$userVerified) {
                $libertyRepresenativesCms = json_decode(
                    Cms::get(
                        'workspaces/' . $this->getCmsConfig(
                            'lmre',
                            'lets_talk_workspace'
                        ) . '/content-types/' . $this->getCmsConfig(
                            'lmre',
                            'liberty_representatives_content_type'
                        ) . '/content-entries?joined=true&query={"status":"publish","operator":"="}'
                    )
                )->data[0]->{$this->getCmsConfig(
                    'lmre',
                    'liberty_representatives'
                )};

                foreach ($libertyRepresenativesCms as $key => $value) {
                    if (
                        strtolower(
                            data_get($value, $this->getCmsConfig('lmre', 'liberty_representative_email'))
                        ) === strtolower($user->email)
                    ) {
                        $business = 'lmre';
                        $userVerified = true;
                    }
                }
            }
        }

        return Response::json(
            [
                'business' => $business,
                //'room_code' => $room_code
            ]
        );
    }
}
