<?php


namespace App\Http\Controllers\Api\V1\LetsTalk;

use App\Models\Broker;
use Twilio\Rest\Client;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use App\Models\LetsTalk\SocialRoomParticipant;

class ParticipantsController extends BaseController
{
    private $client;

    public function __construct()
    {
        $twilioConfig = config('app.twilio.video');
        $twilioApiKey = $twilioConfig['apiKey'];
        $twilioApiSecret = $twilioConfig['apiSecret'];

        $this->client = new Client($twilioApiKey, $twilioApiSecret);
    }

    public function getOrderedParticipantsByRoom($name)
    {
        $time = time();
        Log::info("getOrderedParticipantsByRoom: Enter - " . $time);
        $participants = $this->client->video->rooms($name)->participants->read(["status" => "connected"]);


        $allParticipants = [];
        $videoOnCount = 0;
        $count = count($participants);
        foreach ($participants as $participant) {
            $publishedTracks = $this->client->video->rooms($name)
                ->participants($participant->sid)
                ->publishedTracks->read();
            $videoOn = false;
            foreach ($publishedTracks as $publishedTrack) {
                if ($publishedTrack->kind === 'video' && $publishedTrack->enabled) {
                    $videoOn = true;
                    $videoOnCount++;
                    break;
                }
            }

            $format = 'H:i:s';
            $allParticipants[] = [
                'sid' => $participant->sid,
                'identity' => $participant->identity,
                'created' => $participant->dateCreated->format($format),
                'updated' => $participant->dateUpdated->format($format),
                'started' => $participant->startTime->format($format),
                'videoOn' => $videoOn,
                'order' => $count--,
            ];
        }
        // sort the results by order before sending back to requester
        usort(
            $allParticipants,
            function ($a, $b) {
                if ($a['order'] == $b['order']) {
                    return 0;
                }

                return $a['order'] < $b['order']
                    ? -1
                    : 1;
            }
        );
        Log::info("getOrderedParticipantsByRoom: Return - " . $time);
        return Response::json(
            [
                'response' => 'success',
                'data' => [
                    'allParticipants' => $allParticipants,
                    'videoOnCount' => $videoOnCount,
                ],
            ]
        );
    }

    public function getParticipantBrokerDetails($roomCode, $userCode)
    {
        // Check if participant has external company name
        $query = SocialRoomParticipant::with('broker')
            ->where('user_code', $userCode)
            ->where(
                function ($query) {
                    $query->whereNotNull('broker_id')
                        ->whereNotNull('external_user_company', 'or');
                }
            )
            ->whereHas(
                'socialRoom',
                function ($query) use ($roomCode) {
                    return $query->where('room_code', $roomCode);
                }
            );

        $participant = $query->first();

        if (empty($participant)) {
            return Response::json(
                [
                    'status' => 'fail',
                    'message' => 'Room participant not found.',
                ]
            );
        }

        if (
            empty($participant->broker)
            && empty($participant->external_user_company)
        ) {
            return Response::json(
                [
                    'status' => 'fail',
                    'message' => 'No broker data.',
                ]
            );
        }

        $broker = $participant->broker;

        if (empty($broker)) {
            // Check for a matching lowercase company name
            $broker = Broker::whereRaw(
                'LOWER(TRIM(`name`)) = ?',
                [strtolower($participant->external_user_company)]
            )->first();
        }

        if (empty($broker)) {
            return Response::json(
                [
                    'status' => 'fail',
                    'message' => 'No broker data.',
                ]
            );
        }

        return Response::json(
            [
                'status' => 'success',
                'broker' => [
                    'logo' => $broker->link,
                ],
            ]
        );
    }
}
