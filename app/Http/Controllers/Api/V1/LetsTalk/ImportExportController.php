<?php


namespace App\Http\Controllers\Api\V1\LetsTalk;

use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Hash;
use App\Models\LibertyUser;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ImportExportController extends BaseController
{
    const VALID_MOBILE_NUMBER_REGEX = '/^[+][1-9][0-9]{6,14}/';

    public function importLibertyUsers(Request $request)
    {
        $libertyUsers = $request->get('data');
        // \Log::info($libertyUsers);

        if (!$libertyUsers) {
            return Response::json(
                [
                    'msg' => 'no data found',
                    'response' => 404,
                ]
            );
        }

        $hasEmptyField = false;
        $failedToImportUsers = [];

        foreach ($libertyUsers as $libertyUser) {
            $validator = Validator::make(
                $libertyUser,
                [
                    'first_name' => 'required',
                    'last_name' => 'required',
                    'email' => 'unique:liberty_users|email',
                    // 'phone'         =>  'required'
                ],
                ['email.unique' => 'Email already exist.']
            );

            // dd($validator->fails());
            // skip invalid user
            if ($validator->fails()) {
                $libertyUser['error'] = $validator->messages()->first();
                $failedToImportUsers[] = $libertyUser;
                continue;
            }

            $libertyUser = json_decode(json_encode($libertyUser));

            $first_name = trim($libertyUser->first_name);
            $last_name = trim($libertyUser->last_name);
            $email = trim($libertyUser->email);
            $phone = isset($libertyUser->phone)
                ? $libertyUser->phone
                : "";
            $phone = trim($phone);

            $uuid = substr(Str::uuid()->toString(), 0, 4);
            $expiry = time();
            $data = [
                'first_name' => $first_name,
                'last_name' => $last_name,
                'email' => $email,
                'phone' => $phone,
                'role' => 'virtual-rooms',
                'activated' => 0,
                'password' => Hash::make(uniqid()),
                'secret' => uniqid(),
                'activation_code' => $uuid,
                'activation_code_expires' => $expiry
                // 'branch_id'               => $request->get('branch_id'),
                // 'broker_id'               => $request->get('broker_id'),
                // 'qualified_for'           => $request->get('qualified_for'),
                // 'claims_notification'     => $request->get('claims_notification'),
                // 'image'                      => $request->get('image'),
            ];

            $liberty_model = LibertyUser::create($data);
        }

        return Response::json(
            [
                'msg' => 'liberty users has been imported',
                'response' => 200,
                'failed_imports' => $failedToImportUsers,
            ]
        );
    }
}
