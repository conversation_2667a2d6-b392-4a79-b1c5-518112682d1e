<?php

namespace App\Http\Controllers\Api\V1\LetsTalk;

use Carbon\Carbon;
use Twilio\Jwt\AccessToken;
use Twilio\Jwt\Grants\VideoGrant;
use App\Models\LetsTalk\VideoCallRoom;
use App\Models\LetsTalk\SocialRoomType;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use App\Models\LetsTalk\SocialRoomSchedule;
use App\Models\LetsTalk\VideoCallParticipant;
use App\Models\LetsTalk\SocialRoomParticipant;

class VideoTokenController extends BaseController
{
    const ROOM_TYPES = [
        1 => 'coffee_break',
        2 => 'water_cooler',
        3 => 'theme_rooms',
        4 => '1_to_1_catchup',
        5 => 'team_meeting',
        6 => 'slt_surgeries',
    ];

    public function token($identity, $roomName)
    {
        /**
         * @var VideoCallParticipant $user 
         */
        $userSocial = false;
        $userName = "";
        // Primary check if regular LMRE/LSM user and not social user
        $user = $this->validateIdentity($identity, $roomName);
        if (!$user) {
            //get existing token and return to twilio waiting room
            $token = VideoCallParticipant::where('user_code', $identity)->first();

            // If primary check fails secondary check attempt
            $userSocial = $this->validateIdentitySocial($identity, $roomName);
            if (!$user && !$userSocial) {
                return Response::json(
                    [
                        'response' => 'fail',
                        'data' => [
                            'message' => 'identity not found or link has expired',
                            'token' => ($token && isset($token->token))
                                ? $token->token
                                : 1,
                        ],
                    ]
                );
            }
        }

        if ($user) {
            $business = $this->getBusinessType($roomName);
        } else {
            // TODO: identify the template
            $business = 'lsm';
            $user = $userSocial;
        }

        $is_social = (!$userSocial)
            ? false
            : true;
        $brokerCompanyLogo = null;

        if ($is_social && isset($userSocial->socialRoom->lt_social_room_type_id)) {
            if ($user->socialRoom->lt_social_room_type_id === 5) {
                $roomParticipants = SocialRoomParticipant::where('lt_social_room_id', $userSocial->socialRoom->id)
                    ->with('broker')
                    ->get();

                // Get current room participants count
                $current_room_participants = $roomParticipants->filter(
                    function ($item) {
                        return $item->role == 'virtual-rooms';
                    }
                )->count();

                // Check if a participant has broker details
                $brokerParticipant = $roomParticipants->first(
                    function ($key, $participant) {
                        return isset($participant->broker) && !empty($participant->broker);
                    }
                );

                if (!empty($brokerParticipant)) {
                    $brokerCompanyLogo = $brokerParticipant->broker->link;
                }
            } else {
                $current_room_participants = SocialRoomParticipant::where(
                    'lt_social_room_id',
                    $userSocial->socialRoom->id
                )->count();
            }

            $room_type = $userSocial->socialRoom->lt_social_room_type_id;
            $max_participants = !empty($current_room_participants)
                ? $current_room_participants
                : $userSocial->socialRoomType->max_participants;
        }

        if (isset($user->token) || (isset($user->external_token) && !empty($user->external_token))) {
            $isTeamMeeting = (isset($user->socialRoom) && !empty($user->socialRoom) && $user->socialRoom->lt_social_room_type_id == 5);
            $isPermanent = false;
            if ($isTeamMeeting) {
                $schedule = $socialRoomSchedule = SocialRoomSchedule::where(
                    'lt_social_room_id',
                    $user->socialRoom->id
                )->first();
                $isPermanent = (!empty($schedule) && $schedule->duration_type === 'permanent');
            }

            $createdAt = Carbon::parse($user->created_at);
            $isTokenExpired = $createdAt->diffInHours(now()) >= 6;

            if (!$isTeamMeeting && !$isPermanent) {
                if(!$isTokenExpired){
                    return Response::json(
                        [
                            'response' => 'success',
                            'data' => [
                                'token' => (isset($user->token) && !empty($user->token))
                                    ? $user->token
                                    : $user->external_token,
                                'name' => $user->external_user
                                    ?: $user->name,
                                'role' => $user->role,
                                'business' => $business,
                                'is_social' => $is_social,
                                'max_participants' => isset($max_participants)
                                    ? $max_participants
                                    : 9,
                                'room_type' => isset($room_type)
                                    ? SELF::ROOM_TYPES[$room_type]
                                    : 0,
                                'broker_company_logo' => null,
                            ],
                        ]
                    );
                }
                $userName = $user->external_user ?: $user->name;
            }
        }

        $twilioConfig = config('app.twilio.video');
        $twilioAccountSid = $twilioConfig['sid'];
        $twilioApiKey = $twilioConfig['apiKey'];
        $twilioApiSecret = $twilioConfig['apiSecret'];


        // Create access token, which we will serialize and send to the client
        $token = new AccessToken(
            $twilioAccountSid,
            $twilioApiKey,
            $twilioApiSecret,
            VideoCallParticipant::TOKEN_EXPIRY,
            (isset($user->external_user) && !empty($user->external_user))
                ? $user->external_user
                : $user->user_name
        );

        // Create Video grant
        $videoGrant = new VideoGrant();
        $videoGrant->setRoom($roomName);

        // Add grant to token
        $token->addGrant($videoGrant);

        $statusCallBackUrl = config('app.client_frontend') . '/' . config('app.twilio.vrStatusCallBackUrl');


        $roomData = [
            'UniqueName' => $roomName,
            'StatusCallback' => $statusCallBackUrl,
            'StatusCallbackMethod' => 'POST',
        ];

        $curl = curl_init();
        curl_setopt_array(
            $curl,
            [
                CURLOPT_RETURNTRANSFER => 1,
                CURLOPT_USERPWD => $twilioApiKey . ':' . $twilioApiSecret,
                CURLOPT_URL => 'https://video.twilio.com/v1/Rooms',
                CURLOPT_POST => 1,
                CURLOPT_POSTFIELDS => $roomData,
            ]
        );

        $response = curl_exec($curl);
        curl_close($curl);

        if (!$userSocial) {
            $user->token = $token->toJWT();
            $user->save();
        } else { //if user is social

            if (isset($user->external_user)) {
                unset($user->socialRoomType);
                $user->external_token = $token->toJWT();
                $user->save();
            }
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => [
                    'token' => $token->toJWT(),
                    'name' => $userName ?: $user->user_name,
                    'role' => $user->role,
                    'business' => $business,
                    'is_social' => $is_social,
                    'max_participants' => isset($max_participants)
                        ? $max_participants
                        : 9,
                    'room_type' => isset($room_type)
                        ? SELF::ROOM_TYPES[$room_type]
                        : 0,
                    'broker_company_logo' => isset($brokerCompanyLogo)
                        ? $brokerCompanyLogo
                        : null,
                ],
            ]
        );
    }

    private function validateIdentity($code, $room)
    {
        $user = VideoCallParticipant::where('user_code', $code)->first();

        if ($user && $user->room->name === $room) {
            /**
             * @var VideoCallRoom $videoRoom 
             */
            $videoRoom = VideoCallRoom::where('name', $room)->first();

            if ($videoRoom && !$videoRoom->isExpired()) {
                return $user;
            }
        }

        return false;
    }

    private function validateIdentitySocial($code, $room)
    {
        $socialRoomParticipant = SocialRoomParticipant::with('socialRoom')->where('user_code', $code)->first();
        if (!$socialRoomParticipant || ($socialRoomParticipant->socialRoom->room_code !== $room)) {
            return false;
        }

        // Update last_activity_at on join room
        $socialRoomParticipant->last_activity_at = Carbon::now();
        $socialRoomParticipant->push();

        $socialRoomParticipant->socialRoomType = SocialRoomType::where(
            'id',
            $socialRoomParticipant->socialRoom->lt_social_room_type_id
        )->first();
        return $socialRoomParticipant;
    }

    public function getBusinessType($room)
    {
        $videoRoom = VideoCallRoom::where('name', $room)->first();
        if ($videoRoom && !empty($videoRoom) && isset($videoRoom->business)) {
            return $videoRoom->business;
        }
        return '';
    }
}
