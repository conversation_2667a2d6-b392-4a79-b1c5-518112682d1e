<?php


namespace App\Http\Controllers\Api\V1\LetsTalk;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\LetsTalk\SocialRoom;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use App\Models\LetsTalk\UpcomingSchedule;
use Illuminate\Support\Facades\Validator;
use App\Models\LetsTalk\SocialRoomSchedule;
use Illuminate\Database\Eloquent\Collection;
use App\Models\LetsTalk\SocialRoomParticipant;
use App\Models\LetsTalk\CmsLibertyRepresentative;
use App\Models\LetsTalk\SocialRoomScheduleUpdate;
use App\Models\LetsTalk\SocialRoomSmsNotification;
use App\Models\LetsTalk\SocialRoomCancelledSchedule;
use App\Models\LetsTalk\SocialRoomEmailNotification;

class SocialRoomScheduleController extends BaseController
{
    private $business;

    public function __construct(Request $request)
    {
        $this->business = ($request->has('business') && $request->get('business') === 'lmre')
            ? 'lmre'
            : 'lsm';

        $this->libReps = CmsLibertyRepresentative::fetchAllFromCacheOrCms();

        $personId = $request->get('person_id');
        $this->authUserCms = isset($this->libReps[$personId])
            ? $this->libReps[$personId]
            : [];
        $this->authUserTz = isset($this->authUserCms['office_timezone'])
            ? $this->authUserCms['office_timezone']
            : 'Europe/London';
    }

    public function index()
    {
    }

    public function store(Request $request)
    {
        $data = $request->all();

        $libReps = $this->libReps;
        // dd($libReps);

        $errorResponse = $this->validateStore($data, $libReps);
        if ($errorResponse instanceof JsonResponse) {
            return $errorResponse;
        }

        $socialRoom = SocialRoom::with(['socialRoomParticipants'])->find($data['room_id']);

        $socialRoom = SocialRoom::setCmsData(new Collection([$socialRoom]), false, 'socialRoomParticipants')->first();

        $libRepCreator = $this->authUserCms;

        // Convert start_date & end_date to London tz from creator tz
        $data['start_date'] = Carbon::parse(
            $data['start_date'],
            $libRepCreator['office_timezone']
        )->setTimezone("Europe/London")->format('Y-m-d H:i:s');
        $data['end_date'] = Carbon::parse(
            $data['end_date'],
            $libRepCreator['office_timezone']
        )->setTimezone("Europe/London")->format('Y-m-d H:i:s');
        $data['end_after'] = $data['end_after_frequency'] . ' ' . $data['end_after_frequency_type'];

        //field name
        $data['valid_until'] = SocialRoomSchedule::getValidUntil($data);

        $socialRoomSchedule = SocialRoomSchedule::create($data);

        SocialRoomScheduleUpdate::create(
            [
                'room_id' => $data['room_id'],
                'room_type' => $socialRoom->lt_social_room_type_id,
                'room_code' => $socialRoom->room_code,
            ]
        );

        $socialRoom->socialRoomSchedule()->save($socialRoomSchedule);

        // If added schedule is permanent, we don't want send notification. So we return early here
        if ($socialRoomSchedule->isPermanent()) {
            return Response::json(
                [
                    'response' => 'success',
                    'data' => [
                        'room' => $socialRoom,
                        'schedule' => $socialRoomSchedule,
                    ],
                    'message' => 'Schedule has been successfully created.',
                ]
            );
        }

        $libRepParticipants = $socialRoom->socialRoomParticipants->filter(
            function ($participant) {
                return $participant->role !== 'external-guest';
            }
        );
        $externalParticipants = $socialRoom->socialRoomParticipants->filter(
            function ($participant) {
                return $participant->role === 'external-guest';
            }
        );


        $personId = $libRepCreator['person_id'];
        $roomCode = $socialRoom['room_code'];
        $socialRooms = SocialRoom::with('socialRoomSchedule')
            ->with(['socialRoomParticipants'])
            ->with('deletedScheduleEntries')
            ->where('room_code', $roomCode)
            ->get();


        if (isset($socialRooms->first()->socialRoomSchedule) && $socialRooms->first()->socialRoomSchedule) {
            $socialRooms = SocialRoom::setCmsData($socialRooms, false, 'socialRoomParticipants');
            $tmpData = UpcomingSchedule::generateWithBuffer($socialRooms, $this->authUserCms);
            $upcomingSchedules = $tmpData['upcomingSchedules'];
        } else {
            $upcomingSchedules = [];
        }


        // send email notification here
        if ($socialRoom->isTeamMeetingRoom()) {
            SocialRoomEmailNotification::sendTeamMeetingTemplate1(
                [
                    'room' => $socialRoom->toArray(),
                    'schedule' => $socialRoomSchedule->toArray(),
                    'participants' => $libRepParticipants->toArray(),
                    'creator' => $libRepCreator,
                    'frequency_info' => $data,
                    'upcoming_schedules' => $upcomingSchedules,
                    'externalParticipants' => $externalParticipants,
                    'externalAttendees' => SocialRoomParticipant::where('lt_social_room_id', $socialRoom->id)->where(
                        'role',
                        'external-guest'
                    )->get(),
                ],
                $socialRoom->isTeamMeetingRoom()
            );
        }

        if ($socialRoom->isCatchupRoom()) {
            $externalParticipants = [];
            $participant = $socialRoom->socialRoomParticipants->filter(
                function ($participant) use ($libRepCreator) {
                    return $participant->person_id !== $libRepCreator['person_id'];
                }
            )->first();
            $participant = array_merge(
                $participant->cms,
                ['user_code' => $participant->user_code, 'user_name' => $participant->user_name]
            );

            $libRepCreator = $socialRoom->socialRoomParticipants->filter(
                function ($participant) use ($libRepCreator) {
                    return $participant->person_id === $libRepCreator['person_id'];
                }
            )->first();

            $libRepCreator = array_merge(
                $libRepCreator->cms,
                ['user_code' => $libRepCreator->user_code, 'user_name' => $libRepCreator->user_name]
            );

            SocialRoomEmailNotification::sendTemplate1(
                [
                    'room' => $socialRoom->toArray(),
                    'schedule' => $socialRoomSchedule->toArray(),
                    'participant' => $participant,
                    'creator' => $libRepCreator,
                    'frequency_info' => $data,
                    'upcoming_schedules' => $upcomingSchedules,
                    'externalParticipants' => $externalParticipants,
                ]
            );
        }

        // $getSocialRoomParticipants = SocialRoomParticipant::where('lt_social_room_id', $socialRoom->id)->get();
        $sendSms = new SocialRoomSmsNotification;
        $sendSms->sendSmsTemplate1(
            $socialRoom,
            $libRepCreator,
            $libRepParticipants->toArray(),
            $externalParticipants,
            $socialRoomSchedule,
            $socialRoom->isTeamMeetingRoom()
        );

        return Response::json(
            [
                'response' => 'success',
                'data' => [
                    'room' => $socialRoom,
                    'schedule' => $socialRoomSchedule,
                ],
                'message' => 'Schedule has been successfully created.',
            ]
        );
    }

    private function validateStore($data, $libReps)
    {
        $validator = $this->_validate($data, 'store');

        if ($validator->fails()) {
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        }

        if ($data['person_id'] && !isset($libReps[$data['person_id']])) {
            $validator->errors()->add('person_id', 'Person id does not exists.');
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        }

        // if(isset($data['person_id']) && !isset($libReps[$data['person_id']])) {
        //     $validator->errors()->add('person_id', 'Participant does not exists.');
        //     $errorResponse = [
        //         'response' => 'error',
        //         'errors' => $validator->messages()->toArray(),
        //     ];
        // }

        if (isset($errorResponse)) {
            return Response::json($errorResponse);
        }

        return [];
    }

    private function _validate($data, $action)
    {
        $rules = [
            'room_id' => 'required|exists:lt_social_rooms,id',
            'person_id' => 'required',
            'duration_type' => 'required',
            'frequency' => 'required|integer',
            'frequency_type' => 'required',
            'start_date' => 'required',
            'end_date' => 'required',
        ];

        // // On update, we don't want to validate email, cause it cannot be updated
        // if($action === 'update') {
        //     unset($rules['email']);
        // }

        // TODO: Add appropriate error messages here
        // TODO: Add appropriate error messages here
        // TODO: Add appropriate error messages here
        $messages = [
            // 'name.required' => 'Please enter Name.',
            // 'email.required' => 'Please enter Email.',
            // 'mobile_number.required' => 'Please enter Mobile Phone in a valid format.',
            // 'mobile_number.regex' => 'Please enter Mobile Phone in a valid format.',
        ];

        return Validator::make($data, $rules, $messages);
    }

    public function show($roomCode)
    {
    }

    // validate store method

    public function update($id)
    {
    }

    public function destroy($id)
    {
    }

    public function downloadIcs(Request $request, $socialRoomTypeId, $socialRoomId)
    {
        $person_id = $request->get('person_id');
        $date = $request->get('date');
        $static_download = $request->has('static_download')
            ? $request->get('static_download')
            : 0;
        $isCyberVr = $request->has('is_cyber_vr') ? $request->get('is_cyber_vr') : 0;

        $authUserCms = $this->authUserCms;
        $dateInAuthUserTz = Carbon::parse($date, $this->authUserTz);
        $dateInLondonTz = $dateInAuthUserTz->copy()->setTimezone("Europe/London");

        $socialRoom = SocialRoom::with(['socialRoomSchedule'])->has('socialRoomSchedule')->find($socialRoomId);

        if (!$socialRoom) {
            return Response::json(['response' => 'error', 'message' => 'Unable to download. Space not found.']);
        }

        $deletedSchedule = SocialRoomCancelledSchedule::where(
            [
                'lt_social_room_id' => $socialRoomId,
                'lt_social_room_schedule_id' => $socialRoom->socialRoomSchedule->id,
                'date' => $dateInLondonTz,
            ]
        )->first();

        if (!empty($deletedSchedule)) {
            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'Unable to download. Schedule has been cancelled.',
                ]
            );
        }

        $creator = $socialRoom->cms;
        $receiver = $authUserCms;
        $schedule = $socialRoom->socialRoomSchedule;

        // Set to one-off cause we only need one schedule in ics
        if ($request->get('date')) {
            $schedule->duration_type = SocialRoomSchedule::DURATION_TYPE_ONE_OFF;
        }

        // Details for ics
        if (isset($socialRoom->cms['user_name']) && !empty($socialRoom->cms['user_name'])) {
            $receiver['ics_title'] = $socialRoom->name . ' - ' . $socialRoom->cms['user_name'] . ' - SLT';
        }

        if ($socialRoom->isCommunityThemeRoom()) {
            $receiver['ics_title'] = $socialRoom->name . ' - Community';
        }

        if ($socialRoom->isThemeRoom()) {
            if ($isCyberVr) {
                $receiver['ics_title'] = $socialRoom->name . ' - Cyber Virtual Room';
            } else {
                $receiver['ics_title'] = $socialRoom->name . ' - Theme Room';
            }
        }

        $receiverTimezone = $receiver['office_timezone'];

        $receiver['meetingStart'] = $dateInAuthUserTz->copy();
        $endDate = Carbon::parse($schedule['end_date'], 'Europe/London')->setTimezone($receiverTimezone);
        $receiver['meetingEnd'] = $endDate->setDateFrom($dateInAuthUserTz);

        $other_details_for_ics = (object)[];
        $other_details_for_ics->creator_name = $receiver['user_name'];
        $other_details_for_ics->creator_email = $receiver['email'];
        $receiver['other_details_for_ics'] = $other_details_for_ics;

        $receiver['room_code'] = $socialRoom->room_code;
        $receiver['joinRoomLink'] = SocialRoomParticipant::generateWaitingRoomCommonLink($socialRoom->room_code);

        // Override joinRoomLink/ics link  if it's passed as param
        if ($request->get('ics_url')) {
            $receiver['joinRoomLink'] = $request->get('ics_url');
        }

        $ics = SocialRoomEmailNotification::get_ics_attachment($receiver, $schedule, null, $static_download);
        $data = ['room' => $socialRoom, 'ics' => $ics['ics']['StringValue']];
        return Response::json(['response' => 'success', 'data' => $data]);
    }
}
