<?php

namespace App\Http\Controllers\Api\V1\LetsTalk;

use Carbon\Carbon;
use App\Models\LibertyUser;
use Illuminate\Http\Request;
use App\Models\LetsTalk\SocialRoom;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use App\Models\LetsTalk\SocialRoomParticipant;
use App\Models\LetsTalk\CmsLibertyRepresentative;

class SocialRoomParticipantController extends BaseController
{
    public function birthday(Request $request)
    {
        $data = $request->all();
        $personId = $data['person_id'];

        $socialRooms = SocialRoom::with('socialRoomType')
            ->where('lt_social_room_type_id', 5)
            ->where('is_community', 0)
            ->where('status', 'approved')
            ->with(['socialRoomParticipants', 'socialRoomSchedule'])
            ->whereHas(
                'socialRoomParticipants',
                function ($query) use ($personId) {
                    $query->where('person_id', $personId);
                }
            )
            ->get();

        $socialRoomsId = array_column($socialRooms->toArray(), 'id');
        $participants = SocialRoomParticipant::whereIn('lt_social_room_id', $socialRoomsId)->get()->toArray();
        $participants = $this->setCmsData($participants);
        $participants = array_filter(
            $participants,
            function ($participant) use ($personId) {
                return
                    isset($participant['cms']['birthday']) &&
                    $participant['cms']['birthday'] != '' &&
                    (Carbon::parse($participant['cms']['birthday'])->format('m/d') == Carbon::now()->format('m/d')) &&
                    $participant['person_id'] != $personId;
            }
        );
        $participants = $this->removeDuplicateParticipants($participants);

        return Response::json(
            [
                'status' => 'success',
                'data' => $participants,
            ]
        );
    }

    private function setCmsData($participants)
    {
        $cms = CmsLibertyRepresentative::fetchAllFromCacheOrCms();
        $result = [];
        foreach ($participants as $index => $participant) {
            $person_id = $participant['person_id'];
            $role = $participant['role'];

            if ($role != 'external-guest' && $person_id != 'n/a') {
                $participant['cms'] = $cms[$person_id];
                $libertyUser = LibertyUser::where('email', $participant['cms']['email'])->first();
                if (isset($libertyUser)) {
                    $participant['full_name'] = $libertyUser->first_name . ' ' . $libertyUser->last_name;
                } else {
                    $participant['cms'] = [];
                }
            } else {
                $participant['cms'] = [];
            }
            $result[] = $participant;
        }

        return $result;
    }

    private function removeDuplicateParticipants($participants)
    {
        $models = array_map(
            function ($participant) {
                return $participant['person_id'];
            },
            $participants
        );

        $unique_models = array_unique($models);

        return array_values(array_intersect_key($participants, $unique_models));
    }
}
