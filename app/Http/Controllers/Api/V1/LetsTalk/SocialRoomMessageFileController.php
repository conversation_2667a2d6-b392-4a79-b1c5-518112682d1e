<?php

namespace App\Http\Controllers\Api\V1\LetsTalk;

use Illuminate\Http\Request;
use App\Http\Controllers\BaseController;
use App\Models\LetsTalk\SocialRoomMessageFile;
use App\Models\LetsTalk\SocialRoomMessageFileUpload;

class SocialRoomMessageFileController extends BaseController
{
    private $files;

    public function __construct(SocialRoomMessageFileUpload $fileupload)
    {
        $this->files = $fileupload;
    }

    /**
     * Upload File
     */
    public function upload(Request $request)
    {
        $newfile = SocialRoomMessageFile::create(
            [
                'message_id' => $request->get('message_id'),
                'file' => $request->get('cloud_file'),
                'original_filename' => $request->get('original_file'),
                'type' => $request->get('type'),
            ]
        );

        if ($newfile) {
            $response = [
                'response' => 'success',
                'message' => 'The Liberty Community File has been uploaded successfully',
                'url' => $this->getLink($newfile->id),
                'id' => $newfile->_id,
            ];
        } else {
            $response = [
                'response' => 'error',
                'message' => 'The Liberty Community File has failed to upload',
            ];
        }

        return $response;
    }


    private function getLink($id)
    {
        $url = ($file = SocialRoomMessageFile::find($id))
            ? $this->files->link('virtual-rooms/' . $file->message_id . '/' . $file->file)
            : '#';
        return $url;
    }


    public function link($id)
    {
        if ($file = SocialRoomMessageFile::find($id)) {
            $response = [
                'response' => 'success',
                'message' => 'The specified Liberty Community discussion file was successfully deleted',
                'url' => $this->files->link('virtual-rooms/' . $file->message_id . '/' . $file->file),
            ];
        } else {
            $response = [
                'response' => 'error',
                'message' => 'The specified Liberty Forum discussion file could not be found',
            ];
        }

        return $response;
    }


    public function delete($id)
    {
        if ($file = SocialRoomMessageFile::find($id)) {
            if (SocialRoomMessageFile::destroy($id)) {
                $response = [
                    'response' => 'success',
                    'message' => 'The specified Liberty Community discussion file was successfully deleted',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The specified Liberty Forum discussion file could not be deleted',
                ];
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'The specified Liberty Forum discussion file could not be found',
            ];
        }

        return $response;
        // Response::json($response);
    }
}
