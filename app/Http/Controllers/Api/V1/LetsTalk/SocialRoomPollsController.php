<?php

namespace App\Http\Controllers\Api\V1\LetsTalk;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\LetsTalk\SocialRoom;
use App\Models\LetsTalk\SocialRoomPoll;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use App\Models\LetsTalk\SocialRoomMessage;
use App\Models\LetsTalk\SocialRoomPollVote;
use App\Models\LetsTalk\SocialRoomPollOption;

class SocialRoomPollsController extends BaseController
{
    public function store(Request $request, $roomCode)
    {
        $data = $request->only(['poll_description', 'poll_options', 'person_id']);
        $validator = Validator::make(
            $data,
            [
                'poll_description' => 'required|string',
                'poll_options' => 'required|array',
                'poll_options.1' => 'required|string',
                'poll_options.2' => 'required|string',
                'poll_options.3' => 'string',
                'poll_options.4' => 'string',
                'poll_options.5' => 'string',
                'poll_options.6' => 'string',
                'poll_options.7' => 'string',
                'poll_options.8' => 'string',
                'poll_options.9' => 'string',
                'poll_options.10' => 'string',
                'person_id' => 'required',
            ],
            [
                'poll_options.1.required' => 'The poll choice #1 field is required.',
                'poll_options.2.required' => 'The poll choice #2 field is required.',
            ]
        );

        if ($validator->fails()) {
            return Response::json(
                [
                    'code' => 422,
                    'errors' => $validator->messages(),
                ],
                422
            );
        }

        // Find room
        $room = SocialRoom::where('room_code', $roomCode)
            ->first();

        // Room not found. Wrong room code given
        if (empty($room)) {
            return Response::json(
                [
                    'code' => 404,
                    'message' => 'Room not found.',
                ],
                404
            );
        }

        // Create poll
        $poll = SocialRoomPoll::create(
            [
                'lt_social_room_id' => $room->id,
                'description' => $data['poll_description'],
                'status' => SocialRoomPoll::STATUS_OPEN,
                'created_by_person_id' => $data['person_id'],
            ]
        );

        // Create poll options
        $pollOptions = [];
        foreach ($data['poll_options'] as $optionDesc) {
            if (!empty($optionDesc)) {
                $pollOptions[] = new SocialRoomPollOption(
                    [
                        'description' => $optionDesc,
                        'created_by_person_id' => $data['person_id'],
                    ]
                );
            }
        }
        $poll->options()->saveMany($pollOptions);

        return Response::json(
            [
                'code' => 200,
                'poll' => $poll,
            ],
            200
        );
    }

    public function update(Request $request, $roomCode, $pollId)
    {
        $data = $request->only(['poll_description', 'poll_options', 'person_id', 'status']);
        $validator = Validator::make(
            $data,
            [
                'poll_description' => 'required_without_all:status|string',
                'poll_options' => 'required_without_all:status|array',
                'poll_options.1' => 'required_without_all:status|string',
                'poll_options.2' => 'required_without_all:status|string',
                'poll_options.3' => 'string',
                'poll_options.4' => 'string',
                'poll_options.5' => 'string',
                'poll_options.6' => 'string',
                'poll_options.7' => 'string',
                'poll_options.8' => 'string',
                'poll_options.9' => 'string',
                'poll_options.10' => 'string',
                'status' => 'required_without_all:poll_description,poll_options|string',
                'person_id' => 'required',
            ],
            [
                'poll_options.1.required' => 'The poll choice #1 field is required.',
                'poll_options.2.required' => 'The poll choice #2 field is required.',
            ]
        );

        if ($validator->fails()) {
            return Response::json(
                [
                    'code' => 422,
                    'errors' => $validator->messages(),
                ],
                422
            );
        }

        // Find poll
        $poll = SocialRoomPoll::where('id', $pollId)
            ->whereHas(
                'room',
                function ($query) use ($roomCode) {
                    $query->where('room_code', $roomCode);
                }
            )
            ->with(['options'])
            ->first();

        // Room not found. Wrong room code given
        if (empty($poll)) {
            return Response::json(
                [
                    'code' => 404,
                    'message' => 'Poll not found.',
                ],
                404
            );
        }

        if (!empty($data['status'])) {
            $this->closePoll($poll, $data['person_id']);
        } else {
            // Update poll description if needed
            $updatePollData = [
                'last_updated_by_person_id' => $data['person_id'],
            ];

            if ($poll->description != $data['poll_description']) {
                $updatePollData['description'] = $data['poll_description'];
            }

            $poll->update($updatePollData);

            // Check if options need to be updated/inserted
            $pollOptions = [];
            foreach ($data['poll_options'] as $key => $optionDesc) {
                if (!empty($optionDesc)) {
                    if (
                        !empty($poll->options[$key - 1])
                        && $optionDesc != $poll->options[$key - 1]->description
                    ) {
                        // Update
                        $poll->options[$key - 1]->update(
                            [
                                'description' => $optionDesc,
                                'last_updated_by_person_id' => $data['person_id'],
                            ]
                        );
                    } else {
                        if (empty($poll->options[$key - 1])) {
                            // insert
                            $pollOptions[] = new SocialRoomPollOption(
                                [
                                    'description' => $optionDesc,
                                    'created_by_person_id' => $data['person_id'],
                                ]
                            );
                        }
                    }
                } else {
                    if (!empty($poll->options[$key - 1])) {
                        // delete option
                        $poll->options[$key - 1]->delete();
                    }
                }
            }

            $poll->options()->saveMany($pollOptions);
        }

        return Response::json(
            [
                'code' => 200,
                'poll' => $poll,
            ],
            200
        );
    }

    /**
     * CLoses a poll and creates a poll-type message record
     *
     * @param  SocialRoomPoll $poll
     * @param  String         $updaterPersonId
     * @return void
     */
    protected function closePoll($poll, $updaterPersonId)
    {
        $poll->update(
            [
                'status' => 'closed',
                'closed_at' => Carbon::now(),
                'last_updated_by_person_id' => $updaterPersonId,
            ]
        );

        // Create message record for the poll
        SocialRoomMessage::create(
            [
                'lt_social_room_id' => $poll->room->id,
                'lt_social_room_poll_id' => $poll->id,
                'parent_id' => 0,
                'message' => 'Poll Closed',
                'user_id' => $updaterPersonId,
            ]
        );
    }

    public function delete(Request $request, $roomCode, $pollId)
    {
        $personId = $request->get('person_id');
        // Find poll
        $poll = SocialRoomPoll::where('id', $pollId)
            ->whereHas(
                'room',
                function ($query) use ($roomCode) {
                    $query->where('room_code', $roomCode);
                }
            )
            ->with(['options'])
            ->first();

        // Room not found. Wrong room code given
        if (empty($poll)) {
            return Response::json(
                [
                    'code' => 404,
                    'message' => 'Poll not found.',
                ],
                404
            );
        }

        // Record last person to update the poll
        if (!empty($personId)) {
            $poll->update(
                [
                    'last_updated_by_person_id' => $personId,
                ]
            );
        }

        // Delete votes
        SocialRoomPollVote::where('lt_social_room_poll_id', $poll->id)
            ->delete();

        // Delete options
        SocialRoomPollOption::where('lt_social_room_poll_id', $poll->id)
            ->delete();

        // Delete poll
        $poll->delete();

        return Response::json(
            [
                'code' => 200,
                'message' => 'Poll deleted',
            ],
            200
        );
    }
}
