<?php


namespace App\Http\Controllers\Api\V1\LetsTalk;

use Carbon\Carbon;
use App\Models\Cms;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\LetsTalk\SocialRoom;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Cache;
use App\Models\LetsTalk\SocialRoomPin;
use App\Models\LetsTalk\SocialRoomType;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use App\Models\LetsTalk\UpcomingSchedule;
use App\Models\LetsTalk\PasswordlessToken;
use App\Models\LetsTalk\SocialRoomSchedule;
use App\Models\LetsTalk\VideoCallParticipant;
use App\Models\LetsTalk\SocialRoomParticipant;
use App\Models\LetsTalk\CmsLibertyRepresentative;
use App\Models\LetsTalk\SocialRoomScheduleUpdate;
use App\Models\LetsTalk\SocialRoomSmsNotification;
use App\Models\LetsTalk\SocialRoomEmailNotification;
use App\Models\LetsTalk\SocialRoomRedDotNotification;

class SocialRoomController extends BaseController
{

    public function getSocialSpaceRooms(Request $request)
    {
        $categories = [];
        $socialRooms = SocialRoomType::get();
        $socialWorkSpaceNames = SocialRoomType::groupBy('social_space_name')
            ->get();

        $socialRoomParticipants = SocialRoom::where('status', 'approved')
            ->where('is_community', 0)->get();

        $id = $request->get('person_id');

        $pins = SocialRoomPin::where('person_id', $id)
            ->with('socialRoom')
            ->whereHas(
                'socialRoom',
                function ($query) {
                    $query->where('is_community', 1);
                }
            )
            ->get();

        $pinnedRoomNames = array_map(
            function ($pin) {
                return $pin->social_room->name;
            },
            json_decode($pins)
        );

        $categories = array_merge($categories, $pinnedRoomNames);

        return Response::json(
            [
                'status' => 'success',
                'response' => [
                    'socialRooms' => $socialRooms,
                    'socialWorkSpaceNames' => $socialWorkSpaceNames,
                    'personCategories' => array_merge($categories, Cache::get('globalCategoryNames', [])),
                ],
                'message' => null,
            ]
        );
    }

    public function getSocialRooms(Request $request)
    {
        $data = $request->all();
        $socialRooms = SocialRoom::with('socialRoomType')
            ->where('lt_social_room_type_id', $data['social_room_type_id'])
            ->where('is_community', 0)
            ->where('status', 'approved')
            ->with(
                [
                    'socialRoomParticipants' => function ($query) {
                        $query->whereRaw('(joined_room_at > left_room_at) OR (joined_room_at IS NOT NULL AND left_room_at IS NULL)');
                    },
                ]
            )->get();

        $socialRooms = SocialRoom::setCmsData($socialRooms, true, 'socialRoomParticipants');

        // $cms = CmsLibertyRepresentative::fetchAllFromCacheOrCms();

        // // Assign CMS data
        // foreach ($socialRooms as $key => $socialRoomParticipant) {
        //     $room_id = $socialRoomParticipant['id'];
        //     foreach ($socialRoomParticipant->social_room_participants as $key2 => $participant) {
        //         $socialRoomParticipant->social_room_participants[$key2]->cms = (array) $cms[$participant->person_id];
        //     }
        // }

        return Response::json(
            [
                'status' => 'success',
                'response' => [
                    'socialRooms' => $socialRooms,
                ],
                'message' => null,
            ]
        );
    }

    public function joinRoom(Request $request)
    {
        $data = $request->all();


        // Check if existing user
        $participant_to_room_exist = SocialRoomParticipant::where('lt_social_room_id', $data['lt_social_room_id'])
            ->where('person_id', $data['person_id'])
            ->exists();

        if (!$participant_to_room_exist) {
            $cms = CmsLibertyRepresentative::fetchAllFromCacheOrCms();
            $fetchOldUser = SocialRoomParticipant::where('person_id', $data['person_id'])->first();
            $user_name = "";
            if (!empty($fetchOldUser)) {
                $user_name = $fetchOldUser->user_name;
                $business = (isset($fetchOldUser->business) && !empty($fetchOldUser->business))
                    ? $fetchOldUser->business
                    : 'lsm';
            } else {
                $user_name = isset($cms[$data['person_id']]['user_name'])
                    ? $cms[$data['person_id']]['user_name']
                    : "unknown-user";
                $business = isset($cms[$data['person_id']]['business'])
                    ? $cms[$data['person_id']]['business']
                    : "lsm";
            }

            $user_code = md5(Hash::make(substr(Str::uuid()->toString(), 0, 4)));

            SocialRoomParticipant::create(
                [
                    'lt_social_room_id' => $data['lt_social_room_id'],
                    'person_id' => $data['person_id'],
                    'user_code' => $user_code,
                    'role' => $data['role'],
                    'user_name' => $user_name,
                    'business' => $business,
                ]
            );
        } else {

            $user_code = SocialRoomParticipant::where('lt_social_room_id', $data['lt_social_room_id'])
                ->where('person_id', $data['person_id'])
                ->first();

            $user_code = $user_code->user_code;
        }

        $room_code = SocialRoom::where('id', $data['lt_social_room_id'])->first();

        return Response::json(
            [
                'status' => 'success',
                'response' => [
                    'user_code' => $user_code,
                    'room_code' => $room_code->room_code,
                ],
                'message' => null,
            ]
        );
    }

    public function roomCreation(Request $request)
    {
        $data = $request->all();

        //temporary policy auto approve code block. TODO: Cleanup code
        $cyberVrPersonId = "5d36de5bbf3b21131941e7e2";
        $cyberVrTempId = "640aec823123993dcd5e1a02";
        $cyberVirtualRoom = "Cyber Virtual Room";
        if (in_array($data['created_by_id'], config('app.cyber_virtual_rooms.can_access'))) {
            $cms = CmsLibertyRepresentative::fetchAllFromCacheOrCms();
            $created_by_name = $cms[$cyberVrTempId];

            $startDate = Carbon::parse($data['start_date'])->format('Y-m-d H:i:s');
            $endDate   = Carbon::parse($data['end_date'])->format('Y-m-d H:i:s');
            if (isset($data['current_user_timezone']) && $data['current_user_timezone'] !== 'Europe/London') {
                $startDate = Carbon::parse($data['start_date'], $created_by_name['office_timezone'])->setTimezone("Europe/London")->format('Y-m-d H:i:s');
                $endDate   = Carbon::parse($data['end_date'], $created_by_name['office_timezone'])->setTimezone("Europe/London")->format('Y-m-d H:i:s');
            }

            $data['start_date'] = $startDate;
            $data['end_date']   = $endDate;

            $data['valid_until'] = SocialRoomSchedule::getValidUntil($data);
            // $data['room_code'] = md5($data['name']);

            $room_code = SocialRoom::roomCodeGenerator();
            $social_room = SocialRoom::create([
                'lt_social_room_type_id' => 3,
                'name' => $data['room_name_theme_rooms'],
                'description' => $data['room_theme_description'] . " " . $cyberVirtualRoom,
                'status' => 'approved',
                'room_code' => $room_code,
                'created_by_id' => $data['created_by_id'],
                'published_by' => $cyberVrTempId,
                'published_at' => Carbon::now()->toDateTimeString()
            ]);

            SocialRoomScheduleUpdate::create([
                'room_id' => $social_room->id,
                'room_type' => 3,
                'room_code' => $room_code
            ]);

            $data['lt_social_room_id'] = $social_room->id;

            $socialRoomSchedule = SocialRoomSchedule::create($data);
            $social_room->socialRoomSchedule()->save($socialRoomSchedule);
            if ($social_room) {
                return Response::json([
                    'status' => 'success',
                    'data' => $social_room
                ]);
            }
        }


        //get moderators
        $created_by_name = '';
        $cms = CmsLibertyRepresentative::fetchAllFromCacheOrCms();
        $get_moderators = json_decode(Cms::get('workspaces/' . $this->getCmsConfig('lets_talk_workspace') . '/content-types/' . $this->getCmsConfig('theme_room_moderators_content_type') . '/content-entries/' . $this->getCmsConfig('theme_room_moderators_content_entry')));
        if ($get_moderators) { //get moderator details
            $moderators_id = $get_moderators->{$this->getCmsConfig('theme_room_moderators_selected_representative')};
            $moderator_details = [];
            $ctr = 0;
            foreach ($moderators_id as $key => $value) {
                foreach ($value as $id) {
                    if (isset($cms[$id])) {
                        $moderator_details[] = $cms[$id];
                        $moderator_details[$ctr]['id'] = $id;
                        $ctr++;
                    }
                }
            }
        }
        $data['end_after'] = $data['end_after_frequency'] . ' ' . $data['end_after_frequency_type'];
        $created_by_name = $cms[$data['created_by_id']];
        $social_room_exist = SocialRoom::where('name', $data['room_name_theme_rooms'])->where(
            'lt_social_room_type_id',
            3
        )->first();

        if (!empty($social_room_exist)) {
            return Response::json(
                [
                    'status' => 'failed',
                    'message' => 'Room already exist',
                ]
            );
        }

        // Convert start_date & end_date to London tz from creator tz
        $data['start_date'] = Carbon::parse(
            $data['start_date'],
            $created_by_name['office_timezone']
        )->setTimezone("Europe/London")->format('Y-m-d H:i:s');
        $data['end_date'] = Carbon::parse(
            $data['end_date'],
            $created_by_name['office_timezone']
        )->setTimezone("Europe/London")->format('Y-m-d H:i:s');

        $data['valid_until'] = SocialRoomSchedule::getValidUntil($data);
        // $data['room_code'] = md5($data['name']);

        $room_code = SocialRoom::roomCodeGenerator();

        $social_room = SocialRoom::create(
            [
                'lt_social_room_type_id' => 3,
                'name' => $data['room_name_theme_rooms'],
                'description' => $data['room_theme_description'],
                'status' => 'pending',
                'room_code' => $room_code,
                'created_by_id' => $data['created_by_id'],
            ]
        );

        SocialRoomScheduleUpdate::create(
            [
                'room_id' => $social_room->id,
                'room_type' => 3,
                'room_code' => $room_code,
            ]
        );

        $socialRoomSchedule = new SocialRoomSchedule($data);
        $social_room->socialRoomSchedule()->save($socialRoomSchedule);

        if ($social_room) {

            SocialRoomEmailNotification::themeRoomTemplate6(
                $social_room,
                $moderator_details,
                $created_by_name['name'],
                $data['duration_type'],
                $socialRoomSchedule
            );

            return Response::json(
                [
                    'status' => 'success',
                    'data' => $social_room,
                ]
            );
        }

        return Response::json(
            [
                'status' => 'failed',
            ]
        );
    }

    private function getCmsConfig($config)
    {
        return config('app.cms.' . 'lsm' . '.' . $config);
    }

    public function roomApproval($moderator_id, $room_id, $approval_type)
    {

        //get moderators
        $created_by_name = '';
        $cms = CmsLibertyRepresentative::fetchAllFromCacheOrCms();
        $get_moderators = json_decode(Cms::get('workspaces/' . $this->getCmsConfig('lets_talk_workspace') . '/content-types/' . $this->getCmsConfig('theme_room_moderators_content_type') . '/content-entries/' . $this->getCmsConfig('theme_room_moderators_content_entry')));
        if ($get_moderators) { //get moderator details
            $moderators_id = $get_moderators->{$this->getCmsConfig('theme_room_moderators_selected_representative')};
            $moderator_details = [];
            $ctr = 0;
            foreach ($moderators_id as $key => $value) {
                foreach ($value as $id) {
                    if (isset($cms[$id])) {
                        $moderator_details[] = $cms[$id];
                        $moderator_details[$ctr]['id'] = $id;
                        $ctr++;
                    }
                }
            }
        }

        $social_room = SocialRoom::with(['socialRoomSchedule'])->where('id', $room_id)->first();

        $approved_by = $cms[$moderator_id];
        $created_by_name = $cms[$social_room->created_by_id];
        if ($social_room && $social_room->published_by == null) {
            $social_room->status = $approval_type;
            $social_room->published_at = Carbon::now()->toDateTimeString();
            $social_room->published_by = $moderator_id;
            $social_room->save();

            //Pin room
            SocialRoomPin::create(
                [
                    'person_id' => $social_room->created_by_id,
                    'lt_social_room_id' => $social_room->id,
                ]
            );

            // Store red dot notification
            // Collect all possible members of VR-Socials
            $persons_to_be_notified = PasswordlessToken::groupBy('person_id')->get();

            foreach ($persons_to_be_notified as $value) {
                SocialRoomRedDotNotification::create(
                    [
                        'person_id' => $value->person_id,
                        'room_id' => $social_room->id,
                        'room_type_id' => $social_room->lt_social_room_type_id,
                        'is_visited' => 0,
                    ]
                );
            }

            SocialRoomEmailNotification::themeRoomTemplate7(
                $social_room,
                $created_by_name,
                $approved_by['name'],
                $room_id,
                $approval_type,
                $created_by_name['name']
            );

            return Response::json(
                [
                    'status' => 'success',
                    'approval_type' => $social_room->status,
                    'data' => $social_room,
                ]
            );
        }

        return Response::json(
            [
                'status' => 'success',
                'message' => 'This theme room has already been ' . $social_room->status . ' by your colleague.',
                'approval_type' => $social_room->status,
                'has_been_updated' => 1,
            ]
        );
    }

    public function updateParticipantStatus($status, $user_code, $room_code, $room_type)
    {
        $readableStatus = "";
        if ($room_type == 'social') {
            $participant = SocialRoom::with(
                [
                    'socialRoomParticipants' => function ($query) use ($user_code) {
                        $query->where('user_code', $user_code);
                    },
                ]
            )->whereHas(
                'socialRoomParticipants',
                function ($query) use ($user_code) {
                    $query->where('user_code', $user_code);
                }
            )->first();

            if ($participant && $status == 'join' && sizeof($participant->socialRoomParticipants) > 0) {
                $participant->socialRoomParticipants->each(
                    function ($set) {
                        $set->update(['joined_room_at' => Carbon::now()->toDateTimeString()]);
                    }
                );
                $readableStatus = 'joined';
            } else {
                if ($participant && $status == 'leave' && sizeof($participant->socialRoomParticipants) > 0) {
                    $participant->socialRoomParticipants->each(
                        function ($set) {
                            $set->update(['left_room_at' => Carbon::now()->toDateTimeString()]);
                        }
                    );
                    $readableStatus = 'left';
                }
            }

            if (!$participant) {
                return Response::json(
                    [
                        'status' => 'failed',
                        'data' => '',
                        'message' => 'Cannot find a room',
                    ]
                );
            }
            return Response::json(
                [
                    'status' => 'success',
                    'message' => 'User has ' . $readableStatus . 'the room',
                ]
            );
        }

        //for public room
        $participant = VideoCallParticipant::where('user_code', $user_code)->with('resourceBookings')->first();
        if ($participant->role == "liberty_representative") {
            if ($participant && $status == 'join' && !empty($participant->resourceBookings)) {
                $participant->resourceBookings->is_in_room = 1;
                $participant->push();
            } else {
                if ($participant && $status == 'leave' && !empty($participant->resourceBookings)) {
                    $participant->resourceBookings->is_in_room = 0;
                    $participant->push();
                }
            }
        }

        if (!$participant) {
            return Response::json(
                [
                    'status' => 'failed',
                    'data' => '',
                    'message' => 'Cannot find a room',
                ]
            );
        }
        return Response::json(
            [
                'status' => 'success',
                'message' => 'User has ' . $readableStatus . 'the room',
            ]
        );
    }

    public function updateParticipantLastActivity($user_code, $room_code)
    {
        $socialRoom = SocialRoom::where('room_code', $room_code)->with(
            [
                'socialRoomParticipants' => function ($query) use ($user_code) {
                    $query->where('user_code', $user_code);
                },
            ]
        )
            ->whereHas(
                'socialRoomParticipants',
                function ($query) use ($user_code) {
                    $query->where('user_code', $user_code);
                }
            )->first();

        if (!$socialRoom) {
            return Response::json(
                [
                    'status' => 'failed',
                    'data' => '',
                    'message' => 'Cannot find a room',
                ]
            );
        }

        $participant = $socialRoom->socialRoomParticipants->first();

        $now = Carbon::now();
        $leftRoomAt = $participant->left_room_at;
        if ($leftRoomAt && Carbon::parse($leftRoomAt)->diffInMinutes($now) >= 5) {
            // $participant->left_room_at = null;
            $participant->joined_room_at = $now;
        }

        $participant->last_activity_at = $now;
        $participant->push();

        return Response::json(
            [
                'status' => 'success',
                'message' => 'User last activity has been updated.',
            ]
        );
    }

    public function chatWithMe(Request $request)
    {
        $data = $request->all();
        $room_code = SocialRoom::roomCodeGenerator();

        // Create Room
        $socialRoom = SocialRoom::create(
            [
                'lt_social_room_type_id' => $data['lt_social_room_type_id'],
                'created_by_id' => $data['created_by_id'],
                'name' => 'Casual Room',
                'room_code' => $room_code,
                'status' => 'approved',
                'published_at' => date('Y-m-d H:i:s'),
            ]
        );

        $cms = CmsLibertyRepresentative::fetchAllFromCacheOrCms();

        // Create Participants (Room Creator)
        $room_creator = SocialRoomParticipant::create(
            [
                'lt_social_room_id' => $socialRoom->id,
                'user_name' => $cms[$data['created_by_id']]['name'],
                'role' => 'virtual-rooms',
                'person_id' => $data['created_by_id'],
                'user_code' => SocialRoomParticipant::roomCodeGenerator(),
                'joined_room_at' => date("Y-m-d H:i:s"),
                'business' => $cms[$data['created_by_id']]['business'],
            ]
        );

        // Create Participants (Invited Representative)
        $available_water_cooler_user = SocialRoomParticipant::create(
            [
                'lt_social_room_id' => $socialRoom->id,
                'user_name' => $cms[$data['person_id']]['name'],
                'role' => 'virtual-rooms',
                'person_id' => $data['person_id'],
                'user_code' => SocialRoomParticipant::roomCodeGenerator(),
                'joined_room_at' => date("Y-m-d H:i:s"),
                'business' => $cms[$data['person_id']]['business'],
            ]
        );

        // Build needed parameters
        $params['invited_water_cooler_user'] = $available_water_cooler_user->user_name;
        $params['invited_water_cooler_email'] = $cms[$data['person_id']]['email'];
        $params['invited_water_cooler_user_generated_link'] = SocialRoomParticipant::generateWaitingRoomCommonLink($room_code);
        $params['caller_user'] = $room_creator->user_name;
        $params['caller_email'] = $cms[$data['created_by_id']]['name'];

        // Build Link and send email
        SocialRoomEmailNotification::waterCoolerTemplate8($params);

        $sendSms = new SocialRoomSmsNotification;
        $sendSms->sendMessageWaterCoolerTemplate8(
            $socialRoom,
            $cms[$data['person_id']],
            $cms[$data['created_by_id']],
            $available_water_cooler_user
        );

        return Response::json(
            [
                'status' => 'success',
                'response' => [
                    'user_code' => $room_creator->user_code,
                    'room_code' => $socialRoom->room_code,
                ],
                'message' => null,
            ]
        );
    }

    public function getSocialRoomType($room_name)
    {

        $socialRoomType = SocialRoomType::where('name', $room_name)->first();
        if ($socialRoomType) {
            return Response::json(
                [
                    'response' => 'success',
                    'data' => $socialRoomType,
                ]
            );
        }
        return Response::json(
            [
                'response' => 'failed',
                'message' => 'Room type not found',
            ]
        );
    }

    public function deleteSchedule(Request $request)
    {
        $data = $request->all();
        $room_id = $data['room_id'];
        $social_room_schedule = SocialRoomSchedule::where('lt_social_room_id', $room_id)->first();
        if ($social_room_schedule) {
            $social_room_schedule->delete();
        }
    }

    public function getParticipants($userCode, $roomCode)
    {
        $socialRoom = SocialRoom::where('room_code', $roomCode)
            ->with('socialRoomParticipants')
            ->first();

        if (!$socialRoom) {
            return Response::json(
                [
                    'status' => 'failed',
                    'data' => '',
                    'message' => 'Cannot find a room',
                ]
            );
        }

        SocialRoom::setCmsData([$socialRoom], false, 'socialRoomParticipants');

        return Response::json(
            [
                'status' => 'success',
                'participants' => $socialRoom->socialRoomParticipants,
            ]
        );
    }

    public function archiveRooms(Request $request, $roomTypeId)
    {
        $personId = $request->get('person_id'); //creator
        $carbonNow = Carbon::now();
        $isSltRoom = $roomTypeId == 6;

        $query = SocialRoom::where('lt_social_room_type_id', $roomTypeId)
            ->whereDoesntHave('socialRoomSchedule')
            ->with('expiredSocialRoomSchedule')
            ->with('deletedScheduleEntries');

        if ($isSltRoom) {
            if ($request->get('with_person_id')) {
                $query->where('created_by_id', $request->get('with_person_id'));
            }
        } else {
            $query->whereHas(
                'socialRoomParticipants',
                function ($query) use ($personId) {
                    $query->where('person_id', '=', $personId);
                }
            )
                ->with(
                    [
                        'socialRoomParticipants' => function ($query) use ($personId) {
                            $query->where('person_id', '!=', $personId);
                        },
                    ]
                );
        }

        $withPersonCms = [];
        if ($request->has('with_person_id')) {
            $withPersonId = $request->get('with_person_id');

            if (!$isSltRoom) {
                $query->whereHas(
                    'socialRoomParticipants',
                    function ($query) use ($personId, $withPersonId) {
                        $query->where('person_id', '=', $withPersonId);
                    }
                );
            }

            $libReps = CmsLibertyRepresentative::fetchAllFromCacheOrCms();
            if (isset($libReps[$withPersonId])) {
                $withPersonCms = $libReps[$withPersonId];
            }
        }

        $socialRooms = $query->get()->sortByDesc('expiredSocialRoomSchedule.start_date');

        $socialRooms = SocialRoom::setCmsData($socialRooms, false, 'socialRoomParticipants');

        $libReps = CmsLibertyRepresentative::fetchAllFromCacheOrCms();
        $authUserCms = isset($libReps[$personId])
            ? $libReps[$personId]
            : [];
        $authUserTz = isset($authUserCms['office_timezone'])
            ? $authUserCms['office_timezone']
            : "Europe/London";

        foreach ($socialRooms as $key => $socialRoom) {
            if (!$socialRoom->expiredSocialRoomSchedule) {
                continue;
            }
            $socialRoom->frequency_for_ui = UpcomingSchedule::getFrequencyForUi(
                $socialRoom->expiredSocialRoomSchedule,
                $authUserTz
            );
        }
        // $tmpData = UpcomingSchedule::generate($socialRooms, $this->authUserCms);
        // $upcomingSchedules = $tmpData['upcomingSchedules'];
        // $rooms = $tmpData['rooms'];

        return Response::json(
            [
                'response' => 'success',
                'rooms' => $socialRooms,
                'with_person_cms' => $withPersonCms,
            ]
        );
    }

    public function getSocialRoomDetails($roomCode)
    {
        $room = SocialRoom::where('room_code', $roomCode)->first();

        if (empty($room)) {
            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'Social room not found!',
                ],
                404
            );
        } else {
            return Response::json(
                [
                    'response' => 'success',
                    'data' => [
                        'room' => $room,
                    ],
                ]
            );
        }
    }
}