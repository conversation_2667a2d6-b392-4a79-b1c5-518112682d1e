<?php

namespace App\Http\Controllers\Api\V1\LetsTalk;

use Carbon\Carbon;
use App\Models\Mailqueue;
use Illuminate\Http\Request;
use App\Models\LetsTalk\SocialRoom;
use Illuminate\Support\Facades\Log;
use App\Models\LetsTalk\SocialRoomPin;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use App\Models\LetsTalk\SocialRoomMessage;
use App\Models\LetsTalk\SocialRoomMessageFile;
use App\Models\LetsTalk\SocialRoomMessageLike;
use App\Models\LetsTalk\SocialRoomParticipant;
use App\Models\LetsTalk\SocialRoomMessageFileUpload;
use App\Models\LetsTalk\SocialRoomRedDotNotification;

class SocialRoomMessageController extends BaseController
{

    public function __construct(
        SocialRoomMessageFileUpload $fileupload,
        Mailqueue $mailqueue
    ) {
        $this->files = $fileupload;
        $this->mail = $mailqueue;
    }

    public function show($id)
    {
        $message = SocialRoomMessage::with(['files', 'allReplies', 'parent'])
            ->withTrashed()
            ->find($id);


        if ($message->id) {

            $response = [
                'response' => 'success',
                'message' => 'Succeeded to update the message',
                'data' => $message,
            ];
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Failed to update the message',
            ];
        }

        return Response::json($response);
    }

    /**
     * Store new Message
     */

    public function store(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'message' => 'required',
                'user_id' => 'required',
            ]
        );

        if ($validator->fails()) {
            return [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        }

        $personId = $request->get('user_id', null);

        $participant = SocialRoomParticipant::where('person_id', $personId)->first();

        if (isset($participant)) {
            $isCustomer = $participant->role == 'external-guest';
            if ($isCustomer) {
                $email = $participant->external_user_email;
                $customer = SocialRoomParticipant::where('external_user_email', $email)
                    ->where('lt_social_room_id', $request->get('room', null))
                    ->first();
                $personId = $customer->person_id;
            }
        } else {
            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'Unable to find a participant',
                ],
                500
            );
        }

        $data_message = $request->get('message', null);
        $room_id = $request->get('room', null);
        $commenters_name = $request->get('user_name', null);

        $this->notifyMentionedRepresentative($data_message, $room_id, $commenters_name);

        $data = [
            'message' => $request->get('message', null),
            'user_id'           => $personId,
            'lt_social_room_id' => $request->get('room', null),
            'parent_id'         => $request->get('message_reply_id', null),
            'created_at'        => now(),
            'updated_at'        => now(),
        ];
        Log::info($data);
        $message = SocialRoomMessage::create($data);

        // Identify who currently pinned the room
        $persons_to_notify = SocialRoomPin::where('lt_social_room_id', $request->get('room', null))->get();

        if (count($persons_to_notify) > 0) {
            foreach ($persons_to_notify as $person_to_notify) {
                $person_id = $person_to_notify->person_id;
                SocialRoomRedDotNotification::insert(
                    [
                        'person_id'    => $person_id,
                        'room_id'      => $request->get('room', null),
                        'room_type_id' => 0, // Set default value
                        'is_visited'   => SocialRoomRedDotNotification::IS_VISIT_STATUS_NEW_MESSAGE,
                        'created_at'   => now(),
                        'updated_at'   => now(),
                    ]
                );
            }
        } else {
            $persons_to_notify = SocialRoomParticipant::where('lt_social_room_id', $request->get('room', null))->get();
            foreach ($persons_to_notify as $person_to_notify) {
                $person_id = $person_to_notify->person_id;
                SocialRoomRedDotNotification::insert(
                    [
                        'person_id'    => $person_id,
                        'room_id'      => $request->get('room', null),
                        'room_type_id' => 0, // Set default value
                        'is_visited'   => SocialRoomRedDotNotification::IS_VISIT_STATUS_NEW_MESSAGE,
                        'created_at'   => now(),
                        'updated_at'   => now(),
                    ]
                );
            }
        }

        if (!empty($message)) {

            $response = [
                'response' => 'success',
                'message' => 'Succeeded to create the message',
                'id' => $message->id,
            ];
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Failed to create the message',
            ];
        }

        return Response::json($response);
    }

    private function notifyMentionedRepresentative($data_message = null, $room_id, $commenters_name)
    {
        $admin_domain = config('app.admin_frontend');

        if ($data_message) {
            $emails = explode(':email:', $data_message);
            $room = SocialRoom::where('id', $room_id)->first();
            foreach ($emails as $email) {
                $email = explode("|", $email);
                if (isset($email[0]) && !empty($email[0]) && filter_var($email[0], FILTER_VALIDATE_EMAIL)) {
                    $this->mail->queue(
                        $email[0],
                        $commenters_name . " mentioned you in " . $room->name,
                        $commenters_name . " mentioned you in " . $room->name,
                        "emails.lets-talk.notification_mention",
                        [
                            'theme_room_link' => $admin_domain . '/virtual-rooms/theme-rooms/' . $room->room_code,
                            'theme_room_name' => $room->name,
                            'liberty_representative_recipient' => $email[1],
                            'commenters_name' => $commenters_name,
                        ],
                        null
                    );
                }
            }
        }
    }

    /**
     * Update Message
     */

    public function update(Request $request, $id)
    {
        if (!empty($id)) {

            $validator = Validator::make(
                $request->all(),
                [
                    'message' => 'required',
                    'user_id' => 'required',
                ]
            );
            if ($validator->fails()) {
                return [
                    'response' => 'error',
                    'errors' => $validator->messages()->toArray(),
                ];
            }


            $message = SocialRoomMessage::find($id);


            if ($message->id) {
                $commenters_name = $request->get('user_name', null);
                $this->notifyMentionedRepresentative(
                    $request->get('message', null),
                    $message->lt_social_room_id,
                    $commenters_name
                );
                $message->message = $request->get('message', null);
                $message->save();

                $response = [
                    'response' => 'success',
                    'message' => 'Succeeded to update the message',
                    'id' => $message->id,
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'Failed to update the message',
                ];
            }

            return Response::json($response);
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid message ID',
            ];
        }


        return Response::json($response);
    }

    public function like(Request $request, $id)
    {
        $data = [
            'message_id' => $id,
            'user_id' => $request->get('user_id'),
        ];

        $like = SocialRoomMessageLike::withTrashed()->where($data);
        if (empty($like->first())) {
            SocialRoomMessageLike::create($data);
        } else {
            if (!empty($like->first()->deleted_at)) {
                $like->restore();
            } else {
                $like->delete();
            }
        }
        unset($data['user_id']);
        $likes = SocialRoomMessageLike::where($data)->get()->count();

        return Response::json($likes);
    }

    /**
     * Delete Message
     *
     * @param $id
     *
     * @return mixed
     */
    public function delete($id)
    {
        if ($message = SocialRoomMessage::find($id)) {
            if ($message->delete()) {
                SocialRoomMessageFile::where('message_id', $id)->delete();
                $response = [
                    'response' => 'success',
                    'message' => 'The specified Liberty Forum Message was successfully deleted',
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message' => 'The specified Liberty Forum Message could not be deleted',
                ];
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'The specified Liberty Forum Message could not be found',
            ];
        }

        return Response::json($response);
    }

    /**
     * Get All Messages
     */
    public function index(Request $request)
    {

        $room = $request->get('room', null);
        $includePolls = $request->get('include_polls', false);

        $query = SocialRoomMessage::with(['files', 'allReplies'])
            ->where('lt_social_room_id', $room)
            ->where('parent_id', 0)
            ->orderBy('updated_at', 'desc')
            ->withTrashed();

        if (!empty($includePolls)) {
            $query->with(['poll.votes', 'poll.options.votes']);
        }

        $messages = $query->get();

        $data['messages'] = $messages;

        return Response::json(
            [
                'response' => 'success',
                'data' => $data,
            ]
        );
    }

    private function resolveDateTime(Request $request)
    {
        $dateTime = null;
        if (!empty($request->get('date'))) {
            $date = date("Y-m-d", strtotime($request->get('date')));
            $time = !empty($request->get('time'))
                ? date("H:i:s", strtotime($request->get('time')))
                : "00:00:00";
            $dateTime = Carbon::createFromFormat("Y-m-d H:i:s", "$date $time")->toDateTimeString();
        }

        return $dateTime;
    }
}
