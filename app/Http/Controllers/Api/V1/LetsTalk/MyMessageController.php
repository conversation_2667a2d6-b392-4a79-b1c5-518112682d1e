<?php

namespace App\Http\Controllers\Api\V1\LetsTalk;

use Aloha\Twilio\Twilio;
use App\Http\Controllers\BaseController;
use Carbon\Carbon;
use App\Models\LetsTalk\VideoRecordMessage;
use App\Models\LetsTalk\VideoRecordMessageRecipient;
use App\Models\Mailqueue;
use Illuminate\Support\Facades\Response;
use Twilio\Rest\Client;
use Illuminate\Http\Request;

class MyMessageController extends BaseController
{

    private $twilio;
    private $twilioClient;

    public function __construct(Mailqueue $mailqueue)
    {
        $twilioConfig = config('app.twilio.sms');
        $this->twilio = new Twilio($twilioConfig['sid'], $twilioConfig['token'], $twilioConfig['from']);
        $this->twilioClient = new Client($twilioConfig['sid'], $twilioConfig['token']);
        $this->mail = $mailqueue;
    }

    public function getCustomerMyMessages(Request $request)
    {
        $data = $request->all();
        $person_id = 'external';
        $creator_email = $data['email'];
        $recipient_email = $data['email'];
        $timezone = $data['timezone'];
        $page = (int)$data['selected_page'];

        $message_label = (isset($data['message_label']) && !empty($data['message_label']))
            ? $data['message_label']
            : false;
        $search = (isset($data['search']) && !empty($data['search']))
            ? $data['search']
            : false;

        // Query when searched
        if ($search) {
            $search = trim($search);
            $received = VideoRecordMessage::with(['videoRecordRecipients', 'author'])
                ->where('expiry_date', '>=', Carbon::now())
                ->where('cloudname', '<>', '')
                ->where('filename', '<>', '')
                ->where('email', '<>', $recipient_email)
                ->whereHas(
                    'videoRecordRecipients',
                    function ($query) use ($recipient_email, $search) {
                        return $query->where('email', $recipient_email);
                    }
                )
                ->where(
                    function ($query) use ($search, $recipient_email) {
                        return $query->where('email', 'like', "%" . $search . "%")
                            ->orWhere('name', 'like', "%" . $search . "%")
                            ->orWhereHas(
                                'videoRecordRecipients',
                                function ($query) use ($recipient_email, $search) {
                                    $query->where('email', '<>', $recipient_email);
                                    $query->where(
                                        function ($query) use ($recipient_email, $search) {
                                            return $query->where('email', 'like', "%" . $search . "%")
                                                ->orWhere('name', 'like', "%" . $search . "%");
                                        }
                                    );
                                }
                            );
                    }
                );

            if ($message_label) {
                $received = $received->where('message_label', $message_label);
            }

            $received = $received->get();


            $sent = VideoRecordMessage::with('videoRecordRecipients')
                ->where('expiry_date', '>=', Carbon::now())
                ->where('email', $recipient_email)
                ->where('cloudname', '<>', '')
                ->where('filename', '<>', '')
                ->where(
                    function ($query) use ($search, $recipient_email) {
                        return $query->where('email', 'like', "%" . $search . "%")
                            ->orWhere('name', 'like', "%" . $search . "%")
                            ->orWhereHas(
                                'videoRecordRecipients',
                                function ($query) use ($search, $recipient_email) {
                                    $query->where('email', '<>', $recipient_email);
                                    $query->where(
                                        function ($query) use ($recipient_email, $search) {
                                            return $query->where('email', 'like', "%" . $search . "%")
                                                ->orWhere('name', 'like', "%" . $search . "%");
                                        }
                                    );
                                }
                            );
                    }
                );


            if ($message_label) {
                $sent = $sent->where('message_label', $message_label);
            }
            $sent = $sent->get();

            $merged = $sent->merge($received);
            $data = $merged->all();
            $data = $this->paginate($data, $page);
            $page_count = $data;
        } else { // Query for All, Internal and Customer's inbox
            // Video creators data
            $creator = VideoRecordMessage::with('videoRecordRecipients')
                ->where('expiry_date', '>=', Carbon::now())
                ->where('person_id', $person_id)
                ->where('email', $creator_email)
                ->where('message_label', 'customer message')
                ->orderBy('id', 'DESC')
                ->get();

            // Video recipients data
            $recipients = VideoRecordMessage::with('videoRecordRecipients')
                ->where('expiry_date', '>=', Carbon::now())
                ->where('message_label', 'customer message')
                ->whereHas(
                    'videoRecordRecipients',
                    function ($query) use ($recipient_email) {
                        $query->where('email', $recipient_email);
                        $query->orderBy('id', 'DESC');
                    }
                )
                ->orderBy('id', 'DESC')
                ->get();

            $merged = $recipients->merge($creator);
            $data = $merged->all();
            $page_count = $data;
            $data = $this->paginate($data, $page);
        }

        $data = VideoRecordMessage::setCmsData($data, $timezone);

        return Response::json(
            [
                'status' => 'success',
                'response' => $data,
                'page_count' => ceil(count($page_count) / 10),
            ]
        );
    }

    private function paginate($data, $page = 0)
    {
        usort(
            $data,
            function ($a, $b) {
                return $a['id'] - $b['id'];
            }
        );
        rsort($data);
        $data = array_slice($data, ($page - 1) * 10, 10);
        return $data;
    }

    public function getMyMessageMembers(Request $request)
    {
        $data = $request->all();
        $video_record_message_id = isset($data['video_message_id']) && !empty($data['video_message_id'])
            ? $data['video_message_id']
            : false;

        if ($video_record_message_id) {
            $data = VideoRecordMessage::with('videoRecordRecipients')
                ->where('id', $video_record_message_id)
                ->get();

            $data = VideoRecordMessage::setCmsData($data->toArray());

            return Response::json(
                [
                    'status' => 'success',
                    'response' => $data,
                ]
            );
        }

        return Response::json(
            [
                'status' => 'fail',
                'response' => null,
                'message' => 'Invalid Request',
            ]
        );
    }

    public function getRedDotNotifications(Request $request)
    {
        $data = $request->all();
        $email = $data['email'];

        $data = VideoRecordMessageRecipient::select('video_record_message_id')
            ->where('email', $email)
            ->where('is_played', 0)
            ->get();

        $data = array_column($data->toArray(), 'video_record_message_id');

        return Response::json(
            [
                'status' => 'success',
                'response' => $data,
            ]
        );
    }

    public function updateNotification(Request $request)
    {
        $data = $request->all();
        $video_record_message_id = $data['video_record_message_id'];
        $email = $data['email'];

        VideoRecordMessageRecipient::where('email', $email)
            ->where('video_record_message_id', $video_record_message_id)
            ->update(
                ['is_played' => 1]
            );
    }
}
