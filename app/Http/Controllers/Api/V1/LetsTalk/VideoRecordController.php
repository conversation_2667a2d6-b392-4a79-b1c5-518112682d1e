<?php

namespace App\Http\Controllers\Api\V1\LetsTalk;

use Exception;
use Carbon\Carbon;
use Twilio\Rest\Client;
use Aloha\Twilio\Twilio;
use App\Models\Mailqueue;
use Illuminate\Http\Request;
use App\Models\LetsTalk\Helper;
use App\Models\LetsTalk\VideoRecord;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use App\Models\LetsTalk\VideoRecordToken;
use App\Models\LetsTalk\VideoRecordMessage;
use App\Models\LetsTalk\CmsLibertyRepresentative;
use App\Models\LetsTalk\VideoRecordMessageRecipient;
use App\Models\LetsTalk\VideoRecordEmailNotification;

class VideoRecordController extends BaseController
{

    const ENCRYPT_SALT = '54d9e81e919ab45c2ae6';
    private $twilio;
    private $twilioClient;
    private $twilioConfigUS;

    public function __construct(Mailqueue $mailqueue)
    {
        $twilioConfig = config('app.twilio.sms');
        $this->twilio = new Twilio($twilioConfig['sid'], $twilioConfig['token'], $twilioConfig['from']);
        $this->twilioConfigUS = new Twilio($twilioConfig['sid'], $twilioConfig['token'], $twilioConfig['from-us']);
        $this->twilioClient = new Client($twilioConfig['sid'], $twilioConfig['token']);
        $this->mail = $mailqueue;
    }


    public function token($token)
    {
        $data = VideoRecordToken::where('token', $token)->first();

        if (!$data || empty($data)) {
            return Response::json(
                [
                    'response' => 'fail',
                    'message' => 'Token is invalid or expired',
                ]
            );
        }
        return Response::json(
            [
                'response' => 'success',
                'data' => $data,
            ]
        );
    }

    public function videoUpload(Request $request)
    {
        $data = (object)$request->all();
        $cms = CmsLibertyRepresentative::fetchAllFromCacheOrCms();
        $deleted_recipients = [];
        $personId = "";
        $hasExternal = false;
        $business = isset($data->business)
            ? $data->business
            : 'lsm';
        $isExternal = isset($data->person_id) && $data->person_id === 'external';
        $replyType = (isset($data->reply) && !empty($data->reply))
            ? $data->reply
            : false;

        // To be used, when an external/public user click "Record Message" in a public view video message page to create video message
        $creatorRecipientId = (isset($data->creator_recipient_id) && !empty($data->creator_recipient_id))
            ? $data->creator_recipient_id
            : null;

        //todo: Add checker whether the message is coming from client or liberty staff. Add property "message_label" : values are ('Internal Message', 'Customer Message')
        if (isset($data->subject)) // if riskreduce platform
        {
            // for public users
            if ($replyType && $isExternal && !in_array($replyType, ["all", "creator"])) {
                $hasExternal = true;
                if (!isset($data->video_message_id)) {
                    return;
                }
                $details = VideoRecordMessage::with('videoRecordRecipients')->find($data->video_message_id);
                $details->message_label = 'customer message';
                $details->update((array)$data);
            } else {  //All vr for customers and staff
                if ($data->person_id === 'ext' || $isExternal) {
                    if (isset($data->video_message_id) && !empty($data->video_message_id) && isset($data->user_email)) {
                        $external_details = VideoRecordMessageRecipient::where(
                            'video_record_message_id',
                            $data->video_message_id
                        )
                            ->where('email', $data->user_email)
                            ->first();
                    }


                    $date = Carbon::now();
                    $date->addDays(60);
                    $details = VideoRecordMessage::create(
                        [
                            'name' => $external_details['name'],
                            'mobile' => $external_details['mobile'],
                            'email' => $external_details['email'],
                            'company' => $external_details['company'],
                            'cloudname' => $data->cloudname,
                            'filename' => $data->filename,
                            'subject' => $data->subject,
                            'platform' => 'riskreduce',
                            'message_label' => 'customer message',
                            'person_id' => ($data->person_id === 'ext' || $isExternal)
                                ? 'external'
                                : '',
                            'expiry_date' => $date,
                        ]
                    );
                    $details->save();
                } else {
                    $date = Carbon::now();
                    $date->addDays(60);
                    $details = VideoRecordMessage::create(
                        [
                            'name' => isset($cms[$data->person_id]['name'])
                                ? $cms[$data->person_id]['name']
                                : '',
                            'mobile' => isset($cms[$data->person_id]['mobile'])
                                ? $cms[$data->person_id]['mobile']
                                : '',
                            'email' => isset($cms[$data->person_id]['email'])
                                ? $cms[$data->person_id]['email']
                                : '',
                            'cloudname' => $data->cloudname,
                            'filename' => $data->filename,
                            'subject' => $data->subject,
                            'platform' => 'riskreduce',
                            'message_label' => 'internal message',
                            'person_id' => $data->person_id,
                            'expiry_date' => $date,
                        ]
                    );
                    $details->save();
                }
            }

            $sender = $details;

            $isCreatorLmre = isset($cms[$data->person_id]['business']) && $cms[$data->person_id]['business'] === 'lmre';
            $isCreatorExternal = $sender->person_id === 'external';

            if ($isExternal) {
                if (isset($data->video_message_id) && !empty($data->video_message_id)) {   // just an update from publuc side
                    $video_record_message_id = $data->video_message_id;

                    $video_record_message_recipients = $this->getMyMessageMembers($video_record_message_id);

                    $creator = $video_record_message_recipients[0];


                    $statusCallBack = config('app.twilio.statusCallBackUrl');
                    $params = !empty($statusCallBack)
                        ? ['statusCallback' => config('app.client_frontend') . '/' . $statusCallBack]
                        : [];

                    // SMS Notification
                    $mobile = (isset($creator['mobile']) && !empty($creator['mobile']))
                        ? $creator['mobile']
                        : '';

                    // Send the creator a copy
                    // $this->sendSms($mobile, $data, $sender, $params, $this->generateVideoMessageLink($sender->id, true));

                    // Email Notification
                    // VideoRecordEmailNotification::sendTemplate1([
                    //     'sender' => $sender,
                    //     'recipient' => $creator,
                    //     'video_message_link' => $this->generateVideoMessageLink($sender->id, true),
                    //     'business' => $business
                    // ]);
                }


                $hasExternal = true;
                foreach ($details->videoRecordRecipients as $key => $recipient) {
                    $recipient = CmsLibertyRepresentative::getLibRepByEmail($recipient['email']);
                    if (!isset($recipient)) {
                        continue;
                    }

                    $is_customer = !isset($recipient) || empty($recipient);

                    $isRecipientLmre = isset($recipient['business']) && $recipient['business'] === 'lmre';
                    $linkBusiness = 'lsm';
                    if (($is_customer && $isCreatorLmre) || ($isCreatorExternal && $isRecipientLmre)) {
                        $linkBusiness = 'lmre';
                    }

                    $video_message_link = $this->generateVideoMessageLink($sender->id, $is_customer, $linkBusiness);

                    $statusCallBack = config('app.twilio.statusCallBackUrl');
                    $params = !empty($statusCallBack)
                        ? ['statusCallback' => config('app.client_frontend') . '/' . $statusCallBack]
                        : [];

                    // SMS Notification
                    if ($recipient['person_id'] && $recipient['person_id'] != 'external') {
                        try {
                            if (isset($cms[$recipient['person_id']])) {
                                if ($this->isNumberUS($cms[$recipient['person_id']]['mobile'])) {
                                    $twilioSms = $this->twilioConfigUS->message(
                                        $cms[$recipient['person_id']]['mobile'],
                                        'Video Message ' . $data->subject . ' from ' . (isset($sender->name)
                                            ? $sender->name
                                            : '') . ' on Liberty Virtual Rooms. Visit ' . $video_message_link,
                                        [],
                                        $params
                                    );
                                } else {
                                    $twilioSms = $this->twilio->message(
                                        $cms[$recipient['person_id']]['mobile'],
                                        'Video Message ' . $data->subject . ' from ' . (isset($sender->name)
                                            ? $sender->name
                                            : '') . ' on Liberty Virtual Rooms. Visit ' . $video_message_link,
                                        [],
                                        $params
                                    );
                                }
                            }
                        } catch (Exception $e) {
                        }
                    }

                    VideoRecordEmailNotification::sendTemplate1(
                        [
                            'sender' => $sender,
                            'recipient' => $recipient,
                            'video_message_link' => $video_message_link,
                            'business' => $linkBusiness,
                        ]
                    );
                }
                $details->message_label = 'customer message';
                $details->update();
            }

            // Normal adding of recipients
            if (isset($data->recipients)) {
                $recipients = $data->recipients;

                foreach ($recipients as $recipient) {
                    $recording_app_generated_mobile = (isset($recipient['mobile']) && !empty($recipient['mobile']))
                        ? $recipient['mobile']
                        : '';

                    // Check if there is a single external guest then tag the video as customer message
                    if (empty($recipient['email'])) {
                        continue;
                    }

                    $personId = $data->person_id;

                    $recipientCms = CmsLibertyRepresentative::getLibRepByEmail($recipient['email']);

                    if (isset($recipientCms)) {
                        $personId = $recipientCms['person_id'];
                    } else {
                        $hasExternal = true;
                        $personId = 'external';
                        VideoRecordMessage::where('id', $details->id)
                            ->update(
                                [
                                    'message_label' => 'customer message',
                                ]
                            );
                    }

                    $recipient_data = VideoRecordMessageRecipient::create(
                        [
                            'video_record_message_id' => $details->id,
                            'person_id' => $personId,
                            'name' => isset($recipient['name'])
                                ? $recipient['name']
                                : '',
                            'email' => isset($recipient['email'])
                                ? $recipient['email']
                                : '',
                            'company' => isset($recipient['company'])
                                ? $recipient['company']
                                : '',
                            'mobile' => (isset($recording_app_generated_mobile) && !empty($recording_app_generated_mobile))
                                ? $recording_app_generated_mobile
                                : '',
                        ]
                    );
                    $is_customer = !isset($recipientCms) || empty($recipientCms);

                    $isRecipientLmre = isset($recipientCms['business']) && $recipientCms['business'] === 'lmre';
                    $linkBusiness = 'lsm';
                    if (($is_customer && $isCreatorLmre) || ($isCreatorExternal && $isRecipientLmre)) {
                        $linkBusiness = 'lmre';
                    }

                    $video_message_link = $this->generateVideoMessageLink(
                        $details->id,
                        ($personId === 'external')
                            ? true
                            : false,
                        $linkBusiness,
                        $recipient_data
                    );

                    $statusCallBack = config('app.twilio.statusCallBackUrl');
                    $params = !empty($statusCallBack)
                        ? ['statusCallback' => config('app.client_frontend') . '/' . $statusCallBack]
                        : [];

                    // SMS Notification
                    $mobile = (isset($cms[$recipient_data->person_id]['mobile']) && !empty($cms[$recipient_data->person_id]['mobile']))
                        ? $cms[$recipient_data->person_id]['mobile']
                        : $recording_app_generated_mobile;

                    try {
                        $this->sendSms($mobile, $data, $sender, $params, $video_message_link);
                    } catch (Exception $e) {
                    }

                    // Email Notification
                    VideoRecordEmailNotification::sendTemplate1(
                        [
                            'sender' => $sender,
                            'recipient' => $recipient_data,
                            'video_message_link' => $video_message_link,
                            'business' => $linkBusiness,
                        ]
                    );
                }

                if ($hasExternal) {
                    $details->message_label = 'customer message';
                    $details->update();
                }
            }

            // If the creator replies all recipient
            if (
                $replyType
                && ($replyType === 'all' || $replyType === 'creator')
                && isset($data->video_message_id)
                && !empty($data->video_message_id)
            ) {
                // get the recipients of the replied video record message
                $video_record_message_id = $data->video_message_id;
                $new_record_message_id = $details->id;
                $video_record_message_recipients = $this->getMyMessageMembers($video_record_message_id);

                $creator = $video_record_message_recipients[0];
                $recipients = $video_record_message_recipients[0]['video_record_recipients'];
                $deleted_recipients = (count($data->deleted_recipients) > 0)
                    ? $data->deleted_recipients
                    : [];

                // create a record from the orginal creator
                // ensure that recipient and creator do not duplicate
                if ($sender->name != $creator['name']) {

                    if ($creator['person_id'] === 'external') {
                        VideoRecordMessage::where('id', $details->id)
                            ->update(
                                [
                                    'message_label' => 'customer message',
                                ]
                            );
                    }

                    $recipient_data = VideoRecordMessageRecipient::create(
                        [
                            'video_record_message_id' => $new_record_message_id,
                            'person_id' => $creator['person_id'],
                            'name' => (isset($creator['name']) && !empty($creator['name']))
                                ? $creator['name']
                                : '',
                            'email' => (isset($creator['email']) && !empty($creator['email']))
                                ? $creator['email']
                                : '',
                            'company' => (isset($creator['company']) && !empty($creator['company']))
                                ? $creator['company']
                                : '',
                            'mobile' => (isset($creator['mobile']) && !empty($creator['mobile']))
                                ? $creator['mobile']
                                : '',
                        ]
                    );

                    $statusCallBack = config('app.twilio.statusCallBackUrl');
                    $params = !empty($statusCallBack)
                        ? ['statusCallback' => config('app.client_frontend') . '/' . $statusCallBack]
                        : [];

                    // SMS Notification
                    $mobile = (isset($creator['mobile']) && !empty($creator['mobile']))
                        ? $creator['mobile']
                        : '';
                    $is_customer = $recipient_data->person_id === 'external';

                    $recipientCms = CmsLibertyRepresentative::getLibRepByEmail($recipient_data->email);

                    $isRecipientLmre = isset($recipientCms['business']) && $recipientCms['business'] === 'lmre';
                    $linkBusiness = 'lsm';
                    if (($is_customer && $isCreatorLmre) || ($isCreatorExternal && $isRecipientLmre)) {
                        $linkBusiness = 'lmre';
                    }

                    $video_message_link = $this->generateVideoMessageLink(
                        $sender->id,
                        $is_customer,
                        $linkBusiness,
                        $recipient_data
                    );
                    try {
                        $this->sendSms($mobile, $data, $sender, $params, $video_message_link);
                    } catch (Exception $e) {
                    }
                    // Email Notification
                    VideoRecordEmailNotification::sendTemplate1(
                        [
                            'sender' => $sender,
                            'recipient' => $creator,
                            'video_message_link' => $video_message_link,
                            'business' => $linkBusiness,
                        ]
                    );
                }

                // create records from the original recipients
                foreach ($recipients as $recipient) {
                    // if included in the deleted list from vue do not include the data
                    if (!in_array($recipient['id'], $deleted_recipients)) {
                        $recipientCms = CmsLibertyRepresentative::getLibRepByEmail($recipient['email']);

                        if (isset($recipientCms)) {
                            $personId = $recipientCms['person_id'];
                        } else {
                            $hasExternal = true;
                            $personId = 'external';

                            VideoRecordMessage::where('id', $new_record_message_id)
                                ->update(
                                    [
                                        'message_label' => 'customer message',
                                    ]
                                );
                        }

                        $recipient_data = VideoRecordMessageRecipient::create(
                            [
                                'video_record_message_id' => $new_record_message_id,
                                'person_id' => $personId,
                                'name' => (isset($recipient['name']) && !empty($recipient['name']))
                                    ? $recipient['name']
                                    : '',
                                'email' => (isset($recipient['email']) && !empty($recipient['email']))
                                    ? $recipient['email']
                                    : '',
                                'mobile' => (isset($recipient['mobile']) && !empty($recipient['mobile']))
                                    ? $recipient['mobile']
                                    : '',
                            ]
                        );

                        $statusCallBack = config('app.twilio.statusCallBackUrl');
                        $params = !empty($statusCallBack)
                            ? ['statusCallback' => config('app.client_frontend') . '/' . $statusCallBack]
                            : [];

                        // SMS notification
                        $mobile = (isset($cms[$recipient_data->person_id]['mobile']) && !empty($cms[$recipient_data->person_id]['mobile']))
                            ? $cms[$recipient_data->person_id]['mobile']
                            : $recipient['mobile'];

                        $is_customer = !isset($recipientCms) || empty($recipientCms);

                        $isRecipientLmre = isset($recipientCms['business']) && $recipientCms['business'] === 'lmre';
                        $linkBusiness = 'lsm';
                        if (($is_customer && $isCreatorLmre) || ($isCreatorExternal && $isRecipientLmre)) {
                            $linkBusiness = 'lmre';
                        }

                        // $video_message_link = $this->generateVideoMessageLink($sender->id, $is_customer, $recipient_data);
                        $video_message_link = $this->generateVideoMessageLink(
                            $sender->id,
                            $is_customer,
                            $linkBusiness,
                            $recipient_data
                        );

                        try {
                            $twilioSms = $this->twilio->message(
                                $mobile,
                                'Video Message ' . $data->subject . ' from ' . (isset($sender->name)
                                    ? $sender->name
                                    : '') . ' on Liberty Virtual Rooms. Visit ' . $video_message_link,
                                [],
                                $params
                            );
                        } catch (Exception $e) {
                        }


                        // Email notification
                        VideoRecordEmailNotification::sendTemplate1(
                            [
                                'sender' => $sender,
                                'recipient' => $recipient_data,
                                'video_message_link' => $video_message_link,
                                'business' => $linkBusiness,
                            ]
                        );
                    }
                }

                if ($hasExternal) {
                    $details->message_label = 'customer message';
                    $details->update();
                }
            }
        } else { // CMS Platform
            $details = VideoRecord::create(
                [
                    'cloudname' => $data->cloudname,
                    'filename' => $data->filename,
                    'platform' => 'cms',
                    'cms_id' => $data->cms,
                ]
            );

            $details->save();
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => $details,
            ]
        );
    }

    private function getMyMessageMembers($video_record_message_id)
    {
        if ($video_record_message_id) {
            $data = VideoRecordMessage::with('videoRecordRecipients')
                ->where('id', $video_record_message_id)
                ->get();

            $data = VideoRecordMessage::setCmsData($data->toArray());

            return $data;
        }
    }

    private function generateVideoMessageLink(
        $video_message_id,
        $is_customer = false,
        $business = 'lsm',
        $recipient_data = null
    ) {
        $endpoint = ($is_customer)
            ? 'external-my-message'
            : 'my-message';
        $video_message_id = Helper::encryptInt($video_message_id, self::ENCRYPT_SALT);
        $recipient_id = ($is_customer && isset($recipient_data->id))
            ? Helper::encryptInt($recipient_data->id, self::ENCRYPT_SALT)
            : null;
        // We're going to use the recipient_id so that know in the admin side who is viewing this message,
        // and later on use that recipient_id to get the external user details of that user when he creates a video message
        $business_param = '?business=' . $business;
        $creator_recipient_param = (!empty($recipient_id))
            ? '&creator_recipient_id=' . rawurlencode($recipient_id)
            : "";
        return config('app.admin_frontend') . '/virtual-rooms/' . $endpoint . '/' . rawurlencode($video_message_id) . $business_param . $creator_recipient_param;
    }

    private function isNumberUS($stringNum)
    {
        if (substr($stringNum, 0, 1) == '1' || substr($stringNum, 0, 2) == '+1') {
            return true;
        }
        return false;
    }

    private function sendSms($mobile, $data, $sender, $params, $video_message_link)
    {
        try {
            if ($this->isNumberUS($mobile)) {
                $twilioSms = $this->twilioConfigUS->message(
                    $mobile,
                    'Video Message ' . $data->subject . ' from ' . (isset($sender->name)
                        ? $sender->name
                        : '') . ' on Liberty Virtual Rooms. Visit ' . $video_message_link,
                    [],
                    $params
                );
            } else {
                $twilioSms = $this->twilio->message(
                    $mobile,
                    'Video Message ' . $data->subject . ' from ' . (isset($sender->name)
                        ? $sender->name
                        : '') . ' on Liberty Virtual Rooms. Visit ' . $video_message_link,
                    [],
                    $params
                );
            }
        } catch (Exception $e) {
        }
    }

    public function getUploadedVideo($token, $cms_id)
    {
        // if($this->validateToken($token)){
        //     return \Response::json([
        //         'response' => 'failed',
        //         'msg' => 'token error'
        //     ]);
        // }

        $data = VideoRecord::where('cms_id', $cms_id)->first();
        if ($data && !empty($data)) {
            return Response::json(
                [
                    'response' => 'success',
                    'data' => $data,
                ]
            );
        }

        return Response::json(
            [
                'response' => 'failed',
                'msg' => 'no data found',
            ]
        );
    }

    public function getMyMessages(Request $request)
    {
        $data = $request->all();
        $person_id = $data['person_id'];
        $recipient_email = $data['email'];
        $timezone = $data['timezone'];
        $page = (int)$data['selected_page'];

        $message_label = (isset($data['message_label']) && !empty($data['message_label']))
            ? $data['message_label']
            : false;
        $search = (isset($data['search']) && !empty($data['search']))
            ? $data['search']
            : false;

        // Query when searched
        if ($search) {
            $search = trim($search);

            $received = VideoRecordMessage::with(['videoRecordRecipients', 'author'])
                ->where('expiry_date', '>=', Carbon::now())
                ->where('cloudname', '<>', '')
                ->where('filename', '<>', '')
                ->where('person_id', '<>', $person_id)
                ->whereHas(
                    'videoRecordRecipients',
                    function ($query) use ($search, $person_id) {
                        $query->where('person_id', $person_id);
                    }
                )
                ->where(
                    function ($query) use ($search, $recipient_email) {
                        return $query->where('email', 'like', "%" . $search . "%")
                            ->orWhere('name', 'like', "%" . $search . "%")
                            ->orWhereHas(
                                'videoRecordRecipients',
                                function ($query) use ($recipient_email, $search) {
                                    $query->where('email', '<>', $recipient_email);
                                    $query->where(
                                        function ($query) use ($recipient_email, $search) {
                                            $query->where('email', 'like', "%" . $search . "%")
                                                ->orWhere('name', 'like', "%" . $search . "%");
                                        }
                                    );
                                }
                            );
                    }
                );

            if ($message_label) {
                $received = $received->where('message_label', $message_label);
            }
            $received = $received->get();

            $constraint = function ($query) use ($search) {
                $query->where(
                    function ($wheres) use ($search) {
                        $wheres->where('email', 'like', "%" . $search . "%")
                            ->orWhere('name', 'like', "%" . $search . "%");
                        $wheres->orderBy('id', 'DESC');
                    }
                );
            };

            $sent = VideoRecordMessage::with('videoRecordRecipients')
                ->where('expiry_date', '>=', Carbon::now())
                ->where('person_id', $person_id)
                ->where('cloudname', '<>', '')
                ->where('filename', '<>', '')
                ->where(
                    function ($wheres) use ($search, $recipient_email) {
                        return $wheres->where('email', 'like', "%" . $search . "%")
                            ->orWhere('name', 'like', "%" . $search . "%")
                            ->orWhereHas(
                                'videoRecordRecipients',
                                function ($query) use ($search, $recipient_email) {
                                    $query->where('email', '<>', $recipient_email);
                                    $query->where(
                                        function ($query) use ($recipient_email, $search) {
                                            return $query->where('email', 'like', "%" . $search . "%")
                                                ->orWhere('name', 'like', "%" . $search . "%");
                                        }
                                    );
                                }
                            );
                    }
                )
                ->orderBy('id', 'DESC');

            if ($message_label) {
                $sent = $sent->where('message_label', $message_label);
            }
            $sent = $sent->get();

            $merged = $sent->merge($received);
            $data = $merged->all();
            $page_count = $data;
            $data = $this->paginate($data, $page);
        } else { // Query for All, Internal and Customer's inbox
            // Video creators data
            $creator = VideoRecordMessage::with('videoRecordRecipients')
                ->where('cloudname', '<>', '')
                ->where('filename', '<>', '')
                ->where('expiry_date', '>=', Carbon::now())
                ->where('person_id', $person_id)
                ->orderBy('id', 'DESC');

            if ($message_label) {
                $creator = $creator->where('message_label', $message_label);
            }

            $creator = $creator->get();

            // Video recipients data
            $recipients = VideoRecordMessage::with('videoRecordRecipients')
                ->where('expiry_date', '>=', Carbon::now())
                ->where('cloudname', '<>', '')
                ->where('filename', '<>', '')
                ->whereHas(
                    'videoRecordRecipients',
                    function ($query) use ($recipient_email) {
                        $query->where('email', $recipient_email);
                        $query->orderBy('id', 'DESC');
                    }
                )
                ->orderBy('id', 'DESC');

            if ($message_label) {
                $recipients = $recipients->where('message_label', $message_label);
            }

            $recipients = $recipients->get();

            $merged = $recipients->merge($creator);
            $data = $merged->all();
            $page_count = $data;
            $data = $this->paginate($data, $page);
        }

        $data = VideoRecordMessage::setCmsData($data, $timezone);

        return Response::json(
            [
                'status' => 'success',
                'response' => $data,
                'page_count' => ceil(count($page_count) / 10),
            ]
        );
    }

    private function paginate($data, $page = 0)
    {
        usort(
            $data,
            function ($a, $b) {
                return $a['id'] - $b['id'];
            }
        );
        rsort($data);
        $data = array_slice($data, ($page - 1) * 10, 10);
        return $data;
    }

    public function singlePageView(Request $request, $id)
    {
        $id = Helper::decryptString($id, self::ENCRYPT_SALT);
        $timezone = ($request->has('timezone'))
            ? $request->get('timezone')
            : 'Europe/London';

        // means incorrect id is passed
        if (!is_numeric($id)) {
            return Response::json(
                [
                    'status' => 'failed',
                    'response' => 'no video found',
                ]
            );
        }

        $videoQuery = VideoRecordMessage::where('id', $id)->with('videoRecordRecipients');

        if ($request->get('creator_recipient_id')) {
            $creator_recipient_id = Helper::decryptString($request->get('creator_recipient_id'), self::ENCRYPT_SALT);
            $videoQuery->whereHas(
                'videoRecordRecipients',
                function ($query) use ($creator_recipient_id) {
                    $query->where('id', $creator_recipient_id);
                }
            );
            $video = $videoQuery->get();
        } elseif ($request->get('email')) {
            $email = $request->get('email');
            $videoQuery->where(
                function ($query) use ($email) {
                    $query->where('email', $email)
                        ->orWhereHas(
                            'videoRecordRecipients',
                            function ($query) use ($email) {
                                $query->where('email', $email);
                            }
                        );
                }
            );
            $video = $videoQuery->get();
        } else {
            $video = $videoQuery->get();
        }

        $video = VideoRecordMessage::setCmsData($video, $timezone);

        if (isset($video[0])) {
            return Response::json(
                [
                    'status' => 'success',
                    'response' => $video[0],
                ]
            );
        }

        return Response::json(
            [
                'status' => 'failed',
                'response' => 'no video found',
            ]
        );
    }

    public function customerVideoMessage(Request $request)
    {
        $data = (object)$request->all();
        // \Log::info((array)$data);

        $date = Carbon::now();
        $date->addDays(60);
        $details = VideoRecordMessage::create(
            [
                'name' => isset($data->name)
                    ? $data->name
                    : '',
                'mobile' => isset($data->mobile)
                    ? $data->mobile
                    : '',
                'email' => isset($data->email)
                    ? $data->email
                    : '',
                'company' => isset($data->company)
                    ? $data->company
                    : '',
                'cloudname' => '',
                'filename' => '',
                'subject' => '',
                'platform' => 'riskreduce',
                'message_label' => 'customer message',
                'person_id' => 'external',
                'expiry_date' => $date,
            ]
        );

        $cms = CmsLibertyRepresentative::fetchAllFromCacheOrCms();
        $recipientCms = isset($cms[$data->person_id])
            ? $cms[$data->person_id]
            : [];

        $recipient_data = [];
        if (!empty($recipientCms)) {
            $recipient_data = VideoRecordMessageRecipient::create(
                [
                    'video_record_message_id' => $details->id,
                    'person_id' => $data->person_id,
                    'name' => $recipientCms['name'],
                    'email' => $recipientCms['email'],
                    'company' => "",
                ]
            );
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => $details,
            ]
        );
    }

    private function validateToken($token)
    {
        $data = VideoRecordToken::where('token', $token)->first();

        if ($data && !empty($data)) {
            return true;
        }

        return false;
    }
}
