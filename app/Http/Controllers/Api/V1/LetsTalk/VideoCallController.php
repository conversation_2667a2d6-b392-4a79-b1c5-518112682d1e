<?php

namespace App\Http\Controllers\Api\V1\LetsTalk;

use DateTime;
use stdClass;
use Exception;
use DateTimeZone;
use Carbon\Carbon;
use App\Models\Cms;
use Twilio\Rest\Client;
use <PERSON>oha\Twilio\Twilio;
use App\Models\Mailqueue;
use App\Models\Notification;
use Illuminate\Http\Request;
use App\Models\SuppressionList;
use App\Models\LetsTalk\VideoLog;
use Illuminate\Http\JsonResponse;
use App\Models\LetsTalk\InviteLog;
use Illuminate\Support\Facades\DB;
use App\Models\LetsTalk\SocialRoom;
use Illuminate\Support\Facades\Log;
use App\Models\LetsTalk\VideoLogData;
use Illuminate\Support\Facades\Cache;
use App\Models\LetsTalk\VideoCallRoom;
use App\Models\LetsTalk\SocialRoomType;
use App\Http\Controllers\BaseController;
use App\Models\LetsTalk\ResourceBooking;
use Illuminate\Support\Facades\Response;
use App\Models\LetsTalk\ResourceSchedule;
use App\Models\LetsTalk\UpcomingSchedule;
use App\Models\LetsTalk\SocialRoomSchedule;
use App\Models\LetsTalk\VideoCallParticipant;
use App\Models\LetsTalk\SocialRoomParticipant;
use App\Models\LetsTalk\ResourceExternalContact;
use App\Models\LetsTalk\CmsLibertyRepresentative;
use App\Models\LetsTalk\SocialRoomSmsNotification;
use App\Models\LetsTalk\ResourceBookingAttachedFile;
use App\Models\LetsTalk\SocialRoomCancelledSchedule;
use App\Models\LetsTalk\SocialRoomEmailNotification;
use App\Models\LetsTalk\SocialRoomModifiedRecurringSchedule;

class VideoCallController extends BaseController
{
    const MAX_TOTAL_GUESTS = 15;
    const MAX_LIBREP_GUESTS = 7;
    const MAX_EXTERNAL_GUESTS = 7;
    private $twilio;
    private $twilioClient;
    private $userRequest;

    // TODO: verify if these are correct
    private $isLmre; // creator/organizer is not included, 15 if included
    private $business;
    private $twilioConfigUS;

    public function __construct(Request $request, Mailqueue $mailqueue)
    {
        $twilioConfig = config('app.twilio.sms');
        $this->userRequest = $request;
        $this->twilio = new Twilio($twilioConfig['sid'], $twilioConfig['token'], $twilioConfig['from']);
        $this->twilioConfigUS = new Twilio($twilioConfig['sid'], $twilioConfig['token'], $twilioConfig['from-us']);
        $this->twilioClient = new Client($twilioConfig['sid'], $twilioConfig['token']);

        $this->mail = $mailqueue;

        $this->business = ($request->has('business') && $request->get('business') === 'lmre')
            ? 'lmre'
            : 'lsm';
        $this->isLmre = !(($this->business === 'lsm'));
    }

    public function twilioLookUp()
    {
        $data = $this->userRequest->all();

        // if no data
        if (!count($data)) {
            return Response::json(
                [
                    'response' => 'fail',
                    'data' => 'No data to validate.',
                ]
            );
        }

        $invalidMobileFields = [];
        foreach ($data as $key => $value) {
            try {
                $response = $this->twilioClient->lookups->v1->phoneNumbers($value)->fetch();
            } catch (Exception $e) {
                $invalidMobileFields[] = $key;
            }
        }

        // if there are invalid fields found
        if (count($invalidMobileFields)) {
            return Response::json(
                [
                    'response' => 'fail',
                    'invalid_mobile_fields' => $invalidMobileFields,
                ]
            );
        }

        // if no invalid mobile fields
        return Response::json(
            [
                'response' => 'success',
                'data' => 'No invalid mobile numbers found.',
            ]
        );
    }

    public function addMultipleParticipant()
    {
        $data = $this->userRequest->all();
        $invitees = json_decode($data['invitees']);

        foreach ($invitees as $invitee) {
            $response = $this->addParticipant(json_encode($invitee));
            $responseData = explode(":", $response);

            if (trim($responseData[6]) == '"fail"}') {
                return Response::json(
                    [
                        'response' => 'fail',
                    ]
                );
            }
        }
        // if ($response->response=='fail')
        // {
        //     return Response::json([
        //         'response' => 'fail',
        //         'data' => $response
        //     ]);
        // }


        return Response::json(
            [
                'response' => 'success',
            ]
        );
    }

    /**
     * Save the details of the room and the participants
     *
     * @return json
     */
    public function addParticipant($setData = null)
    {
        if (isset($setData) && !empty($setData)) {
            $data = (array)json_decode($setData);
        } else {
            $data = $this->userRequest->all();
        }

        // Social Rooms
        if (isset($data['is_social']) && $data['is_social'] == true) {
            $resultSuccess = $this->addSocialParticipant($data);
            if (!$resultSuccess) {
                return Response::json(
                    [
                        'response' => 'fail',
                    ]
                );
            }
            return Response::json(
                [
                    'response' => 'success',
                ]
            );
        }

        // Public Rooms
        $data["liberty_representative"] = $data['name'];
        $data["liberty_representative_email"] = $data['email'];
        $data['client_phone'] = $data['mobile'];

        $room = VideoCallRoom::where('name', $data["room_id"])->first();

        $getRoomInfo = VideoCallParticipant::where('room_id', $data["room_id"])->first();

        if ($getRoomInfo) {
            $meeting_subject = $getRoomInfo["meeting_subject"];
            $data["meeting_subject"] = $meeting_subject;
        }

        if ($room) {
            $client = VideoCallParticipant::where('room_id', $room["_id"])->where('role', 'client')->first();
            $representativeA = VideoCallParticipant::where('room_id', $room["_id"])->where(
                'user_code',
                $data['self_user_code']
            )->first();

            $room_id = $room["_id"];
            $data["room_id"] = $room_id;

            $getRoomInfo = VideoCallParticipant::where('room_id', $room_id)->orderBy('created_at', 'asc')->first();


            if ($getRoomInfo && !empty($getRoomInfo)) {
                $meeting_subject = $getRoomInfo["meeting_subject"];
                $data["meeting_subject"] = $meeting_subject;
            }
            $getAttachedFiles = ResourceBooking::with('bookingattachedfiles')->where('room_id', $room_id)->first();

            if ($getAttachedFiles && !empty($getAttachedFiles)) {
                // ${getAttachedFiles[i].uuid}/downloads3link`);
                //     link.attr("title", getAttachedFiles[i].filename);
                $attached_files = [];
                if (isset($getAttachedFiles['bookingattachedfiles']) && !empty($getAttachedFiles['bookingattachedfiles'])) {
                    $attached_files = $getAttachedFiles['bookingattachedfiles'];
                    $get_proper_object = [];
                    foreach ($attached_files as $file) {
                        array_push($get_proper_object, [$file['filename'] => $file['uuid']]);
                    }
                    $data['attached_files'] = $get_proper_object;
                }
            }
        } else {
            return Response::json(
                [
                    'response' => 'fail',
                    'data' => 'Invalid room',
                ]
            );
        }

        // the first representative included on the meeting
        $libRep = VideoCallParticipant::where('room_id', $data["room_id"])->where(
            'role',
            'liberty_representative'
        )->first();


        $schedule = ResourceBooking::where('room_id', $data['room_id'])->first();

        if ($representativeA) {
            $data['representative_a'] = ($representativeA->user_name === 'Receptionist')
                ? 'Liberty Reception'
                : $representativeA->user_name;
            $inviteeIsReception = ($representativeA->user_name === 'Receptionist')
                ? 1
                : 0;
        }

        if ($schedule) {
            $data['schedule'] = $schedule['from'] . ' to ' . $schedule['to'];
            $data['conference_date'] = Carbon::parse($schedule['from'])->format('Y-m-d');
            $data['time_start'] = Carbon::parse($schedule['from'])->format('H:i:00');
            $data['time_end'] = Carbon::parse($schedule['to'])->format('H:i:00');
        }

        $conference_start_date = date("Y-m-d H:i:s", strtotime($data['conference_date'] . ' ' . $data['time_start']));
        $conference_end_date = date("Y-m-d H:i:s", strtotime($data['conference_date'] . ' ' . $data['time_end']));

        $booked_timezone = $representativeA->booked_timezone;
        $booked_timezone_offset = $this->get_timezone_offset('UTC', $booked_timezone);

        $booked_timezone_from = Carbon::parse(
            $conference_start_date,
            "Europe/London"
        )->setTimezone($representativeA->booked_timezone)->format('Y-m-d H:i:s');
        $booked_timezone_to = Carbon::parse(
            $conference_end_date,
            "Europe/London"
        )->setTimezone($representativeA->booked_timezone)->format('Y-m-d H:i:s');

        $participantsCount = VideoCallParticipant::where('room_id', $data["room_id"])->get()->count();

        if ($participantsCount >= 15) {
            return Response::json(
                [
                    'response' => 'info',
                    'data' => 'Maximum number of participants have been invited for this meeting',
                ]
            );
        }

        // Check the number of liberty_representatives
        // $participantsLibertyRepresentativeCount = VideoCallParticipant::where('room_id', $data["room_id"])
        //     ->where('role', 'liberty_representative')
        //     ->get()->count();

        // if ($participantsLibertyRepresentativeCount >= 4 && $data['role']=='liberty_representative') {
        //     return Response::json([
        //         'response' => 'info',
        //         'data' => 'Maximum number of Liberty Staff have been invited for this meeting',
        //     ]);
        // }

        // // Check the number of clients
        // $participantsMainClientCount = VideoCallParticipant::where('room_id', $data["room_id"])
        //     ->where('role', 'client')
        //     ->get()->count();

        // $participantsClientCount = VideoCallParticipant::where('room_id', $data["room_id"])
        //     ->where('role', 'guest_clients')
        //     ->get()->count();

        // if (($participantsClientCount + $participantsMainClientCount) >= 4   && $data["role"] == 'guest_clients') {
        //     return Response::json([
        //         'response' => 'info',
        //         'data' => 'Maximum number of Clients have been invited for this meeting',
        //     ]);
        // }

        if ($data['role'] == "") {
            return Response::json(
                [
                    'response' => 'info',
                    'data' => 'Please invite an External Guest or Liberty Colleague',
                ]
            );
        }

        $isMobileNumberValid = true;

        try {
            $this->twilioClient->lookups->v1->phoneNumbers($data['mobile'])->fetch();
        } catch (Exception $e) {

            // We set the $isMobileNumberValid flag here to false when phone number is invalid
            // because we still want to continue adding that liberty_representative participant to the meeting
            // but we WILL NOT trigger sending sms notif via Twilio
            // otherwise if role is not liberty_representative, we want to return invalid phone number error message
            if ($data['role'] === 'liberty_representative') {
                $isMobileNumberValid = false;
            } else {
                return Response::json(
                    [
                        'response' => 'fail',
                        'messages' => ['mobile' => ['Please enter Mobile Phone in a valid format.']],
                    ]
                );
            }
        }

        $response = [
            'room_id' => $data['room_id'],
        ];

        $statusCallBack = config('app.twilio.vrStatusCallBackUrl');
        $params = !empty($statusCallBack)
            ? ['statusCallback' => config('app.client_frontend') . '/' . $statusCallBack]
            : [];

        $log = VideoLog::create(
            [
                'room_id' => $data['room_id'],
                'type' => VideoLog::TYPE_SMS,
            ]
        );

        $userCode = VideoCallParticipant::generateUserCode();

        $attachment = null;

        // SMS notification 18
        if ($data['booking_type'] == "future" && $data['role'] == "liberty_representative") {
            $invitedLibRepData = $this->getLibRepDetails($room->business, $data['person_id']);

            // set representative timezone details
            $messageParamaters = [
                'representative_time_zone' => '',
                'representative_time_zone_offset' => '',
                'representative_time_zone_from' => '',
                'representative_time_zone_to' => '',
            ];

            if (isset($invitedLibRepData['office_timezone']) && !empty($invitedLibRepData['office_timezone'])) {
                $messageParamaters = [
                    'representative_time_zone' => $invitedLibRepData['office_timezone'],
                    'representative_time_zone_offset' => $this->get_timezone_offset(
                        'UTC',
                        $invitedLibRepData['office_timezone']
                    ),
                    'representative_time_zone_from' => Carbon::parse(
                        $booked_timezone_from,
                        $booked_timezone
                    )->setTimezone($invitedLibRepData['office_timezone']),
                    'representative_time_zone_to' => Carbon::parse(
                        $booked_timezone_to,
                        $booked_timezone
                    )->setTimezone($invitedLibRepData['office_timezone']),
                ];
            }

            $message = VideoCallParticipant::generateMessageN18(
                $room->name,
                $data['conference_date'],
                $conference_start_date,
                $conference_end_date,
                $booked_timezone,
                $booked_timezone_offset,
                $booked_timezone_from,
                $booked_timezone_to,
                $client['user_name'],
                $client['company'],
                $messageParamaters['representative_time_zone'],
                $messageParamaters['representative_time_zone_offset'],
                $messageParamaters['representative_time_zone_from'],
                $messageParamaters['representative_time_zone_to']
            );
        }

        // SMS notification 15
        if ($data['booking_type'] == "future" && $data['role'] == "guest_clients") {
            // set representative timezone details

            // Check if the guest clients email exist on CMS
            $checkExternalGuestLocation = json_decode(Cms::get('workspaces/' . config('app.cms.workspace_id') . '/content-types/' . config('app.cms.people_content_type') . '/content-entries?joined=true&query={"' . config('app.cms.people_profile_email') . '":"' . urlencode($data['email']) . '","status":"publish","operator":"="}'));

            // If found, use the tiemzone inforatmation
            if (isset($checkExternalGuestLocation->data) && count($checkExternalGuestLocation->data) > 0) {
                $booked_timezone = $checkExternalGuestLocation->data[0]->{config('app.cms.people_profile_office')}[0]->{config('app.cms.people_profile_office_timezone')};
                $booked_timezone_offset = $this->get_timezone_offset('UTC', $booked_timezone);
                $booked_timezone_from = Carbon::parse(
                    $conference_start_date,
                    "Europe/London"
                )->setTimezone($booked_timezone)->format('Y-m-d H:i:s');
                $booked_timezone_to = Carbon::parse(
                    $conference_end_date,
                    "Europe/London"
                )->setTimezone($booked_timezone)->format('Y-m-d H:i:s');
            } else { // get representative details
                // $booked_timezone = $representativeA->details['office_timezone'];
                $booked_timezone = $libRep->booked_timezone;
                $booked_timezone_offset = $this->get_timezone_offset('UTC', $booked_timezone);
                $booked_timezone_from = Carbon::parse(
                    $conference_start_date,
                    "Europe/London"
                )->setTimezone($booked_timezone)->format('Y-m-d H:i:s');
                $booked_timezone_to = Carbon::parse(
                    $conference_end_date,
                    "Europe/London"
                )->setTimezone($booked_timezone)->format('Y-m-d H:i:s');
            }

            $message = VideoCallParticipant::generateMessageN15(
                $room->name,
                $data['conference_date'],
                $conference_start_date,
                $conference_end_date,
                $booked_timezone,
                $booked_timezone_offset,
                $booked_timezone_from,
                $booked_timezone_to,
                $libRep->user_name
            );
        }

        // SMS Notification 13
        if (
            $data['booking_type'] == "now"
            && $data['role'] == 'guest_clients'
            && ($representativeA->role == "guest_clients" || $representativeA->role == "client")
        ) {
            $message = VideoCallParticipant::generateMessageN13($room->name, $representativeA->user_name);
        }

        // SMS Notification 14
        if (
            $data['booking_type'] == "now"
            && $data['role'] == 'guest_clients'
            && ($representativeA->role == "liberty_representative" || $representativeA->role == "receptionist")
        ) {
            $invitedBy = ($representativeA->user_name === 'Receptionist')
                ? 'Liberty Reception'
                : $representativeA->user_name;
            $message = VideoCallParticipant::generateMessageN14($room->name, $invitedBy);
        }

        if ($data['booking_type'] == "now" && $data['role'] == 'liberty_representative') {
            // SMS notification 6 A/B

            //if client and company is null or undefined
            $get_user_name = isset($data['client']) && !empty($data['client'])
                ? $data['client']
                : $client['user_name'];
            $get_user_company = isset($data['company']) && !empty($data['company'])
                ? $data['company']
                : $client['company'];

            $message = VideoCallParticipant::generateMessageN6($room->name, $get_user_name, $get_user_company);
        }


        try {
            if (
                SuppressionList::where('From', '=', sha1($data['mobile']))->where(
                    'is_suppressed',
                    '=',
                    1
                )->get()->count() == 0
            ) {
                if ($isMobileNumberValid) {
                    if ($this->isNumberUS($data['mobile'])) { //use US config phone number
                        $twilioSms = $this->twilioConfigUS->message(
                            $data['mobile'],
                            $message,
                            [],
                            $params
                        );
                    } else {
                        $twilioSms = $this->twilio->message(
                            $data['mobile'],
                            $message,
                            [],
                            $params
                        );
                    }
                } else {
                    $twilioSms = (object)[];
                    $twilioSms->sid = "Invalid phone number.";
                    $twilioSms->status = "Invalid phone number.";
                }
            } else {
                $twilioSms = (object)[];
                $twilioSms->sid = "SMS suppressed.";
                $twilioSms->status = "SMS suppressed.";
            }

            if ($data["role"] == 'liberty_representative') {
                // Cache was generated when accessing the invite page so it can be used here as well.
                $liberty_representatives_data = Cache::has($room->business . '_libertyRepresentativesLongLived')
                    ? Cache::get($room->business . '_libertyRepresentativesLongLived')
                    : [];
            }

            if ($data["role"] == 'guest_clients' && $representativeA->role == 'liberty_representative') {

                // Check if the guest clients email exist on CMS
                $checkExternalGuestLocation = json_decode(Cms::get('workspaces/' . config('app.cms.workspace_id') . '/content-types/' . config('app.cms.people_content_type') . '/content-entries?joined=true&query={"' . config('app.cms.people_profile_email') . '":"' . urlencode($data['email']) . '","status":"publish","operator":"="}'));

                // If found, use the tiemzone inforatmation
                if (count($checkExternalGuestLocation->data) > 0) {
                    $booked_timezone = $checkExternalGuestLocation->data[0]->{config('app.cms.people_profile_office')}[0]->{config('app.cms.people_profile_office_timezone')};
                    $booked_timezone_offset = $this->get_timezone_offset('UTC', $booked_timezone);
                    $booked_timezone_from = Carbon::parse(
                        $conference_start_date,
                        "Europe/London"
                    )->setTimezone($booked_timezone)->format('Y-m-d H:i:s');
                    $booked_timezone_to = Carbon::parse(
                        $conference_end_date,
                        "Europe/London"
                    )->setTimezone($booked_timezone)->format('Y-m-d H:i:s');
                } else { // get representative details
                    // $booked_timezone = $representativeA->details['office_timezone'];
                    $booked_timezone = $libRep->booked_timezone;
                    $booked_timezone_offset = $this->get_timezone_offset('UTC', $booked_timezone);
                    $booked_timezone_from = Carbon::parse(
                        $conference_start_date,
                        "Europe/London"
                    )->setTimezone($booked_timezone)->format('Y-m-d H:i:s');
                    $booked_timezone_to = Carbon::parse(
                        $conference_end_date,
                        "Europe/London"
                    )->setTimezone($booked_timezone)->format('Y-m-d H:i:s');
                }
            }

            $participant = VideoCallParticipant::create(
                [
                    'room_id' => $room_id,
                    'user_name' => $data['name'],
                    'user_code' => $userCode,
                    'mobile_number' => $data['mobile'],
                    'company' => (isset($data['company']) && !empty($data['company'])
                        ? $data['company']
                        : ''),
                    'email' => (isset($data['email']) && !empty($data['email'])
                        ? $data['email']
                        : ''),
                    'meeting_subject' => (isset($data['meeting_subject']) && !empty($data['meeting_subject'])
                        ? $data['meeting_subject']
                        : ''),
                    'role' => $data['role'],
                    'notes' => 'invited from either invite page(laravel) or (react)',
                    'representative_job_title' => (isset($data['liberty_representative_job_title']) && !empty($data['liberty_representative_job_title'])
                        ? $data['liberty_representative_job_title']
                        : ''),
                    'booked_timezone' => $booked_timezone,
                    'booked_timezone_offset' => $booked_timezone_offset,
                    'booked_timezone_from' => $booked_timezone_from,
                    'booked_timezone_to' => $booked_timezone_to,
                    'details' => ($data["role"] == 'liberty_representative' && isset($liberty_representatives_data[$data['person_id']]))
                        ? $liberty_representatives_data[$data['person_id']]
                        : json_encode([]),
                ]
            );

            // Get other representative
            $otherRepresentatives = VideoCallParticipant::where('room_id', $data["room_id"])
                ->where('role', 'liberty_representative')
                //->whereNot('_id', $libRep->_id)
                ->get();

            if ($client) {
                $data['client'] = $client['user_name'];
                $data['company'] = $client['company'];
                $data['email'] = $client['email'];
                $data['client_phone'] = $client['mobile_number'];
            } else {
                $data['client'] = "";
                $data['company'] = "";
            }

            $participant->sms_id = $twilioSms->sid;
            $participant->save();

            VideoLogData::create(
                [
                    'log_id' => $log->_id,
                    'sms_id' => $twilioSms->sid,
                    'human_readable' => 'Sending message to ' . $participant->user_name,
                    'data' => in_array($twilioSms->status, ["SMS suppressed.", "Invalid phone number."])
                        ? (array)$twilioSms
                        : $twilioSms->toArray(),
                ]
            );

            $log->status = $twilioSms->status;
            $log->save();

            // Log the main public user attendee but not the Liberty Representative
            if ($participant->role != "liberty_representative") {
                $this->createExternalContacts(
                    [
                        'business' => $room->business,
                        'name' => $participant->user_name,
                        'mobile_number' => $participant->mobile_number,
                        'company' => $participant->company,
                        'email' => $participant->email,
                    ]
                );
            }

            $response['participants']['success'] = $participant->toArray();
            $response['participant_count'] = $participantsCount + 1;

            $datetime_start = date("Ymd\THis", strtotime($booked_timezone_from));
            $datetime_end = date("Ymd\THis", strtotime($booked_timezone_to));
            $data['time_start'] = date("H:i", strtotime($data['time_start']));

            if (isset($data['email']) && !empty($data['email'])) {
                //$clientsLink = VideoCallParticipant::generateLink($userCode);
                $clientsLink = VideoCallRoom::generateWaitingRoomLink($room->name);
                $twilio_data = $clientsLink;
                $clientsLink = parse_url($clientsLink);
                $clientsLink = $clientsLink['scheme'] . "://" . $clientsLink['host'] . "\r\n\t" . $clientsLink['path'];
                $guests = [];
                $guestList = VideoCallParticipant::where('room_id', $room["_id"])->where(
                    'role',
                    'guest_clients'
                )->get();

                $guests = [];
                if (count($guestList) > 0) {
                    foreach ($guestList as $key => $guest) {
                        $guests['guest_client'][] = $guest->user_name;
                        $guests['guest_emails'][] = $guest->email;
                        $guests['guest_clientPhones'][] = $guest->mobile_number;
                    }
                }

                $viewParameters = [
                    'to_name' => $data['name'],
                    'video_call_details' => $data,
                    'twilio_data' => $twilio_data,
                    'role' => $data['role'],
                    'is_reminder' => false,
                    'is_reminder_before_5' => false,
                    'is_added_participant' => true,
                    'title' => ($data['booking_type'] == "future")
                        ? 'You have a new meeting'
                        : 'Now - Incoming call',
                    'guests' => (count($guests) > 0)
                        ? $guests
                        : "",
                    'booked_timezone' => $booked_timezone,
                    'booked_timezone_offset' => $booked_timezone_offset,
                    'booked_timezone_from' => $booked_timezone_from,
                    'booked_timezone' => $booked_timezone,
                    'representative_time_zone' => ($data['role'] == 'liberty_representative' && isset($liberty_representatives_data[$data['person_id']]) && !empty($liberty_representatives_data[$data['person_id']]))
                        ? $liberty_representatives_data[$data['person_id']]['office_timezone']
                        : '',
                    'representative_time_zone_offset' => ($data['role'] == 'liberty_representative' && isset($liberty_representatives_data[$data['person_id']]) && !empty($liberty_representatives_data[$data['person_id']]))
                        ? $this->get_timezone_offset(
                            'UTC',
                            $liberty_representatives_data[$data['person_id']]['office_timezone']
                        )
                        : '',
                    'representative_time_zone_from' => ($data['role'] == 'liberty_representative' && isset($liberty_representatives_data[$data['person_id']]) && !empty($liberty_representatives_data[$data['person_id']]))
                        ? Carbon::parse(
                            $booked_timezone_from,
                            $booked_timezone
                        )->setTimezone($liberty_representatives_data[$data['person_id']]['office_timezone'])
                        : '',
                    'other_representatives' => (count($otherRepresentatives) > 0)
                        ? $otherRepresentatives
                        : '',
                ];

                // $subject = 'Now - Liberty Virtual Rooms Meeting';
                $subject = "Now - Liberty Virtual Rooms Meeting";

                if ($data['booking_type'] == "future" && $data['role'] == "guest_clients") {
                    $viewParameters['is_added_participant'] = false;
                    $viewParameters['title'] = 'Your meeting has been scheduled';
                    $viewParameters['main_representative'] = $libRep->user_name;
                    $viewParameters['main_representative_job_title'] = $libRep->representative_job_title;
                    $viewParameters['main_representative_email'] = $libRep->email;

                    $description = "DESCRIPTION;LANGUAGE=en-US:" . $data['meeting_subject'] . " Link: " . $clientsLink;

                    $attendees = $this->getIcalUpdatedAllParticipants($room_id);

                    $attachmentSubject = $libRep->user_name . " - Liberty Virtual Rooms";
                    $attachment = [
                        'ics' => [
                            'StringValue' => "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Microsoft Corporation//Outlook 10.0 MIMEDIR//EN\r\nMETHOD:REQUEST\r\nBEGIN:VTIMEZONE\r\nTZID:" . $booked_timezone . "\r\nEND:VTIMEZONE\r\nBEGIN:VEVENT\r\nSUMMARY:" . $attachmentSubject . "\r\n" . $description . "\r\nDTSTART;VALUE=DATE-TIME:" . $datetime_start . "\r\nDTEND;VALUE=DATE-TIME:" . $datetime_end . "\r\nDTSTAMP;VALUE=DATE-TIME:" . $datetime_start . "\r\nUID:" . $room_id . $datetime_start . "\r\nSEQUENCE:1\r\n" . "CREATED;VALUE=DATE-TIME:" . $datetime_start . "\r\nURL;VALUE=URI:" . $clientsLink . "\r\nLOCATION:" . $clientsLink . $attendees . "\r\nPRIORITY:5\r\nSTATUS:CONFIRMED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-P15M\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR",
                            'DataType' => 'String',
                        ],
                    ];

                    // $subject = 'Your Liberty Virtual Rooms meeting has been scheduled';
                    $subject = $libRep->user_name . " - Liberty Virtual Rooms meeting has been scheduled";
                }

                if ($data['booking_type'] == "future" && $data['role'] == "liberty_representative") {
                    $booked_timezone_from = Carbon::parse(
                        $conference_start_date,
                        'Europe/London'
                    )->setTimezone($invitedLibRepData['office_timezone'])->format('Y-m-d H:i:s');
                    $booked_timezone_to = Carbon::parse(
                        $conference_end_date,
                        'Europe/London'
                    )->setTimezone($invitedLibRepData['office_timezone'])->format('Y-m-d H:i:s');
                    $datetime_start = date("Ymd\THis", strtotime($booked_timezone_from));
                    $datetime_end = date("Ymd\THis", strtotime($booked_timezone_to));

                    $viewParameters['is_added_participant'] = false;

                    // if there are multiple representative then the info should show the first included rep
                    $viewParameters['main_representative'] = $libRep->user_name;
                    $viewParameters['main_representative_job_title'] = $libRep->representative_job_title;
                    $viewParameters['main_representative_email'] = $libRep->email;
                    $viewParameters['user_code'] = ($data['role'] == 'liberty_representative')
                        ? VideoCallParticipant::inviteLink($userCode)
                        : '';
                    //$viewParameters['user_code'] = ($data['role']=='liberty_representative') ? VideoCallRoom::generateWaitingRoomLink($room->name) : '';

                    $description = "DESCRIPTION;LANGUAGE=en-US:" . $data['meeting_subject'] . " Link: " . $clientsLink;

                    $attendees = $this->getIcalUpdatedAllParticipants($room_id);

                    $client = ($data['client'] != "")
                        ? $data['client']
                        : $libRep->user_name;

                    $company = ($data['company'] != "")
                        ? ", " . $data['company']
                        : "";

                    $attachmentSubject = $client . $company . " - Liberty Virtual Rooms";

                    $method = "REQUEST";

                    if ($data['liberty_representative_email'] == $viewParameters['main_representative_email']) {
                        $method = "PUBLISH";
                    }

                    $attachment = [
                        'ics' => [
                            'StringValue' => "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Microsoft Corporation//Outlook 10.0 MIMEDIR//EN\r\nMETHOD:" . $method . "\r\nBEGIN:VTIMEZONE\r\nTZID:" . $invitedLibRepData['office_timezone'] . "\r\nEND:VTIMEZONE\r\nBEGIN:VEVENT\r\nSUMMARY:" . $attachmentSubject . "\r\n" . $description . "\r\nDTSTART;TZID=" . $invitedLibRepData['office_timezone'] . ":" . $datetime_start . "\r\nDTEND;TZID=" . $invitedLibRepData['office_timezone'] . ":" . $datetime_end . "\r\nDTSTAMP;VALUE=DATE-TIME:" . $datetime_start . "\r\nUID:" . $room_id . $datetime_start . "\r\nSEQUENCE:1\r\n" . "CREATED;VALUE=DATE-TIME:" . $datetime_start . "\r\nURL;VALUE=URI:" . $clientsLink . "\r\nLOCATION:" . $clientsLink . $attendees . "\r\nPRIORITY:5\r\nSTATUS:CONFIRMED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-P15M\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR",
                            'DataType' => 'String',
                        ],
                    ];

                    // $subject = 'You have a new Liberty Virtual Rooms meeting';
                    $subject = $client . " - New Liberty Virtual Rooms Meeting";
                }

                if ($data['booking_type'] == "now" && $data['role'] == "liberty_representative") {
                    $viewParameters['is_added_participant'] = true;

                    // if there are multiple representative then the info should show the first included rep
                    if (isset($libRep->user_name) && !empty($libRep->user_name)) {
                        $viewParameters['main_representative'] = $libRep->user_name;
                        $viewParameters['main_representative_job_title'] = $libRep->representative_job_title;
                        $viewParameters['main_representative_email'] = $libRep->email;
                    }

                    $viewParameters['title'] = "Now - Incoming call";
                    $attachment = false;

                    // $subject = 'Now - Liberty Virtual Rooms';
                    $subject = "Now - " . $libRep->user_name . " - Liberty Virtual Rooms Meeting";
                }

                /* If invited by reception */

                if ($data['booking_type'] == "now" && $data['role'] == "liberty_representative" && isset($inviteeIsReception) && $inviteeIsReception == 1) {
                    $viewParameters['is_added_participant'] = true;

                    // if there are multiple representative then the info should show the first included rep
                    if (isset($libRep->user_name) && !empty($libRep->user_name)) {
                        $viewParameters['main_representative'] = $libRep->user_name;
                        $viewParameters['main_representative_job_title'] = $libRep->representative_job_title;
                        $viewParameters['main_representative_email'] = $libRep->email;
                    }

                    $viewParameters['title'] = "Now - Incoming call";
                    $attachment = false;
                    $client = ($data['client'] != "")
                        ? $data['client']
                        : $libRep->user_name;

                    // $subject = 'Now - Liberty Virtual Rooms';
                    $subject = "Now - " . $client . " - Liberty Virtual Rooms Meeting";
                }


                if ($data['booking_type'] == "now" && $data['role'] == "guest_clients") {
                    $viewParameters['is_added_participant'] = true;

                    if (isset($libRep->user_name) && !empty($libRep->user_name)) {
                        // if there are multiple representative then the info should show the first included rep
                        $viewParameters['main_representative'] = $libRep->user_name;
                        $viewParameters['main_representative_job_title'] = $libRep->representative_job_title;
                        $viewParameters['main_representative_email'] = $libRep->email;
                    }

                    $attachment = false;
                    // $subject = 'Now - Liberty Virtual Rooms Meeting';
                    $subject = "Now - Liberty Virtual Rooms Meeting";
                }

                $viewParameters['business'] = $room->business;

                $this->mail->queue(
                    $data['liberty_representative_email'],
                    "Liberty Representative",
                    $subject,
                    ($room->business === 'lsm')
                        ? 'emails.lets-talk.notification'
                        : 'emails.lets-talk-lmre.notification',
                    $viewParameters,
                    $attachment
                );
            }
        } catch (Exception $e) {
            if (isset($participant) && !empty($participant)) {
                $response['participants']['failed'][] = [
                    'name' => $participant['user_name'],
                    'message' => $e->getMessage(),
                ];

                // save the error in the record
                $participant->error = $e->getMessage();
                $participant->save();
            } else {
                $response['participants']['failed'][] = [
                    'message' => $e->getMessage(),
                ];
            }
        }

        if (empty($response['participants']['failed'])) {
            return Response::json(
                [
                    'response' => 'success',
                    'clientsLink' => $clientsLink,
                    'data' => $response,
                ]
            );
        }

        return Response::json(
            [
                'response' => 'fail',
                'data' => $response,
            ]
        );
    }

    private function addSocialParticipant($data)
    {
        $room_code = $data['room_id'];
        $invite_originator_code = $data['self_user_code'];
        $is_external_user = $data['is_external_user'];

        // Get room
        $room = SocialRoom::where('room_code', $room_code)
            ->with('socialRoomType')
            ->first();

        // Get invite originator
        $invite_originator = SocialRoomParticipant::where('user_code', $invite_originator_code)
            ->first();

        if (!$room || !$invite_originator) {
            return false;
        }

        if ($is_external_user) {
            // Initialize data
            $name = $data['name'];
            $email = $data['email'];
            $phone = $data['mobile'];
            // Create external participant
            $social_room_participant = SocialRoomParticipant::create(
                [
                    'lt_social_room_id' => $room->id,
                    'user_name' => 'n/a',
                    'role' => 'guest_clients',
                    'person_id' => uniqid(),
                    'mobile' => 'n/a',
                    'user_code' => SocialRoomParticipant::roomCodeGenerator(),
                    'external_user' => $name,
                    'external_user_mobile' => $phone,
                    'external_user_email' => $email,
                ]
            );

            $params = [
                'room_name' => $room->name,
                'room_type' => $room->socialRoomType->name,
                'room_type_id' => $room->lt_social_room_type_id,
                'is_community' => $room->is_community,
                'external_user' => $social_room_participant->external_user,
                'invite_originator' => ($invite_originator->user_name == "n/a")
                    ? $invite_originator->external_user
                    : $invite_originator->user_name,
                //'join_room_link' => SocialRoomParticipant::generateLink($social_room_participant->user_code),
                'join_room_link' => SocialRoomParticipant::generateWaitingRoomCommonLink($room_code),
                'external_user_email' => $social_room_participant->external_user_email,
                'creator' => isset($room->cms)
                    ? $room->cms
                    : [],
            ];

            SocialRoomEmailNotification::externalUserTemplate4($params);

            $sendSms = new SocialRoomSmsNotification;
            $sendSms->sendMessageInvitedExternalUser4($room, $social_room_participant, $invite_originator);

            return true;
        } else {
            if (isset($data['person_id'])) {
                $libreps = CmsLibertyRepresentative::fetchAllFromCacheOrCms();

                if (isset($libreps[$data['person_id']])) {
                    $invitedLibRep = $libreps[$data['person_id']];
                    // Create librep participant
                    $social_room_participant = SocialRoomParticipant::create(
                        [
                            'lt_social_room_id' => $room->id,
                            'user_name' => $invitedLibRep['user_name'],
                            // 'role' => 'virtual-rooms',
                            'role' => 'librep_guest',
                            'person_id' => $invitedLibRep['person_id'],
                            'mobile' => $invitedLibRep['mobile'],
                            'user_code' => SocialRoomParticipant::roomCodeGenerator(),
                        ]
                    );

                    $params = [
                        'room_name' => $room->name,
                        'room_type' => $room->socialRoomType->name,
                        'room_type_id' => $room->lt_social_room_type_id,
                        'is_community' => $room->is_community,
                        'invite_originator' => ($invite_originator->user_name == "n/a")
                            ? $invite_originator->external_user
                            : $invite_originator->user_name,
                        //'join_room_link' => SocialRoomParticipant::generateLink($social_room_participant->user_code),
                        'join_room_link' => SocialRoomParticipant::generateWaitingRoomCommonLink($room_code),
                        'email' => $invitedLibRep['email'],
                        'user_name' => $invitedLibRep['user_name'],
                        'mobile' => $invitedLibRep['mobile'],
                        'creator' => isset($room->cms)
                            ? $room->cms
                            : [],
                    ];

                    SocialRoomEmailNotification::librepUserTemplate4($params);

                    $sendSms = new SocialRoomSmsNotification;
                    $participant = (object)$params;
                    $sendSms->sendMessageInvitedLibRepUser4($room, $participant);
                    return true;
                }
            }
        }
    }

    public function get_timezone_offset($remote_tz, $origin_tz = null)
    {
        if ($origin_tz === null) {
            if (!is_string($origin_tz = date_default_timezone_get())) {
                return false; // A UTC timestamp was returned -- bail out!
            }
        }
        $origin_dtz = new DateTimeZone($origin_tz);
        $remote_dtz = new DateTimeZone($remote_tz);
        $origin_dt = new DateTime("now", $origin_dtz);
        $remote_dt = new DateTime("now", $remote_dtz);
        $offset = $origin_dtz->getOffset($origin_dt) - $remote_dtz->getOffset($remote_dt);

        $hours = floor($offset / 3600);
        $mins = floor($offset / 60 % 60);
        $utcOffset = sprintf('%02d:%02d', $hours, $mins);
        return strpos($utcOffset, '-') !== false
            ? $utcOffset
            : "+" . $utcOffset;
    }

    private function getLibRepDetails($business, $personId)
    {
        // Cache was generated when accessing the invite page so it can be used here as well.
        $libertyRepresentatives = Cache::has($business . '_libertyRepresentativesLongLived')
            ? Cache::get($business . '_libertyRepresentativesLongLived')
            : [];

        if (empty($libertyRepresentatives || empty($personId))) {
            return [];
        }

        if (!isset($libertyRepresentatives[$personId]) && empty($libertyRepresentatives[$personId])) {
            [];
        }

        return $libertyRepresentatives[$personId];
    }

    private function isNumberUS($stringNum)
    {
        if (substr($stringNum, 0, 1) == '1' || substr($stringNum, 0, 2) == '+1') {
            return true;
        }
        return false;
    }

    private function createExternalContacts($data)
    {
        if (!isset($data['email'])) {
            return;
        }

        $business = $this->business;
        if (isset($data['business'])) {
            $business = $data['business'];
        }

        $existingContact = ResourceExternalContact::email($data['email'])->first();
        // Log the main public user attendee
        if (!$existingContact) {
            $newlyAddedResourceExternalContact = ResourceExternalContact::create(
                [
                    'business' => $business,
                    'name' => $data['name'],
                    'mobile_number' => $data['mobile_number'],
                    'company' => (isset($data['company']) && !empty($data['company']))
                        ? $data['company']
                        : null,
                    'email' => $data['email'],
                ]
            );
        }
    }

    private function getIcalUpdatedAllParticipants($room_id, $social = 0)
    {
        if ($social == 1) {
            $socialRoom = SocialRoom::where('room_code', $room_id)->first();
            $socialRoomSchedule = SocialRoomSchedule::where('lt_social_room_id', $socialRoom->id)->first();
            $participants = SocialRoomParticipant::where('lt_social_room_id', $socialRoom->id)->get();
            $organizerDetails = $this->getLibRepDetails('lsm', $socialRoom->created_by_id);
            $attendees = [];
            $attendees['string'] = "";
            $attendees['array'] = [];
            $attendees['string'] .= "\r\nORGANIZER;CN=" . $organizerDetails['name'] . ":MAILTO:" . config('app.cms.lsm.invite_email');
            $attendees['string'] .= "\r\nATTENDEE;CN=" . $organizerDetails['name'] . ";ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE:MAILTO:" . $organizerDetails['email'];

            $attendees['array'][$socialRoom->created_by_id] = $organizerDetails['email'];

            foreach ($participants as $participant) {
                // Add all attendees except organizer
                if ($participant->person_id !== $socialRoom->created_by_id) {
                    if ($participant->role == 'virtual-rooms') {
                        $details = $this->getLibRepDetails('lsm', $participant->person_id);
                        $attendees['string'] .= "\r\nATTENDEE;CN=" . $details['name'] . ";ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE:MAILTO:" . $details['email'];
                        $attendees['array'][$participant->person_id] = $details['email'];
                    } else {
                        $attendees['string'] .= "\r\nATTENDEE;CN=" . $participant->user_name . ";ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE:MAILTO:" . $participant->external_user_email;
                        $attendees['array'][$participant->person_id] = $participant->external_user_email;
                    }
                }
            }
            return $attendees;
        } else {
            $participants = VideoCallParticipant::where('room_id', $room_id)
                ->where('role', '!=', 'liberty_representative')
                ->orderBy('created_at', 'asc')
                ->get();

            $attendees = "";
            foreach ($participants as $key => $participant) {
                $attendees .= "\r\nATTENDEE;CN=" . $participant->user_name . ";ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE:MAILTO:" . $participant->email;
            }

            $libertyRepresentatives = VideoCallParticipant::where('room_id', $room_id)
                ->where('role', 'liberty_representative')
                ->orderBy('created_at', 'asc')
                ->get();

            $invite_email = config('app.cms.' . $this->business . '.invite_email');

            foreach ($libertyRepresentatives as $keyRep => $libertyRepresentative) {
                if ($keyRep == 0) { // Get the main Liberty Representative as the organizer
                    $attendees .= "\r\nORGANIZER;CN=Liberty Virtual Rooms:MAILTO:" . $invite_email;
                    $attendees .= "\r\nATTENDEE;CN=" . $libertyRepresentative->user_name . ";ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE:MAILTO:" . $libertyRepresentative->email;
                } else {
                    $attendees .= "\r\nATTENDEE;CN=" . $libertyRepresentative->user_name . ";ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE:MAILTO:" . $libertyRepresentative->email;
                }
            }

            return $attendees;
        }
    }

    /**
     * Save the details of the room and the participants
     *
     * @return json
     */
    public function generateRoom()
    {
        $data = $this->userRequest->all();

        $roles = [
            'client',
            'receptionist',
            'liberty_representative',
            'guest_clients',
        ];

        $data["client_phone"] = str_replace('(0)', '', $data["client_phone"]);
        if (substr($data["client_phone"], 0, 1) === '0') {
            $data["client_phone"] = ltrim($data["client_phone"], '0');
        } elseif (substr($data["client_phone"], 0, 1) !== '+') {
            $data["client_phone"] = "+44" . $data["client_phone"];
        }

        $liberty_representatives_data = Cache::has($this->business . '_libertyRepresentativesLongLived')
            ? Cache::get($this->business . '_libertyRepresentativesLongLived')
            : [];

        $errors = $this->validateNumber($data, $roles);

        if (!empty($errors)) {
            return Response::json(
                [
                    'response' => 'fail',
                    'data' => $errors,
                ]
            );
        }
        $getAttachedFiles = (isset($data['attached_files']) && $data['attached_files'] && !empty($data['attached_files']))
            ? $data['attached_files']
            : null;

        $conference_start_date = date(
            "Y-m-d H:i:s",
            strtotime($data['conference_date'] . ' ' . $data['time_start'] . ":00")
        );
        $conference_end_date = date(
            "Y-m-d H:i:s",
            strtotime($data['conference_date'] . ' ' . $data['time_end'] . ":00")
        );

        $booked_timezone = $data['booked_timezone'];
        $booked_timezone_offset = $this->get_timezone_offset('UTC', $booked_timezone);
        $booked_timezone_from = Carbon::parse(
            $conference_start_date,
            "Europe/London"
        )->setTimezone($booked_timezone)->format('Y-m-d H:i:s');
        $booked_timezone_to = Carbon::parse(
            $conference_end_date,
            "Europe/London"
        )->setTimezone($booked_timezone)->format('Y-m-d H:i:s');

        if ($data['booking_type'] == 'future' && !isset($data['ignore'])) {
            unset($data['ignore']);
            $checkForExisting = ResourceBooking::where('person_id', '=', $data['person_id'])
                ->where(
                    function ($query) use ($conference_start_date, $conference_end_date) {
                        $query->where(
                            function ($query) use ($conference_start_date, $conference_end_date) {
                                $query->where('from', '=', $conference_start_date)
                                    ->where('to', '=', $conference_end_date);
                            }
                        )
                            ->orWhere(
                                function ($query) use ($conference_start_date, $conference_end_date) {
                                    $query->where('from', '>=', $conference_start_date)
                                        ->where('from', '<', $conference_end_date);
                                }
                            )->orWhere(
                                function ($query) use ($conference_start_date, $conference_end_date) {
                                    $query->where('to', '>', $conference_start_date)
                                        ->where('to', '<=', $conference_end_date);
                                }
                            );
                    }
                )
                ->get()->count();

            if ($checkForExisting > 0) {
                return Response::json(
                    [
                        'response' => 'fail',
                        'data' => ['The slot has been taken. Please refresh the page to see new slots.'],
                    ]
                );
            }
        }

        $room = VideoCallRoom::create(
            [
                'name' => VideoCallRoom::createRoomName(),
                'conference_date_start' => $conference_start_date,
                'conference_date_end' => $conference_end_date,
                'created_at' => Carbon::now(),
                'created_by_id' => $data['created_by_id'],
                'created_by_login_type' => $data['created_by_login_type'],
                'created_by_role' => $data['created_by_role'],
                'booked_timezone' => $booked_timezone,
                'booked_timezone_offset' => $booked_timezone_offset,
                'booked_timezone_from' => $booked_timezone_from,
                'booked_timezone_to' => $booked_timezone_to,
                'business' => $this->business,
            ]
        );

        $response = [
            'room' => $room->name,
            'room_id' => $room->_id,
            'from' => $conference_start_date,
            'to' => $conference_end_date,
        ];

        // Book if created by client.
        $attached_files_info = [];
        if ($data['created_by_role'] === "client") {
            $bookingNowType = (isset($data['booking_now_type']) && !empty($data['booking_now_type']))
                ? $data['booking_now_type']
                : '';

            $resource_booking_data = ResourceBooking::create(
                [
                    'person_id' => $data['person_id'],
                    'room_id' => $room->_id,
                    'from' => $conference_start_date,
                    'to' => $conference_end_date,
                    'is_reminded' => 0,
                    'is_call_now' => ($data['booking_type'] == 'now')
                        ? 1
                        : 0,
                    'is_call_now_rep' => ($bookingNowType === 'now_representative')
                        ? 1
                        : 0,
                ]
            );

            $attached_files = json_decode($getAttachedFiles);

            if (!empty($attached_files)) {
                foreach ($attached_files as $files) {
                    foreach ($files as $filename => $cloudname) {
                        $uuid = explode('/', $cloudname)[1];

                        array_push($attached_files_info, [$filename => $uuid]);

                        ResourceBookingAttachedFile::create(
                            [
                                'cloudname' => $cloudname,
                                'filename' => $filename,
                                'uuid' => $uuid,
                                'resource_booking_id' => $resource_booking_data->id,
                            ]
                        );
                    }
                }

                $data['attached_files'] = $attached_files_info;
            }
        }

        $statusCallBack = config('app.twilio.vrStatusCallBackUrl');
        $params = !empty($statusCallBack)
            ? ['statusCallback' => config('app.client_frontend') . '/' . $statusCallBack]
            : [];

        $log = VideoLog::create(
            [
                'room_id' => $room->id,
                'type' => VideoLog::TYPE_SMS,
            ]
        );

        $externalUserLink = "";
        $guests = [];
        if (isset($data['guest_clients']) && !empty($data['guest_clients'])) {
            $data['guest_clients'] = json_decode($data['guest_clients']);
            $data['guest_emails'] = json_decode($data['guest_emails']);
            $data['guest_clientPhones'] = json_decode($data['guest_clientPhones']);
            //$data['guest_companies'] = json_decode($data['guest_companies']);

            // Structure the array components
            $guests = [];

            foreach ($data['guest_clients'] as $gclients) {
                $guests['guest_client'][] = $gclients;
            }

            foreach ($data['guest_emails'] as $emails) {
                $guests['guest_emails'][] = $emails;
            }

            foreach ($data['guest_clientPhones'] as $gclientPhones) {
                $guests['guest_clientPhones'][] = $gclientPhones;
            }
        }

        foreach ($roles as $role) {
            if ($role == "receptionist" && isset($data['booking_type']) && $data['booking_type'] == 'future') {
                continue;
            }

            //When a user invited a guest then add those participants
            if ($role == "guest_clients") {
                $guests = (array)$guests;
                $gkey = 0;

                if (isset($guests['guest_client']) && !empty($guests['guest_client'])) {
                    foreach ($guests['guest_client'] as $key => $gValues) {
                        if (isset($guests['guest_client'][$gkey]) && !empty($guests['guest_client'][$gkey])) {


                            $booked_timezone_from = Carbon::parse(
                                $conference_start_date,
                                "Europe/London"
                            )->setTimezone($booked_timezone)->format('Y-m-d H:i:s');
                            $booked_timezone_to = Carbon::parse(
                                $conference_end_date,
                                "Europe/London"
                            )->setTimezone($booked_timezone)->format('Y-m-d H:i:s');

                            $datetime_start = date("Ymd\THis", strtotime($booked_timezone_from));
                            $datetime_end = date("Ymd\THis", strtotime($booked_timezone_to));

                            $userCode = VideoCallParticipant::generateUserCode();


                            $participant = VideoCallParticipant::create(
                                [
                                    'room_id' => $room->_id,
                                    'user_name' => $guests['guest_client'][$gkey],
                                    'user_code' => $userCode,
                                    //'company' => $guests['guest_companies'][$gkey],
                                    'mobile_number' => $guests['guest_clientPhones'][$gkey],
                                    'email' => $guests['guest_emails'][$gkey],
                                    'meeting_subject' => '',
                                    'liberty_representative' => '',
                                    'representative_job_title' => '',
                                    'role' => $role,
                                    'booked_timezone' => $booked_timezone,
                                    'booked_timezone_offset' => $booked_timezone_offset,
                                    'booked_timezone_from' => $booked_timezone_from,
                                    'booked_timezone_to' => $booked_timezone_to,
                                ]
                            );

                            // SMS Guest Notification set this as text notification 15
                            $smsMessaging = VideoCallParticipant::generateMessageGuest(
                                $room->name,
                                $data['conference_date'],
                                $conference_start_date,
                                $conference_end_date,
                                $booked_timezone,
                                $booked_timezone_offset,
                                $booked_timezone_from,
                                $booked_timezone_to,
                                $data['liberty_representative']
                            );

                            if (isset($smsMessaging) && !empty($smsMessaging)) {
                                if (
                                    SuppressionList::where(
                                        'From',
                                        '=',
                                        sha1($guests['guest_clientPhones'][$gkey])
                                    )->where(
                                        'is_suppressed',
                                        '=',
                                        1
                                    )->get()->count() == 0
                                ) {
                                    if ($this->isNumberUS($guests['guest_clientPhones'][$gkey])) { //use US config phone number
                                        $twilioSms = $this->twilioConfigUS->message(
                                            $guests['guest_clientPhones'][$gkey],
                                            $smsMessaging,
                                            [],
                                            $params
                                        );
                                    } else {
                                        $twilioSms = $this->twilio->message(
                                            $guests['guest_clientPhones'][$gkey],
                                            $smsMessaging,
                                            [],
                                            $params
                                        );
                                    }
                                } else {
                                    $twilioSms = (object)[];
                                    $twilioSms->sid = "SMS suppressed.";
                                    $twilioSms->status = "SMS suppressed.";
                                }
                            }

                            $participant->sms_id = $twilioSms->sid;
                            $participant->save();


                            // Email Guest Notification
                            $is_reminder = false;
                            $is_reminder_before_5 = false;
                            $is_added_participant = false;

                            //$clientsLink = VideoCallParticipant::generateLink($userCode);
                            $clientsLink = VideoCallRoom::generateWaitingRoomLink($room->name);

                            $viewParameters = [
                                'to_name' => $guests['guest_client'][$gkey],
                                'video_call_details' => $data,
                                'twilio_data' => $clientsLink,
                                'role' => $role,
                                'is_reminder' => $is_reminder,
                                'is_reminder_before_5' => $is_reminder_before_5,
                                'is_added_participant' => $is_added_participant,
                                'title' => 'Your meeting has been scheduled',
                                'guests' => $guests,
                                'booked_timezone' => $booked_timezone,
                                'booked_timezone_offset' => $booked_timezone_offset,
                                'booked_timezone_from' => $booked_timezone_from,
                                'booked_timezone_to' => $booked_timezone_to,
                                'representative_time_zone' => '',
                                'representative_time_zone_from' => '',
                                'representative_time_zone_offset' => '',
                                'business' => $this->business,
                            ];

                            // $subject = "Your Liberty Virtual Rooms meeting has been scheduled";
                            $subject = $data['liberty_representative'] . " - Liberty Virtual Rooms meeting has been scheduled";

                            $description = "DESCRIPTION;LANGUAGE=en-US:" . $data['meeting_subject'] . " Link: " . $clientsLink;

                            $attendees = $this->getIcalUpdatedParticipants($viewParameters);

                            $attachmentSubject = $data['liberty_representative'] . " - Liberty Virtual Rooms";
                            $attachment = [
                                'ics' => [
                                    'StringValue' => "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Microsoft Corporation//Outlook 10.0 MIMEDIR//EN\r\nMETHOD:REQUEST\r\nBEGIN:VTIMEZONE\r\nTZID:" . $booked_timezone . "\r\nEND:VTIMEZONE\r\nBEGIN:VEVENT\r\nSUMMARY:" . $attachmentSubject . "\r\n" . $description . "\r\nDTSTART;VALUE=DATE-TIME:" . $datetime_start . "\r\nDTEND;VALUE=DATE-TIME:" . $datetime_end . "\r\nDTSTAMP;VALUE=DATE-TIME:" . $datetime_start . "\r\UID:" . $room_id . $datetime_start . "\r\nSEQUENCE:1\r\n" . "CREATED;VALUE=DATE-TIME:" . $datetime_start . "\r\nURL;VALUE=URI:" . $clientsLink . "\r\nLOCATION:" . $clientsLink . $attendees . "\r\nPRIORITY:5\r\nSTATUS:CONFIRMED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-P15M\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR",
                                    'DataType' => 'String',
                                ],
                            ];


                            $this->mail->queue(
                                $guests['guest_emails'][$gkey],
                                $guests['guest_client'][$gkey],
                                $subject,
                                ($this->business === 'lsm')
                                    ? 'emails.lets-talk.notification'
                                    : 'emails.lets-talk-lmre.notification',
                                $viewParameters,
                                $attachment
                            );


                            VideoLogData::create(
                                [
                                    'log_id' => $log->_id,
                                    'sms_id' => $twilioSms->sid,
                                    'human_readable' => 'Sending message to ' . $participant->user_name,
                                    'data' => $twilioSms->status == "SMS suppressed."
                                        ? (array)$twilioSms
                                        : $twilioSms->toArray(),
                                ]
                            );

                            $log->status = $twilioSms->status;
                            $log->save();
                        }
                        $gkey++;
                    }
                }
            }

            if (!empty($data[$role]) && !empty($data[$role . '_email'])) {
                $userCode = VideoCallParticipant::generateUserCode();

                $participant = VideoCallParticipant::create(
                    [
                        'room_id' => $room->_id,
                        'user_name' => $data[$role],
                        'user_code' => $userCode,
                        'mobile_number' => (isset($data[$role . '_phone']) && !empty($data[$role . '_phone']))
                            ? $data[$role . '_phone']
                            : '',
                        'company' => (isset($data['company']) && !empty($data['company'])
                            ? $data['company']
                            : ''),
                        'email' => (isset($data[$role . '_email']) && !empty($data[$role . '_email'])
                            ? $data[$role . '_email']
                            : ''),
                        'meeting_subject' => (isset($data['meeting_subject']) && !empty($data['meeting_subject'])
                            ? $data['meeting_subject']
                            : ''),
                        'representative_job_title' => ($role == 'liberty_representative')
                            ? $data['liberty_representative_job_title']
                            : '',
                        'role' => $role,
                        'booked_timezone' => $booked_timezone,
                        'booked_timezone_offset' => $booked_timezone_offset,
                        'booked_timezone_from' => $booked_timezone_from,
                        'booked_timezone_to' => $booked_timezone_to,
                        'details' => ($role == 'liberty_representative' && isset($liberty_representatives_data[$data['person_id']]))
                            ? $liberty_representatives_data[$data['person_id']]
                            : json_encode([]),
                    ]
                );

                try {
                    if ($role == "receptionist") {
                        $phoneNumbers = config('app.receptionPhones');
                        foreach ($phoneNumbers as $phoneNumber) {

                            // SMS Notification 2
                            if ($role == 'receptionist' && $data['booking_type'] == 'now' && $data['liberty_representative'] == 'Reception') {
                                if (
                                    SuppressionList::where('From', '=', sha1($phoneNumber))->where(
                                        'is_suppressed',
                                        '=',
                                        1
                                    )->get()->count() == 0
                                ) {
                                    if ($this->isNumberUS($phoneNumber)) { //use US config phone number
                                        $twilioSms = $this->twilioConfigUS->message(
                                            $phoneNumber,
                                            VideoCallParticipant::generateMessageN2(
                                                $room->name,
                                                $data['client'],
                                                $data['company']
                                            ),
                                            [],
                                            $params
                                        );
                                    } else {
                                        $twilioSms = $this->twilio->message(
                                            $phoneNumber,
                                            VideoCallParticipant::generateMessageN2(
                                                $room->name,
                                                $data['client'],
                                                $data['company']
                                            ),
                                            [],
                                            $params
                                        );
                                    }
                                } else {
                                    $twilioSms = (object)[];
                                    $twilioSms->sid = "SMS suppressed.";
                                    $twilioSms->status = "SMS suppressed.";
                                }
                            }

                            // SMS Notification 4
                            if ($role == 'receptionist' && $data['booking_type'] == 'now' && $data['liberty_representative'] != 'Reception') {
                                if (
                                    SuppressionList::where('From', '=', sha1($phoneNumber))->where(
                                        'is_suppressed',
                                        '=',
                                        1
                                    )->get()->count() == 0
                                ) {
                                    if ($this->isNumberUS($phoneNumber)) { //use US config phone number
                                        $twilioSms = $this->twilioConfigUS->message(
                                            $phoneNumber,
                                            VideoCallParticipant::generateMessageN4(
                                                $room->name,
                                                $data['client'],
                                                $data['company']
                                            ),
                                            [],
                                            $params
                                        );
                                    } else {
                                        $twilioSms = $this->twilio->message(
                                            $phoneNumber,
                                            VideoCallParticipant::generateMessageN4(
                                                $room->name,
                                                $data['client'],
                                                $data['company']
                                            ),
                                            [],
                                            $params
                                        );
                                    }
                                } else {
                                    $twilioSms = (object)[];
                                    $twilioSms->sid = "SMS suppressed.";
                                    $twilioSms->status = "SMS suppressed.";
                                }
                            }
                        }

                        if (isset($data['booking_type']) && $data['booking_type'] == 'now') {
                            $ids = Notification::select('token')->pluck('token')->all();

                            $message = [
                                'title' => 'Reception',
                                'body' => 'New incoming call',
                                "icon" => "/img/favicon.ico",
                                'vibrate' => 1,
                                'sound' => 1,
                                'link' => config('app.client_frontend') . '/virtual-rooms/v/' . $userCode,
                                'booked_timezone' => $booked_timezone,
                                'booked_timezone_offset' => $booked_timezone_offset,
                                'booked_timezone_from' => $booked_timezone_from,
                                'booked_timezone_to' => $booked_timezone_to,
                            ];

                            Notification::send($ids, $message);
                        }
                    } else {
                        if ((isset($data[$role . '_phone']) && !empty($data[$role . '_phone']))) {

                            // SMS based on type
                            // SMS Notification 1
                            if ($role == 'client' && $data['booking_type'] == 'now' && $data['liberty_representative'] == 'Reception') {
                                //$smsMessaging = VideoCallParticipant::generateMessageN1($userCode);
                                $smsMessaging = VideoCallParticipant::generateMessageN1($room->name);
                            }

                            // SMS Notification 3
                            if ($role == 'client' && $data['booking_type'] == 'now' && $data['liberty_representative'] != 'Reception') {
                                $smsMessaging = VideoCallParticipant::generateMessageN3($room->name);
                            }

                            // SMS Notficiation 5
                            if ($role == 'liberty_representative' && $data['booking_type'] == 'now') {
                                $smsMessaging = VideoCallParticipant::generateMessageN5(
                                    $room->name,
                                    $data['client'],
                                    $data['company']
                                );
                            }

                            // SMS Notification 7
                            if ($role == 'client' && $data['booking_type'] == 'future') {
                                $smsMessaging = VideoCallParticipant::generateMessageN7(
                                    $room->name,
                                    $data['conference_date'],
                                    $conference_start_date,
                                    $conference_end_date,
                                    $booked_timezone,
                                    $booked_timezone_offset,
                                    $booked_timezone_from,
                                    $booked_timezone_to,
                                    $data['liberty_representative']
                                );
                            }

                            // SMS Notification 8
                            if ($role == 'liberty_representative' && $data['booking_type'] == 'future') {
                                $smsMessaging = VideoCallParticipant::generateMessageN8(
                                    $room->name,
                                    $data['conference_date'],
                                    $conference_start_date,
                                    $conference_end_date,
                                    $booked_timezone,
                                    $booked_timezone_offset,
                                    $booked_timezone_from,
                                    $booked_timezone_to,
                                    $data['client'],
                                    $data['company'],
                                    (isset($liberty_representatives_data[$data['person_id']]) && !empty($liberty_representatives_data[$data['person_id']]))
                                        ? $liberty_representatives_data[$data['person_id']]['office_timezone']
                                        : '',
                                    (isset($liberty_representatives_data[$data['person_id']]) && !empty($liberty_representatives_data[$data['person_id']]))
                                        ? $this->get_timezone_offset(
                                            'UTC',
                                            $liberty_representatives_data[$data['person_id']]['office_timezone']
                                        )
                                        : '',
                                    (isset($liberty_representatives_data[$data['person_id']]) && !empty($liberty_representatives_data[$data['person_id']]))
                                        ? Carbon::parse(
                                            $booked_timezone_from,
                                            $booked_timezone
                                        )->setTimezone($liberty_representatives_data[$data['person_id']]['office_timezone'])
                                        : '',
                                    (isset($liberty_representatives_data[$data['person_id']]) && !empty($liberty_representatives_data[$data['person_id']]))
                                        ? Carbon::parse(
                                            $booked_timezone_to,
                                            $booked_timezone
                                        )->setTimezone($liberty_representatives_data[$data['person_id']]['office_timezone'])
                                        : ''
                                );
                            }

                            if (isset($smsMessaging) && !empty($smsMessaging)) {
                                if (
                                    SuppressionList::where(
                                        'From',
                                        '=',
                                        sha1($data[$role . '_phone'])
                                    )->where(
                                        'is_suppressed',
                                        '=',
                                        1
                                    )->get()->count() == 0
                                ) {
                                    if ($this->isNumberUS($data[$role . '_phone'])) { //use US config phone number
                                        $twilioSms = $this->twilioConfigUS->message(
                                            $data[$role . '_phone'],
                                            $smsMessaging,
                                            [],
                                            $params
                                        );
                                    } else {
                                        $twilioSms = $this->twilio->message(
                                            $data[$role . '_phone'],
                                            $smsMessaging,
                                            [],
                                            $params
                                        );
                                    }
                                } else {
                                    $twilioSms = (object)[];
                                    $twilioSms->sid = "SMS suppressed.";
                                    $twilioSms->status = "SMS suppressed.";
                                }
                            }
                        }
                    }


                    $participant->sms_id = $twilioSms->sid;
                    $participant->save();

                    VideoLogData::create(
                        [
                            'log_id' => $log->_id,
                            'sms_id' => $twilioSms->sid,
                            'human_readable' => 'Sending message to ' . $participant->user_name,
                            'data' => $twilioSms->status == "SMS suppressed."
                                ? (array)$twilioSms
                                : $twilioSms->toArray(),
                        ]
                    );

                    $log->status = $twilioSms->status;
                    $log->save();

                    $response['participants']['success'] = $participant->toArray();

                    // Check representative timezone

                    if ($role == 'liberty_representative' && isset($liberty_representatives_data[$data['person_id']]) && !empty($liberty_representatives_data[$data['person_id']])) {
                        $compareTimezone = $liberty_representatives_data[$data['person_id']]['office_timezone'];
                    } else {
                        $compareTimezone = $booked_timezone;
                    }

                    //$conference_start_date = Carbon::createFromFormat('Y-m-d H:i:s', $conference_start_date, $compareTimezone)->setTimezone("Europe/London");
                    //$conference_end_date = Carbon::createFromFormat('Y-m-d H:i:s', $conference_end_date, $compareTimezone)->setTimezone("Europe/London");

                    $datetime_start = date("Ymd\THis", strtotime($booked_timezone_from));
                    $datetime_end = date("Ymd\THis", strtotime($booked_timezone_to));

                    if (isset($data[$role . '_email']) && !empty($data[$role . '_email']) && !($role == 'receptionist' && $data['booking_type'] == 'future')) {
                        //$clientsLink = VideoCallParticipant::generateLink($userCode);
                        $clientsLink = VideoCallRoom::generateWaitingRoomLink($room->name);
                        if ($role == "client") {
                            $externalUserLink = $clientsLink;
                        }
                        $twilio_data = $clientsLink;
                        $clientsLink = parse_url($clientsLink);
                        $clientsLink = $clientsLink['scheme'] . "://" . $clientsLink['host'] . "\r\n\t" . $clientsLink['path'];

                        $is_reminder = false;
                        $is_reminder_before_5 = false;
                        $is_added_participant = false;

                        $viewParameters = [
                            'to_name' => $data[$role],
                            'video_call_details' => $data,
                            'twilio_data' => $twilio_data,
                            'role' => $role,
                            'is_reminder' => $is_reminder,
                            'is_reminder_before_5' => $is_reminder_before_5,
                            'is_added_participant' => $is_added_participant,
                            'user_code' => ($role == 'liberty_representative')
                                ? VideoCallParticipant::inviteLink($userCode)
                                : "",
                            'booked_timezone' => $booked_timezone,
                            'booked_timezone_offset' => $booked_timezone_offset,
                            'booked_timezone_from' => $booked_timezone_from,
                            'booked_timezone_to' => $booked_timezone_to,
                            'representative_time_zone' => ($role == 'liberty_representative' && isset($liberty_representatives_data[$data['person_id']]) && !empty($liberty_representatives_data[$data['person_id']]))
                                ? $liberty_representatives_data[$data['person_id']]['office_timezone']
                                : '',
                            'representative_time_zone_offset' => ($role == 'liberty_representative' && isset($liberty_representatives_data[$data['person_id']]) && !empty($liberty_representatives_data[$data['person_id']]))
                                ? $this->get_timezone_offset(
                                    'UTC',
                                    $liberty_representatives_data[$data['person_id']]['office_timezone']
                                )
                                : '',
                            'representative_time_zone_from' => ($role == 'liberty_representative' && isset($liberty_representatives_data[$data['person_id']]) && !empty($liberty_representatives_data[$data['person_id']]))
                                ? Carbon::parse(
                                    $booked_timezone_from,
                                    $booked_timezone
                                )->setTimezone($liberty_representatives_data[$data['person_id']]['office_timezone'])
                                : '',
                            'business' => $this->business,
                        ];

                        $attachment = null;
                        $subject = "Liberty Virtual Rooms";

                        // Email Notification 1 || 3
                        if ($role == 'client' && $data['booking_type'] == 'now') {
                            $subject = "Your Ongoing Liberty Virtual Rooms Meeting";
                            $viewParameters['title'] = "Your Ongoing Meeting";
                            $attachment = null;
                        }

                        // Email Notification 2 || 4
                        if ($role == 'receptionist' && $data['booking_type'] == 'now') {
                            $subject = "Now - Liberty Virtual Rooms Meeting";
                            $viewParameters['title'] = "Now - Incoming call";
                            $attachment = null;
                        }

                        // Email Notification 5 || 6
                        if ($role == 'liberty_representative' && $data['booking_type'] == 'now') {
                            // $subject = "Now - Liberty Virtual Rooms Meeting";
                            $subject = "Now - " . $data['client'] . " - Liberty Virtual Rooms Meeting";
                            $viewParameters['title'] = "Now - Incoming call";
                            $attachment = null;
                        }

                        if ($role == 'client' && $data['booking_type'] == 'future') {
                            if ($is_reminder && !$is_reminder_before_5) {
                                // Email Notification 9
                                // $subject = "Today - Your Liberty Virtual Rooms Meeting";
                                $subject = "Today - " . $data['liberty_representative'] . " - Liberty Virtual Rooms Meeting";
                                $description = "DESCRIPTION;LANGUAGE=en-US:" . $data['meeting_subject'] . " Link: " . $clientsLink;
                                $attachmentSubject = $data['liberty_representative'] . " - Liberty Virtual Rooms";

                                $attachment = [
                                    'ics' => [
                                        'StringValue' => "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Microsoft Corporation//Outlook 10.0 MIMEDIR//EN\r\nMETHOD:REQUEST\r\nBEGIN:VTIMEZONE\r\nTZID:" . $compareTimezone . "\r\nEND:VTIMEZONE\r\nBEGIN:VEVENT\r\nSUMMARY:" . $attachmentSubject . "\r\n" . $description . "\r\nDTSTART;VALUE=DATE-TIME:" . $datetime_start . "\r\nDTEND;VALUE=DATE-TIME:" . $datetime_end . "\r\nDTSTAMP;VALUE=DATE-TIME:" . $datetime_start . "\r\UID:" . $room_id . $datetime_start . "\r\nSEQUENCE:1\r\nATTENDEE:" . $data['email'] . "\r\n" . "CREATED;VALUE=DATE-TIME:" . $datetime_start . "\r\nURL;VALUE=URI:" . $clientsLink . "\r\nLOCATION:" . $clientsLink . "\r\nORGANIZER:mailto:" . $data['email'] . "\r\nPRIORITY:5\r\nSTATUS:CONFIRMED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-P15M\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR",
                                        'DataType' => 'String',
                                    ],
                                ];
                            } elseif (!$is_reminder && $is_reminder_before_5) {
                                // Email Notification 11
                                $subject = "Starting - Your Liberty Virtual Rooms Meeting";
                            } else {
                                // Email Notification 7
                                // $subject = "Your Liberty Virtual Rooms meeting has been scheduled";
                                $subject = $data['liberty_representative'] . " - Liberty Virtual Rooms meeting has been scheduled";
                                $viewParameters['title'] = "Your meeting has been scheduled";
                                $viewParameters['guests'] = (isset($guests['guest_client']) && !empty($guests['guest_client']))
                                    ? $guests
                                    : "";
                                $description = "DESCRIPTION;LANGUAGE=en-US:" . $data['meeting_subject'] . " Link: " . $clientsLink;

                                $attendees = $this->getIcalUpdatedParticipants($viewParameters);
                                $attachmentSubject = $data['liberty_representative'] . " - Liberty Virtual Rooms";
                                $attachment = [
                                    'ics' => [
                                        'StringValue' => "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Microsoft Corporation//Outlook 10.0 MIMEDIR//EN\r\nMETHOD:REQUEST\r\nBEGIN:VTIMEZONE\r\nTZID:" . $booked_timezone . "\r\nEND:VTIMEZONE\r\nBEGIN:VEVENT\r\nSUMMARY:" . $attachmentSubject . "\r\n" . $description . "\r\nDTSTART;VALUE=DATE-TIME:" . $datetime_start . "\r\nDTEND;VALUE=DATE-TIME:" . $datetime_end . "\r\nDTSTAMP;VALUE=DATE-TIME:" . $datetime_start . "\r\UID:" . $room_id . $datetime_start . "\r\nSEQUENCE:1\r\n" . "CREATED;VALUE=DATE-TIME:" . $datetime_start . "\r\nURL;VALUE=URI:" . $clientsLink . "\r\nLOCATION:" . $clientsLink . $attendees . "\r\nPRIORITY:5\r\nSTATUS:CONFIRMED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-P15M\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR",
                                        'DataType' => 'String',
                                    ],
                                ];
                            }
                        }

                        $method = "REQUEST";

                        if ($role == 'liberty_representative' && $data['booking_type'] == 'future') {
                            $booked_timezone_from = Carbon::parse(
                                $booked_timezone_from,
                                $booked_timezone
                            )->setTimezone($compareTimezone)->format('Y-m-d H:i:s');
                            $booked_timezone_to = Carbon::parse(
                                $booked_timezone_to,
                                $booked_timezone
                            )->setTimezone($compareTimezone)->format('Y-m-d H:i:s');
                            $datetime_start = date("Ymd\THis", strtotime($booked_timezone_from));
                            $datetime_end = date("Ymd\THis", strtotime($booked_timezone_to));
                            // Email Notification 10
                            if ($is_reminder && !$is_reminder_before_5) {
                                $subject = "Today - Liberty Virtual Rooms Meeting";
                                $description = "DESCRIPTION;LANGUAGE=en-US:" . $data['meeting_subject'] . " Link: " . $clientsLink;
                                // $chunks = str_split($fullDescription, 75);
                                // $description = implode("\r\n\t", $chunks);
                                $attachmentSubject = $data['client'] . " (" . $data['company'] . ") - Liberty Virtual Rooms";
                                $attachment = [
                                    'ics' => [
                                        'StringValue' => "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Microsoft Corporation//Outlook 10.0 MIMEDIR//EN\r\nMETHOD:REQUEST\r\nBEGIN:VTIMEZONE\r\nTZID:" . $compareTimezone . "\r\nEND:VTIMEZONE\r\nBEGIN:VEVENT\r\nSUMMARY:" . $attachmentSubject . "\r\n" . $description . "\r\nDTSTART;VALUE=DATE-TIME:" . $datetime_start . "\r\nDTEND;VALUE=DATE-TIME:" . $datetime_end . "\r\nDTSTAMP;VALUE=DATE-TIME:" . $datetime_start . "\r\UID:" . $room_id . $datetime_start . "\r\nSEQUENCE:1\r\nATTENDEE:" . $data['email'] . "\r\n" . "CREATED;VALUE=DATE-TIME:" . $datetime_start . "\r\nURL;VALUE=URI:" . $clientsLink . "\r\nLOCATION:" . $clientsLink . "\r\nORGANIZER:mailto:" . $data['email'] . "\r\nPRIORITY:5\r\nSTATUS:CONFIRMED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-P15M\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR",
                                        'DataType' => 'String',
                                    ],
                                ];
                            } elseif (!$is_reminder && $is_reminder_before_5) {
                                // Email Notification 12
                                $subject = "Starting - Liberty Virtual Rooms Meeting";
                            } else {
                                // Email Notification 8
                                // $subject = "You have a new Liberty Virtual Rooms meeting";
                                $subject = $data['client'] . " - New Liberty Virtual Rooms Meeting";
                                $viewParameters['title'] = "You have a new meeting";
                                $viewParameters['guests'] = (isset($guests['guest_client']) && !empty($guests['guest_client']))
                                    ? $guests
                                    : "";
                                $description = "DESCRIPTION;LANGUAGE=en-US:" . $data['meeting_subject'] . " Link: " . $clientsLink;

                                $attendees = $this->getIcalUpdatedParticipants($viewParameters, "self");

                                $attachmentSubject = $data['client'] . ", " . $data['company'] . " - Liberty Virtual Rooms";

                                $method = "REQUEST";

                                // if($data['liberty_representative_email'] == $viewParameters['main_representative_email']) {
                                //     $method = "PUBLISH";
                                // }

                                $attachmentSubject = $data['client'] . ", " . $data['company'] . " - Liberty Virtual Rooms";

                                $attachment = [
                                    'ics' => [
                                        'StringValue' => "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Microsoft Corporation//Outlook 10.0 MIMEDIR//EN\r\nMETHOD:" . $method . "\r\nBEGIN:VTIMEZONE\r\nTZID:" . $compareTimezone . "\r\nEND:VTIMEZONE\r\nBEGIN:VEVENT\r\nSUMMARY:" . $attachmentSubject . "\r\n" . $description . "\r\nDTSTART;TZID=" . $compareTimezone . ":" . $datetime_start . "\r\nDTEND;TZID=" . $compareTimezone . ":" . $datetime_end . "\r\nDTSTAMP;VALUE=DATE-TIME:" . $datetime_start . "\r\UID:" . $room_id . $datetime_start . "\r\nSEQUENCE:1\r\n" . "CREATED;VALUE=DATE-TIME:" . $datetime_start . "\r\nURL;VALUE=URI:" . $clientsLink . $attendees . "\r\nLOCATION:" . $clientsLink . "\r\nPRIORITY:5\r\nSTATUS:CONFIRMED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-P15M\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR",
                                        'DataType' => 'String',
                                    ],
                                ];
                            }
                        }


                        if ($role == 'receptionist') {
                            $this->mail->queue(
                                config('app.receptionEmail'),
                                $data[$role],
                                $subject,
                                ($this->business === 'lsm')
                                    ? 'emails.lets-talk.notification'
                                    : 'emails.lets-talk-lmre.notification',
                                $viewParameters,
                                $attachment
                            );
                        } else {
                            Log::info($role);
                            $this->mail->queue(
                                $data[$role . '_email'],
                                $data[$role],
                                $subject,
                                ($this->business === 'lsm')
                                    ? (($role == 'liberty_representative')
                                        ? 'emails.lets-talk.notification_yellow'
                                        : 'emails.lets-talk.notification')
                                    : 'emails.lets-talk-lmre.notification',
                                $viewParameters,
                                $attachment,
                                null,
                                'email',
                                $method
                            );
                        }
                    }
                } catch (Exception $e) {
                    $response['participants']['failed'][] = [
                        'name' => $participant['user_name'],
                        'message' => $e->getMessage(),
                    ];

                    // save the error in the record
                    $participant->error = $e->getMessage();
                    $participant->save();

                    // exit immediately if there was an error with any participant
                    break;
                }
            }
        }

        if (empty($response['participants']['failed'])) {
            return Response::json(
                [
                    'response' => 'success',
                    'clientsLink' => $externalUserLink,
                    'data' => $response,
                ]
            );
        }

        return Response::json(
            [
                'response' => 'fail',
                'data' => $response,
            ]
        );
    }

    /**
     * @param  $data
     * @param  $roles
     * @return array
     */
    private function validateNumber($data, $roles)
    {
        $fieldsToValidate = [];

        foreach ($roles as $role) {
            if (isset($data[$role . '_phone']) && !empty($data[$role . '_phone'])) {
                $fieldsToValidate[$role . '_phone'] = $data[$role . '_phone'];
            }
        }

        if (isset($data['guest_clientPhones']) && !empty($data['guest_clientPhones'])) {
            $guestClientPhones = $data['guest_clientPhones'];
            if (is_string($data['guest_clientPhones'])) {
                $guestClientPhones = json_decode($data['guest_clientPhones']);
            }

            if (!empty($guestClientPhones)) {
                foreach ($guestClientPhones as $key => $number) {
                    if (!empty($number)) {
                        $fieldsToValidate['guest_clientPhones.' . $key] = $number;
                    }
                }
            }
        }

        $errorMessages = [];

        foreach ($fieldsToValidate as $key => $number) {
            try {
                $this->twilioClient->lookups->v1->phoneNumbers($number)->fetch();
            } catch (Exception $e) {
                $errorMessages[$key] = ['Please enter Mobile Phone in a valid format.'];
            }
        }

        return $errorMessages;
    }

    private function getIcalUpdatedParticipants($viewParameters, $organizer = "other")
    {
        $attendees = "";

        $invite_email = config('app.cms.' . $this->business . '.invite_email');

        if ( // Get the Liberty Representative as the organizer
            isset($viewParameters['main_representative']) && !empty($viewParameters['main_representative'])
        ) {
            $attendees .= "\r\nORGANIZER;CN=Liberty Virtual Rooms:MAILTO:" . $invite_email;
            $attendees .= "\r\nATTENDEE;CN=" . $viewParameters['main_representative'] . ";ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE:MAILTO:" . $viewParameters['main_representative_email'];
        } else {
            $attendees .= "\r\nORGANIZER;CN=Liberty Virtual Rooms:MAILTO:" . $invite_email;
            $attendees .= "\r\nATTENDEE;CN=" . $viewParameters['video_call_details']['liberty_representative'] . ";ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE:MAILTO:" . $viewParameters['video_call_details']['liberty_representative_email'];
        }

        if ( // Get clients (guest 1) name and email
            isset($viewParameters['video_call_details']['client']) && !empty($viewParameters['video_call_details']['client'])
            && isset($viewParameters['video_call_details']['email']) && !empty($viewParameters['video_call_details']['email'])
        ) {
            $attendees .= "\r\nATTENDEE;CN=" . $viewParameters['video_call_details']['client'] . ";ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE:MAILTO:" . $viewParameters['video_call_details']['email'];

            // Log the main public user attendee
            $this->createExternalContacts(
                [
                    'business' => $viewParameters['business'],
                    'name' => $viewParameters['video_call_details']['client'],
                    'mobile_number' => $viewParameters['video_call_details']['client_phone'],
                    'company' => $viewParameters['video_call_details']['company'],
                    'email' => $viewParameters['video_call_details']['email'],
                ]
            );
        }

        if ( // Get guests 2 to 4's name and email
            isset($viewParameters['guests']['guest_client']) && !empty($viewParameters['guests']['guest_client'])
        ) {
            $gkey = -1;
            foreach ($viewParameters['guests']['guest_client'] as $gValues) {
                $gkey++;
                if (isset($viewParameters['guests']['guest_client'][$gkey]) && !empty($viewParameters['guests']['guest_client'][$gkey])) {
                    $attendees .= "\r\nATTENDEE;CN=" . $viewParameters['guests']['guest_client'][$gkey] . ";ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE:MAILTO:" . $viewParameters['guests']['guest_emails'][$gkey];

                    // Log the main public user attendee
                    $this->createExternalContacts(
                        [
                            'business' => $viewParameters['business'],
                            'name' => $viewParameters['guests']['guest_client'][$gkey],
                            'mobile_number' => $viewParameters['guests']['guest_clientPhones'][$gkey],
                            'company' => null,
                            'email' => $viewParameters['guests']['guest_emails'][$gkey],
                        ]
                    );
                }
            }
        }

        // Get other Librep guest
        if (isset($viewParameters['other_representatives']) && !empty($viewParameters['other_representatives'])) {
            foreach ($viewParameters['other_representatives'] as $librep) {
                if (isset($librep->user_name) && isset($librep->email) && !empty($librep->user_name) && !empty($librep->email)) {
                    $attendees .= "\r\nATTENDEE;CN=" . $librep->user_name . ";ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE:MAILTO:" . $librep->email;
                }
            }
        }

        return $attendees;
    }

    /**
     * Return the details of the call so users can login to call setup
     *
     * @return mixed
     */
    public function login()
    {
        $code = $this->userRequest->get('code');
        $participant = VideoCallParticipant::where('user_code', $code)->first();

        // check for SocialRoomParticipant
        if (!$participant) {

            $socialRoomParticipant = SocialRoomParticipant::with('socialRoom')->where('user_code', $code)->first();
            $roomCode = $socialRoomParticipant->socialRoom->room_code;
            $socialRoomType = $socialRoomParticipant->socialRoom->lt_social_room_type_id;
            if ($socialRoomType == 1 || $socialRoomType == 2) { //always allow entry for watercooler and coffee break rooms

                return Response::json(
                    [
                        'response' => 'success',
                        'data' => [
                            'room' => $socialRoomParticipant->socialRoom->room_code,
                            'name' => $socialRoomParticipant->user_name,
                            'role' => $socialRoomParticipant->role,
                            'business' => (isset($socialRoomParticipant->business) && !empty($socialRoomParticipant->business))
                                ? $socialRoomParticipant->business
                                : 'lsm',
                            // 'participant_count' => $participantCount
                        ],
                    ]
                );
            } else {
                $social_room = SocialRoom::where('lt_social_room_type_id', $socialRoomType)
                    ->where('status', 'approved')
                    ->where('room_code', $socialRoomParticipant->socialRoom->room_code)
                    ->with('socialRoomSchedule')
                    ->first();

                if (!empty($social_room->socialRoomSchedule)) {

                    if ($social_room->socialRoomSchedule->duration_type == 'permanent') { //always allow entry for permanent
                        return Response::json(
                            [
                                'response' => 'success',
                                'data' => [
                                    'room' => $socialRoomParticipant->socialRoom->room_code,
                                    'name' => $socialRoomParticipant->user_name,
                                    'role' => $socialRoomParticipant->role,
                                    'business' => 'lsm',
                                    // 'participant_count' => $participantCount
                                ],
                            ]
                        );
                    } else { // check schedule
                        $room[0] = $social_room;
                        $upcoming_schedule = UpcomingSchedule::generateWithBuffer($room, 'Europe/London');
                        $isRoomReady = false;
                        if (isset($upcoming_schedule['upcomingSchedules'][0]['schedule'])) {
                            $now = Carbon::now()->timezone('Europe/London');
                            $latestSchedule = Carbon::parse(
                                $upcoming_schedule['upcomingSchedules'][0]['schedule'],
                                'Europe/London'
                            );

                            $validUntil = Carbon::parse($social_room->socialRoomSchedule->valid_until, 'Europe/London');

                            // Room is still accessable if valid until is still greater than the current date time
                            $isRoomReady = ((((int)$latestSchedule->diffInMinutes(
                                $now,
                                false
                            ) >= -5)) 
                            && (((int)$latestSchedule->diffInMinutes($now, false) <= 60))) || $validUntil >= $now;
                        }

                        if ($isRoomReady) {
                            return Response::json(
                                [
                                    'response' => 'success',
                                    'data' => [
                                        'room' => $socialRoomParticipant->socialRoom->room_code,
                                        'name' => $socialRoomParticipant->user_name,
                                        'role' => $socialRoomParticipant->role,
                                        'business' => 'lsm',
                                        // 'participant_count' => $participantCount
                                    ],
                                ]
                            );
                        } else {
                            return Response::json(
                                [
                                    'response' => 'fail',
                                    'data' => [
                                        'room' => $socialRoomParticipant->socialRoom->room_code,
                                        'name' => $socialRoomParticipant->user_name,
                                        'role' => $socialRoomParticipant->role,
                                        'business' => 'lsm',
                                    ],
                                ]
                            );
                        }
                    }
                }
            }
        }


        if ($participant) {
            /**
             * @var VideoCallRoom $videoRoom
             */
            $videoRoom = VideoCallRoom::where('name', $participant->room->name)->first();
            $participantCount = VideoCallParticipant::where('room_id', $participant->room_id)->get()->count();

            if (!$videoRoom->isExpired()) {
                return Response::json(
                    [
                        'response' => 'success',
                        'data' => [
                            'room' => $participant->room->name,
                            'name' => $participant->user_name,
                            'role' => $participant->role,
                            'business' => isset($videoRoom->business)
                                ? $videoRoom->business
                                : 'lsm',
                            'participant_count' => $participantCount,
                        ],
                    ]
                );
            } else {
                return Response::json(
                    [
                        'response' => 'fail',
                        'data' => [
                            'room' => $participant->room->name,
                            'name' => $participant->user_name,
                            'role' => $participant->role,
                            'business' => isset($videoRoom->business)
                                ? $videoRoom->business
                                : 'lsm',
                            'participant_count' => $participantCount,
                        ],
                    ]
                );
            }
        }
    }

    /**
     * Return Liberty CMS Landing Page Content
     *
     * @return mixed
     */
    public function landingPageContent()
    {
        $landingPageList = json_decode(Cms::get('workspaces/' . $this->getCmsConfig('lets_talk_workspace') . '/content-types/' . $this->getCmsConfig('landing_page_content_type') . '/content-entries/' . $this->getCmsConfig('landing_page_content_entry')));

        $features = $landingPageList->{$this->getCmsConfig('landing_page_content_list')};

        $data['features'] = $features;

        $data['subtitle'] = $landingPageList->{$this->getCmsConfig('landing_page_subtitle')};
        $data['benefits'] = $landingPageList->{$this->getCmsConfig('landing_page_benefits_title')};
        $data['banner'] = $landingPageList->{$this->getCmsConfig('landing_page_banner')};
        $data['how_it_works_title'] = $landingPageList->{$this->getCmsConfig('landing_page_how_it_works_title')};
        $data['how_it_works_step_1'] = $landingPageList->{$this->getCmsConfig('landing_page_how_it_works_step_1')};
        $data['how_it_works_step_2'] = $landingPageList->{$this->getCmsConfig('landing_page_how_it_works_step_2')};
        $data['how_it_works_step_3'] = $landingPageList->{$this->getCmsConfig('landing_page_how_it_works_step_3')};
        $data['how_it_works_description'] = $landingPageList->{$this->getCmsConfig('landing_page_how_it_works_description')};

        return Response::json(
            [
                'data' => $data,
            ]
        );
    }

    /**
     * Return Liberty Representative schedules
     *
     * @return mixed
     */
    public function schedules($date = null)
    {
        $date = (isset($date) && !empty($date))
            ? $date
            : $this->userRequest->get('date');
        $input = $this->userRequest->all();
        if (isset($input['is_social']) && $input['is_social'] == true) {
            $libertyRepresentatives = CmsLibertyRepresentative::fetchAllFromCacheOrCms();
            return Response::json(
                [
                    'response' => 'success',
                    'data' => [
                        'liberty_representatives' => $libertyRepresentatives,
                    ],
                ]
            );
        }
        $libertyRepresentatives = $this->fetchLibRepFromCacheOrCms();

        if ($this->userRequest->has('force_recache')) {
            return Response::json(
                [
                    'response' => 'success',
                    'data' => 'Cache rebuilt.',
                ]
            );
        }

        if ($this->userRequest->has('return') && $this->userRequest->get('return') == 'no-availability') {
            return Response::json(
                [
                    'response' => 'success',
                    'data' => [
                        'liberty_representatives' => isset($limitedLibertyRepresentatives)
                            ? $limitedLibertyRepresentatives
                            : $libertyRepresentatives,
                        'options' => $this->getCmsOptions(),
                    ],
                ]
            );
        }

        // Search liberty representatives by name
        $libertyRepresentatives = $this->searchLibertyRepresentative($libertyRepresentatives);

        // if no date provided, we will get schedule for today until next business day's schedule
        if ($date == null) {
            $startOfDay = Carbon::now();
            $endOfDay = Carbon::now()->copy()->endOfDay();
        } else {
            $startOfDay = Carbon::parse($date);
            $endOfDay = $startOfDay->copy()->endOfDay();

            // if date provided is today, we only need today and next business day's schedule
            // and within the operating hours with respect to timezone

            $tzCurrentTime = Carbon::parse(date('Y-m-d H:i:s'), 'Europe/London')->setTimezone(
                $this->userRequest->get(
                    'timezone',
                    'Europe/London'
                )
            )->format('Y-m-d H:i:s');
            $tzOfficeStart = date('Y-m-d 08:00:00');
            $tzOfficeEnd = date('Y-m-d 18:00:00');

            if (
                $startOfDay->isToday()
                && ((strtotime($tzCurrentTime) >= strtotime($tzOfficeStart))
                    && (strtotime($tzCurrentTime) <= strtotime($tzOfficeEnd)))
            ) {
                $endOfDay = $startOfDay->copy()->addWeekday(1)->endOfDay();
            }
        }


        $schedules = ResourceSchedule::whereBetween('from', [$startOfDay, $endOfDay])->orWhereBetween(
            'to',
            [$startOfDay, $endOfDay]
        )->where('deleted_at', null)->get();

        $bookings = ResourceBooking::whereBetween('from', [$startOfDay, $endOfDay])->orWhereBetween(
            'to',
            [$startOfDay, $endOfDay]
        )->where('deleted_at', null)->get();

        $timezone = $this->userRequest->get('timezone', 'Europe/London');
        //dd($libertyRepresentatives);
        $libertyRepresentatives = $this->getRepresentatives(
            $libertyRepresentatives,
            $schedules,
            $bookings,
            $startOfDay,
            $endOfDay,
            $timezone
        );

        // Limit number of representatives
        if ($this->userRequest->has('limit') && is_numeric($this->userRequest->get('limit'))) {
            $limit = (int)$this->userRequest->get('limit');
            $limitedLibertyRepresentatives = (!empty($libertyRepresentatives) && is_array($libertyRepresentatives))
                ? array_slice($libertyRepresentatives, 0, $limit)
                : [];
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => [
                    'liberty_representatives' => isset($limitedLibertyRepresentatives)
                        ? $limitedLibertyRepresentatives
                        : $libertyRepresentatives,
                    'options' => $this->getCmsOptions(),
                ],
            ]
        );
    }

    private function fetchLibRepFromCacheOrCms()
    {
        // Forget social room cache libreps on force_recache
        if ($this->userRequest->has('force_recache')) {
            Cache::forget($this->business . '_socialRoomLibertyRepresenativesCms');
            Cache::forget($this->business . '_socialRoomPeopleProfileCms');
            Cache::forget($this->business . '_socialRoomLibertyRepresentatives');
        }

        if (Cache::has($this->business . '_libertyRepresenativesCms') && !$this->userRequest->has('force_recache')) {
            $libertyRepresenativesCms = Cache::get($this->business . '_libertyRepresenativesCms');
        } else {
            $libertyRepresenativesCms = json_decode(Cms::get('workspaces/' . $this->getCmsConfig('lets_talk_workspace') . '/content-types/' . $this->getCmsConfig('liberty_representatives_content_type') . '/content-entries?joined=true&query={"status":"publish","operator":"="}'));
            Cache::put($this->business . '_libertyRepresenativesCms', $libertyRepresenativesCms, 1440);
        }

        if (Cache::has($this->business . '_peopleProfileCms') && !$this->userRequest->has('force_recache')) {
            $peopleProfileCms = Cache::get($this->business . '_peopleProfileCms');
        } else {
            $peopleProfileCms = json_decode(Cms::get('workspaces/' . $this->getCmsConfig('workspace_id') . '/content-types/' . $this->getCmsConfig('people_content_type') . '/content-entries?joined=true&query={"status":"publish","operator":"="}'));
            Cache::put($this->business . '_peopleProfileCms', $peopleProfileCms, 1440);
        }

        if (Cache::has($this->business . '_libertyRepresentatives') && !$this->userRequest->has('force_recache')) {
            $libertyRepresentatives = Cache::get($this->business . '_libertyRepresentatives');
        } else {
            $peopleProfile = [];
            if (isset($peopleProfileCms->data)) {
                foreach ($peopleProfileCms->data as $profile) {
                    $peopleProfile[$profile->_id]['line_of_business'] = [];
                    if (isset($profile->{$this->getCmsConfig('liberty_representative_line_of_business')})) {
                        foreach ($profile->{$this->getCmsConfig('liberty_representative_line_of_business')} as $lob) {
                            if (isset($lob->name)) {
                                $peopleProfile[$profile->_id]['line_of_business'][] = $lob->name;
                                $peopleProfile[$profile->_id]['line_of_business_slug'][] = $lob->slug;
                            }
                        }
                    }
                    $timezoneAssignment = isset($profile->{$this->getCmsConfig('people_profile_office')})
                        ? $profile->{$this->getCmsConfig('people_profile_office')}
                        : null;
                    $peopleProfile[$profile->_id]['timezone'] = isset($timezoneAssignment[0])
                        ? $timezoneAssignment[0]->{$this->getCmsConfig('people_profile_office_timezone')}
                        : null;
                    $peopleProfile[$profile->_id]['office_title'] = isset($timezoneAssignment[0])
                        ? $timezoneAssignment[0]->{$this->getCmsConfig('people_profile_office_title')}
                        : null;
                }
            }

            if (isset($libertyRepresenativesCms->data) && isset($libertyRepresenativesCms->data['0']) && isset($libertyRepresenativesCms->data['0']->{$this->getCmsConfig('liberty_representatives')})) {
                $indexLibRep = 0;
                $i = 0;
                foreach ($libertyRepresenativesCms->data as $key => $lrc) {
                    if ($lrc->slug == "liberty-representatives") {
                        $indexLibRep = $i;
                    }
                    $i = $i + 1;
                }
                $libertyRepresenatives = $libertyRepresenativesCms->data[$indexLibRep]->{$this->getCmsConfig('liberty_representatives')};
                // if($this->business == 'lsm') {
                //     $libertyRepresenatives = $libertyRepresenativesCms->data['2']->{$this->getCmsConfig('liberty_representatives')};
                // } else {
                //     $libertyRepresenatives = $libertyRepresenativesCms->data['0']->{$this->getCmsConfig('liberty_representatives')};
                // }

            } else {
                $libertyRepresenatives = [];
            }

            foreach ($libertyRepresenatives as $key => $libRep) {
                // $this->getCmsConfig('liberty_representative_email')
                if (!isset($libRep->status) || $libRep->status != 'publish' || empty($libRep->{$this->getCmsConfig('liberty_representative_mobile_number')}) || empty($libRep->{$this->getCmsConfig('liberty_representative_email')})) {
                    unset($libertyRepresenatives[$key]);
                    continue;
                }

                if (isset($libRep->{$this->getCmsConfig('liberty_representative_line_of_business')}) && isset($peopleProfile[$libRep->_id]['line_of_business'])) {
                    $libRep->{$this->getCmsConfig('liberty_representative_line_of_business')} = $peopleProfile[$libRep->_id]['line_of_business'];
                }
                if (isset($peopleProfile[$libRep->_id]['line_of_business_slug'])) {
                    $libRep->{$this->getCmsConfig('liberty_representative_line_of_business') . '_slug'} = $peopleProfile[$libRep->_id]['line_of_business_slug'];
                }
            }
            array_splice($libertyRepresenatives, 0, 0);

            $libertyRepresentatives = $this->compileRepData($libertyRepresenatives, $peopleProfile);

            Cache::put($this->business . '_libertyRepresentatives', $libertyRepresentatives, 1440);
            Cache::put($this->business . '_libertyRepresentativesLongLived', $libertyRepresentatives, 1440);
        }

        return $libertyRepresentatives;
    }

    // Search Liberty Representative by name

    private function compileRepData($representatives, $timezone)
    {
        if (empty($representatives)) {
            return [];
        }

        foreach ($representatives as $value) {
            if (isset($value->{$this->getCmsConfig('liberty_representative_mobile_number')}) && isset($value->{$this->getCmsConfig('liberty_representative_email')})) {
                $mobileNumber = str_replace(
                    '(0)',
                    '',
                    $value->{$this->getCmsConfig('liberty_representative_mobile_number')}
                );
                if (substr($mobileNumber, 0, 1) === '0') {
                    $mobileNumber = ltrim($mobileNumber, '0');
                } elseif (substr($mobileNumber, 0, 1) !== '+') {
                    $mobileNumber = "+44" . $mobileNumber;
                }
                if (is_array($value->{$this->getCmsConfig('liberty_representative_profile_picture')}) && isset($value->{$this->getCmsConfig('liberty_representative_profile_picture')}[0])) {
                    $parsedUrl = parse_url($value->{$this->getCmsConfig('liberty_representative_profile_picture')}[0]->url);
                    $parsedUrl['query'] = urlencode($parsedUrl['query']);
                    $profileImage = $parsedUrl['scheme'] . "://" . $parsedUrl['host'] . $parsedUrl['path'] . '?' . $parsedUrl['query'];
                } else {
                    $profileImage = "https://asset-management-dev.s3.eu-west-1.amazonaws.com/assets/p2NoaMtcZQmyXDW9hoGy0GVtKpcZOQBi5mQg1nKe.png?name%3Duser-icon-silhouette-ae9ddcaf4a156a47931d5719ecee17b9.png%26size%3D6KB%26mime%3Dimage%2Fpng";
                }

                $officeNumber = !empty($value->{config('app.cms.people_profile_office_number')})
                    ? $value->{config('app.cms.people_profile_office_number')}
                    : '';

                $reprsentativeList[$value->_id] = [
                    'name' => $value->name,
                    'line_of_business' => isset($value->{$this->getCmsConfig('liberty_representative_line_of_business')})
                        ? $value->{$this->getCmsConfig('liberty_representative_line_of_business')}
                        : [],
                    'line_of_business_slug' => isset($value->{$this->getCmsConfig('liberty_representative_line_of_business') . '_slug'})
                        ? $value->{$this->getCmsConfig('liberty_representative_line_of_business') . '_slug'}
                        : [],
                    'bio' => isset($value->{config('app.cms.people_profile_bio')})
                        ? $value->{config('app.cms.people_profile_bio')}
                        : '',
                    'business_function' => isset($value->{$this->getCmsConfig('liberty_representative_business_function')})
                        ? $value->{$this->getCmsConfig('liberty_representative_business_function')}
                        : null,
                    'email' => $value->{$this->getCmsConfig('liberty_representative_email')},
                    'mobile' => "+" . preg_replace('/[^0-9]/', '', $mobileNumber),
                    'office_number' => $officeNumber,
                    'profile_picture' => $profileImage,
                    'job_title' => $value->{$this->getCmsConfig('liberty_representative_job_title')},
                    'office_timezone' => isset($timezone[$value->_id]['timezone'])
                        ? $timezone[$value->_id]['timezone']
                        : 'Europe/London',
                    'office_title' => isset($timezone[$value->_id]['office_title'])
                        ? $timezone[$value->_id]['office_title']
                        : 'N/A',
                    'schedule' => isset($reprsentativeSchedule[$value->_id])
                        ? $reprsentativeSchedule[$value->_id]
                        : [],
                ];
            }
        }

        return $reprsentativeList;
    }

    private function getCmsConfig($config)
    {
        return config('app.cms.' . $this->business . '.' . $config);
    }

    private function getCmsOptions()
    {
        if (Cache::has($this->business . '_cms_vr_options') && !$this->userRequest->has('force_recache')) {
            $options = Cache::get($this->business . '_cms_vr_options');
        } else {
            $options = json_decode(Cms::get('workspaces/' . $this->getCmsConfig('lets_talk_workspace') . '/content-types/' . $this->getCmsConfig('options_content_type') . '/content-entries?joined=true&query={"status":"publish","operator":"=","locale_id":1}'));
            Cache::put($this->business . '_cms_vr_options', $options, 1440);
        }

        $optionsArray = [];

        if ($options && $options->data) {
            foreach ($options->data as $entry) {
                $optionsArray[$entry->name] = [
                    'intro' => $entry->{$this->getCmsConfig('options_intro')},
                    'heading' => $entry->{$this->getCmsConfig('options_heading')},
                ];
            }
        }

        return $optionsArray;
    }

    private function searchLibertyRepresentative($libertyRepresentatives)
    {
        // key name is also the key name to be searched on the librep data
        $searchQueryArr = [
            'name' => $this->userRequest->get('name')
                ? strtolower(trim($this->userRequest->get('name')))
                : null,
            'office_title' => $this->userRequest->get('office_title')
                ? strtolower(trim($this->userRequest->get('office_title')))
                : null,
            'line_of_business' => $this->userRequest->get('line_of_business')
                ? strtolower(trim($this->userRequest->get('line_of_business')))
                : null,
            'business_function' => $this->userRequest->get('business_function')
                ? strtolower(trim($this->userRequest->get('business_function')))
                : null,
        ];
        // filter $searchQueryArr, make sure empty values are removed
        $searchQueryArr = array_filter(
            $searchQueryArr,
            function ($value) {
                return !empty($value);
            }
        );
        // early return if no search query provided
        if (empty($searchQueryArr)) {
            return $libertyRepresentatives;
        }

        $foundRepresentatives = [];
        foreach ($libertyRepresentatives as $key => $libRep) {

            $search_score_lb = 0;
            $search_score_lb_selected = false;
            $search_score_bf = 0;
            $search_score_bf_selected = false;
            $search_score_ot = 0;
            $search_score_ot_selected = false;
            $search_score_ne = 0;
            $search_score_ne_selected = false;

            foreach ($searchQueryArr as $searchKey => $searchValue) {

                if (!isset($libRep[$searchKey])) {
                    continue;
                }

                $librepDataCol = $libRep[$searchKey];

                // Check if the criteria is selected
                if ($searchKey === 'line_of_business') {
                    $search_score_lb_selected = true;
                }

                if ($searchKey === 'office_title') {
                    $search_score_ot_selected = true;
                }

                if ($searchKey === 'business_function') {
                    $search_score_bf_selected = true;
                }

                if ($searchKey === 'name') {
                    $search_score_ne_selected = true;
                }

                // Identify if it hits the criteria
                // if (is_array($librepDataCol) && $searchKey==='line_of_business' && array_search($this->titleCase($searchValue), $librepDataCol)>-1) {
                //     $search_score_lb++;
                // };

                if (is_array($librepDataCol) && $searchKey === 'line_of_business') {
                    foreach ($librepDataCol as $dataCol) {
                        if (is_string($dataCol) && strtolower($dataCol) === $searchValue) {
                            $search_score_lb++;
                            continue 1;
                        }
                    }
                }

                //if (($searchKey==='office_title') && strtolower($searchValue)==strtolower($librepDataCol)) {
                if ($searchKey === 'office_title') {

                    if ($searchValue == 'bogotá') {
                        $searchValue = str_replace('bogotá', 'BOGOT&AACUTE;', $searchValue);
                    }

                    if (strtoupper($searchValue) == $this->unaccent(strtoupper($librepDataCol))) {
                        $search_score_ot++;
                    }
                }

                if (($searchKey === 'business_function') && strtoupper($searchValue) == strtoupper($librepDataCol)) {
                    $search_score_bf++;
                }

                if ($searchKey === 'name' && strpos(strtolower($librepDataCol), $searchValue) !== false) {
                    $search_score_ne++;
                }

                if (($search_score_ne_selected && $search_score_ne == 0)
                    || ($search_score_lb_selected && $search_score_lb == 0)
                    || ($search_score_bf_selected && $search_score_bf == 0)
                    || ($search_score_ot_selected && $search_score_ot == 0)

                    || ($search_score_ne_selected && $search_score_lb_selected && ($search_score_ne == 0 || $search_score_lb == 0))
                    || ($search_score_lb_selected && $search_score_bf_selected && ($search_score_lb_selected == 0 || $search_score_bf == 0))
                    || ($search_score_bf_selected && $search_score_ne_selected && ($search_score_ne == 0 || $search_score_bf == 0))
                    || ($search_score_ot_selected && $search_score_ne_selected && ($search_score_ot == 0 || $search_score_ne == 0))

                    || ($search_score_ne_selected && $search_score_lb_selected && $search_score_bf_selected && ($search_score_ne == 0 || $search_score_lb == 0 || $search_score_bf == 0))
                ) {
                    continue 2;
                }
            }

            // note the use of $key, this is necessary
            $foundRepresentatives[$key] = $libRep;
        }

        return $foundRepresentatives;
    }

    private function unaccent($str)
    {
        $transliteration = [
            'Ĳ' => 'I',
            'Ö' => 'O',
            'Œ' => 'O',
            'Ü' => 'U',
            'ä' => 'a',
            'æ' => 'a',
            'ĳ' => 'i',
            'ö' => 'o',
            'œ' => 'o',
            'ü' => 'u',
            'ß' => 's',
            'ſ' => 's',
            'À' => 'A',
            'Á' => 'A',
            'Â' => 'A',
            'Ã' => 'A',
            'Ä' => 'A',
            'Å' => 'A',
            'Æ' => 'A',
            'Ā' => 'A',
            'Ą' => 'A',
            'Ă' => 'A',
            'Ç' => 'C',
            'Ć' => 'C',
            'Č' => 'C',
            'Ĉ' => 'C',
            'Ċ' => 'C',
            'Ď' => 'D',
            'Đ' => 'D',
            'È' => 'E',
            'É' => 'E',
            'Ê' => 'E',
            'Ë' => 'E',
            'Ē' => 'E',
            'Ę' => 'E',
            'Ě' => 'E',
            'Ĕ' => 'E',
            'Ė' => 'E',
            'Ĝ' => 'G',
            'Ğ' => 'G',
            'Ġ' => 'G',
            'Ģ' => 'G',
            'Ĥ' => 'H',
            'Ħ' => 'H',
            'Ì' => 'I',
            'Í' => 'I',
            'Î' => 'I',
            'Ï' => 'I',
            'Ī' => 'I',
            'Ĩ' => 'I',
            'Ĭ' => 'I',
            'Į' => 'I',
            'İ' => 'I',
            'Ĵ' => 'J',
            'Ķ' => 'K',
            'Ľ' => 'K',
            'Ĺ' => 'K',
            'Ļ' => 'K',
            'Ŀ' => 'K',
            'Ł' => 'L',
            'Ñ' => 'N',
            'Ń' => 'N',
            'Ň' => 'N',
            'Ņ' => 'N',
            'Ŋ' => 'N',
            'Ò' => 'O',
            'Ó' => 'O',
            'Ô' => 'O',
            'Õ' => 'O',
            'Ø' => 'O',
            'Ō' => 'O',
            'Ő' => 'O',
            'Ŏ' => 'O',
            'Ŕ' => 'R',
            'Ř' => 'R',
            'Ŗ' => 'R',
            'Ś' => 'S',
            'Ş' => 'S',
            'Ŝ' => 'S',
            'Ș' => 'S',
            'Š' => 'S',
            'Ť' => 'T',
            'Ţ' => 'T',
            'Ŧ' => 'T',
            'Ț' => 'T',
            'Ù' => 'U',
            'Ú' => 'U',
            'Û' => 'U',
            'Ū' => 'U',
            'Ů' => 'U',
            'Ű' => 'U',
            'Ŭ' => 'U',
            'Ũ' => 'U',
            'Ų' => 'U',
            'Ŵ' => 'W',
            'Ŷ' => 'Y',
            'Ÿ' => 'Y',
            'Ý' => 'Y',
            'Ź' => 'Z',
            'Ż' => 'Z',
            'Ž' => 'Z',
            'à' => 'a',
            'á' => 'a',
            'â' => 'a',
            'ã' => 'a',
            'ā' => 'a',
            'ą' => 'a',
            'ă' => 'a',
            'å' => 'a',
            'ç' => 'c',
            'ć' => 'c',
            'č' => 'c',
            'ĉ' => 'c',
            'ċ' => 'c',
            'ď' => 'd',
            'đ' => 'd',
            'è' => 'e',
            'é' => 'e',
            'ê' => 'e',
            'ë' => 'e',
            'ē' => 'e',
            'ę' => 'e',
            'ě' => 'e',
            'ĕ' => 'e',
            'ė' => 'e',
            'ƒ' => 'f',
            'ĝ' => 'g',
            'ğ' => 'g',
            'ġ' => 'g',
            'ģ' => 'g',
            'ĥ' => 'h',
            'ħ' => 'h',
            'ì' => 'i',
            'í' => 'i',
            'î' => 'i',
            'ï' => 'i',
            'ī' => 'i',
            'ĩ' => 'i',
            'ĭ' => 'i',
            'į' => 'i',
            'ı' => 'i',
            'ĵ' => 'j',
            'ķ' => 'k',
            'ĸ' => 'k',
            'ł' => 'l',
            'ľ' => 'l',
            'ĺ' => 'l',
            'ļ' => 'l',
            'ŀ' => 'l',
            'ñ' => 'n',
            'ń' => 'n',
            'ň' => 'n',
            'ņ' => 'n',
            'ŉ' => 'n',
            'ŋ' => 'n',
            'ò' => 'o',
            'ó' => 'o',
            'ô' => 'o',
            'õ' => 'o',
            'ø' => 'o',
            'ō' => 'o',
            'ő' => 'o',
            'ŏ' => 'o',
            'ŕ' => 'r',
            'ř' => 'r',
            'ŗ' => 'r',
            'ś' => 's',
            'š' => 's',
            'ť' => 't',
            'ù' => 'u',
            'ú' => 'u',
            'û' => 'u',
            'ū' => 'u',
            'ů' => 'u',
            'ű' => 'u',
            'ŭ' => 'u',
            'ũ' => 'u',
            'ų' => 'u',
            'ŵ' => 'w',
            'ÿ' => 'y',
            'ý' => 'y',
            'ŷ' => 'y',
            'ż' => 'z',
            'ź' => 'z',
            'ž' => 'z',
            'Α' => 'A',
            'Ά' => 'A',
            'Ἀ' => 'A',
            'Ἁ' => 'A',
            'Ἂ' => 'A',
            'Ἃ' => 'A',
            'Ἄ' => 'A',
            'Ἅ' => 'A',
            'Ἆ' => 'A',
            'Ἇ' => 'A',
            'ᾈ' => 'A',
            'ᾉ' => 'A',
            'ᾊ' => 'A',
            'ᾋ' => 'A',
            'ᾌ' => 'A',
            'ᾍ' => 'A',
            'ᾎ' => 'A',
            'ᾏ' => 'A',
            'Ᾰ' => 'A',
            'Ᾱ' => 'A',
            'Ὰ' => 'A',
            'ᾼ' => 'A',
            'Β' => 'B',
            'Γ' => 'G',
            'Δ' => 'D',
            'Ε' => 'E',
            'Έ' => 'E',
            'Ἐ' => 'E',
            'Ἑ' => 'E',
            'Ἒ' => 'E',
            'Ἓ' => 'E',
            'Ἔ' => 'E',
            'Ἕ' => 'E',
            'Ὲ' => 'E',
            'Ζ' => 'Z',
            'Η' => 'I',
            'Ή' => 'I',
            'Ἠ' => 'I',
            'Ἡ' => 'I',
            'Ἢ' => 'I',
            'Ἣ' => 'I',
            'Ἤ' => 'I',
            'Ἥ' => 'I',
            'Ἦ' => 'I',
            'Ἧ' => 'I',
            'ᾘ' => 'I',
            'ᾙ' => 'I',
            'ᾚ' => 'I',
            'ᾛ' => 'I',
            'ᾜ' => 'I',
            'ᾝ' => 'I',
            'ᾞ' => 'I',
            'ᾟ' => 'I',
            'Ὴ' => 'I',
            'ῌ' => 'I',
            'Θ' => 'T',
            'Ι' => 'I',
            'Ί' => 'I',
            'Ϊ' => 'I',
            'Ἰ' => 'I',
            'Ἱ' => 'I',
            'Ἲ' => 'I',
            'Ἳ' => 'I',
            'Ἴ' => 'I',
            'Ἵ' => 'I',
            'Ἶ' => 'I',
            'Ἷ' => 'I',
            'Ῐ' => 'I',
            'Ῑ' => 'I',
            'Ὶ' => 'I',
            'Κ' => 'K',
            'Λ' => 'L',
            'Μ' => 'M',
            'Ν' => 'N',
            'Ξ' => 'K',
            'Ο' => 'O',
            'Ό' => 'O',
            'Ὀ' => 'O',
            'Ὁ' => 'O',
            'Ὂ' => 'O',
            'Ὃ' => 'O',
            'Ὄ' => 'O',
            'Ὅ' => 'O',
            'Ὸ' => 'O',
            'Π' => 'P',
            'Ρ' => 'R',
            'Ῥ' => 'R',
            'Σ' => 'S',
            'Τ' => 'T',
            'Υ' => 'Y',
            'Ύ' => 'Y',
            'Ϋ' => 'Y',
            'Ὑ' => 'Y',
            'Ὓ' => 'Y',
            'Ὕ' => 'Y',
            'Ὗ' => 'Y',
            'Ῠ' => 'Y',
            'Ῡ' => 'Y',
            'Ὺ' => 'Y',
            'Φ' => 'F',
            'Χ' => 'X',
            'Ψ' => 'P',
            'Ω' => 'O',
            'Ώ' => 'O',
            'Ὠ' => 'O',
            'Ὡ' => 'O',
            'Ὢ' => 'O',
            'Ὣ' => 'O',
            'Ὤ' => 'O',
            'Ὥ' => 'O',
            'Ὦ' => 'O',
            'Ὧ' => 'O',
            'ᾨ' => 'O',
            'ᾩ' => 'O',
            'ᾪ' => 'O',
            'ᾫ' => 'O',
            'ᾬ' => 'O',
            'ᾭ' => 'O',
            'ᾮ' => 'O',
            'ᾯ' => 'O',
            'Ὼ' => 'O',
            'ῼ' => 'O',
            'α' => 'a',
            'ά' => 'a',
            'ἀ' => 'a',
            'ἁ' => 'a',
            'ἂ' => 'a',
            'ἃ' => 'a',
            'ἄ' => 'a',
            'ἅ' => 'a',
            'ἆ' => 'a',
            'ἇ' => 'a',
            'ᾀ' => 'a',
            'ᾁ' => 'a',
            'ᾂ' => 'a',
            'ᾃ' => 'a',
            'ᾄ' => 'a',
            'ᾅ' => 'a',
            'ᾆ' => 'a',
            'ᾇ' => 'a',
            'ὰ' => 'a',
            'ᾰ' => 'a',
            'ᾱ' => 'a',
            'ᾲ' => 'a',
            'ᾳ' => 'a',
            'ᾴ' => 'a',
            'ᾶ' => 'a',
            'ᾷ' => 'a',
            'β' => 'b',
            'γ' => 'g',
            'δ' => 'd',
            'ε' => 'e',
            'έ' => 'e',
            'ἐ' => 'e',
            'ἑ' => 'e',
            'ἒ' => 'e',
            'ἓ' => 'e',
            'ἔ' => 'e',
            'ἕ' => 'e',
            'ὲ' => 'e',
            'ζ' => 'z',
            'η' => 'i',
            'ή' => 'i',
            'ἠ' => 'i',
            'ἡ' => 'i',
            'ἢ' => 'i',
            'ἣ' => 'i',
            'ἤ' => 'i',
            'ἥ' => 'i',
            'ἦ' => 'i',
            'ἧ' => 'i',
            'ᾐ' => 'i',
            'ᾑ' => 'i',
            'ᾒ' => 'i',
            'ᾓ' => 'i',
            'ᾔ' => 'i',
            'ᾕ' => 'i',
            'ᾖ' => 'i',
            'ᾗ' => 'i',
            'ὴ' => 'i',
            'ῂ' => 'i',
            'ῃ' => 'i',
            'ῄ' => 'i',
            'ῆ' => 'i',
            'ῇ' => 'i',
            'θ' => 't',
            'ι' => 'i',
            'ί' => 'i',
            'ϊ' => 'i',
            'ΐ' => 'i',
            'ἰ' => 'i',
            'ἱ' => 'i',
            'ἲ' => 'i',
            'ἳ' => 'i',
            'ἴ' => 'i',
            'ἵ' => 'i',
            'ἶ' => 'i',
            'ἷ' => 'i',
            'ὶ' => 'i',
            'ῐ' => 'i',
            'ῑ' => 'i',
            'ῒ' => 'i',
            'ῖ' => 'i',
            'ῗ' => 'i',
            'κ' => 'k',
            'λ' => 'l',
            'μ' => 'm',
            'ν' => 'n',
            'ξ' => 'k',
            'ο' => 'o',
            'ό' => 'o',
            'ὀ' => 'o',
            'ὁ' => 'o',
            'ὂ' => 'o',
            'ὃ' => 'o',
            'ὄ' => 'o',
            'ὅ' => 'o',
            'ὸ' => 'o',
            'π' => 'p',
            'ρ' => 'r',
            'ῤ' => 'r',
            'ῥ' => 'r',
            'σ' => 's',
            'ς' => 's',
            'τ' => 't',
            'υ' => 'y',
            'ύ' => 'y',
            'ϋ' => 'y',
            'ΰ' => 'y',
            'ὐ' => 'y',
            'ὑ' => 'y',
            'ὒ' => 'y',
            'ὓ' => 'y',
            'ὔ' => 'y',
            'ὕ' => 'y',
            'ὖ' => 'y',
            'ὗ' => 'y',
            'ὺ' => 'y',
            'ῠ' => 'y',
            'ῡ' => 'y',
            'ῢ' => 'y',
            'ῦ' => 'y',
            'ῧ' => 'y',
            'φ' => 'f',
            'χ' => 'x',
            'ψ' => 'p',
            'ω' => 'o',
            'ώ' => 'o',
            'ὠ' => 'o',
            'ὡ' => 'o',
            'ὢ' => 'o',
            'ὣ' => 'o',
            'ὤ' => 'o',
            'ὥ' => 'o',
            'ὦ' => 'o',
            'ὧ' => 'o',
            'ᾠ' => 'o',
            'ᾡ' => 'o',
            'ᾢ' => 'o',
            'ᾣ' => 'o',
            'ᾤ' => 'o',
            'ᾥ' => 'o',
            'ᾦ' => 'o',
            'ᾧ' => 'o',
            'ὼ' => 'o',
            'ῲ' => 'o',
            'ῳ' => 'o',
            'ῴ' => 'o',
            'ῶ' => 'o',
            'ῷ' => 'o',
            'А' => 'A',
            'Б' => 'B',
            'В' => 'V',
            'Г' => 'G',
            'Д' => 'D',
            'Е' => 'E',
            'Ё' => 'E',
            'Ж' => 'Z',
            'З' => 'Z',
            'И' => 'I',
            'Й' => 'I',
            'К' => 'K',
            'Л' => 'L',
            'М' => 'M',
            'Н' => 'N',
            'О' => 'O',
            'П' => 'P',
            'Р' => 'R',
            'С' => 'S',
            'Т' => 'T',
            'У' => 'U',
            'Ф' => 'F',
            'Х' => 'K',
            'Ц' => 'T',
            'Ч' => 'C',
            'Ш' => 'S',
            'Щ' => 'S',
            'Ы' => 'Y',
            'Э' => 'E',
            'Ю' => 'Y',
            'Я' => 'Y',
            'а' => 'A',
            'б' => 'B',
            'в' => 'V',
            'г' => 'G',
            'д' => 'D',
            'е' => 'E',
            'ё' => 'E',
            'ж' => 'Z',
            'з' => 'Z',
            'и' => 'I',
            'й' => 'I',
            'к' => 'K',
            'л' => 'L',
            'м' => 'M',
            'н' => 'N',
            'о' => 'O',
            'п' => 'P',
            'р' => 'R',
            'с' => 'S',
            'т' => 'T',
            'у' => 'U',
            'ф' => 'F',
            'х' => 'K',
            'ц' => 'T',
            'ч' => 'C',
            'ш' => 'S',
            'щ' => 'S',
            'ы' => 'Y',
            'э' => 'E',
            'ю' => 'Y',
            'я' => 'Y',
            'ð' => 'd',
            'Ð' => 'D',
            'þ' => 't',
            'Þ' => 'T',
            'ა' => 'a',
            'ბ' => 'b',
            'გ' => 'g',
            'დ' => 'd',
            'ე' => 'e',
            'ვ' => 'v',
            'ზ' => 'z',
            'თ' => 't',
            'ი' => 'i',
            'კ' => 'k',
            'ლ' => 'l',
            'მ' => 'm',
            'ნ' => 'n',
            'ო' => 'o',
            'პ' => 'p',
            'ჟ' => 'z',
            'რ' => 'r',
            'ს' => 's',
            'ტ' => 't',
            'უ' => 'u',
            'ფ' => 'p',
            'ქ' => 'k',
            'ღ' => 'g',
            'ყ' => 'q',
            'შ' => 's',
            'ჩ' => 'c',
            'ც' => 't',
            'ძ' => 'd',
            'წ' => 't',
            'ჭ' => 'c',
            'ხ' => 'k',
            'ჯ' => 'j',
            'ჰ' => 'h',
            'á' => 'a',
            'à' => 'a',
            'á' => 'a',
            'â' => 'a',
            'ã' => 'a',
            'ä' => 'a',
            'å' => 'a',
            'æ' => 'a',
        ];
        $str = str_replace(
            array_keys($transliteration),
            array_values($transliteration),
            $str
        );
        return $str;
    }

    private function getRepresentatives($representatives, $schedules, $bookings, $startOfDay, $endOfDay, $timezone)
    {
        if (empty($representatives)) {
            return [];
        }

        $cacheKey = $this->business . $this->getSlug($startOfDay) . $this->getSlug($endOfDay) . 'getRepresentatives';
        if (Cache::has($cacheKey)) {
            $reprsentativeList = Cache::get($cacheKey);
            return $reprsentativeList;
        }

        foreach ($schedules as $schedule) {
            $from = Carbon::parse($schedule->from, 'Europe/London');
            $from->setTimezone($timezone);
            $from = $from->format('Y-m-d H:i:s');

            $to = Carbon::parse($schedule->to, 'Europe/London');
            $to->setTimezone($timezone);
            $to = $to->format('Y-m-d H:i:s');

            $set = $from . " to " . $to;
            $reprsentativeSchedule[$schedule->person_id]['available'][] = $set;
            $reprsentativeSchedule[$schedule->person_id]['is_recurring'][md5(
                preg_replace(
                    '/\s+/',
                    '',
                    $from . $to
                )
            )] = $schedule->is_recurring;
        }


        foreach ($bookings as $booking) {
            $from = Carbon::parse($booking->from, 'Europe/London');
            $from->setTimezone($timezone);
            $from = $from->format('Y-m-d H:i:s');

            $to = Carbon::parse($booking->to, 'Europe/London');
            $to->setTimezone($timezone);
            $to = $to->format('Y-m-d H:i:s');

            $set = $from . " to " . $to;
            $reprsentativeSchedule[$booking->person_id]['booked'][] = $set;
        }


        foreach ($representatives as $person_id => $value) {
            $reprsentativeList[$person_id]['schedule'] = isset($reprsentativeSchedule[$person_id])
                ? $reprsentativeSchedule[$person_id]
                : [];

            if (!isset($reprsentativeList[$person_id]['schedule']["available"])) {
                // unset($reprsentativeList[$person_id]);
                // continue;
                $reprsentativeList[$person_id]['schedule']["available"] = [];
            }
            if (!isset($reprsentativeList[$person_id]['schedule']["is_recurring"])) {
                // unset($reprsentativeList[$person_id]);
                // continue;
                $reprsentativeList[$person_id]['schedule']["is_recurring"] = [];
            }
            if (!isset($reprsentativeList[$person_id]['schedule']["booked"])) {
                $reprsentativeList[$person_id]['schedule']["booked"] = [];
            }
            sort($reprsentativeList[$person_id]['schedule']["booked"]);
            sort($reprsentativeList[$person_id]['schedule']["available"]);

            $nextAvailability = $this->nextAvailability(
                $reprsentativeList[$person_id]['schedule']["available"],
                $reprsentativeList[$person_id]['schedule']["booked"],
                $startOfDay,
                $endOfDay,
                60,
                15,
                $timezone
            );

            // print_r($reprsentativeList[$person_id]['schedule']["available"]); exit;


            $reprsentativeList[$person_id]['schedule']['mergedAvailableSlots'] = $nextAvailability['mergedAvailableSlots'];
            $reprsentativeList[$person_id]['schedule']['nextAvailability'] = $nextAvailability['availableSlots'];
            $reprsentativeList[$person_id] = array_merge($value, $reprsentativeList[$person_id]);
        }

        uasort(
            $reprsentativeList,
            function ($a, $b) {
                if (count($a['schedule']['nextAvailability']) == count($b['schedule']['nextAvailability']) && count($a['schedule']['nextAvailability']) == 0) {
                    if (!isset($a['name']) || !isset($b['name'])) {
                        Log::error("------------");
                        Log::error($a);
                        Log::error($b);
                        return true;
                    }
                    return (strcmp($b['name'], $a['name']));
                }
                if (count($a['schedule']['nextAvailability']) > 0) {
                    if (count($b['schedule']['nextAvailability']) > 0) {
                        if ($b['schedule']['nextAvailability'][0]['start'] == $a['schedule']['nextAvailability'][0]['start']) {
                            if (!isset($a['name']) || !isset($b['name'])) {
                                Log::error("------------");
                                Log::error($a);
                                Log::error($b);
                                return true;
                            }
                            return (strcmp($b['name'], $a['name']));
                        }
                        return strcmp(
                            $b['schedule']['nextAvailability'][0]['start'],
                            $a['schedule']['nextAvailability'][0]['start']
                        );
                    } else {
                        return 1;
                    }
                }

                if (count($b['schedule']['nextAvailability']) > 0) {
                    if (count($a['schedule']['nextAvailability']) > 0) {
                        return strcmp(
                            $b['schedule']['nextAvailability'][0]['start'],
                            $a['schedule']['nextAvailability'][0]['start']
                        );
                    } else {
                        return -1;
                    }
                }
            }
        );

        $repListOverride = array_reverse($reprsentativeList);
        Cache::put($cacheKey, $repListOverride, 300);
        return $repListOverride;
    }

    /**
     * Return Liberty Representative schedules
     *
     * @return mixed
     */
    public function nextAvailability(
        $available,
        $booked,
        $startOfDay,
        $endOfDay,
        $timezone,
        $availabilitySlots = 60,
        $bookingInterval = 15
    ){
        $availableSlots = [];
        $availableSlotsUnformatted = [];
        $bookedSlots = [];

        $diffAvailableSlots = [];
        foreach ($available as $availableSlot) {
            $availableSlotBetween = array_map('trim', explode('to', $availableSlot));

            $availableStartTime = Carbon::parse($availableSlotBetween[0], $timezone)->format('Y-m-d H:i:s');

            $availableEndTime = Carbon::parse($availableSlotBetween[1], $timezone)->format('Y-m-d H:i:s');

            $diffAvailableSlots[] = ['start' => $availableStartTime, 'end' => $availableEndTime];
        }

        $mergedAvailableSlots = [];
        for ($i = 0; $i < count($diffAvailableSlots); $i++) {
            $tempMerged = [];
            $k = $i;
            for ($j = $i + 1; $j < count($diffAvailableSlots); $j++) {
                if ($diffAvailableSlots[$j]['start'] == $diffAvailableSlots[$i]['end']) {
                    $tempMerged = [
                        'start' => $diffAvailableSlots[$k]['start'],
                        'end' => $diffAvailableSlots[$j]['end'],
                    ];


                    $i = $j;
                } else {
                    if (count($tempMerged) == 0) {
                        $tempMerged = [
                            'start' => $diffAvailableSlots[$i]['start'],
                            'end' => $diffAvailableSlots[$i]['end'],
                        ];
                    }
                }
            }

            if ($i == count($diffAvailableSlots) - 1 && empty($tempMerged)) {
                $tempMerged = ['start' => $diffAvailableSlots[$i]['start'], 'end' => $diffAvailableSlots[$i]['end']];
            }

            if (!empty($tempMerged)) {
                $mergedAvailableSlots[] = $tempMerged;
            }
        }

        foreach ($mergedAvailableSlots as $availableSlot) {
            $availableStartTime = Carbon::parse($availableSlot['start'], $timezone);

            $ast = clone $availableStartTime;

            $availableEndTime = Carbon::parse($availableSlot['end'], $timezone);


            $ast = clone $availableStartTime;

            $thisSlot = $availableStartTime->diffInMinutes($availableEndTime, false);


            for ($i = clone $ast; $ast->lt($availableEndTime); $ast->addMinutes($bookingInterval)) {
                for ($j = $bookingInterval; $j <= $thisSlot; $j = $j + $bookingInterval) {
                    $end = clone $i;
                    $end->addMinutes($j);
                    if (
                        $end->lte($availableEndTime) && $end->gt($ast) && $ast->diffInMinutes(
                            $end,
                            false
                        ) <= $availabilitySlots && $end->gte(Carbon::now($timezone))
                    ) {
                        $availableSlots[] = [
                            'start' => $ast->format('Y-m-d H:i:s'),
                            'end' => $end->format('Y-m-d H:i:s'),
                        ];
                    }
                }
            }
        }

        foreach ($booked as $bookedSlot) {
            $bookedSlotBetween = array_map('trim', explode('to', $bookedSlot));

            $bookedStartTime = Carbon::parse($bookedSlotBetween[0], $timezone);

            $bst = clone $bookedStartTime;

            $bookedEndTime = Carbon::parse($bookedSlotBetween[1], $timezone);

            $bookedSlots[] = [
                'start' => $bookedStartTime->format('Y-m-d H:i:s'),
                'end' => $bookedEndTime->format('Y-m-d H:i:s'),
            ];
        }


        if (count($bookedSlots) > 0) {
            foreach ($availableSlots as $key => $as) {
                $aStart = Carbon::parse($as['start'], $timezone);
                $aEnd = Carbon::parse($as['end'], $timezone);

                foreach ($bookedSlots as $b) {
                    $bStart = Carbon::parse($b['start'], $timezone);
                    $bEnd = Carbon::parse($b['end'], $timezone);

                    if ($aStart->eq($bStart) && $aEnd->eq($bEnd)) {
                        unset($availableSlots[$key]);
                    } elseif ($aStart->gte($bStart) && $aEnd->lte($bEnd)) {
                        unset($availableSlots[$key]);
                    } elseif ($aStart->lte($bStart) && $aEnd->gte($bEnd)) {
                        unset($availableSlots[$key]);
                    } elseif ($bEnd->lt($aEnd) && $bEnd->gt($aStart)) {
                        unset($availableSlots[$key]);
                    } elseif ($bStart->lt($aEnd) && $bStart->gt($aStart)) {
                        unset($availableSlots[$key]);
                    } elseif ($aEnd->lt($startOfDay)) {
                        unset($availableSlots[$key]);
                    }
                }
            }
        }


        $availableSlots = array_values($availableSlots);


        return ["availableSlots" => $availableSlots, "mergedAvailableSlots" => $mergedAvailableSlots];
    }

    /**
     * Return Liberty Representative schedules for Client
     * Ideally we should have one schedules function for both Admin & Client
     * but this current change to logic will not work to Admin yet so we need to have a separate one for now
     * so that Admin booking availability page will still work after these changes
     *
     * @return mixed
     */
    public function schedulesForClient($date = null)
    {
        $date = (isset($date) && !empty($date))
            ? $date
            : $this->userRequest->get('date');
        $libertyRepresentatives = $this->fetchLibRepFromCacheOrCms();
        if ($this->userRequest->has('force_recache')) {
            return Response::json(
                [
                    'response' => 'success',
                    'data' => 'Cache rebuilt.',
                ]
            );
        }

        if ($this->userRequest->has('return') && $this->userRequest->get('return') == 'no-availability') {
            return Response::json(
                [
                    'response' => 'success',
                    'data' => [
                        'liberty_representatives' => isset($limitedLibertyRepresentatives)
                            ? $limitedLibertyRepresentatives
                            : $libertyRepresentatives,
                        'options' => $this->getCmsOptions(),
                    ],
                ]
            );
        }
        // $timezone = $this->userRequest->get('timezone', 'Europe/London');
        // $testNow = Carbon::parse('2020-09-21 7am', $timezone);
        // Carbon::setTestNow($testNow);

        // Search liberty representatives by name
        $libertyRepresentatives = $this->searchLibertyRepresentative($libertyRepresentatives);

        // if no date provided, we will get schedule for today until next business day's schedule
        if ($date == null) {
            $startOfDay = Carbon::now();
            $endOfDay = Carbon::now()->addWeekday(1)->endOfDay();
        } else {
            $startOfDay = Carbon::parse($date);
            $endOfDay = $startOfDay->copy()->addWeekday(4)->endOfDay();

            // if date provided is today, we only need today and next business day's schedule
            $tzCurrentTime = Carbon::parse(date('Y-m-d H:i:s'), 'Europe/London')->setTimezone(
                $this->userRequest->get(
                    'timezone',
                    'Europe/London'
                )
            )->format('Y-m-d H:i:s');
            $tzOfficeStart = date('Y-m-d 08:00:00');
            $tzOfficeEnd = date('Y-m-d 18:00:00');

            if (
                $startOfDay->isToday()
                && ((strtotime($tzCurrentTime) >= strtotime($tzOfficeStart))
                    && (strtotime($tzCurrentTime) <= strtotime($tzOfficeEnd)))
            ) {
                $endOfDay = $startOfDay->copy()->addWeekday(1)->endOfDay();
            }
        }

        $timezone = $this->userRequest->get('timezone', 'Europe/London');

        // TODO: add person_id filter here to decrease
        $librepIds = array_keys($libertyRepresentatives);

        // If getting a five-day schedule
        if ($startOfDay->diffInDays($endOfDay) >= 4) {
            // TODO: convert to eloquent query
            $query = DB::raw(
                "SELECT lrs.*, lrb.from as 'booking_from', lrb.to as 'booking_to'
                            FROM lt_resource_schedule lrs
                            LEFT OUTER JOIN lt_resource_bookings lrb
                            ON (
                                lrs.person_id = lrb.person_id
                                AND (
                                    CAST(lrb.from AS DATETIME) BETWEEN CAST(lrs.from AS DATETIME) AND DATE_ADD(CAST(lrs.from AS DATETIME), INTERVAL 59 minute)
                                    OR CAST(lrb.to AS DATETIME) BETWEEN DATE_ADD(CAST(lrs.from AS DATETIME), INTERVAL 1 minute) AND CAST(lrs.to AS DATETIME)
                                ) AND lrb.deleted_at IS NULL
                            )
                            WHERE CAST(lrs.from AS DATETIME) BETWEEN CAST('" . $startOfDay->format('Y-m-d H:i:s') . "' AS DATETIME) AND CAST('" . $endOfDay->format('Y-m-d H:i:s') . "' AS DATETIME)
                            AND lrs.person_id IN ('" . join("','", $librepIds) . "')
                            AND lrs.deleted_at IS NULL
                            GROUP BY lrb.id, lrs.id
                            ORDER BY lrs.person_id asc, lrb.from asc, lrs.from asc"
            );

            $schedules = DB::select($query);
            // return Response::json($schedules);
            // $from = Carbon::parse('2020-09-24 08:00:00', 'Europe/London')->setTimezone($timezone);
            // $to = Carbon::parse('2020-09-24 08:15:00', 'Europe/London')->setTimezone($timezone);
            // $res = $from->diffInMinutes($to);
            // dd($res);

            $libertyRepresentatives = $this->getRepresentativesForClient(
                $libertyRepresentatives,
                $schedules,
                $startOfDay,
                $endOfDay,
                $timezone
            );
        } else {
            $schedules = ResourceSchedule::whereIn('person_id', $librepIds)->whereDateRange(
                $startOfDay,
                $endOfDay
            )->get();
            $bookings = ResourceBooking::whereIn('person_id', $librepIds)->whereDateRange(
                $startOfDay,
                $endOfDay
            )->get();
            $libertyRepresentatives = $this->getRepresentatives(
                $libertyRepresentatives,
                $schedules,
                $bookings,
                $startOfDay,
                $endOfDay,
                $timezone
            );
        }

        // Limit number of representatives
        if ($this->userRequest->has('limit') && is_numeric($this->userRequest->get('limit'))) {
            $limit = (int)$this->userRequest->get('limit');
            $limitedLibertyRepresentatives = (!empty($libertyRepresentatives) && is_array($libertyRepresentatives))
                ? array_slice($libertyRepresentatives, 0, $limit)
                : [];
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => [
                    'liberty_representatives' => isset($limitedLibertyRepresentatives)
                        ? $limitedLibertyRepresentatives
                        : $libertyRepresentatives,
                    'options' => $this->getCmsOptions(),
                ],
            ]
        );
    }

    private function getRepresentativesForClient($representatives, $schedules, $startOfDay, $endOfDay, $timezone)
    {
        if (empty($representatives)) {
            return [];
        }

        // Log::error("log");

        foreach ($schedules as $schedule) {
            $personId = $schedule->person_id;
            // If librep not found skip this sched
            if (!isset($representatives[$personId])) {
                continue;
            }

            $from = Carbon::parse($schedule->from, 'Europe/London')->setTimezone($timezone);
            $to = Carbon::parse($schedule->to, 'Europe/London')->setTimezone($timezone);
            // if schedule date is already set as available, we can skip that
            if (
                isset($representativeSchedule[$personId]['schedule']['available_dates'])
                && in_array(
                    $from->copy()->format('Y-m-d'),
                    $representativeSchedule[$personId]['schedule']['available_dates']
                )
            ) {
                continue;
            }

            // If no booking found at the first sched, we set that day as available
            if (!isset($schedule->booking_from)) {
                $representativeSchedule[$personId]['schedule']['available_dates'][] = $from->format('Y-m-d');
                $representativeSchedule[$personId] = array_merge(
                    $representatives[$personId],
                    $representativeSchedule[$personId]
                );
                continue;
            }

            // Get All bookings for this slot
            $currentSlotBookings = array_filter(
                $schedules,
                function ($item) use ($schedule) {
                    return $item->id === $schedule->id;
                }
            );
            $totalMinutesBookedInSlot = 0;
            foreach ($currentSlotBookings as $booking) {
                $totalMinutesBookedInSlot += Carbon::parse($schedule->booking_from)->diffInMinutes(Carbon::parse($schedule->booking_to));
            }

            // This means there is still 15 minutes available slot in this schedule
            // So we will set this date as available and go to next sched
            if ($totalMinutesBookedInSlot <= 45) {
                $representativeSchedule[$personId]['schedule']['available_dates'][] = $from->format('Y-m-d');
                $representativeSchedule[$personId] = array_merge(
                    $representatives[$personId],
                    $representativeSchedule[$personId]
                );
            } else { // TODO: this else is just helpful for debugging we can remove this
                if (
                    !isset($representativeSchedule[$personId]['schedule']['fully_booked'])
                    || !in_array(
                        $from->copy()->format('Y-m-d'),
                        $representativeSchedule[$personId]['schedule']['fully_booked']
                    )
                ) {
                    $representativeSchedule[$personId]['schedule']['fully_booked'][] = $from->format('Y-m-d');
                    $representativeSchedule[$personId] = array_merge(
                        $representatives[$personId],
                        $representativeSchedule[$personId]
                    );
                }
            }
        }

        // Merge libreps that doesn't have schedule
        $representativeSchedule = array_merge(
            $representatives,
            isset($representativeSchedule)
                ? $representativeSchedule
                : []
        );


        // Sort LibRep by most availability first, then alphabetical
        uasort(
            $representativeSchedule,
            function ($a, $b) {
                // sort by number of days available
                if (isset($a['schedule']['available_dates']) && isset($b['schedule']['available_dates'])) {
                    if (count($a['schedule']['available_dates']) === count($b['schedule']['available_dates'])) {
                        if (!isset($a['name']) || !isset($b['name'])) {
                            Log::error("------------");
                            Log::error($a);
                            Log::error($b);
                            return true;
                        }
                        return strcmp($a['name'], $b['name']);
                    }
                    return count($b['schedule']['available_dates']) - count($a['schedule']['available_dates']);
                }
                if (!isset($a['schedule']['available_dates']) && !isset($b['schedule']['available_dates'])) {
                    if (!isset($a['name']) || !isset($b['name'])) {
                        Log::error("------------");
                        Log::error($a);
                        Log::error($b);
                        return true;
                    }
                    return strcmp($a['name'], $b['name']);
                }
                if (!isset($a['schedule']['available_dates']) && isset($b['schedule']['available_dates'])) {
                    return 1;
                }
                if (isset($a['schedule']['available_dates']) && !isset($b['schedule']['available_dates'])) {
                    return -1;
                }
            }
        );

        //Log::error($representativeSchedule);

        return $representativeSchedule;
    }

    public function show_date($value, $key)
    {
        echo $key, ': ', date('r', $value), PHP_EOL;
    }

    public function createRepresentativeCallAvaialbility()
    {
        $data = $this->userRequest->all();

        $date = explode("to", $data['date']);

        // if schedule exist delete
        $availaibilityExist = ResourceSchedule::where('from', $date[0])
            ->where('to', $date[1])
            ->where('person_id', $data['person_id'])
            ->exists();

        // if bookings exist cannot delete
        $bookingsExists = ResourceBooking::where('from', '>=', $date[0])
            ->where('to', '<=', $date[1])
            ->where('person_id', $data['person_id'])
            ->exists();

        if ($availaibilityExist && !$bookingsExists && $data['is_enabled'] == "false") {
            ResourceSchedule::where('from', $date[0])
                ->where('to', $date[1])
                ->where('person_id', $data['person_id'])
                ->forceDelete();

            return Response::json(
                [
                    'response' => 'success',
                    'message' => 'Availability has been disabled.',
                ]
            );
        } else {
            if ($data['is_enabled'] == "true" && !$availaibilityExist) {

                ResourceSchedule::create(
                    [
                        'person_id' => $data['person_id'],
                        'from' => $date[0],
                        'to' => $date[1],
                        'is_recurring' => (isset($data['is_recurring']) && $data['is_recurring'] == 'true')
                            ? 1
                            : 0,
                    ]
                );
            } else {
                ResourceSchedule::where('person_id', $data['person_id'])
                    ->where('from', $date[0])
                    ->where('to', $date[1])
                    ->update(['is_recurring' => 1]);
            }

            return Response::json(
                [
                    'response' => 'success',
                    'message' => 'Availability has been created/detected.',
                ]
            );
        }

        if ($bookingsExists) {
            return Response::json(
                [
                    'response' => 'bookingExist',
                    'message' => 'Unable to disable the availability. A booking already exists.',
                ]
            );
        }
    }

    public function getBookings()
    {
        $b = [];
        $bookings = ResourceBooking::with('participants')->with('bookingattachedfiles')->with('room')->orderBy(
            'lt_resource_bookings.from',
            'desc'
        )->get();
        foreach ($bookings as $booking) {
            if (isset($booking->room->business) && $booking->room->business == $this->business) {
                array_push($b, $booking);
            } elseif (!isset($booking->room->business) && $this->business == 'lsm') {
                array_push($b, $booking);
            }
        }
        $representatives = $this->getRerpresentativeList($this->business);
        return Response::json(
            [
                'data' => [
                    'bookings' => $b,
                    'representatives' => $representatives,
                ],
            ]
        );
    }

    private function getRerpresentativeList($business)
    {
        if (Cache::has($business . '_libertyRepresenativesCms') && !$this->userRequest->has('force_recache')) {
            $libertyRepresenativesCms = Cache::get($business . '_libertyRepresenativesCms');
        } else {
            $libertyRepresenativesCms = json_decode(Cms::get('workspaces/' . $this->getCmsConfig('lets_talk_workspace') . '/content-types/' . $this->getCmsConfig('liberty_representatives_content_type') . '/content-entries?joined=true&query={"status":"publish","operator":"="}'));
            Cache::put($business . '_libertyRepresenativesCms', $libertyRepresenativesCms, 1440);
        }

        if (Cache::has($business . '_peopleProfileCms') && !$this->userRequest->has('force_recache')) {
            $peopleProfileCms = Cache::get($business . '_peopleProfileCms');
        } else {
            $peopleProfileCms = json_decode(Cms::get('workspaces/' . $this->getCmsConfig('workspace_id') . '/content-types/' . $this->getCmsConfig('people_content_type') . '/content-entries?joined=true&query={"status":"publish","operator":"="}'));
            Cache::put($business . '_peopleProfileCms', $peopleProfileCms, 1440);
        }

        $peopleProfile = [];
        if ($peopleProfileCms) {
            foreach ($peopleProfileCms->data as $profile) {
                $peopleProfile[$profile->_id]['line_of_business'] = [];
                if (isset($profile->{$this->getCmsConfig('liberty_representative_line_of_business')})) {
                    foreach ($profile->{$this->getCmsConfig('liberty_representative_line_of_business')} as $lob) {
                        if (isset($lob->name)) {
                            $peopleProfile[$profile->_id]['line_of_business'][] = $lob->name;
                        }
                    }
                }
            }
        }

        $libertyRepresenatives = [];
        if ($libertyRepresenativesCms) {
            if (isset($libertyRepresenativesCms->data) && isset($libertyRepresenativesCms->data['0']) && isset($libertyRepresenativesCms->data['0']->{$this->getCmsConfig('liberty_representatives')})) {
                $libertyRepresenatives = $libertyRepresenativesCms->data['0']->{$this->getCmsConfig('liberty_representatives')};
            }
        }

        foreach ($libertyRepresenatives as $key => $libRep) {
            if (isset($libRep->{$this->getCmsConfig('liberty_representative_line_of_business')}) && isset($peopleProfile[$libRep->_id]['line_of_business'])) {
                $libRep->{$this->getCmsConfig('liberty_representative_line_of_business')} = $peopleProfile[$libRep->_id]['line_of_business'];
            }
        }

        $reprsentativeList = [];
        foreach ($libertyRepresenatives as $value) {
            if (isset($value->{$this->getCmsConfig('liberty_representative_mobile_number')}) && isset($value->{$this->getCmsConfig('liberty_representative_email')})) {
                $mobileNumber = str_replace(
                    '(0)',
                    '',
                    $value->{$this->getCmsConfig('liberty_representative_mobile_number')}
                );
                if (substr($mobileNumber, 0, 1) === '0') {
                    $mobileNumber = ltrim($mobileNumber, '0');
                } elseif (substr($mobileNumber, 0, 1) !== '+') {
                    $mobileNumber = "+44" . $mobileNumber;
                }
                if (is_array($value->{$this->getCmsConfig('liberty_representative_profile_picture')}) && isset($value->{$this->getCmsConfig('liberty_representative_profile_picture')}[0])) {
                    $parsedUrl = parse_url($value->{$this->getCmsConfig('liberty_representative_profile_picture')}[0]->url);
                    $parsedUrl['query'] = urlencode($parsedUrl['query']);
                    $profileImage = $parsedUrl['scheme'] . "://" . $parsedUrl['host'] . $parsedUrl['path'] . '?' . $parsedUrl['query'];
                } else {
                    $profileImage = "https://asset-management-dev.s3.eu-west-1.amazonaws.com/assets/p2NoaMtcZQmyXDW9hoGy0GVtKpcZOQBi5mQg1nKe.png?name%3Duser-icon-silhouette-ae9ddcaf4a156a47931d5719ecee17b9.png%26size%3D6KB%26mime%3Dimage%2Fpng";
                }

                $reprsentativeList[$value->_id] = [
                    'name' => $value->name,
                    'line_of_business' => isset($value->{$this->getCmsConfig('liberty_representative_line_of_business')})
                        ? $value->{$this->getCmsConfig('liberty_representative_line_of_business')}
                        : [],
                    'business_function' => isset($value->{$this->getCmsConfig('liberty_representative_business_function')})
                        ? $value->{$this->getCmsConfig('liberty_representative_business_function')}
                        : null,
                    'email' => $value->{$this->getCmsConfig('liberty_representative_email')},
                    'mobile' => "+" . preg_replace('/[^0-9]/', '', $mobileNumber),
                    'profile_picture' => $profileImage,
                    'job_title' => $value->{$this->getCmsConfig('liberty_representative_job_title')},
                    'office_timezone' => isset($value->{$this->getCmsConfig('people_profile_office')}) && isset($value->{$this->getCmsConfig('people_profile_office')}->{$this->getCmsConfig('people_profile_office_timezone')})
                        ? $value->{$this->getCmsConfig('people_profile_office')}->{$this->getCmsConfig('people_profile_office_timezone')}
                        : 'Europe/London',
                    'office_title' => isset($value->{$this->getCmsConfig('people_profile_office')}) && isset($value->{$this->getCmsConfig('people_profile_office')}->{$this->getCmsConfig('people_profile_office_title')})
                        ? $value->{$this->getCmsConfig('people_profile_office')}->{$this->getCmsConfig('people_profile_office_title')}
                        : 'N/A',
                ];
            }
        }

        return $reprsentativeList;
    }

    public function getBookingDetails()
    {
        $user_code = $this->userRequest->get('user_code');
        $bookingDetails = VideoCallParticipant::with('room')->where('user_code', $user_code)->get();
        return Response::json(
            [
                'data' => $bookingDetails[0],
            ]
        );
    }

    // wrapper function to get config based on business

    public function deleteBooking()
    {
        $data = $this->userRequest->all();
        ResourceBooking::where('id', $data['booking_id'])->delete();

        return Response::json(
            [
                'response' => 'success',
            ]
        );
    }

    public function checkCallNowBookings()
    {
        $callNowExists = ResourceBooking::where('is_call_now', 1)
            ->where('from', '>', Carbon::now()->addSeconds(-65)->format('Y-m-d H:i:s'))
            ->exists();

        if ($callNowExists) {
            return Response::json(
                [
                    'response' => 'calling',
                ]
            );
        } else {
            return Response::json(
                [
                    'response' => 'standby',
                ]
            );
        }
    }

    // Return as string so it will be ready to use for Icals

    public function testNotification()
    {
        $ids = Notification::select('token')->pluck('token')->all();

        $message = [
            'title' => 'Reception',
            'body' => 'New incoming call',
            "icon" => "/img/favicon.ico",
            'vibrate' => 1,
            'sound' => 1,
            'link' => config('app.client_frontend') . '/virtual-rooms/v/',
        ];

        return Notification::send($ids, $message);
    }

    public function getAttachedFiles($uuid)
    {
        $attachedFile = null;
        $attachedFile = ResourceBookingAttachedFile::where('uuid', $uuid)->first();
        if ($attachedFile && $attachedFile != null && !empty($attachedFile)) {
            return Response::json(
                [
                    'response' => 'success',
                    'data' => $attachedFile,
                ]
            );
        }
        return Response::json(
            [
                'response' => 'fail',
                'data' => null,
                'msg' => 'data not found',
            ]
        );
    }

    public function termsAndConditions()
    {
        if (Cache::has($this->business . '_cms_vr_terms_and_conditions') && !$this->userRequest->has('force_recache')) {
            $terms_and_conditions = Cache::get($this->business . '_cms_vr_terms_and_conditions');
        } else {
            $terms_and_conditions = json_decode(Cms::get('workspaces/' . $this->getCmsConfig('tc_workspace') . '/content-types/' . $this->getCmsConfig('tc_content_type') . '/content-entries/' . $this->getCmsConfig('tc_content_entry')));
            Cache::put($this->business . '_cms_vr_terms_and_conditions', $terms_and_conditions, 1440);
        }

        return Response::json(
            [
                'title' => $terms_and_conditions->name,
                'description' => $terms_and_conditions->{$this->getCmsConfig('tc_description')},
                'body' => $terms_and_conditions->{$this->getCmsConfig('tc_intro_text')},
                'data' => $terms_and_conditions,
                'options' => $this->getCmsOptions(),
            ]
        );
    }

    public function primaryLibRepFromRoomId($roomId)
    {
        $resourceBooking = ResourceBooking::where('room_id', $roomId)->first();

        if (isset($resourceBooking->person_id)) {
            $primaryLibRepId = $resourceBooking->person_id;
        } else {
            $resourceBooking = SocialRoom::where('room_code', $roomId)->first();
            $primaryLibRepId = $resourceBooking->created_by_id;
        }
        //print_r($resourceBooking); exit;
        $libRep = $this->getLibRepById($primaryLibRepId);

        return Response::json(
            [
                'response' => 'success',
                'data' => [
                    'name' => $libRep['name'],
                    'id' => $primaryLibRepId,
                    'email' => $libRep['email'],
                ],
            ]
        );
    }

    private function getLibRepById($id)
    {
        $libertyRepresentatives = $this->fetchLibRepFromCacheOrCms();

        //print_r($libertyRepresentatives)

        $foundLibRep = null;
        foreach ($libertyRepresentatives as $key => $librep) {
            if (!isset($librep['email'])) {
                continue;
            }

            if ($id == $key) {
                $foundLibRep = $librep;
                break;
            }
        }

        return $foundLibRep;
    }

    public function receiveEmail()
    {
        $data = $this->userRequest->except('_token');

        $inviteLog = InviteLog::create($data);

        // Cancel booking using ICS
        if (isset($data['parsed_data']['method']) && $data['parsed_data']['method'] == 'CANCEL') {
            if (ResourceBooking::where('room_id', $data['parsed_data']['uid'])->delete()) {
                return Response::json(
                    [
                        'response' => 'success',
                    ]
                );
            }
        }

        // Modify booking schedule using ICS
        if (isset($data['parsed_data']['method']) && ($data['parsed_data']['method'] == 'REQUEST' || $data['parsed_data']['method'] == 'COUNTER')) {

            if (ResourceBooking::where('room_id', $data['parsed_data']['uid'])->count() > 0) {
                $start_date = $data['parsed_data']['conference_date'] . ' ' . $data['parsed_data']['time_start'] . ':00';
                $end_date = $data['parsed_data']['conference_date'] . ' ' . $data['parsed_data']['time_end'] . ':00';
                ResourceBooking::where('room_id', $data['parsed_data']['uid'])
                    ->update(
                        [
                            'from' => $start_date,
                            'to' => $end_date,
                        ]
                    );

                // NOTE: Make sure booking has been updated before force creation
                $this->forceCreateLibRepSchedule($data);

                $firstParticipant = VideoCallParticipant::where('room_id', $data['parsed_data']['uid'])->first();
                $booking_timezone = $firstParticipant['booked_timezone'];

                $conference_start_date = date(
                    "Y-m-d H:i:s",
                    strtotime($data['parsed_data']['conference_date'] . ' ' . $data['parsed_data']['time_start'] . ":00")
                );
                $conference_end_date = date(
                    "Y-m-d H:i:s",
                    strtotime($data['parsed_data']['conference_date'] . ' ' . $data['parsed_data']['time_end'] . ":00")
                );


                $booked_timezone_from = Carbon::parse(
                    $conference_start_date,
                    "Europe/London"
                )->setTimezone($booking_timezone)->format('Y-m-d H:i:s');
                $booked_timezone_to = Carbon::parse(
                    $conference_end_date,
                    "Europe/London"
                )->setTimezone($booking_timezone)->format('Y-m-d H:i:s');

                VideoCallParticipant::where('room_id', $data['parsed_data']['uid'])
                    ->update(
                        [
                            'booked_timezone_from' => $booked_timezone_from,
                            'booked_timezone_to' => $booked_timezone_to,
                        ]
                    );

                VideoCallRoom::where('_id', $data['parsed_data']['uid'])
                    ->update(
                        [
                            'conference_date_start' => $conference_start_date,
                            'conference_date_end' => $conference_end_date,
                            'booked_timezone_from' => $booked_timezone_from,
                            'booked_timezone_to' => $booked_timezone_to,
                        ]
                    );

                $this->sendIcsEmail($data['parsed_data']['uid'], $data['parsed_data']['sequence']);

                return Response::json(
                    [
                        'response' => 'success',
                    ]
                );
            }
            if (SocialRoom::where('room_code', $data['parsed_data']['uid'])->count() > 0) {
                $socialRoom = SocialRoom::where('room_code', $data['parsed_data']['uid'])->first();
                $socialRoomSchedule = SocialRoomSchedule::where('lt_social_room_id', $socialRoom->id)->first();
                $start_date = $data['parsed_data']['conference_date'] . ' ' . $data['parsed_data']['time_start'] . ':00';
                $end_date = $data['parsed_data']['conference_date'] . ' ' . $data['parsed_data']['time_end'] . ':00';
                if (!isset($data['parsed_data']['recurrence_id'])) {
                    SocialRoomSchedule::where('lt_social_room_id', $socialRoom->id)
                        ->update(
                            [
                                'start_date' => $start_date,
                                'end_date' => $end_date,
                            ]
                        );
                } else {
                    SocialRoomModifiedRecurringSchedule::updateOrCreate(
                        [
                            'room_id' => $socialRoom->id,
                            'room_uid' => $data['parsed_data']['uid'],
                            'original_schedule' => $data['parsed_data']['recurrence_id'],
                            'schedule_id' => $socialRoomSchedule->id,
                        ],
                        [
                            'room_id' => $socialRoom->id,
                            'room_uid' => $data['parsed_data']['uid'],
                            'original_schedule' => $data['parsed_data']['recurrence_id'],
                            'schedule_id' => $socialRoomSchedule->id,
                            'start_date' => $start_date,
                            'end_date' => $end_date,
                        ]
                    );
                }

                // $booking_timezone = 'Europe/London';

                // $conference_start_date = date("Y-m-d H:i:s", strtotime($data['parsed_data']['conference_date'].' '.$data['parsed_data']['time_start'].":00"));
                // $conference_end_date = date("Y-m-d H:i:s", strtotime($data['parsed_data']['conference_date'].' '.$data['parsed_data']['time_end'].":00"));


                // $booked_timezone_from = Carbon::parse($conference_start_date, "Europe/London")->setTimezone($booking_timezone)->format('Y-m-d H:i:s');
                // $booked_timezone_to = Carbon::parse($conference_end_date, "Europe/London")->setTimezone($booking_timezone)->format('Y-m-d H:i:s');

                // VideoCallParticipant::where('room_id', $data['parsed_data']['uid'])
                //     ->update([
                //         'booked_timezone_from' => $booked_timezone_from,
                //         'booked_timezone_to' => $booked_timezone_to
                //     ]);

                // VideoCallRoom::where('_id', $data['parsed_data']['uid'])
                //     ->update([
                //         'conference_date_start' => $conference_start_date,
                //         'conference_date_end' => $conference_end_date,
                //         'booked_timezone_from' => $booked_timezone_from,
                //         'booked_timezone_to' => $booked_timezone_to
                //     ]);

                $this->sendIcsEmailSocial(
                    $data['parsed_data']['uid'],
                    $data['parsed_data']['sequence'],
                    $data['parsed_data']
                );

                return Response::json(
                    [
                        'response' => 'success',
                    ]
                );
            }
        }

        // Cancel booking using ICS
        if (isset($data['parsed_data']['method']) && $data['parsed_data']['method'] == 'CANCEL') {
            if (SocialRoom::where('room_code', $data['parsed_data']['uid'])->count() > 0) {
                $this->sendIcsEmailSocial(
                    $data['parsed_data']['uid'],
                    $data['parsed_data']['sequence'],
                    $data['parsed_data'],
                    1
                );
                return Response::json(
                    [
                        'response' => 'success',
                    ]
                );
            } else {
                $this->sendIcsEmail($data['parsed_data']['uid'], $data['parsed_data']['sequence'], 1);
                if (ResourceBooking::where('room_id', $data['parsed_data']['uid'])->delete()) {
                    return Response::json(
                        [
                            'response' => 'success',
                        ]
                    );
                }
            }
        }

        if (isset($data['parsed_data']['method']) && $data['parsed_data']['method'] == 'REQUEST') {
            $response = $this->generateRoomFromOutlook($data['parsed_data']);
        }

        if ($response instanceof JsonResponse) {
            $inviteLog->booking_result = $response->getData();
            // We only want to log all data if it fails for debugging purposes
            if (isset($response->getData()->response) && $response->getData()->response === 'success') {
                $inviteLog->booking_result = 'success';
            }
            $inviteLog->save();
        }

        return $response;
    }

    /**
     * Force LibRep as available by creating scchedule based on the start date booked when updating meeting via outlook
     */
    private function forceCreateLibRepSchedule($data)
    {
        if (!isset($data['parsed_data']['uid'])) {
            return;
        }

        $resourceBookings = ResourceBooking::where('room_id', $data['parsed_data']['uid'])->get();
        if ($resourceBookings->count() === 0) {
            return;
        }

        $resourceBooking = $resourceBookings->first();
        $newSchedFrom = Carbon::parse($resourceBooking->from)->startOfHour();
        $newSchedTo = $newSchedFrom->copy()->addHour();

        // If no schedule, we need to create one for the booked slot
        ResourceSchedule::firstOrCreate(
            [
                'person_id' => $resourceBooking->person_id,
                'from' => $newSchedFrom,
                'to' => $newSchedTo,
            ]
        );
    }

    // Create guest external contacts if email does not exist

    public function sendIcsEmail($room_id, $sequence, $cancelled = 0)
    {


        $attendees = $this->getIcalUpdatedAllParticipants($room_id);


        $participants = VideoCallParticipant::where('room_id', $room_id)->get();

        $room = VideoCallRoom::where('_id', $room_id)->first();


        $clientsLink = VideoCallRoom::generateWaitingRoomLink($room->name);

        $primaryClient = VideoCallParticipant::where('room_id', $room_id)->where('role', 'client')->first();

        $resourceBooking = ResourceBooking::where('room_id', $room_id)->first();
        $primaryLibRepId = $resourceBooking->person_id;

        $libRep = $this->getLibRepById($primaryLibRepId);

        $sequence = intval($sequence) + 1;

        $subjectStatus = ($cancelled == 0)
            ? 'Modified'
            : 'Deleted';

        foreach ($participants as $participant) {

            $role = $participant->role;

            if ($role == 'reception') {
                continue;
            } elseif ($role == 'liberty_representative') {
                $libRepDetails = $participant->details;
                $timezone = $libRepDetails['office_timezone'];
                $attachmentSubject = $primaryClient->user_name . ", " . $primaryClient->company . " - Liberty Virtual Rooms";
                $datetime_start = Carbon::parse(
                    $resourceBooking->from,
                    "Europe/London"
                )->setTimezone($timezone)->format('Ymd\THis');
                $datetime_end = Carbon::parse(
                    $resourceBooking->to,
                    "Europe/London"
                )->setTimezone($timezone)->format('Ymd\THis');

                $datetime_start_formatted = Carbon::parse(
                    $resourceBooking->from,
                    "Europe/London"
                )->setTimezone($timezone)->format('d-m-Y H:i');
                $datetime_end_formatted = Carbon::parse(
                    $resourceBooking->to,
                    "Europe/London"
                )->setTimezone($timezone)->format('d-m-Y H:i');

                $subject = $primaryClient->user_name . " - " . $subjectStatus . " : Liberty Virtual Rooms Meeting";
            } else {
                $timezone = $room->booked_timezone;
                $attachmentSubject = $libRep['name'] . " - Liberty Virtual Rooms";
                $datetime_start = Carbon::parse(
                    $resourceBooking->from,
                    "Europe/London"
                )->setTimezone($timezone)->format('Ymd\THis');
                $datetime_end = Carbon::parse(
                    $resourceBooking->to,
                    "Europe/London"
                )->setTimezone($timezone)->format('Ymd\THis');

                $datetime_start_formatted = Carbon::parse(
                    $resourceBooking->from,
                    "Europe/London"
                )->setTimezone($timezone)->format('d-m-Y H:i');
                $datetime_end_formatted = Carbon::parse(
                    $resourceBooking->to,
                    "Europe/London"
                )->setTimezone($timezone)->format('d-m-Y H:i');

                $subject = $libRep['name'] . " - " . $subjectStatus . " : Liberty Virtual Rooms Meeting";
            }

            $description = "DESCRIPTION;LANGUAGE=en-US:" . $participant->meeting_subject . " Link: " . $clientsLink;

            $method = "REQUEST";

            $viewParameters = [];

            if ($cancelled == 0) {
                $attachment = [
                    'ics' => [
                        'StringValue' => "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Microsoft Corporation//Outlook 10.0 MIMEDIR//EN\r\nMETHOD:" . $method . "\r\nBEGIN:VTIMEZONE\r\nTZID:" . $timezone . "\r\nEND:VTIMEZONE\r\nBEGIN:VEVENT\r\nSUMMARY:" . $attachmentSubject . "\r\n" . $description . "\r\nDTSTART;TZID=" . $timezone . ":" . $datetime_start . "\r\nDTEND;TZID=" . $timezone . ":" . $datetime_end . "\r\nDTSTAMP;VALUE=DATE-TIME:" . $datetime_start . "\r\UID:" . $room_id . $datetime_start . "\r\nSEQUENCE:" . $sequence . "\r\nCREATED;VALUE=DATE-TIME:" . $datetime_start . "\r\nURL;VALUE=URI:" . $clientsLink . $attendees . "\r\nLOCATION:" . $clientsLink . "\r\nPRIORITY:5\r\nSTATUS:CONFIRMED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-P15M\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR",
                        'DataType' => 'String',
                    ],
                ];
                $viewParameters['body'] = "The meeting link for " . $participant->meeting_subject . " is <a href=\"" . $clientsLink . "\">" . $clientsLink . "</a>. It has now been scheduled from " . $datetime_start_formatted . " (" . $timezone . ") to " . $datetime_end_formatted . " (" . $timezone . ").";
            } else {
                $description = "DESCRIPTION;LANGUAGE=en-US:" . $participant->meeting_subject . " - This meeting has been cancelled by the organiser.";
                $attachment = [
                    'ics' => [
                        'StringValue' => "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Microsoft Corporation//Outlook 10.0 MIMEDIR//EN\r\nMETHOD:CANCEL\r\nBEGIN:VTIMEZONE\r\nTZID:" . $timezone . "\r\nEND:VTIMEZONE\r\nBEGIN:VEVENT\r\nSUMMARY:" . $attachmentSubject . "\r\n" . $description . "\r\nDTSTART;TZID=" . $timezone . ":" . $datetime_start . "\r\nDTEND;TZID=" . $timezone . ":" . $datetime_end . "\r\nDTSTAMP;VALUE=DATE-TIME:" . $datetime_start . "\r\UID:" . $room_id . $datetime_start . "\r\nSEQUENCE:" . $sequence . "\r\nCREATED;VALUE=DATE-TIME:" . $datetime_start . "\r\nURL;VALUE=URI:" . $clientsLink . $attendees . "\r\nLOCATION: The meeting has been deleted.\r\nPRIORITY:5\r\nSTATUS:CANCELLED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-P15M\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR",
                        'DataType' => 'String',
                    ],
                ];
                $method = 'CANCEL';
                $viewParameters['body'] = $participant->meeting_subject . " - This meeting has been cancelled by the organiser.";
            }

            $viewParameters['title'] = $subjectStatus . " : Liberty Virtual Rooms Meeting";

            $viewParameters['business'] = strtoupper($this->business);


            $this->mail->queue(
                $participant->email,
                $participant->role,
                $subject,
                'emails.lets-talk.notification_modified_vr_meeting',
                $viewParameters,
                $attachment,
                null,
                'email',
                $method
            );
        }
    }

    public function sendIcsEmailSocial($room_id, $sequence, $ics_data = [], $cancelled = 0)
    {


        $attendees = $this->getIcalUpdatedAllParticipants($room_id, 1);

        $room = SocialRoom::where('room_code', $room_id)->first();

        $participants = SocialRoomParticipant::where('lt_social_room_id', $room->id)->get();

        $room = SocialRoom::where('room_code', $room_id)->first();

        $roomType = SocialRoomType::where('id', $room->lt_social_room_type_id)->first();
        $roomSchedule = SocialRoomSchedule::where('lt_social_room_id', $room->id)->first();

        $startDate = Carbon::parse($roomSchedule->start_date);

        $rule = '';
        $exdate = '';

        if ($roomSchedule->duration_type == 'on-a-schedule') {
            $until = $roomSchedule->valid_until;
            $validUntill = date("Ymd\THis", strtotime($until));
            $frequencyType = '';
            $extras = "";

            $weekNumberOfMonth = $startDate->weekOfMonth;
            if ($weekNumberOfMonth == 5) {
                $weekOfMonth = '-1';
            } else {
                $weekOfMonth = $weekNumberOfMonth;
            }
            $dayOfWeek = strtoupper(substr($startDate->format('l'), 0, 2));

            if ($roomSchedule->frequency_type == 'day') {
                $frequencyType = 'DAILY';
                $extras = ";BYDAY=MO,TU,WE,TH,FR;";
            } else {
                if ($roomSchedule->frequency_type == 'week') {
                    $frequencyType = 'WEEKLY';
                } else {
                    $frequencyType = 'MONTHLY';
                    $extras = ";BYSETPOS=" . $weekOfMonth . ";BYDAY=" . $dayOfWeek . ";";
                }
            }

            $interval = $roomSchedule->frequency;
            if ($roomSchedule->frequency_type == 'quarter') {
                $interval = 3;
            }
            $rule = "\r\nRRULE:FREQ=" . $frequencyType . ";UNTIL=" . $validUntill . "Z;INTERVAL=" . $interval . $extras;

            $socialRooms = SocialRoom::with('socialRoomSchedule')
                ->with(['socialRoomParticipants'])
                ->with('deletedScheduleEntries')
                ->where('room_code', $room_id)
                ->get();

            Log::info($socialRooms->first()->deletedScheduleEntries);

            $deletedScheduleEntries = $socialRooms->first()->deletedScheduleEntries;

            // foreach ($deletedScheduleEntries as $deletedScheduleEntry) {
            //     $deletedScheduleEntryDate = Carbon::parse($deletedScheduleEntry->date, 'Europe/London')->setTimezone($timezone)->format('Ymd\THis');
            //     $exdate .= "\r\nEXDATE;TZID=".$timezone.":".$deletedScheduleEntryDate;
            // }

            Log::info($rule);
        }


        $clientsLink = VideoCallRoom::generateWaitingRoomLink($room->room_code);

        // $primaryClient = $this->getLibRepDetails('lsm', $socialRoom->person_id);

        // $resourceBooking = ResourceBooking::where('room_id', $room_id)->first();
        $primaryLibRepId = $room->created_by_id;

        $libRep = $this->getLibRepById($primaryLibRepId);

        foreach ($participants as $participant) {

            $exdate = '';

            $role = $participant->role;

            if ($role == 'reception') {
                continue;
            } elseif ($role == 'virtual-rooms') {
                // $libRepDetails = $participant->details;
                $timezone = $libRep['office_timezone'];
                $attachmentSubject = $room->name . " - " . $roomType->name . " - Modified - Virtual Rooms for staff";
                $datetime_start = Carbon::parse(
                    $roomSchedule->start_date,
                    "Europe/London"
                )->setTimezone($timezone)->format('Ymd\THis');
                $datetime_end = Carbon::parse(
                    $roomSchedule->end_date,
                    "Europe/London"
                )->setTimezone($timezone)->format('Ymd\THis');
                $subject = $room->name . " - " . $roomType->name . " - Modified - Virtual Rooms for staff";
                $email = $attendees['array'][$participant->person_id];
            } else {
                $timezone = 'Europe/London';
                $attachmentSubject = $room->name . " - " . $roomType->name . " - Modified - Virtual Rooms for staff";
                $datetime_start = Carbon::parse(
                    $roomSchedule->start_date,
                    "Europe/London"
                )->setTimezone($timezone)->format('Ymd\THis');
                $datetime_end = Carbon::parse(
                    $roomSchedule->end_date,
                    "Europe/London"
                )->setTimezone($timezone)->format('Ymd\THis');
                $subject = $room->name . " - " . $roomType->name . " - Modified - Virtual Rooms for staff";
                $email = $attendees['array'][$participant->external_user_email];
            }

            if (isset($ics_data['recurrence_id'])) {

                $start_date = $ics_data['conference_date'] . ' ' . $ics_data['time_start'] . ':00';
                $end_date = $ics_data['conference_date'] . ' ' . $ics_data['time_end'] . ':00';

                $datetime_start = Carbon::parse(
                    $start_date,
                    $ics_data['timezone']
                )->setTimezone($timezone)->format('Ymd\THis');
                $datetime_end = Carbon::parse(
                    $end_date,
                    $ics_data['timezone']
                )->setTimezone($timezone)->format('Ymd\THis');

                $recurrence_id = Carbon::parse(
                    $ics_data['recurrence_id'],
                    $ics_data['timezone']
                )->setTimezone($timezone)->format('Ymd\THis');
            }
            if (isset($deletedScheduleEntries)) {
                foreach ($deletedScheduleEntries as $deletedScheduleEntry) {
                    $deletedScheduleEntryDate = Carbon::parse(
                        $deletedScheduleEntry->date,
                        'Europe/London'
                    )->setTimezone($timezone)->format('Ymd\THis');
                    $exdate .= "\r\nEXDATE;TZID=" . $timezone . ":" . $deletedScheduleEntryDate;
                }
            }


            $description = "DESCRIPTION;LANGUAGE=en-US:" . $room->name . " - " . $roomType->name . " - Modified - Virtual Rooms for staff. Link: " . $clientsLink;

            $method = "REQUEST";

            $sequence = (int)$sequence + 1;

            $viewParameters['title'] = $room->name . " - " . $roomType->name . " - Modified - Virtual Rooms for staff";

            if ($cancelled == 0) {
                if (isset($ics_data['recurrence_id'])) {
                    $attachment = [
                        'ics' => [
                            'StringValue' => "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Microsoft Corporation//Outlook 10.0 MIMEDIR//EN\r\nMETHOD:REQUEST\r\nBEGIN:VTIMEZONE\r\nTZID:" . $timezone . "\r\nEND:VTIMEZONE\r\nBEGIN:VEVENT\r\nSUMMARY:" . $attachmentSubject . "\r\n" . $description . "\r\nDTSTART;TZID=" . $timezone . ":" . $datetime_start . "\r\nRECURRENCE-ID;TZID=" . $timezone . ":" . $recurrence_id . $rule . $exdate . "\r\nDTEND;TZID=" . $timezone . ":" . $datetime_end . "\r\nDTSTAMP;VALUE=DATE-TIME:" . $datetime_start . "\r\nUID:" . $room->room_code . $datetime_start . $attendees['string'] . "\r\nSEQUENCE:" . $sequence . "\r\n" . "CREATED;VALUE=DATE-TIME:" . $datetime_start . "\r\nURL;VALUE=URI:" . $clientsLink . "\r\nLOCATION:" . $clientsLink . "\r\nPRIORITY:5\r\nSTATUS:CONFIRMED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-P15M\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR",
                            'DataType' => 'String',
                        ],
                    ];
                } else {
                    $attachment = [
                        'ics' => [
                            'StringValue' => "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Microsoft Corporation//Outlook 10.0 MIMEDIR//EN\r\nMETHOD:REQUEST\r\nBEGIN:VTIMEZONE\r\nTZID:" . $timezone . "\r\nEND:VTIMEZONE\r\nBEGIN:VEVENT\r\nSUMMARY:" . $attachmentSubject . "\r\n" . $description . "\r\nDTSTART;TZID=" . $timezone . ":" . $datetime_start . "\r\nDTEND;TZID=" . $timezone . ":" . $datetime_end . "\r\nDTSTAMP;VALUE=DATE-TIME:" . $datetime_start . $rule . $exdate . "\r\nUID:" . $room->room_code . $datetime_start . "\r\nSEQUENCE:" . $sequence . "\r\n" . "CREATED;VALUE=DATE-TIME:" . $datetime_start . "\r\nURL;VALUE=URI:" . $clientsLink . "\r\nLOCATION:" . $clientsLink . "\r\nPRIORITY:5\r\nSTATUS:CONFIRMED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-P15M\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR",
                            'DataType' => 'String',
                        ],
                    ];
                }
            } else {
                $viewParameters['title'] = $room->name . " - " . $roomType->name . " - Cancelled - Virtual Rooms for staff";
                $attachmentSubject = $room->name . " - " . $roomType->name . " - Cancelled - Virtual Rooms for staff";
                if (isset($ics_data['recurrence_id'])) {
                    SocialRoomModifiedRecurringSchedule::where(
                        'original_schedule',
                        $ics_data['recurrence_id']
                    )->delete();
                    $socialRoomCancelledSchedule = SocialRoomCancelledSchedule::create(
                        [
                            'lt_social_room_id' => $room->id,
                            'lt_social_room_schedule_id' => $roomSchedule->id,
                            'date' => $ics_data['recurrence_id'],
                        ]
                    );
                    $attachment = [
                        'ics' => [
                            'StringValue' => "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Microsoft Corporation//Outlook 10.0 MIMEDIR//EN\r\nMETHOD:CANCEL\r\nBEGIN:VTIMEZONE\r\nTZID:" . $timezone . "\r\nEND:VTIMEZONE\r\nBEGIN:VEVENT\r\nSUMMARY:" . $attachmentSubject . "\r\n" . $description . "\r\nDTSTART;TZID=" . $timezone . ":" . $datetime_start . "\r\nRECURRENCE-ID;TZID=" . $timezone . ":" . $recurrence_id . $rule . "\r\nDTEND;TZID=" . $timezone . ":" . $datetime_end . "\r\nDTSTAMP;VALUE=DATE-TIME:" . $datetime_start . "\r\nUID:" . $room->room_code . $datetime_start . $attendees['string'] . "\r\nSEQUENCE:" . $sequence . "\r\n" . "CREATED;VALUE=DATE-TIME:" . $datetime_start . "\r\nURL;VALUE=URI:" . $clientsLink . "\r\nLOCATION:" . $clientsLink . "\r\nPRIORITY:5\r\nSTATUS:CANCELLED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-P15M\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR",
                            'DataType' => 'String',
                        ],
                    ];
                } else {
                    SocialRoomSchedule::where('lt_social_room_id', $room->id)->delete();
                    $attachment = [
                        'ics' => [
                            'StringValue' => "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Microsoft Corporation//Outlook 10.0 MIMEDIR//EN\r\nMETHOD:CANCEL\r\nBEGIN:VTIMEZONE\r\nTZID:" . $timezone . "\r\nEND:VTIMEZONE\r\nBEGIN:VEVENT\r\nSUMMARY:" . $attachmentSubject . "\r\n" . $description . "\r\nDTSTART;TZID=" . $timezone . ":" . $datetime_start . "\r\nDTEND;TZID=" . $timezone . ":" . $datetime_end . "\r\nDTSTAMP;VALUE=DATE-TIME:" . $datetime_start . "\r\nUID:" . $room->room_code . $datetime_start . $attendees['string'] . "\r\nSEQUENCE:" . $sequence . "\r\n" . "CREATED;VALUE=DATE-TIME:" . $datetime_start . "\r\nURL;VALUE=URI:" . $clientsLink . "\r\nLOCATION:" . $clientsLink . "\r\nPRIORITY:5\r\nSTATUS:CANCELLED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-P15M\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR",
                            'DataType' => 'String',
                        ],
                    ];
                }
                $method = 'CANCEL';
            }

            $viewParameters = [];

            $viewParameters['body'] = "";
            $viewParameters['business'] = strtoupper($this->business);


            $this->mail->queue(
                $email,
                $participant->role,
                $subject,
                'emails.lets-talk.notification_modified_vr_meeting',
                $viewParameters,
                $attachment,
                null,
                'email',
                $method
            );
        }
    }

    /**
     * Save the details of the room and the participants from outlook ics
     *
     * @return json
     */
    public function generateRoomFromOutlook($data = null)
    {
        if (!isset($data)) {
            $data = $this->userRequest->all();
        }

        $roles = [
            'client',
            'liberty_representative',
            'guest_clients',
        ];

        // hardcode booking_type cause autoschedule booking from outlook is always for future
        $data['booking_type'] = 'future';

        $libertyRepresentativesData = $this->fetchLibRepFromCacheOrCms();

        if (isset($data['created_by_id'])) {
            $librepBookingCreator = $this->getLibRepByEmail($data['created_by_id'], $libertyRepresentativesData);
        }

        // Ensure the creator is a valid Virtual Rooms Liberty Representative
        if (!isset($librepBookingCreator)) {
            return Response::json(
                [
                    'response' => 'fail',
                    'data' => ['Cannot create booking. The creator is not a valid Virtual Rooms Liberty Representative.'],
                ]
            );
        }

        if (!isset($data['guest_clients']) && empty($data['guest_clients'])) {
            return Response::json(
                [
                    'response' => 'fail',
                    'data' => 'Cannot create booking. You must invite atleast 1 guest.',
                ]
            );
        }

        // Set librep data here after verifying that $librepBookingCreator is valid
        $data['person_id'] = $librepBookingCreator['id'];
        $data['liberty_representative'] = $librepBookingCreator['name'];
        $data['liberty_representative_job_title'] = $librepBookingCreator['job_title'];
        $data['liberty_representative_phone'] = $librepBookingCreator['mobile'];

        $conference_start_date = date(
            "Y-m-d H:i:s",
            strtotime($data['conference_date'] . ' ' . $data['time_start'] . ":00")
        );
        $conference_end_date = date(
            "Y-m-d H:i:s",
            strtotime($data['conference_date'] . ' ' . $data['time_end'] . ":00")
        );

        $booked_timezone = $librepBookingCreator['office_timezone'];
        $booked_timezone_offset = $this->get_timezone_offset('UTC', $booked_timezone);
        $carbon_booked_timezone_from = Carbon::parse(
            $conference_start_date,
            "Europe/London"
        )->setTimezone($booked_timezone);
        $booked_timezone_from = $carbon_booked_timezone_from->format('Y-m-d H:i:s');
        $booked_timezone_to = Carbon::parse(
            $conference_end_date,
            "Europe/London"
        )->setTimezone($booked_timezone)->format('Y-m-d H:i:s');

        if ($carbon_booked_timezone_from->lt(Carbon::now($booked_timezone))) {
            if (isset($librepBookingCreator['email']) && !empty($librepBookingCreator['email'])) {
                $viewParameters['body'] = "It was not possible to generate your Liberty Virtual Rooms Meeting from your Outlook invitation.<br />
                The date and time of your meeting can not be in the past.<br />
                Please edit the current item from your Outlook calendar to send an updated invitation to the system. Alternatively, cancel the meeting and rebook it anytime in the future.";

                $this->mail->queue(
                    $librepBookingCreator['email'],
                    $librepBookingCreator['name'],
                    'Error: Virtual Room NOT created',
                    'emails.lets-talk.notification_plain',
                    $viewParameters
                );
            }

            return Response::json(
                [
                    'response' => 'fail',
                    'data' => ['Cannot create booking. The selected time has already passed.'],
                ]
            );
        }

        // Ensure the creator's schedule is vacant for the selected booking
        $checkForExisting = ResourceBooking::where('person_id', '=', $data['person_id'])
            ->where(
                function ($query) use ($conference_start_date, $conference_end_date) {
                    $query->where(
                        function ($query) use ($conference_start_date, $conference_end_date) {
                            $query->where('from', '=', $conference_start_date)
                                ->where('to', '=', $conference_end_date);
                        }
                    )
                        ->orWhere(
                            function ($query) use ($conference_start_date, $conference_end_date) {
                                $query->where('from', '>=', $conference_start_date)
                                    ->where('from', '<', $conference_end_date);
                            }
                        )->orWhere(
                            function ($query) use ($conference_start_date, $conference_end_date) {
                                $query->where('to', '>', $conference_start_date)
                                    ->where('to', '<=', $conference_end_date);
                            }
                        );
                }
            )
            ->get()->count();

        if ($checkForExisting > 0) {
            // Send warning email to the creator informing him/her that he/she is already busy at that time
            if (isset($librepBookingCreator['email']) && !empty($librepBookingCreator['email'])) {
                $viewParameters['body'] = "It was not possible to generate your Liberty Virtual Rooms Meeting from your Outlook invitation.<br />
                You already have a meeting scheduled at the same time on the Virtual Rooms platform. Please edit the current item from your Outlook calendar to send an updated time to the system. This should be a time when you are set as available on the Virtual Rooms platform.";

                $this->mail->queue(
                    $librepBookingCreator['email'],
                    $librepBookingCreator['name'],
                    'Error: Virtual Room NOT created',
                    'emails.lets-talk.notification_plain',
                    $viewParameters
                );
            }

            return Response::json(
                [
                    'response' => 'fail',
                    'data' => ['Cannot create booking. The creator has already a scheduled meeting at the selected time.'],
                ]
            );
        }

        $data['guest_clients'] = json_decode($data['guest_clients']);
        $data['guest_emails'] = json_decode($data['guest_emails']);
        $data['guest_clientPhones'] = json_decode($data['guest_clientPhones']);
        $data['guest_clientCompanies'] = json_decode($data['guest_clientCompanies']);

        // Ensure the invited guests does not exceed the maximum number of guest allowed which is 8
        if (count((array)$data['guest_clients']) > self::MAX_TOTAL_GUESTS) {
            // SEND EMAIL, INFORM THE CREATOR/ORGANIZER ABOUT THIS
            if (isset($librepBookingCreator['email']) && !empty($librepBookingCreator['email'])) {
                $this->sendMaxLimitExceededWarningEmail($librepBookingCreator['email'], $librepBookingCreator['name']);
            }
            return Response::json(
                [
                    'response' => 'fail',
                    'data' => ['Cannot create booking. The maximum number of guests has been reached.'],
                ]
            );
        }

        // Prepare guest list, this will separate Guest LibRep from Guest Clients/External
        [$guestLibRepArray, $guests] = $this->prepareVideoCallParticipants($data, $libertyRepresentativesData);

        // Default main client data
        $data['email'] = "";
        $data['client'] = "";
        $data['client_email'] = "";
        $data['client_phone'] = "";
        $data['company'] = "";
        // Here we set the first public guest as the main client
        // and then remove the main client from the guest list to avoid being added twice
        if (count($guests)) {
            $data['email'] = array_shift($guests['guest_emails']);
            $data['client_email'] = $data['email'];
            $data['client'] = array_shift($guests['guest_client']);
            $data['client_phone'] = array_shift($guests['guest_clientPhones']);
            $data['company'] = array_shift($guests['guest_clientCompanies']);
        }

        $data["client_phone"] = str_replace('(0)', '', $data["client_phone"]);
        if (substr($data["client_phone"], 0, 1) === '0') {
            $data["client_phone"] = ltrim($data["client_phone"], '0');
        } elseif (substr($data["client_phone"], 0, 1) !== '+' && !empty($data["client_phone"])) {
            $data["client_phone"] = "+44" . $data["client_phone"];
        }

        // validate guest numbers
        $errors = $this->validateNumber($data, $roles);
        if (!empty($errors)) {
            return Response::json(
                [
                    'response' => 'fail',
                    'data' => $errors,
                ]
            );
        }

        // Set all $allRepresentatives to be used on email templates
        $allRepresentatives[] = (object)$librepBookingCreator; // force array to object
        $allRepresentatives[0]->user_name = $librepBookingCreator['name'];
        $allRepresentatives[0]->representative_job_title = $librepBookingCreator['job_title'];
        if (count($guestLibRepArray)) {
            foreach ($guestLibRepArray as $guestLibRep) {
                $libRepParticipant = new stdClass();
                $libRepParticipant->user_name = (isset($guestLibRep['name']) && !empty($guestLibRep['name']))
                    ? $guestLibRep['name']
                    : "";
                $libRepParticipant->email = (isset($guestLibRep['email']) && !empty($guestLibRep['email']))
                    ? $guestLibRep['email']
                    : "";
                $libRepParticipant->representative_job_title = (isset($guestLibRep['job_title']) && !empty($guestLibRep['job_title']))
                    ? $guestLibRep['job_title']
                    : "";
                array_push($allRepresentatives, $libRepParticipant);
            }
        }

        // set invited librep timezone details
        $librepBookingCreatorTzDetails = [
            'representative_time_zone' => '',
            'representative_time_zone_offset' => '',
            'representative_time_zone_from' => '',
            'representative_time_zone_to' => '',
        ];

        if (isset($librepBookingCreator['office_timezone']) && !empty($librepBookingCreator['office_timezone'])) {
            $librepBookingCreatorTzDetails = [
                'representative_time_zone' => $librepBookingCreator['office_timezone'],
                'representative_time_zone_offset' => $this->get_timezone_offset(
                    'UTC',
                    $librepBookingCreator['office_timezone']
                ),
                'representative_time_zone_from' => Carbon::parse(
                    $booked_timezone_from,
                    $booked_timezone
                )->setTimezone($librepBookingCreator['office_timezone']),
                'representative_time_zone_to' => Carbon::parse(
                    $booked_timezone_to,
                    $booked_timezone
                )->setTimezone($librepBookingCreator['office_timezone']),
            ];
        }

        $room = VideoCallRoom::create(
            [
                'name' => VideoCallRoom::createRoomName(),
                'conference_date_start' => $conference_start_date,
                'conference_date_end' => $conference_end_date,
                'created_at' => Carbon::now(),
                'created_by_id' => $data['created_by_id'],
                'created_by_login_type' => $data['created_by_login_type'],
                'created_by_role' => $data['created_by_role'],
                'booked_timezone' => $booked_timezone,
                'booked_timezone_offset' => $booked_timezone_offset,
                'booked_timezone_from' => $booked_timezone_from,
                'booked_timezone_to' => $booked_timezone_to,
                'business' => $this->business,
            ]
        );

        $response = [
            'room' => $room->name,
            'room_id' => $room->_id,
            'from' => $conference_start_date,
            'to' => $conference_end_date,
        ];

        $attached_files_info = [];
        if ($data['created_by_role'] === "client") {
            $resource_booking_data = ResourceBooking::create(
                [
                    'person_id' => $data['person_id'],
                    'room_id' => $room->_id,
                    'from' => $conference_start_date,
                    'to' => $conference_end_date,
                    'is_reminded' => 0,
                    'is_call_now' => 0,
                    'is_call_now_rep' => 0,
                ]
            );

            $attached_files = (isset($data['attached_files']) && !empty($data['attached_files']))
                ? $data['attached_files']
                : [];

            if (!empty($attached_files)) {
                foreach ($attached_files as $files) {
                    foreach ($files as $filename => $cloudname) {
                        $uuid = explode('/', $cloudname)[1];

                        array_push($attached_files_info, [$filename => $uuid]);

                        ResourceBookingAttachedFile::create(
                            [
                                'cloudname' => $cloudname,
                                'filename' => $filename,
                                'uuid' => $uuid,
                                'resource_booking_id' => $resource_booking_data->id,
                            ]
                        );
                    }
                }

                $data['attached_files'] = $attached_files_info;
            }
        }

        $statusCallBack = config('app.twilio.vrStatusCallBackUrl');
        $params = !empty($statusCallBack)
            ? ['statusCallback' => config('app.client_frontend') . '/' . $statusCallBack]
            : [];

        $log = VideoLog::create(
            [
                'room_id' => $room->id,
                'type' => VideoLog::TYPE_SMS,
            ]
        );

        $externalUserLink = "";
        // default $mainClientParticipant
        $mainClientParticipant = [];
        foreach ($roles as $role) {
            // default twilioSms
            $twilioSms = new stdClass();
            $twilioSms->sid = 'No SMS sent. Mobile number not provided.';
            $twilioSms->status = 'No SMS sent. Mobile number not provided.';

            //When a user invited a guest then add those participants
            if ($role == "guest_clients" && count($guests)) {
                $guests = (array)$guests;
                foreach ($guests['guest_emails'] as $gkey => $gValues) {
                    if (isset($guests['guest_client'][$gkey]) && !empty($guests['guest_client'][$gkey])) {
                        $userCode = VideoCallParticipant::generateUserCode();

                        $guestClientMobile = (isset($guests['guest_clientPhones'][$gkey]) && !empty($guests['guest_clientPhones'][$gkey]))
                            ? $guests['guest_clientPhones'][$gkey]
                            : '';

                        $participant = VideoCallParticipant::create(
                            [
                                'room_id' => $room->_id,
                                'user_name' => $guests['guest_client'][$gkey],
                                'user_code' => $userCode,
                                'company' => $guests['guest_clientCompanies'][$gkey],
                                'mobile_number' => $guestClientMobile,
                                'email' => $guests['guest_emails'][$gkey],
                                'meeting_subject' => (isset($data['meeting_subject']) && !empty($data['meeting_subject'])
                                    ? $data['meeting_subject']
                                    : ''),
                                'liberty_representative' => '',
                                'representative_job_title' => '',
                                'role' => $role,
                                'notes' => 'invited from outlook autoschedule',
                                'booked_timezone' => $booked_timezone,
                                'booked_timezone_offset' => $booked_timezone_offset,
                                'booked_timezone_from' => $booked_timezone_from,
                                'booked_timezone_to' => $booked_timezone_to,
                            ]
                        );

                        // SMS Guest Notification set this as text notification 15
                        if (!empty($guestClientMobile)) {
                            $smsMessaging = VideoCallParticipant::generateMessageGuest(
                                $room->name,
                                $data['conference_date'],
                                $conference_start_date,
                                $conference_end_date,
                                $booked_timezone,
                                $booked_timezone_offset,
                                $booked_timezone_from,
                                $booked_timezone_to,
                                $data['liberty_representative']
                            );
                        }

                        if (isset($smsMessaging) && !empty($smsMessaging) && !empty($guestClientMobile)) {
                            if (
                                SuppressionList::where('From', '=', sha1($guestClientMobile))->where(
                                    'is_suppressed',
                                    '=',
                                    1
                                )->get()->count() == 0
                            ) {
                                if ($this->isNumberUS($guestClientMobile)) { //use US config phone number
                                    $twilioSms = $this->twilioConfigUS->message(
                                        $guestClientMobile,
                                        // '+639395514403',
                                        $smsMessaging,
                                        [],
                                        $params
                                    );
                                } else {
                                    $twilioSms = $this->twilio->message(
                                        $guestClientMobile,
                                        // '+639395514403',
                                        $smsMessaging,
                                        [],
                                        $params
                                    );
                                }
                            } else {
                                $twilioSms = new stdClass();
                                $twilioSms->sid = "SMS suppressed.";
                                $twilioSms->status = "SMS suppressed.";
                            }
                        }

                        $participant->sms_id = $twilioSms->sid;
                        $participant->save();

                        // Email Guest Notification
                        $is_reminder = false;
                        $is_reminder_before_5 = false;
                        $is_added_participant = false;
                        //$clientsLink = VideoCallParticipant::generateLink($userCode);
                        $clientsLink = VideoCallRoom::generateWaitingRoomLink($room->name);

                        $viewParameters = [
                            'to_name' => $guests['guest_client'][$gkey],
                            'video_call_details' => $data,
                            'twilio_data' => $clientsLink,
                            'role' => $role,
                            'is_reminder' => $is_reminder,
                            'is_reminder_before_5' => $is_reminder_before_5,
                            'is_added_participant' => $is_added_participant,
                            'title' => 'Your meeting has been scheduled',
                            'guests' => $guests,
                            'booked_timezone' => $booked_timezone,
                            'booked_timezone_offset' => $booked_timezone_offset,
                            'booked_timezone_from' => $booked_timezone_from,
                            'booked_timezone_to' => $booked_timezone_to,
                            'representative_time_zone' => '',
                            'representative_time_zone_from' => '',
                            'representative_time_zone_offset' => '',
                            'other_representatives' => (count($allRepresentatives) > 0)
                                ? $allRepresentatives
                                : '',
                            'business' => $this->business,
                        ];

                        // $subject = "Your Liberty Virtual Rooms meeting has been scheduled";
                        $subject = $data['liberty_representative'] . " - Liberty Virtual Rooms meeting has been scheduled";

                        $description = "DESCRIPTION;LANGUAGE=en-US:" . $data['meeting_subject'] . " Link: " . $clientsLink;

                        $attendees = $this->getIcalUpdatedParticipants($viewParameters);

                        $attachmentSubject = $data['liberty_representative'] . " - Liberty Virtual Rooms";

                        $attachment = [
                            'ics' => [
                                'StringValue' => "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Microsoft Corporation//Outlook 10.0 MIMEDIR//EN\r\nMETHOD:REQUEST\r\nBEGIN:VTIMEZONE\r\nTZID:" . $booked_timezone . "\r\nEND:VTIMEZONE\r\nBEGIN:VEVENT\r\nSUMMARY:" . $attachmentSubject . "\r\n" . $description . "\r\nDTSTART;TZID=" . $booked_timezone . ":" . $datetime_start . "\r\nDTEND;TZID=" . $booked_timezone . ":" . $datetime_end . "\r\nDTSTAMP;VALUE=DATE-TIME:" . $datetime_start . "\r\UID:" . $room_id . $datetime_start . "\r\nSEQUENCE:1\r\n" . "CREATED;VALUE=DATE-TIME:" . $datetime_start . "\r\nURL;VALUE=URI:" . $clientsLink . "\r\nLOCATION:" . $clientsLink . $attendees . "\r\nPRIORITY:5\r\nSTATUS:CONFIRMED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-P15M\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR",
                                'DataType' => 'String',
                            ],
                        ];

                        $this->mail->queue(
                            $guests['guest_emails'][$gkey],
                            $guests['guest_client'][$gkey],
                            $subject,
                            ($this->business === 'lsm')
                                ? 'emails.lets-talk.notification'
                                : 'emails.lets-talk-lmre.notification',
                            $viewParameters,
                            $attachment
                        );

                        VideoLogData::create(
                            [
                                'log_id' => $log->_id,
                                'sms_id' => $twilioSms->sid,
                                'human_readable' => 'Sending message to ' . $participant->user_name,
                                'data' => ($twilioSms instanceof stdClass)
                                    ? (array)$twilioSms
                                    : $twilioSms->toArray(),
                            ]
                        );

                        $log->status = $twilioSms->status;
                        $log->save();
                    }
                }
            }

            if (!empty($data[$role]) && !empty($data[$role . '_email'])) {
                $userCode = VideoCallParticipant::generateUserCode();
                $mainClientCompany = (isset($data['company']) && !empty($data['company']))
                    ? $data['company']
                    : "";
                $data['company'] = $mainClientCompany;

                $participant = VideoCallParticipant::create(
                    [
                        'room_id' => $room->_id,
                        'user_name' => $data[$role],
                        'user_code' => $userCode,
                        'mobile_number' => (isset($data[$role . '_phone']) && !empty($data[$role . '_phone']))
                            ? $data[$role . '_phone']
                            : '',
                        'company' => $mainClientCompany,
                        'email' => (isset($data[$role . '_email']) && !empty($data[$role . '_email'])
                            ? $data[$role . '_email']
                            : ''),
                        'meeting_subject' => (isset($data['meeting_subject']) && !empty($data['meeting_subject'])
                            ? $data['meeting_subject']
                            : ''),
                        'representative_job_title' => ($role == 'liberty_representative')
                            ? $data['liberty_representative_job_title']
                            : '',
                        'role' => $role,
                        'notes' => 'invited from outlook autoschedule',
                        'booked_timezone' => $booked_timezone,
                        'booked_timezone_offset' => $booked_timezone_offset,
                        'booked_timezone_from' => $booked_timezone_from,
                        'booked_timezone_to' => $booked_timezone_to,
                        'details' => $librepBookingCreator,
                    ]
                );

                try {
                    if ((isset($data[$role . '_phone']) && !empty($data[$role . '_phone']))) {
                        // SMS Notification 7
                        if ($role == 'client' && $data['booking_type'] == 'future') {
                            $smsMessaging = VideoCallParticipant::generateMessageN7(
                                $room->name,
                                $data['conference_date'],
                                $conference_start_date,
                                $conference_end_date,
                                $booked_timezone,
                                $booked_timezone_offset,
                                $booked_timezone_from,
                                $booked_timezone_to,
                                $data['liberty_representative']
                            );
                        }

                        // SMS Notification 8
                        if ($role == 'liberty_representative' && $data['booking_type'] == 'future') {
                            $smsMessaging = VideoCallParticipant::generateMessageN8(
                                $room->name,
                                $data['conference_date'],
                                $conference_start_date,
                                $conference_end_date,
                                $booked_timezone,
                                $booked_timezone_offset,
                                $booked_timezone_from,
                                $booked_timezone_to,
                                $data['client'],
                                $mainClientCompany,
                                $librepBookingCreatorTzDetails['representative_time_zone'],
                                $librepBookingCreatorTzDetails['representative_time_zone_offset'],
                                $librepBookingCreatorTzDetails['representative_time_zone_from'],
                                $librepBookingCreatorTzDetails['representative_time_zone_to']
                            );
                        }

                        if (isset($smsMessaging) && !empty($smsMessaging)) {
                            if (
                                SuppressionList::where(
                                    'From',
                                    '=',
                                    sha1($data[$role . '_phone'])
                                )->where(
                                    'is_suppressed',
                                    '=',
                                    1
                                )->get()->count() == 0
                            ) {
                                if ($this->isNumberUS($data[$role . '_phone'])) { //use US config phone number
                                    $twilioSms = $this->twilioConfigUS->message(
                                        $data[$role . '_phone'],
                                        // '+639395514403',
                                        $smsMessaging,
                                        [],
                                        $params
                                    );
                                } else {
                                    $twilioSms = $this->twilio->message(
                                        $data[$role . '_phone'],
                                        // '+639395514403',
                                        $smsMessaging,
                                        [],
                                        $params
                                    );
                                }
                            } else {
                                $twilioSms = (object)[];
                                $twilioSms->sid = "SMS suppressed.";
                                $twilioSms->status = "SMS suppressed.";
                            }
                        }
                    }


                    $participant->sms_id = $twilioSms->sid;
                    $participant->save();

                    // Set main librep participant to be used when adding librep guests
                    if ($role === 'liberty_representative') {
                        $mainLibRepParticipant = $participant;
                    }

                    // Set main client participant to be used when adding librep guests
                    if ($role === 'client') {
                        $mainClientParticipant = $participant;
                    }

                    VideoLogData::create(
                        [
                            'log_id' => $log->_id,
                            'sms_id' => $twilioSms->sid,
                            'human_readable' => 'Sending message to ' . $participant->user_name,
                            'data' => ($twilioSms instanceof stdClass)
                                ? (array)$twilioSms
                                : $twilioSms->toArray(),
                        ]
                    );

                    $log->status = $twilioSms->status;
                    $log->save();

                    $response['participants']['success'] = $participant->toArray();

                    // Check representative timezone
                    if ($role == 'liberty_representative' && $librepBookingCreator['office_timezone']) {
                        $compareTimezone = $librepBookingCreator['office_timezone'];
                    } else {
                        $compareTimezone = $booked_timezone;
                    }

                    //$conference_start_date = Carbon::createFromFormat('Y-m-d H:i:s', $conference_start_date, $compareTimezone)->setTimezone("Europe/London");
                    //$conference_end_date = Carbon::createFromFormat('Y-m-d H:i:s', $conference_end_date, $compareTimezone)->setTimezone("Europe/London");

                    $datetime_start = date("Ymd\THis", strtotime($booked_timezone_from));
                    $datetime_end = date("Ymd\THis", strtotime($booked_timezone_to));

                    if (isset($data[$role . '_email']) && !empty($data[$role . '_email'])) {
                        //$clientsLink = VideoCallParticipant::generateLink($userCode);
                        $clientsLink = VideoCallRoom::generateWaitingRoomLink($room->name);
                        if ($role == "client") {
                            $externalUserLink = $clientsLink;
                        }
                        $twilio_data = $clientsLink;
                        $clientsLink = parse_url($clientsLink);
                        $clientsLink = $clientsLink['scheme'] . "://" . $clientsLink['host'] . "\r\n\t" . $clientsLink['path'];

                        $is_reminder = false;
                        $is_reminder_before_5 = false;
                        $is_added_participant = false;

                        $viewParameters = [
                            'to_name' => $data[$role],
                            'video_call_details' => $data,
                            'twilio_data' => $twilio_data,
                            'role' => $role,
                            'is_reminder' => $is_reminder,
                            'is_reminder_before_5' => $is_reminder_before_5,
                            'is_added_participant' => $is_added_participant,
                            //'user_code' => ($role=='liberty_representative') ? VideoCallParticipant::inviteLink($userCode) : "",
                            'user_code' => ($role == 'liberty_representative')
                                ? VideoCallRoom::generateWaitingRoomLink($room->name)
                                : "",
                            'booked_timezone' => $booked_timezone,
                            'booked_timezone_offset' => $booked_timezone_offset,
                            'booked_timezone_from' => $booked_timezone_from,
                            'booked_timezone_to' => $booked_timezone_to,
                            'representative_time_zone' => ($role === 'liberty_representative')
                                ? $librepBookingCreatorTzDetails['representative_time_zone']
                                : '',
                            'representative_time_zone_offset' => ($role === 'liberty_representative')
                                ? $librepBookingCreatorTzDetails['representative_time_zone_offset']
                                : '',
                            'representative_time_zone_from' => ($role === 'liberty_representative')
                                ? $librepBookingCreatorTzDetails['representative_time_zone_from']
                                : '',
                            'other_representatives' => (count($allRepresentatives) > 0)
                                ? $allRepresentatives
                                : '',
                            'business' => $this->business,
                        ];

                        $attachment = null;
                        $subject = "Liberty Virtual Rooms";

                        if ($role == 'client' && $data['booking_type'] == 'future') {
                            // Email Notification 7
                            // $subject = "Your Liberty Virtual Rooms meeting has been scheduled";
                            $subject = $data['liberty_representative'] . " - Liberty Virtual Rooms meeting has been scheduled";

                            $viewParameters['title'] = "Your meeting has been scheduled";
                            $viewParameters['guests'] = (isset($guests['guest_client']) && !empty($guests['guest_client']))
                                ? $guests
                                : "";
                            $description = "DESCRIPTION;LANGUAGE=en-US:" . $data['meeting_subject'] . " Link: " . $clientsLink;

                            // HERE
                            $attendees = $this->getIcalUpdatedParticipants($viewParameters);

                            $attachmentSubject = $data['liberty_representative'] . " - Liberty Virtual Rooms";
                            $attachment = [
                                'ics' => [
                                    'StringValue' => "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Microsoft Corporation//Outlook 10.0 MIMEDIR//EN\r\nMETHOD:REQUEST\r\nBEGIN:VTIMEZONE\r\nTZID:" . $booked_timezone . "\r\nEND:VTIMEZONE\r\nBEGIN:VEVENT\r\nSUMMARY:" . $attachmentSubject . "\r\n" . $description . "\r\nDTSTART;TZID=" . $booked_timezone . ":" . $datetime_start . "\r\nDTEND;TZID=" . $booked_timezone . ":" . $datetime_end . "\r\nDTSTAMP;VALUE=DATE-TIME:" . $datetime_start . "\r\UID:" . $room_id . $datetime_start . "\r\nSEQUENCE:1\r\n" . "CREATED;VALUE=DATE-TIME:" . $datetime_start . "\r\nURL;VALUE=URI:" . $clientsLink . "\r\nLOCATION:" . $clientsLink . $attendees . "\r\nPRIORITY:5\r\nSTATUS:CONFIRMED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-P15M\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR",
                                    'DataType' => 'String',
                                ],
                            ];
                        }

                        if ($role == 'liberty_representative' && $data['booking_type'] == 'future') {
                            $booked_timezone_from = Carbon::parse(
                                $booked_timezone_from,
                                $booked_timezone
                            )->setTimezone($compareTimezone)->format('Y-m-d H:i:s');
                            $booked_timezone_to = Carbon::parse(
                                $booked_timezone_to,
                                $booked_timezone
                            )->setTimezone($compareTimezone)->format('Y-m-d H:i:s');
                            $datetime_start = date("Ymd\THis", strtotime($booked_timezone_from));
                            $datetime_end = date("Ymd\THis", strtotime($booked_timezone_to));

                            // default ics title
                            $summary = "SUMMARY:Liberty Virtual Rooms";
                            $subject = "New Liberty Virtual Rooms Meeting";
                            // Email Notification 8
                            // $subject = "You have a new Liberty Virtual Rooms meeting";
                            if (!empty($data['client'])) {
                                $subject = $data['client'] . " - New Liberty Virtual Rooms Meeting";
                                $fromCompany = !empty($mainClientCompany)
                                    ? $mainClientCompany
                                    : "";
                                $summary = "SUMMARY:" . $data['client'] . ", " . $fromCompany . " - Liberty Virtual Rooms";
                            } else {
                                if (isset($allRepresentatives[1]->user_name)) {
                                    $userName = !empty($allRepresentatives[1]->user_name)
                                        ? $allRepresentatives[1]->user_name
                                        : "";
                                    $summary = !empty($userName)
                                        ? "SUMMARY:" . $userName . " - Liberty Virtual Rooms"
                                        : "SUMMARY:Liberty Virtual Rooms";
                                    $subject = !empty($userName)
                                        ? $userName . " - New Liberty Virtual Rooms Meeting"
                                        : "New Liberty Virtual Rooms Meeting";
                                }
                            }


                            $viewParameters['title'] = "You have a new meeting";
                            $viewParameters['guests'] = (isset($guests['guest_client']) && !empty($guests['guest_client']))
                                ? $guests
                                : "";
                            $description = "DESCRIPTION;LANGUAGE=en-US:" . $data['meeting_subject'] . " Link: " . $clientsLink;
                            $attendees = $this->getIcalUpdatedParticipants($viewParameters);

                            $attachment = [
                                'ics' => [
                                    'StringValue' => "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Microsoft Corporation//Outlook 10.0 MIMEDIR//EN\r\nMETHOD:REQUEST\r\nBEGIN:VTIMEZONE\r\nTZID:" . $compareTimezone . "\r\nEND:VTIMEZONE\r\nBEGIN:VEVENT\r\n" . $summary . "\r\n" . $description . "\r\nDTSTART;TZID=" . $compareTimezone . ":" . $datetime_start . "\r\nDTEND;TZID=" . $compareTimezone . ":" . $datetime_end . "\r\nDTSTAMP;VALUE=DATE-TIME:" . $datetime_start . "\r\UID:" . $room_id . $datetime_start . "\r\nSEQUENCE:1\r\n" . "CREATED;VALUE=DATE-TIME:" . $datetime_start . "\r\nURL;VALUE=URI:" . $clientsLink . $attendees . "\r\nLOCATION:" . $clientsLink . "\r\nPRIORITY:5\r\nSTATUS:CONFIRMED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-P15M\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR",
                                    'DataType' => 'String',
                                ],
                            ];
                        }

                        $viewParameters['business'] = $this->business;

                        $this->mail->queue(
                            $data[$role . '_email'],
                            $data[$role],
                            $subject,
                            ($this->business === 'lsm')
                                ? 'emails.lets-talk.notification'
                                : 'emails.lets-talk-lmre.notification',
                            $viewParameters,
                            $attachment
                        );
                    }
                } catch (Exception $e) {
                    Log::error($e->getTraceAsString());

                    $response['participants']['failed'][] = [
                        'name' => $participant['user_name'],
                        'message' => $e->getMessage(),
                    ];

                    // save the error in the record
                    $participant->error = $e->getMessage();
                    $participant->save();

                    // exit immediately if there was an error with any participant
                    break;
                }
            }
        }

        // Add Liberty Representative participant
        if (count($guestLibRepArray) > 0 && isset($room, $mainLibRepParticipant)) {
            foreach ($guestLibRepArray as $guestLibRep) {
                $addGuestLibRep = $this->addGuestLibRepParticipants(
                    [
                        'room' => $room,
                        'invited_librep' => $guestLibRep, // the librep to be added as guest
                        'main_librep_participant' => $mainLibRepParticipant,
                        'main_client_participant' => $mainClientParticipant,
                        'all_representatives' => $allRepresentatives,
                    ]
                );
                if ($addGuestLibRep['response'] === 'fail') {
                    return Response::json($addGuestLibRep);
                }
            }
        }

        if (empty($response['participants']['failed'])) {
            return Response::json(
                [
                    'response' => 'success',
                    'clientsLink' => $externalUserLink,
                    'data' => $response,
                ]
            );
        }

        return Response::json(
            [
                'response' => 'fail',
                'data' => $response,
            ]
        );
    }

    // Prepare guest clients & guest LibRep for AutoSchedule Outlook
    // Check logic here: https://github.com/fastfwd/Risk-Reduce/issues/2198

    private function getLibRepByEmail($email, $libertyRepresentatives = null)
    {
        if (!isset($libertyRepresentatives)) {
            $libertyRepresentatives = $this->fetchLibRepFromCacheOrCms();
        }

        $foundLibRep = null;
        foreach ($libertyRepresentatives as $key => $librep) {
            if (!isset($librep['email'])) {
                continue;
            }

            if (strtolower(trim($librep['email'])) === strtolower(trim($email))) {
                $foundLibRep = $librep;
                $foundLibRep['id'] = $key;
                break;
            }
        }

        return $foundLibRep;
    }

    private function sendMaxLimitExceededWarningEmail($email, $name)
    {
        if (!isset($email) || empty($email)) {
            return false;
        }

        $viewParameters['body'] = "It was not possible to generate your Liberty Virtual Rooms Meeting from your Outlook invitation.<br />
            The maximum number of participants has been exceeded. The meeting can host maximum of 9 participants.<br />
            Please edit the current item from your Outlook calendar to send an updated invitation to the system. Alternatively, cancel the meeting and rebook it with fewer guests.";

        $this->mail->queue(
            $email,
            isset($name)
                ? $name
                : "",
            'Error: Virtual Room NOT created',
            'emails.lets-talk.notification_plain',
            $viewParameters
        );
    }

    // Send max limit reached warning email for booking came from outlook autoschedule

    private function prepareVideoCallParticipants($data, $libertyRepresentativesData)
    {
        $defaultGuestName = "Guest";
        // guest client
        $guests = [];
        $guestLibRepArray = [];
        foreach ($data['guest_emails'] as $key => $email) {
            // Check if the guest email exist in the librep list
            $foundLibRepGuest = $this->getLibRepByEmail($email, $libertyRepresentativesData);
            // If found, we add them in $guestLibRepArray and not $guests(which is an external guest array)
            if (isset($foundLibRepGuest)) {
                $guestLibRepArray[$key] = $foundLibRepGuest;
            } else {
                $guests['guest_emails'][] = $email;
            }
        }

        $guestLibRepEmailKeys = array_keys($guestLibRepArray);

        foreach ($data['guest_clientPhones'] as $key => $gclientPhone) {
            $equivalentEmailKey = 'guestEmail' . str_replace('guest_clientPhone', '', $key);
            // Ensure we dont include $gclientPhone that was identified as librep
            if (!in_array($equivalentEmailKey, $guestLibRepEmailKeys)) {
                $guests['guest_clientPhones'][] = $gclientPhone;
            }
        }

        foreach ($data['guest_clientCompanies'] as $key => $gclientcompany) {
            $equivalentEmailKey = 'guestEmail' . str_replace('guest_clientCompany', '', $key);
            // Ensure we dont include $gclientcompany that was identified as librep
            if (!in_array($equivalentEmailKey, $guestLibRepEmailKeys)) {
                $guests['guest_clientCompanies'][] = $gclientcompany;
            }
        }

        foreach ($data['guest_clients'] as $key => $gclient) {
            $equivalentEmailKey = 'guestEmail' . str_replace('guestClient', '', $key);
            // Ensure we dont include $gclient that was identified as librep
            if (!in_array($equivalentEmailKey, $guestLibRepEmailKeys)) {
                $guests['guest_client'][] = $gclient;
            }
        }
        // Early return if no guest client found
        if (!count($guests)) {
            return [$guestLibRepArray, $guests];
        }

        // Cross check guest email from ExternalContactLogs
        $foundExternalContacts = ResourceExternalContact::whereIn('email', $guests['guest_emails'])->get();

        // Set found external contacts as guest details
        foreach ($guests['guest_emails'] as $key => $guestEmail) {
            $foundExternalContact = $foundExternalContacts->first(
                function ($colKey, $item) use ($guestEmail) {
                    return $item->email === $guestEmail;
                }
            );

            if (!isset($foundExternalContact)) {
                // Check if ICS includes the relevant Name of the contact for this email address.
                // If none, we set the name to $defaultGuestName
                if (
                    empty($guests['guest_client'][$key]) || strpos(
                        strtolower($guests['guest_client'][$key]),
                        strtolower($guestEmail)
                    ) !== false
                ) {
                    $guests['guest_client'][$key] = $defaultGuestName;
                }
                continue;
            }

            // Set guest details from External Contact Logs

            // set guest mobile
            if (isset($foundExternalContact->mobile_number) && !empty($foundExternalContact->mobile_number)) {
                $guests['guest_clientPhones'][$key] = $foundExternalContact->mobile_number;
            }
            // default guest name, in case the
            $guests['guest_client'][$key] = $defaultGuestName;
            // set guest name
            if (
                isset($foundExternalContact->name) && !empty($foundExternalContact->name) && strpos(
                    strtolower($foundExternalContact->name),
                    strtolower($guestEmail)
                ) === false
            ) {
                $guests['guest_client'][$key] = $foundExternalContact->name;
            }
            // set guest company
            if (isset($foundExternalContact->company) && !empty($foundExternalContact->company)) {
                $guests['guest_clientCompanies'][$key] = $foundExternalContact->company;
            }
        }

        return [$guestLibRepArray, $guests];
    }

    // Fetch Librep from cache, if no cache found, It will be fetched from cms and store to cache

    /**
     * Save the details of the room and the participants
     *
     * @return json
     */
    public function addGuestLibRepParticipants($params)
    {
        // \Log::info('addGuestLibRepParticipants $params==>', $params);

        if (!isset($params['room'])) {
            return [
                'response' => 'fail',
                'data' => 'Invalid room',
            ];
        }

        $room = $params['room'];
        $room_id = $room->_id;

        $data['booking_type'] = "future";
        $data['role'] = 'liberty_representative';
        $data['room_id'] = $room_id;

        // the librep guest to be added, invited by the $mainLibRepParticipant
        $invitedLibRepArr = $params['invited_librep'];
        $data['mobile'] = $invitedLibRepArr['mobile'];
        $data['name'] = $invitedLibRepArr['name'];
        $data['email'] = $invitedLibRepArr['email'];
        $data['liberty_representative'] = $invitedLibRepArr['name'];

        // All representatives list, will be used on email template
        $otherRepresentatives = $params['all_representatives'];

        // Main librep participant, the organizer of this meeting
        $mainLibRepParticipant = $params['main_librep_participant'];

        $meeting_subject = (isset($mainLibRepParticipant->meeting_subject) && !empty($mainLibRepParticipant->meeting_subject))
            ? $mainLibRepParticipant->meeting_subject
            : '';
        $data['meeting_subject'] = $meeting_subject;

        // Main client participant, the first external/public guest entered by the $mainLibRepParticipant in outlook
        $mainClientParticipant = $params['main_client_participant'];
        // Main client could be null sometimes coz it's allowed to schedule a call between LibRep to LibRep only
        // So we need to add null $mainClientParticipant object to avoid checking if isset
        if (!isset($mainClientParticipant) || empty($mainClientParticipant)) {
            $mainClientParticipant = new stdClass();
            $mainClientParticipant->user_name = "";
            $mainClientParticipant->mobile_number = "";
            $mainClientParticipant->email = "";
            $mainClientParticipant->company = "";
        }

        $data['client'] = $mainClientParticipant->user_name;
        $data['client_phone'] = $mainClientParticipant->mobile_number;

        $getAttachedFiles = ResourceBooking::with('bookingattachedfiles')->where('room_id', $room_id)->first();

        if ($getAttachedFiles && !empty($getAttachedFiles)) {
            // ${getAttachedFiles[i].uuid}/downloads3link`);
            //     link.attr("title", getAttachedFiles[i].filename);
            $attached_files = [];
            if (isset($getAttachedFiles['bookingattachedfiles']) && !empty($getAttachedFiles['bookingattachedfiles'])) {
                $attached_files = $getAttachedFiles['bookingattachedfiles'];
                $get_proper_object = [];
                foreach ($attached_files as $file) {
                    array_push($get_proper_object, [$file['filename'] => $file['uuid']]);
                }
                $data['attached_files'] = $get_proper_object;
            }
        }

        $schedule = ResourceBooking::where('room_id', $room_id)->first();
        if (!$schedule) {
            return [
                'response' => 'fail',
                'data' => 'Schedule was not found.',
            ];
        }

        $data['schedule'] = $schedule['from'] . ' to ' . $schedule['to'];
        $data['conference_date'] = Carbon::parse($schedule['from'])->format('Y-m-d');
        $data['time_start'] = Carbon::parse($schedule['from'])->format('H:i:00');
        $data['time_end'] = Carbon::parse($schedule['to'])->format('H:i:00');

        $conference_start_date = date("Y-m-d H:i:s", strtotime($data['conference_date'] . ' ' . $data['time_start']));
        $conference_end_date = date("Y-m-d H:i:s", strtotime($data['conference_date'] . ' ' . $data['time_end']));

        $booked_timezone = $mainLibRepParticipant->booked_timezone;
        $booked_timezone_offset = $this->get_timezone_offset('UTC', $booked_timezone);

        $booked_timezone_from = Carbon::parse(
            $conference_start_date,
            "Europe/London"
        )->setTimezone($mainLibRepParticipant->booked_timezone)->format('Y-m-d H:i:s');
        $booked_timezone_to = Carbon::parse(
            $conference_end_date,
            "Europe/London"
        )->setTimezone($mainLibRepParticipant->booked_timezone)->format('Y-m-d H:i:s');

        $participantsCount = VideoCallParticipant::where('room_id', $room_id)->get()->count();

        $isMobileNumberValid = true;

        if (!isset($data['mobile']) || empty($data['mobile'])) {
            $isMobileNumberValid = false;
        } else {
            try {
                $this->twilioClient->lookups->v1->phoneNumbers($data['mobile'])->fetch();
            } catch (Exception $e) {
                $isMobileNumberValid = false;
            }
        }

        $response = [
            'room_id' => $room_id,
        ];

        $statusCallBack = config('app.twilio.vrStatusCallBackUrl');
        $params = !empty($statusCallBack)
            ? ['statusCallback' => config('app.client_frontend') . '/' . $statusCallBack]
            : [];

        $log = VideoLog::create(
            [
                'room_id' => $room_id,
                'type' => VideoLog::TYPE_SMS,
            ]
        );

        $userCode = VideoCallParticipant::generateUserCode();

        // set invited librep timezone details
        $invitedLibRepTimezoneDetails = [
            'representative_time_zone' => '',
            'representative_time_zone_offset' => '',
            'representative_time_zone_from' => '',
            'representative_time_zone_to' => '',
        ];

        if (isset($invitedLibRepArr['office_timezone']) && !empty($invitedLibRepArr['office_timezone'])) {
            $invitedLibRepTimezoneDetails = [
                'representative_time_zone' => $invitedLibRepArr['office_timezone'],
                'representative_time_zone_offset' => $this->get_timezone_offset(
                    'UTC',
                    $invitedLibRepArr['office_timezone']
                ),
                'representative_time_zone_from' => Carbon::parse(
                    $booked_timezone_from,
                    $booked_timezone
                )->setTimezone($invitedLibRepArr['office_timezone']),
                'representative_time_zone_to' => Carbon::parse(
                    $booked_timezone_to,
                    $booked_timezone
                )->setTimezone($invitedLibRepArr['office_timezone']),
            ];
        }

        $mainClientCompany = $mainClientParticipant->company;

        // SMS notification 18
        // SMS notification 18
        $message = VideoCallParticipant::generateMessageN18(
            $room->name,
            $data['conference_date'],
            $conference_start_date,
            $conference_end_date,
            $booked_timezone,
            $booked_timezone_offset,
            $booked_timezone_from,
            $booked_timezone_to,
            $mainClientParticipant->user_name,
            $mainClientParticipant->company,
            $invitedLibRepTimezoneDetails['representative_time_zone'],
            $invitedLibRepTimezoneDetails['representative_time_zone_offset'],
            $invitedLibRepTimezoneDetails['representative_time_zone_from'],
            $invitedLibRepTimezoneDetails['representative_time_zone_to']
        );

        try {
            if (
                SuppressionList::where('From', '=', sha1($data['mobile']))->where(
                    'is_suppressed',
                    '=',
                    1
                )->get()->count() == 0
            ) {
                if ($isMobileNumberValid) {
                    if ($this->isNumberUS($data['mobile'])) { //use US config phone number
                        $twilioSms = $this->twilioConfigUS->message(
                            $data['mobile'],
                            // '+639395514403',
                            $message,
                            [],
                            $params
                        );
                    } else {
                        $twilioSms = $this->twilio->message(
                            $data['mobile'],
                            // '+639395514403',
                            $message,
                            [],
                            $params
                        );
                    }
                } else {
                    $twilioSms = (object)[];
                    $twilioSms->sid = "Invalid phone number.";
                    $twilioSms->status = "Invalid phone number.";
                }
            } else {
                $twilioSms = (object)[];
                $twilioSms->sid = "SMS suppressed.";
                $twilioSms->status = "SMS suppressed.";
            }

            $participant = VideoCallParticipant::create(
                [
                    'room_id' => $room_id,
                    'user_name' => $data['name'],
                    'user_code' => $userCode,
                    'mobile_number' => $data['mobile'],
                    'company' => $mainClientCompany,
                    'email' => (isset($data['email']) && !empty($data['email'])
                        ? $data['email']
                        : ''),
                    'meeting_subject' => $meeting_subject,
                    'role' => $data['role'],
                    'notes' => 'invited from outlook autoschedule',
                    'representative_job_title' => (isset($invitedLibRepArr['job_title']) && !empty($invitedLibRepArr['job_title']))
                        ? $invitedLibRepArr['job_title']
                        : '',
                    'booked_timezone' => $booked_timezone,
                    'booked_timezone_offset' => $booked_timezone_offset,
                    'booked_timezone_from' => $booked_timezone_from,
                    'booked_timezone_to' => $booked_timezone_to,
                    'details' => $invitedLibRepArr,
                ]
            );

            // Set this to be used in email template
            $data['client'] = $mainClientParticipant->user_name;
            $data['company'] = $mainClientParticipant->company;
            $data['email'] = $mainClientParticipant->email;

            $participant->sms_id = $twilioSms->sid;
            $participant->save();

            VideoLogData::create(
                [
                    'log_id' => $log->_id,
                    'sms_id' => $twilioSms->sid,
                    'human_readable' => 'Sending message to ' . $participant->user_name,
                    'data' => in_array($twilioSms->status, ["SMS suppressed.", "Invalid phone number."])
                        ? (array)$twilioSms
                        : $twilioSms->toArray(),
                ]
            );

            $log->status = $twilioSms->status;
            $log->save();

            $response['participants']['success'] = $participant->toArray();
            $response['participant_count'] = $participantsCount + 1;

            $datetime_start = date("Ymd\THis", strtotime($booked_timezone_from));
            $datetime_end = date("Ymd\THis", strtotime($booked_timezone_to));
            $data['time_start'] = date("H:i", strtotime($data['time_start']));

            if (isset($invitedLibRepArr['email']) && !empty($invitedLibRepArr['email'])) {
                //$clientsLink = VideoCallParticipant::generateLink($userCode);
                $clientsLink = VideoCallRoom::generateWaitingRoomLink($room->name);
                $twilio_data = $clientsLink;
                $clientsLink = parse_url($clientsLink);
                $clientsLink = $clientsLink['scheme'] . "://" . $clientsLink['host'] . "\r\n\t" . $clientsLink['path'];
                $guests = [];
                $guestList = VideoCallParticipant::where('room_id', $room["_id"])->where(
                    'role',
                    'guest_clients'
                )->get();

                $guests = [];
                if (count($guestList) > 0) {
                    foreach ($guestList as $key => $guest) {
                        $guests['guest_client'][] = $guest->user_name;
                        $guests['guest_emails'][] = $guest->email;
                        $guests['guest_clientPhones'][] = $guest->mobile_number;
                    }
                }

                $viewParameters = [
                    'to_name' => $data['name'],
                    'video_call_details' => $data,
                    'twilio_data' => $twilio_data,
                    'role' => $data['role'],
                    'is_reminder' => false,
                    'is_reminder_before_5' => false,
                    'is_added_participant' => true,
                    'title' => 'You have a new meeting',
                    'guests' => (count($guests) > 0)
                        ? $guests
                        : "",
                    'booked_timezone' => $booked_timezone,
                    'booked_timezone_offset' => $booked_timezone_offset,
                    'booked_timezone_from' => $booked_timezone_from,
                    'booked_timezone' => $booked_timezone,
                    'representative_time_zone' => $invitedLibRepTimezoneDetails['representative_time_zone'],
                    'representative_time_zone_offset' => $invitedLibRepTimezoneDetails['representative_time_zone_offset'],
                    'representative_time_zone_from' => $invitedLibRepTimezoneDetails['representative_time_zone_from'],
                    'other_representatives' => (count($otherRepresentatives) > 0)
                        ? $otherRepresentatives
                        : '',
                    'business' => $this->business,
                ];

                $viewParameters['is_added_participant'] = false;

                // if there are multiple representative then the info should show the first included rep
                $viewParameters['main_representative'] = $mainLibRepParticipant->user_name;
                $viewParameters['main_representative_job_title'] = $mainLibRepParticipant->representative_job_title;
                $viewParameters['main_representative_email'] = $mainLibRepParticipant->email;
                $viewParameters['user_code'] = VideoCallParticipant::inviteLink($mainLibRepParticipant->user_code);

                $description = "DESCRIPTION;LANGUAGE=en-US:" . $meeting_subject . " Link: " . $clientsLink;
                $withClient = !empty($data['client'])
                    ? $data['client']
                    : "";
                $fromCompany = !empty($mainClientCompany)
                    ? $mainClientCompany
                    : "";
                $summary = "SUMMARY:" . $withClient . ", " . $fromCompany . " - Liberty Virtual Rooms";
                if (empty($withClient)) {
                    $withClient = $mainLibRepParticipant->user_name;
                    $summary = "SUMMARY:" . $withClient . " - Liberty Virtual Rooms";
                }
                $attendees = $this->getIcalUpdatedParticipants($viewParameters);

                $attachment = [
                    'ics' => [
                        'StringValue' => "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Microsoft Corporation//Outlook 10.0 MIMEDIR//EN\r\nMETHOD:REQUEST\r\nBEGIN:VTIMEZONE\r\nTZID:" . $booked_timezone . "\r\nEND:VTIMEZONE\r\nBEGIN:VEVENT\r\n" . $summary . "\r\n" . $description . "\r\nDTSTART;TZID=" . $booked_timezone . ":" . $datetime_start . "\r\nDTEND;TZID=" . $booked_timezone . ":" . $datetime_end . "\r\nDTSTAMP;VALUE=DATE-TIME:" . $datetime_start . "\r\nUID:" . $room_id . "\r\nSEQUENCE:1\r\n" . "CREATED;VALUE=DATE-TIME:" . $datetime_start . "\r\nURL;VALUE=URI:" . $clientsLink . "\r\nLOCATION:" . $clientsLink . $attendees . "\r\nPRIORITY:5\r\nSTATUS:CONFIRMED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-P15M\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR",
                        'DataType' => 'String',
                    ],
                ];

                if (!empty($mainClientParticipant->user_name)) {
                    $subject = $mainClientParticipant->user_name . " - New Liberty Virtual Rooms Meeting";
                } else {
                    $subject = $mainLibRepParticipant->user_name . " - New Liberty Virtual Rooms Meeting";
                }

                $this->mail->queue(
                    $invitedLibRepArr['email'],
                    "Liberty Representative",
                    $subject,
                    ($room->business === 'lmre')
                        ? 'emails.lets-talk-lmre.notification'
                        : 'emails.lets-talk.notification',
                    $viewParameters,
                    $attachment
                );
            }
        } catch (Exception $e) {
            Log::error($e->getTraceAsString());

            if (isset($participant) && !empty($participant)) {
                $response['participants']['failed'][] = [
                    'name' => $participant['user_name'],
                    'message' => $e->getMessage(),
                ];

                // save the error in the record
                $participant->error = $e->getMessage();
                $participant->save();
            } else {
                $response['participants']['failed'][] = [
                    'message' => $e->getMessage(),
                ];
            }
        }

        if (empty($response['participants']['failed'])) {
            return [
                'response' => 'success',
                'clientsLink' => $clientsLink,
                'data' => $response,
            ];
        }

        return [
            'response' => 'fail',
            'data' => $response,
        ];
    }

    public function getParticipantSchedule($identity, $roomName)
    {
        $getUserTz = "";
        $booked_timezone = "";
        $user = VideoCallParticipant::where('user_code', $identity)->first();

        if ($user && $user->room->name === $roomName) {
            if (!isset($user->booked_timezone)) {
                $booked_timezone = "Europe/London";
            } else {
                $booked_timezone = $user->booked_timezone;
            }
            if ($user->role == 'liberty_representative') {
                $getUserTz = isset($user->details['office_timezone'])
                    ? $user->details['office_timezone']
                    : $booked_timezone;
            } else {
                $getUserTz = $booked_timezone;
            }

            $schedule = new Carbon($user->booked_timezone_from, new DateTimeZone($booked_timezone));

            return Response::json(
                [
                    'response' => 'success',
                    'data' => [
                        'timestamp' => $schedule->toIso8601String(),
                        'timezone' => $getUserTz,
                    ],
                ]
            );
        }

        return Response::json(
            [
                'response' => 'fail',
                'message' => 'User or room not found',
            ]
        );
    }

    public function exportContacts()
    {
        $existingContact = VideoCallParticipant::whereIn('role', ['guest_clients', 'clients', 'client'])->get();

        if ($existingContact) {
            return Response::json(
                [
                    'data' => $existingContact,
                    'response' => 200,
                ]
            );
        }

        return Response::json(
            [
                'data' => $existingContact,
                'response' => 404,
            ]
        );
    }

    public function importContacts()
    {

        $data = json_decode(json_encode($this->userRequest->all()));
        if (!$data) {
            return Response::json(
                [
                    'msg' => 'no data found',
                    'response' => 404,
                ]
            );
        }

        $hasEmptyField = false;
        $emptyFieldRow = [];

        $externalContacts = json_decode($data->data);

        foreach ($externalContacts as $contact) {
            $jsonContact = json_decode(json_encode($contact));

            //check if field is empty or null
            foreach ($contact as $field) {
                if (empty($field) || $field == "" || $field == null) {
                    $emptyFieldRow[] = $contact;
                    $hasEmptyField = true;
                }
            }

            //reset flag and don't save the data
            if ($hasEmptyField) {
                $hasEmptyField = false;
                continue;
            }

            $trimEmail = isset($jsonContact->email) && !empty($jsonContact->company)
                ? trim($jsonContact->email, " ")
                : '';

            $this->createExternalContacts(
                [
                    'business' => $jsonContact->business,
                    'name' => $jsonContact->name,
                    'mobile_number' => $jsonContact->mobile,
                    'company' => $jsonContact->company,
                    'email' => $trimEmail,
                ]
            );
        }

        return Response::json(
            [
                'msg' => 'external contacts has been imported',
                'response' => 200,
                'row_that_has_empty_fields' => $emptyFieldRow,
            ]
        );
    }

    public function getLibRepByName()
    {
        $name = $this->userRequest->get('name');
        $business = $this->userRequest->get('business');
        if ($business) {
            $this->business = $business;
        }

        $libertyRepresentatives = $this->fetchLibRepFromCacheOrCms();

        $matchLibRep = [];

        //getall librep
        foreach ($libertyRepresentatives as $key => $librep) {
            if (!$name) {
                $matchLibRep[] = array_merge($librep, ['id' => $key]);
                continue;
            }
            if (!isset($librep['name'])) {
                continue;
            }

            if (strpos(strtolower($librep['name']), strtolower($name)) !== false) {
                $matchLibRep[] = array_merge($librep, ['id' => $key]);
            }
        }


        return Response::json(
            [
                'data' => $matchLibRep,
                'status' => 200,
            ]
        );
    }

    public function exportProfiles()
    {
        $lsmLibReps = $this->fetchLibRepFromCacheOrCms();
        $this->business = 'lmre';
        $lmreLibReps = $this->fetchLibRepFromCacheOrCms();

        if (!empty($lsmLibReps) || !empty($lmreLibReps)) {
            return Response::json(
                [
                    'data' => [
                        'lsm_libreps' => $lsmLibReps,
                        'lmre_libreps' => $lmreLibReps,
                    ],
                    'response' => 200,
                ]
            );
        }

        return Response::json(
            [
                'data' => [
                    'lsm_libreps' => [],
                    'lmre_libreps' => [],
                ],
                'response' => 404,
            ]
        );
    }

    public function removeAllRecurringAvailability()
    {
        $person_id = $this->userRequest->get('person_id');
        $timezone = $this->userRequest->get('timezone', 'Europe/London');
        $from = $this->userRequest->get('from');
        $to = $this->userRequest->get('to');
        $now = Carbon::now()->format("Y-m-d 06:00:00");
        $now = Carbon::parse($now, $timezone)->setTimezone('Europe/London')->format('Y-m-d H:i:s');

        // Check all recurring schedule
        $recurring_schedule = ResourceSchedule::where('person_id', $person_id)
            ->where('from', '>=', $now)
            ->where('is_recurring', 1)
            ->get();

        $cannot_deleted_list = [];

        foreach ($recurring_schedule as $schedule) {

            // Mutate the time in the correct timezone perspective
            $fromTime = Carbon::parse($from)->format('H:i:s');
            $fromDate = Carbon::parse($schedule->from)->format('Y-m-d');
            $toTime = Carbon::parse($to)->format('H:i:s');
            $toDate = Carbon::parse($schedule->to)->format('Y-m-d');

            // Convert timezone to Europe/London, this will determine the time slot
            $fromConvertedToEurope = Carbon::parse(
                $fromDate . ' ' . $fromTime,
                $timezone
            )->setTimezone('Europe/London')->format('H:i:s');
            $toConvertedToEurope = Carbon::parse(
                $toDate . ' ' . $toTime,
                $timezone
            )->setTimezone('Europe/London')->format('H:i:s');

            // Check for any existing bookings
            $bookingsExists = ResourceBooking::where('person_id', $person_id)
                ->where("from", '>=', $schedule->from)
                ->where("to", '<=', $schedule->to)
                ->exists();

            // Check if this is the recurring schedule is within the correct time slot
            $has_slot = ResourceSchedule::where('person_id', $person_id)
                ->where('from', date('Y-m-d', strtotime($schedule->from)) . ' ' . $fromConvertedToEurope)
                ->where('to', date('Y-m-d', strtotime($schedule->to)) . ' ' . $toConvertedToEurope)
                ->where('is_recurring', 1)
                ->exists();

            if ($has_slot) {
                if (!$bookingsExists) {
                    ResourceSchedule::where('person_id', $person_id)
                        ->where('from', date('Y-m-d', strtotime($schedule->from)) . ' ' . $fromConvertedToEurope)
                        ->where('to', date('Y-m-d', strtotime($schedule->to)) . ' ' . $toConvertedToEurope)
                        ->where('is_recurring', 1)
                        ->forceDelete();
                } else { // Cannot be deleted then count it

                    $scheduledList = ResourceSchedule::where('id', $schedule->id)->first();

                    $cannot_deleted_list[] = [
                        'id' => $scheduledList->id,
                        'person_id' => $scheduledList->person_id,
                        'from' => Carbon::parse(
                            $scheduledList->from,
                            'Europe/London'
                        )->setTimezone($timezone)->format('Y-m-d H:i:s'),
                        'to' => Carbon::parse(
                            $scheduledList->to,
                            'Europe/London'
                        )->setTimezone($timezone)->format('Y-m-d H:i:s'),
                        'is_recurring' => $scheduledList->is_recurring,
                    ];

                    // Update recurring to non recurring status
                    ResourceSchedule::where('person_id', $person_id)
                        ->where('id', $schedule->id)
                        ->update(
                            [
                                'is_recurring' => 0,
                            ]
                        );
                }
            }
        }

        if (count($cannot_deleted_list) == 0) {
            return Response::json(
                [
                    'status' => 'success',
                    'message' => 'All recurring schedule has been deleted',
                ],
                200
            );
        } else {
            return Response::json(
                [
                    'status' => 'warning',
                    'message' => 'All recurring schedule has been deleted',
                    'response' => $cannot_deleted_list,
                ],
                200
            );
        }
    }

    public function staff($librepEmail)
    {
        // Search first from LSM staff
        $representative = $this->getLibRepByEmail($librepEmail);

        // Search LibRep from Lmre staff if not found in LSM staff
        if (!$representative) {
            $this->business = 'lmre';
            $representative = $this->getLibRepByEmail($librepEmail);
        }

        if (!$representative) {
            return Response::json(
                [
                    'response' => 'fail',
                    'message' => 'Representative not found.',
                ]
            );
        }

        $schedule = $this->getRepSchedules($representative['id'], $this->userRequest->get('date'), $returnAsResponse = false);
        if (isset($schedule[$representative['id']])) {
            $schedule = $schedule[$representative['id']];
        }

        $lineOfBusiness = [];
        if ($representative['business_function'] === 'Underwriting') {
            $lineOfBusiness = $this->getProductBySlug($representative['line_of_business_slug']);
        }

        $riskAppetiteProductUrl = config('app.riskappetite.url') . 'product/';

        $options = $this->getCmsOptions();

        $business = $this->business;

        return Response::json(
            [
                'response' => 'success',
                'data' => compact(
                    'business',
                    'riskAppetiteProductUrl',
                    'representative',
                    'schedule',
                    'lineOfBusiness',
                    'options'
                ),
            ]
        );
    }

    public function getRepSchedules($person_id, $date = null, $returnAsResponse = true)
    {
        if ($date == null) {
            $startOfDay = Carbon::now();
            $endOfDay = Carbon::now()->addDays(14)->endOfDay();
        } else {
            $startOfDay = Carbon::parse($date);
            $eod = clone $startOfDay;
            $endOfDay = $eod->addDays(14)->endOfDay();
        }

        if ($this->userRequest->get('end_of_day')) {
            $endOfDay = Carbon::parse($this->userRequest->get('end_of_day'))->endOfDay();
        }

        $schedules = ResourceSchedule::where('person_id', "=", $person_id)->where(
            function ($query) use (
                $startOfDay,
                $endOfDay
            ) {
                $query->whereBetween('from', [$startOfDay, $endOfDay])->orWhereBetween('to', [$startOfDay, $endOfDay]);
            }
        )->where('deleted_at', null)->orderBy('from', 'asc')->get();


        $bookings = ResourceBooking::where('person_id', "=", $person_id)->where(
            function ($query) use (
                $startOfDay,
                $endOfDay
            ) {
                $query->whereBetween('from', [$startOfDay, $endOfDay])->orWhereBetween('to', [$startOfDay, $endOfDay]);
            }
        )->where('deleted_at', null)->orderBy('from', 'asc')->get();

        $timezone = $this->userRequest->get('timezone', 'Europe/London');

        foreach ($schedules as $schedule) {
            $from = Carbon::parse($schedule->from, 'Europe/London');
            $from->setTimezone($timezone);
            $from = $from->format('Y-m-d H:i:s');

            $to = Carbon::parse($schedule->to, 'Europe/London');
            $to->setTimezone($timezone);
            $to = $to->format('Y-m-d H:i:s');

            $set = $from . " to " . $to;
            $reprsentativeSchedule[$schedule->person_id]['available'][] = $set;

            $isRecurringIdentifier = date("YmdHis", strtotime($from));

            $reprsentativeSchedule[$schedule->person_id]['is_recurring'][$schedule->person_id . $isRecurringIdentifier] = $schedule->is_recurring;
        }

        foreach ($bookings as $booking) {
            $from = Carbon::parse($booking->from, 'Europe/London');
            $from->setTimezone($timezone);
            $from = $from->format('Y-m-d H:i:s');

            $to = Carbon::parse($booking->to, 'Europe/London');
            $to->setTimezone($timezone);
            $to = $to->format('Y-m-d H:i:s');

            $set = $from . " to " . $to;
            $reprsentativeSchedule[$booking->person_id]['booked'][] = $set;
        }

        if (!isset($reprsentativeSchedule)) {
            $reprsentativeSchedule = [];
        }
        foreach ($reprsentativeSchedule as $person_id => $schedule) {
            $nextAvailability = $this->nextAvailability(
                isset($schedule["available"])
                    ? $schedule['available']
                    : [],
                isset($schedule["booked"])
                    ? $schedule['booked']
                    : [],
                $startOfDay,
                $endOfDay,
                60,
                15,
                $timezone
            );
            $reprsentativeSchedule[$person_id]['mergedAvailableSlots'] = $nextAvailability['mergedAvailableSlots'];
            usort(
                $nextAvailability['availableSlots'],
                function ($a, $b) {
                    if ($a['start'] < $b['start']) {
                        return -1;
                    } elseif ($a['start'] > $b['start']) {
                        return 1;
                    } else {
                        return 0;
                    }
                }
            );

            $reprsentativeSchedule[$person_id]['nextAvailability'] = $nextAvailability['availableSlots'];
        }

        if (!$returnAsResponse) {
            return $reprsentativeSchedule;
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => $reprsentativeSchedule,
            ]
        );
    }

    private function getProductBySlug($slugs)
    {
        $products = $this->getProductsFromCacheOrCms();

        if (!is_array($slugs)) {
            return [];
        }

        $foundProducts = [];
        foreach ($products as $key => $product) {
            if (!isset($product['slug'])) {
                continue;
            }
            if (in_array($product['slug'], $slugs)) {
                $foundProducts[] = $product;
            }
        }

        return $foundProducts;
    }

    private function getProductsFromCacheOrCms()
    {
        if (Cache::has('vr_productsCms') && !$this->userRequest->has('force_recache')) {
            $products = Cache::get('vr_productsCms');
        } else {
            $query = json_encode(
                [
                    'region_1564412684538' => [],
                    'subsector_1564412664986' => [],
                    'sector_1564412648774' => [],
                    'variation' => 'default',
                    'operator' => '=',
                    'locale_id' => 1,
                ]
            );

            $responseProducts = json_decode(Cms::get('workspaces/' . config('app.cms.workspace_id') . '/content-types/' . config('app.cms.products_content_type') . '/content-entries?joined=true&query=' . $query . '&page=1&limit=99999'));

            $products = [];
            foreach ($responseProducts->data as $product) {
                if (isset($product->name) && isset($product->slug)) {
                    $productImage = $product->{config('app.cms.product_image')};
                    $products[$product->slug] = [
                        '_id' => $product->_id,
                        'name' => $product->name,
                        'slug' => $product->slug,
                        'image' => isset($productImage[0]->url)
                            ? $productImage[0]->url
                            : '',
                    ];
                }
            }

            Cache::put('vr_productsCms', $products, 1440);
        }

        return $products;
    }

    public function getAvailableRepresentative($page = 1, $search = "", $excluded_person_id = "")
    {
        $time_now = Carbon::now()->format('Y-m-d H:i:s');
        $person_id = [];
        $availableRepresentative = [];

        // 1st Layer Check
        $person_id = $this->availableRepresentativeLayer1Check($time_now);

        $person_ids = array_unique($person_id);

        // 2nd or 3rd Layer Check
        $person_ids = $this->availableRepresentativeLayer2Check($time_now, $person_ids);

        $person_ids = array_unique($person_ids);

        // 4th Layer Check
        $person_ids = $this->availableRepresentativeLayer4Check($time_now, $person_ids);

        $person_ids = array_unique($person_ids);

        // Determine the final list of available representative
        $libertyRepresentativesLmre = CmsLibertyRepresentative::fetchFromCacheOrCms('lmre');
        $libertyRepresentativesLsm = CmsLibertyRepresentative::fetchFromCacheOrCms('lsm');
        $libertyRepresentatives = array_merge($libertyRepresentativesLmre, $libertyRepresentativesLsm);
        $is_whole_word_searched = false;
        foreach ($person_ids as $person_id) {
            if ($person_id != $excluded_person_id) {
                if (isset($search) && !empty($search) && $search != 'false') {

                    // Check for identical search
                    similar_text(
                        strtolower($search),
                        strtolower($libertyRepresentatives[$person_id]['name']),
                        $identity_search
                    );

                    if ($identity_search == 100) {
                        $whole_word = $libertyRepresentatives[$person_id];
                        $whole_word_key = str_replace(" ", "", $libertyRepresentatives[$person_id]['name']);
                        $is_whole_word_searched = true;
                    } else {
                        $search_terms = explode(" ", $libertyRepresentatives[$person_id]['name']);

                        foreach ($search_terms as $search_term) {
                            similar_text($search, $search_term, $percentage);


                            if (
                                $percentage > 60 || (strtolower($search) == strtolower(
                                    substr(
                                        $search_term,
                                        0,
                                        strlen($search)
                                    )
                                ))
                            ) {
                                $availableRepresentative[str_replace(
                                    " ",
                                    "",
                                    $libertyRepresentatives[$person_id]['name']
                                )] = $libertyRepresentatives[$person_id];
                            }
                        }
                    }
                } else {
                    $availableRepresentative[str_replace(
                        " ",
                        "",
                        $libertyRepresentatives[$person_id]['name']
                    )] = $libertyRepresentatives[$person_id];
                }
            }
        }

        if ($is_whole_word_searched === true) {
            unset($availableRepresentative);
            $availableRepresentative[$whole_word_key] = $whole_word;
        }

        ksort($availableRepresentative);
        array_filter($availableRepresentative);

        $updatedRepresentative = $this->paginate($page, $availableRepresentative);

        return Response::json(
            [
                'status' => 'success',
                'response' => [
                    'representatives' => $updatedRepresentative,
                    'total_page' => ceil(count($availableRepresentative) / 18),
                    'total_count' => count($availableRepresentative),
                    'page' => $page,
                ],
                'message' => null,
            ]
        );
    }

    private function availableRepresentativeLayer1Check($time_now)
    {
        // Layer 1 check
        $avalable_slots = ResourceSchedule::where('from', '<=', $time_now)
            ->where('to', '>=', $time_now)
            ->get();

        // Collect all id's for Layer 1
        foreach ($avalable_slots as $available_slot) {
            $person_id[] = $available_slot->person_id;
        }

        return $person_id;
    }

    // process pagination for available representative

    private function availableRepresentativeLayer2Check($time_now, $person_ids)
    {
        $call_now_person_ids = $person_ids;
        // Layer 2 check: Booking
        $bookings = ResourceBooking::where('from', '<=', $time_now)
            ->where('to', '>=', $time_now)
            ->whereIn('person_id', $person_ids)
            ->where('is_call_now', 0)
            ->get();

        // Layer 3 check: Call now
        $now_calls = ResourceBooking::whereIn('person_id', $person_ids)
            ->where('is_call_now', 1)
            ->where('is_in_room', 1)
            ->get();

        // Collect all id's and remove person_id from the list
        foreach ($now_calls as $now_call) {
            if (($key = array_search($now_call->person_id, $call_now_person_ids)) !== false) {
                unset($person_ids[$key]);
            }
        }

        foreach ($bookings as $booking) {
            if (($key = array_search($booking->person_id, $person_ids)) !== false) {
                unset($person_ids[$key]);
            }
        }


        return $person_ids;
    }

    // Availability Checks

    private function availableRepresentativeLayer4Check($time_now, $person_ids)
    {
        $social_room_participants = SocialRoomParticipant::whereIn('person_id', $person_ids)
            ->get();

        foreach ($social_room_participants as $social_room_participant) {
            // intialize
            $joined_room_at = $social_room_participant->joined_room_at;
            $left_room_at = $social_room_participant->left_room_at;
            $person_id = $social_room_participant->person_id;

            // // if the user is in the room
            if ((isset($joined_room_at) && is_null($left_room_at))
                || (isset($joined_room_at) && isset($left_room_at) && (strtotime($joined_room_at) > strtotime($left_room_at)))
            ) {
                if (($key = array_search($person_id, $person_ids)) !== false) {
                    unset($person_ids[$key]);
                }
            }
        }

        return $person_ids;
    }

    // Booking and Call now Checks

    private function paginate($page, $data)
    {
        $per_page = 18;
        $limit = $per_page * $page;
        $offset = $limit - ($per_page);
        $updated_data = array_slice(
            $data,
            ($offset == 0)
                ? 0
                : $offset + 1,
            $per_page,
            true
        ); // true retained key
        return $updated_data;
    }

    // Check

    private function titleCase($value)
    {
        $str = $value;
        $result = "";

        $arr = [];
        $pattern = '/([;:,-.\/ X])/';
        $array = preg_split($pattern, $str, -1, PREG_SPLIT_DELIM_CAPTURE | PREG_SPLIT_NO_EMPTY);

        foreach ($array as $k => $v) {
            // Exception
            if ($v != 'and') {
                $result .= ucwords(strtolower($v));
            } else {
                $result .= $v;
            }
        }


        return $result;
    }

    private function getSlug(string $string): string
    {
        $delimiter = '_';
        $slug = strtolower(trim(preg_replace('/[\s-]+/', $delimiter, preg_replace('/[^A-Za-z0-9-]+/', $delimiter, preg_replace('/[&]/', 'and', preg_replace('/[\']/', '', iconv('UTF-8', 'ASCII//TRANSLIT', $string))))), $delimiter));
        return $slug;
    }
}
