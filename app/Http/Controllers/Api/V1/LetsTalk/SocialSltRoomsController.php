<?php


namespace App\Http\Controllers\Api\V1\LetsTalk;

use Carbon\Carbon;
use App\Models\Cms;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\LetsTalk\SocialRoom;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use App\Models\LetsTalk\UpcomingSchedule;
use Illuminate\Support\Facades\Validator;
use App\Models\LetsTalk\SocialRoomSchedule;
use Illuminate\Database\Eloquent\Collection;
use App\Models\LetsTalk\SocialRoomParticipant;
use App\Models\LetsTalk\CmsLibertyRepresentative;
use App\Models\LetsTalk\SocialRoomScheduleUpdate;
use App\Models\LetsTalk\SocialRoomSmsNotification;
use App\Models\LetsTalk\SocialRoomCancelledSchedule;
use App\Models\LetsTalk\SocialRoomEmailNotification;


class SocialSltRoomsController extends BaseController
{
    const SLT_ROOM_TYPE_ID = 6;
    private $business;

    public function __construct(Request $request)
    {
        $this->business = ($request->has('business') && $request->get('business') === 'lmre')
            ? 'lmre'
            : 'lsm';

        $this->libReps = CmsLibertyRepresentative::fetchAllFromCacheOrCms();

        $personId = $request->get('person_id');
        $this->authUserCms = isset($this->libReps[$personId])
            ? $this->libReps[$personId]
            : [];
        $this->authUserTz = isset($this->authUserCms['office_timezone'])
            ? $this->authUserCms['office_timezone']
            : 'Europe/London';
    }

    public function index(Request $request)
    {
        $errorResponseData = [
            'status' => 'fail',
            'data' => [],
            'is_slt_space_empty' => true,
            'is_auth_user_slt' => false,
        ];
        if (!isset($this->authUserCms['person_id'])) {
            return Response::json($errorResponseData);
        }

        if ($request->has('check') && $request->get('check') == 'auth') {
            return Response::json(['status' => 'success']);
        }

        $social_room = SocialRoom::where('lt_social_room_type_id', self::SLT_ROOM_TYPE_ID)
            ->where('status', 'approved')
            ->where('is_community', 0)
            ->with('socialRoomParticipants')
            ->with('socialRoomScheduleWithBuffer')
            ->with('deletedScheduleEntries')
            ->with('modifiedScheduleEntries')
            ->whereHas(
                'socialRoomScheduleWithBuffer',
                function ($query) {
                    $query->where('valid_until', '>=', Carbon::now()->subMinutes(2));
                }
            )
            ->get()
            ->sortBy('socialRoomSchedule.valid_until');

        $isSltSpaceEmpty = $social_room->isEmpty();

        $social_room = SocialRoom::setCmsData($social_room, false, 'socialRoomParticipants');

        $leaders = [];

        $leaders_nosched = [];
        $leaders_sched = [];

        $i = 0;

        foreach ($social_room as $room) {

            $room->socialRoomSchedule = $room->socialRoomScheduleWithBuffer;


            if (!empty($room->socialRoomSchedule)) {

                $currSched = Carbon::parse(
                    $room->socialRoomSchedule->start_date,
                    'Europe/London'
                )->timezone($this->authUserTz);
                $freq_for_ui = UpcomingSchedule::getFrequencyForUi($room->socialRoomSchedule, $this->authUserTz);

                $schedForUi = $freq_for_ui . ' ' . $currSched->copy()->format('@ G:ia');
                $room->socialRoomSchedule->frequency_for_ui = $schedForUi;
                $now = Carbon::now()->timezone($this->authUserTz);

                $startDate = $currSched->format('Y-m-d');
                $endTime = Carbon::parse(
                    $room->socialRoomSchedule->end_date,
                    $this->authUserTz
                )->format('H:i:s');
                $validUntil = Carbon::parse($startDate . ' ' . $endTime)->setTimezone($this->authUserTz)->addMinutes(30);
                $isRoomReady = (((int)$currSched->diffInMinutes($now, false) >= -5) && $validUntil->gt($now))
                    ? true
                    : false;

                $room->socialRoomSchedule->is_room_ready = $isRoomReady;

                $meetingSpan = Carbon::parse($room->socialRoomSchedule->start_date)->diffForHumans(
                    Carbon::parse($room->socialRoomSchedule->end_date),
                    true
                );
            }


            if (isset($room->socialRoomSchedule) && !empty($room->socialRoomSchedule) && $room->socialRoomSchedule->duration_type != 'permanent') {
                $slt_room = new Collection([$room]);

                $tmpData = UpcomingSchedule::generate($slt_room, ['office_timezone' => 'Europe/London'], 2, 1);

                if (isset($tmpData['upcomingSchedules'][0]) && !empty($tmpData['upcomingSchedules'][0])) {
                    $leaders_sched[$i] = $tmpData['upcomingSchedules'][0];
                    $leaders_sched[$i]['nextsched'] = $leaders_sched[$i]['schedule']->toDateTimeString();
                    //$leaders[$i]['next']= \Carbon\Carbon::parse($leaders[$i]['schedule']->toDateTimeString(), $leaders[$i]['schedule']->timezoneName)->timezone($this->authUserTz);
                    $leaders_sched[$i]['ready'] = $isRoomReady;
                    $leaders_sched[$i] = array_merge($leaders_sched[$i], $room->cms);
                    $leaders_sched[$i]['room'] = $room->room_code;
                    $leaders_sched[$i]['joinRoomLink'] = SocialRoomParticipant::generateWaitingRoomCommonLink($room->room_code);
                    $leaders_sched[$i]['room_id'] = $room->id;
                    $leaders_sched[$i]['slt_subject'] = $room->name;
                    $leaders_sched[$i]['meeting_span'] = $meetingSpan;
                }
            }


            $i++;
        }


        $leaders_sched = array_values(
            Arr::sort(
                $leaders_sched,
                function ($value) {
                    return $value['nextsched'];
                }
            )
        );
        $leaders = array_merge($leaders_sched);

        //    $leaders=array_reverse(array_values(array_column(array_reverse($leaders),null,'person_id')));


        $all_leaders = $this->getCmsSltModerators();

        foreach ($all_leaders as $leader) {
            if (!in_array($leader['person_id'], array_column($leaders, 'person_id'))) {
                $leaders[] = $leader;
            }
        }


        return Response::json(
            [
                'auth_users_slt' => $this->getSltModeratorsId(),
                'status' => 'success',
                'data' => $leaders,
                'is_slt_space_empty' => $isSltSpaceEmpty,
            ]
        );
    }

    public function getCmsSltModerators()
    {
        $cms = CmsLibertyRepresentative::fetchAllFromCacheOrCms();
        $get_moderators = json_decode(Cms::get('workspaces/' . $this->getCmsConfig('lets_talk_workspace') . '/content-types/' . $this->getCmsConfig('slt_room_moderators_content_type') . '/content-entries/' . $this->getCmsConfig('slt_room_moderators_content_entry')));
        $moderator_details = [];
        if ($get_moderators) { //get moderator details
            $moderators_id = $get_moderators->{$this->getCmsConfig('slt_room_moderators_selected_representative')};
            $ctr = 0;
            foreach ($moderators_id as $key => $value) {
                foreach ($value as $id) {
                    if (!isset($cms[$id])) {
                        continue;
                    }
                    $moderator_details[] = $cms[$id];
                    $moderator_details[$ctr]['id'] = $id;
                    $ctr++;
                }
            }
        }

        return $moderator_details;
    }

    private function getCmsConfig($config)
    {
        return config('app.cms.' . 'lsm' . '.' . $config);
    }

    private function getSltModeratorsId()
    {
        $slts = $this->getCmsSltModerators();

        if (empty($slts)) {
            return [];
        }

        return Arr::pluck($slts, 'person_id');
    }

    public function show(Request $request, $roomCode)
    {
        $upcomingSchedules = [];
        $personId = $request->get('person_id');
        $userCode = $request->get('user_code');

        // Set person id based on user code
        if (!empty($userCode) && empty($personId)) {
            $participant = SocialRoomParticipant::where('user_code', $userCode)->first();
            $personId = $participant->person_id;
        }

        $socialRooms = SocialRoom::with('socialRoomSchedule')
            ->with(['socialRoomParticipants'])
            ->with('deletedScheduleEntries')
            ->with('modifiedScheduleEntries')
            ->where('room_code', $roomCode)->first();

        if (!empty($socialRooms)) {
            $socialRooms = SocialRoom::setCmsData(
                new Collection([$socialRooms]),
                false,
                'socialRoomParticipants'
            )->first();

            if (isset($socialRooms->socialRoomSchedule) && !empty($socialRooms->socialRoomSchedule)) {
                $currSched = Carbon::parse(
                    $socialRooms->socialRoomSchedule->start_date,
                    'Europe/London'
                )->timezone($this->authUserTz);
                $freq_for_ui = UpcomingSchedule::getFrequencyForUi($socialRooms->socialRoomSchedule, $this->authUserTz);

                // include the word Today if date is today
                $today = ($currSched->isToday())
                    ? " Today "
                    : " ";
                $schedForUi = $freq_for_ui . $today . $currSched->copy()->format('@ G:ia');
                $socialRooms->socialRoomSchedule->frequency_for_ui = $schedForUi;
                $now = Carbon::now()->timezone($this->authUserTz);
                $startDate = $currSched->format('Y-m-d');
                $endTime = Carbon::parse(
                    $socialRooms->socialRoomSchedule->end_date,
                    $this->authUserTz
                )->format('H:i:s');
                $validUntil = Carbon::parse($startDate . ' ' . $endTime)->setTimezone($this->authUserTz)->addMinutes(30);
                $isRoomReady = (((int)$currSched->diffInMinutes($now, false) >= -5) && $validUntil->gt($now))
                    ? true
                    : false;
                $socialRooms->socialRoomSchedule->is_room_ready = $isRoomReady;
            }


            if (isset($socialRooms->socialRoomSchedule) && !empty($socialRooms->socialRoomSchedule) && $socialRooms->socialRoomSchedule->duration_type != 'permanent') {
                $social_room = new Collection([$socialRooms]);

                $tmpData = UpcomingSchedule::generateWithBuffer($social_room, $this->authUserCms);

                $upcomingSchedules = $tmpData['upcomingSchedules'];
            }

            $social_rooms = isset($tmpData['rooms'][0]) && !empty($tmpData['rooms'][0])
                ? $tmpData['rooms'][0]
                : $socialRooms;


            $joinRoomLink = '';
            if ($socialRooms->first()) {
                $participant = SocialRoomParticipant::where('lt_social_room_id', $socialRooms->first()->id)
                    ->where('person_id', $personId)->first();
                if (!(empty($participant))) //$joinRoomLink = SocialRoomParticipant::generateLink($participant->user_code);
                {
                    $joinRoomLink = SocialRoomParticipant::generateWaitingRoomCommonLink($roomCode);
                }
            }

            return Response::json(
                [
                    'response' => 'success',
                    'data' => [
                        'room' => $social_rooms,
                        'upcoming_schedules' => $upcomingSchedules,
                        'join_room_link' => $joinRoomLink,
                    ],
                ]
            );
        }


        return Response::json(
            [
                'response' => 'error',
                'message' => 'Social room not found!',
            ]
        );
    }

    public function store(Request $request)
    {
        $data = $request->all();
        Log::info($data);

        //get moderators
        $creatorCms = '';
        $cms = $this->libReps;

        if (!isset($cms[$data['created_by_id']])) {
            return Response::json(
                [
                    'status' => 'failed',
                    'message' => 'Creator CMS data not found.',
                ]
            );
        }

        if (!$this->isSltModerator($data['created_by_id'])) {
            return Response::json(
                [
                    'status' => 'failed',
                    'message' => 'You are not allowed to create an SLT room.',
                ]
            );
        }

        $creatorCms = $cms[$data['created_by_id']];
        $creatorCms['id'] = $data['created_by_id'];
        // $social_room_exist = SocialRoom::where('name', $data['name'])->where('lt_social_room_type_id', self::SLT_ROOM_TYPE_ID)->first();

        // if(!empty($social_room_exist)){
        //     return \Response::json([
        //         'status' => 'failed',
        //         'message' => 'Room already exist'
        //     ]);
        // }

        $data['end_after'] = $data['end_after_frequency'] . ' ' . $data['end_after_frequency_type'];

        // Convert start_date & end_date to London tz from creator tz
        $data['start_date'] = Carbon::parse(
            $data['start_date'],
            $creatorCms['office_timezone']
        )->setTimezone("Europe/London")->format('Y-m-d H:i:s');
        $data['end_date'] = Carbon::parse(
            $data['end_date'],
            $creatorCms['office_timezone']
        )->setTimezone("Europe/London")->format('Y-m-d H:i:s');

        $data['valid_until'] = SocialRoomSchedule::getValidUntil($data);
        // $data['room_code'] = md5($data['name']);

        $room_code = SocialRoom::roomCodeGenerator();

        $social_room = SocialRoom::create(
            [
                'lt_social_room_type_id' => self::SLT_ROOM_TYPE_ID,
                'name' => $data['name'],
                'description' => "",
                'status' => SocialRoom::STATUS_APPROVED,
                'room_code' => $room_code,
                'created_by_id' => $data['created_by_id'],
            ]
        );

        $socialRoomSchedule = SocialRoomSchedule::create($data);

        SocialRoomScheduleUpdate::create(
            [
                'room_id' => $social_room->id,
                'room_type' => self::SLT_ROOM_TYPE_ID,
                'room_code' => $room_code,
            ]
        );

        $social_room->socialRoomSchedule()->save($socialRoomSchedule);

        $participant['business'] = $creatorCms['business'];
        $participant['person_id'] = $creatorCms['person_id'];
        $participant['user_name'] = $creatorCms['user_name'];
        $participant['role'] = 'virtual-rooms';
        $participant['user_code'] = md5(Hash::make(substr(Str::uuid()->toString(), 0, 4)));
        $socialRoomParticipant = SocialRoomParticipant::create($participant);
        $social_room->socialRoomParticipants()->save($socialRoomParticipant);

        if ($social_room) {
            (new SocialRoomSmsNotification())->sendSltSmsTemplate1($social_room, [$creatorCms], $socialRoomSchedule);

            SocialRoomEmailNotification::sltRoomTemplate6(
                $social_room,
                [$creatorCms],
                $creatorCms['name'],
                $data['duration_type'],
                $socialRoomSchedule
            );

            return Response::json(
                [
                    'status' => 'success',
                    'data' => $social_room,
                ]
            );
        }

        return Response::json(
            [
                'status' => 'failed',
            ]
        );
    }

    private function isSltModerator($personId)
    {
        if (!in_array($personId, $this->getSltModeratorsId())) {
            return false;
        }
        return true;
    }


    // validate store method

    public function createSchedule(Request $request, $roomId)
    {
        $data = $request->all();

        $libReps = $this->libReps;

        $errorResponse = $this->validateCreateSchedule($data, $libReps);
        if ($errorResponse instanceof JsonResponse) {
            return $errorResponse;
        }

        $libRepCreator = $this->authUserCms;
        $libRepCreator['id'] = $libRepCreator['person_id'];


        // Convert start_date & end_date to London tz from creator tz
        $data['start_date'] = Carbon::parse(
            $data['start_date'],
            $libRepCreator['office_timezone']
        )->setTimezone("Europe/London")->format('Y-m-d H:i:s');
        $data['end_date'] = Carbon::parse(
            $data['end_date'],
            $libRepCreator['office_timezone']
        )->setTimezone("Europe/London")->format('Y-m-d H:i:s');
        $data['end_after'] = $data['end_after_frequency'] . ' ' . $data['end_after_frequency_type'];

        //field name
        $data['valid_until'] = SocialRoomSchedule::getValidUntil($data);

        $socialRoomSchedule = SocialRoomSchedule::create($data);

        $socialRoom = SocialRoom::with(['socialRoomParticipants'])->find($roomId);

        SocialRoomScheduleUpdate::create(
            [
                'room_id' => $roomId,
                'room_type' => $socialRoom->lt_social_room_type_id,
                'room_code' => $socialRoom->room_code,
            ]
        );

        $socialRoom = SocialRoom::setCmsData(new Collection([$socialRoom]), false, 'socialRoomParticipants')->first();
        $socialRoom->socialRoomSchedule()->save($socialRoomSchedule);

        $roomCode = $socialRoom->room_code;

        $socialRooms = SocialRoom::with('socialRoomSchedule')
            ->with(['socialRoomParticipants'])
            ->with('deletedScheduleEntries')
            ->where('room_code', $roomCode)
            ->get();


        // if (isset($socialRooms->first()->socialRoomSchedule) && $socialRooms->first()->socialRoomSchedule) {
        //     $socialRooms = SocialRoom::setCmsData($socialRooms, false, 'socialRoomParticipants');
        //     $tmpData = UpcomingSchedule::generateWithBuffer($socialRooms, $this->authUserCms);
        //     $upcomingSchedules = $tmpData['upcomingSchedules'];
        // } else {
        //     $upcomingSchedules = [];
        // }
        (new SocialRoomSmsNotification())->sendSltSmsTemplate1($socialRoom, [$libRepCreator], $socialRoomSchedule);

        SocialRoomEmailNotification::sltRoomTemplate6(
            $socialRoom,
            [$libRepCreator],
            $libRepCreator['name'],
            $data['duration_type'],
            $socialRoomSchedule
        );

        return Response::json(
            [
                'response' => 'success',
                'data' => [
                    'room' => $socialRoom,
                    'schedule' => $socialRoomSchedule,
                ],
                'message' => 'Schedule has been successfully created.',
            ]
        );
    }

    private function validateCreateSchedule($data, $libReps)
    {
        $rules = [
            'lt_social_room_id' => 'required|exists:lt_social_rooms,id',
            'person_id' => 'required',
            'duration_type' => 'required',
            'frequency' => 'required|integer',
            'frequency_type' => 'required',
            'start_date' => 'required',
            'end_date' => 'required',
        ];
        $messages = [];

        $validator = Validator::make($data, $rules, $messages);

        if ($validator->fails()) {
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        }

        if ($data['person_id'] && !isset($libReps[$data['person_id']])) {
            $validator->errors()->add('person_id', 'Person id does not exists.');
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        }

        if (isset($errorResponse)) {
            return Response::json($errorResponse);
        }

        return [];
    }

    public function deleteSchedule(Request $request)
    {
        $socialRoomId = $request->get('lt_social_room_id');
        $scheduleId = $request->get('schedule_id');
        $scheduleDate = $request->get('date');

        $socialRoom = $socialRoom = SocialRoom::with(
            [
                'socialRoomParticipants',
                'socialRoomSchedule',
            ]
        )->find($socialRoomId);
        if (!$socialRoom) {
            return Response::json(['response' => 'error', 'message' => 'Unable to delete. Space not found.'], 400);
        }

        // If schedule date not provided, we will delete the whole schedule
        if (empty($scheduleDate)) {
            $socialRoomSchedule = SocialRoomSchedule::find($scheduleId);
            if (!$socialRoomSchedule) {
                return Response::json(
                    ['response' => 'error', 'message' => 'Unable to delete. Schedule not found.'],
                    400
                );
            }
            SocialRoomEmailNotification::sltTemplate3($socialRoom->toArray(), $socialRoom->cms);

            $socialRoomSchedule->delete();

            $newRoomCode = SocialRoom::roomCodeGenerator();

            $socialRoom->room_code = $newRoomCode;

            $updateRoomCode = SocialRoom::where('id', '=', $socialRoomId)->update(['room_code' => $newRoomCode]);

            $deleteDeletedSchedules = SocialRoomCancelledSchedule::where(
                'lt_social_room_id',
                '=',
                $socialRoomId
            )->delete();

            return Response::json(
                [
                    'response' => 'success',
                    'message' => 'Space schedule deleted.',
                    'room' => $socialRoom,
                ]
            );
        }

        $scheduleDateCarbon = Carbon::parse($scheduleDate, $this->authUserTz)->setTimezone("Europe/London");
        $socialRoomCancelledSchedule = SocialRoomCancelledSchedule::create(
            [
                'lt_social_room_id' => $socialRoomId,
                'lt_social_room_schedule_id' => $scheduleId,
                'date' => $scheduleDateCarbon,
            ]
        );

        SocialRoomEmailNotification::sltTemplate3($socialRoom->toArray(), $socialRoom->cms, $scheduleDateCarbon);

        return Response::json(['response' => 'success', 'message' => 'Space schedule deleted.', 'room' => $socialRoom]);
    }

    // validate store method

    public function deleteSpace(Request $request, $socialRoomId)
    {
        $person_id = $request->get('person_id');
        $socialRoom = SocialRoom::with(
            [
                'socialRoomParticipants',
                'socialRoomSchedule',
                'socialRoomPins',
            ]
        )->find($socialRoomId);

        if (!$socialRoom) {
            return Response::json(['response' => 'error', 'message' => 'Unable to delete. Space not found.']);
        }

        if ($socialRoom->is_community) {
            return Response::json(['response' => 'error', 'message' => 'Unable to delete community.']);
        }
        if ($socialRoom->created_by_id != $person_id) {
            return Response::json(['response' => 'error', 'message' => 'Unable to delete! Not allowed.']);
        }

        // Only send notification if has upcoming schedule
        if (isset($socialRoom->socialRoomSchedule) && !empty($socialRoom->socialRoomSchedule)) {
            SocialRoomEmailNotification::sltTemplate3($socialRoom->toArray(), $socialRoom->cms);
        }

        $socialRoom->delete();

        return Response::json(['response' => 'success', 'message' => 'Space deleted.']);
    }

    function getAllSltSpaceByCreator()
    {
        $socialRooms = SocialRoom::selectRaw('created_by_id')->where('lt_social_room_type_id', self::SLT_ROOM_TYPE_ID)
            ->distinct()
            ->where('status', 'approved')
            ->where('is_community', 0)
            ->get();

        return Response::json(
            [
                'response' => 'success',
                'data' => $socialRooms,
            ]
        );
    }

    private function validateStore($data, $libReps)
    {
        $validator = $this->_validate($data, 'store');

        if ($validator->fails()) {
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        }

        if ($data['created_by_id'] && !isset($libReps[$data['created_by_id']])) {
            $validator->errors()->add('created_by_id', 'Created by id does not exists.');
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        }


        if (isset($errorResponse)) {
            return Response::json($errorResponse);
        }

        return [];
    }

    private function _validate($data, $action)
    {
        $rules = [
            // 'room_code' => 'required',
            'name' => 'required',
            'duration_type' => 'required',
            'start_date' => 'required_if:duration_type,on-a-schedule|required_if:duration_type,one-off',
            'start_time' => 'required_if:duration_type,on-a-schedule|required_if:duration_type,one-off',
            'end_time' => 'required_if:duration_type,on-a-schedule|required_if:duration_type,one-off',
            'frequency' => 'required_if:duration_type,on-a-schedule',
            'end_after' => 'required_if:duration_type,on-a-schedule',
        ];

        // // On update, we don't want to validate email, cause it cannot be updated
        // if($action === 'update') {
        //     unset($rules['email']);
        // }

        // TODO: Add appropriate error messages here
        // TODO: Add appropriate error messages here
        // TODO: Add appropriate error messages here
        $messages = [
            // 'name.required' => 'Please enter Name.',
            // 'email.required' => 'Please enter Email.',
            // 'mobile_number.required' => 'Please enter Mobile Phone in a valid format.',
            // 'mobile_number.regex' => 'Please enter Mobile Phone in a valid format.',
        ];

        return Validator::make($data, $rules, $messages);
    }
}
