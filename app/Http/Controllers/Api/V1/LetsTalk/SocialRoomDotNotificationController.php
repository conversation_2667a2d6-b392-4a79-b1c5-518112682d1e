<?php

namespace App\Http\Controllers\Api\V1\LetsTalk;

use Illuminate\Http\Request;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use App\Models\LetsTalk\SocialRoomRedDotNotification;

class SocialRoomDotNotificationController extends BaseController
{
    public function __construct()
    {
    }

    public function getDotNotification(Request $request)
    {
        $data = $request->all();

        $red_dot_notifications = SocialRoomRedDotNotification::where('person_id', $data['person_id'])
            ->whereIn(
                'is_visited',
                [
                    SocialRoomRedDotNotification::IS_VISIT_STATUS_NEW_ROOM,
                    SocialRoomRedDotNotification::IS_VISIT_STATUS_NEW_MESSAGE,
                ]
            )
            ->get();

        if (count($red_dot_notifications) > 0) {
            return Response::json(
                [
                    'status' => 'success',
                    'response' => [
                        'red_dot_notifications' => $red_dot_notifications,
                    ],
                    'message' => null,
                ]
            );
        }

        return Response::json(
            [
                'status' => null,
                'response' => [
                    'red_dot_notifications' => null,
                ],
                'message' => 'nothing to return',
            ]
        );
    }

    public function dismissThemeRoomNotification(Request $request)
    {
        $data = $request->all();
        SocialRoomRedDotNotification::where('person_id', $data['person_id'])
            ->where('room_type_id', 3)
            ->where('is_community', $data['is_community'])
            ->update(['is_visited' => 1]);

        return Response::json(
            [
                'status' => null,
                'response' => [
                    'red_dot_notifications' => null,
                ],
                'message' => 'viewed all theme rooms',
            ]
        );
    }

    public function dismissThemeRoomNotificationMessage(Request $request)
    {
        $data = $request->all();

        if (isset($data['person_id']) && !empty($data['person_id'])) {
            $person_id = $data['person_id'];
            $room_id = $data['room_id'];

            SocialRoomRedDotNotification::where('person_id', $person_id)
                ->where('room_id', $room_id)
                ->update(
                    [
                        'is_visited' => SocialRoomRedDotNotification::IS_VISIT_STATUS_NEW_MESSAGE_VISITED,
                    ]
                );
        }

        return Response::json(
            [
                'status' => null,
                'response' => [
                    'red_dot_notifications' => null,
                ],
                'message' => 'viewed message, notification has been dismissed',
            ]
        );
    }

    // TODO: add this API when individual view is ready
    // public function updateVisitedRedDotNotification()
    // {
    //     $data = \$request->all();

    //     // Intitialize
    //     $person_id = $data['person_id'];
    //     $room_id = $data['room_id'];

    //     SocialRoomRedDotNotification::where('person_id', $person_id)
    //         ->where('room_id', $room_id)
    //         ->update([
    //             'is_visited' => 1
    //         ]);
    // }
}
