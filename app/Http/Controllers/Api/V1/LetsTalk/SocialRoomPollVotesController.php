<?php

namespace App\Http\Controllers\Api\V1\LetsTalk;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\LetsTalk\SocialRoomPoll;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use App\Models\LetsTalk\SocialRoomPollVote;

class SocialRoomPollVotesController extends BaseController
{
    public function store(Request $request, $roomCode, $pollId)
    {
        $data = $request->only(['vote', 'person_id']);
        $validator = Validator::make(
            $data,
            [
                'vote' => 'required',
                'person_id' => 'required',
            ]
        );

        if ($validator->fails()) {
            return Response::json(
                [
                    'code' => 422,
                    'errors' => $validator->messages(),
                ],
                422
            );
        }

        // Find poll
        $poll = SocialRoomPoll::where('id', $pollId)
            ->whereHas(
                'room',
                function ($query) use ($roomCode) {
                    $query->where('room_code', $roomCode);
                }
            )
            ->with('votes')
            ->first();

        // Room not found. Wrong room code given
        if (empty($poll)) {
            return Response::json(
                [
                    'code' => 404,
                    'message' => 'Poll not found.',
                ],
                404
            );
        }

        // Create poll vote
        $currentPollCount = count($poll->votes);

        $vote = new SocialRoomPollVote(
            [
                'lt_social_room_poll_option_id' => $data['vote'],
                'created_by_person_id' => $data['person_id'],
            ]
        );
        $poll->votes()->save($vote);

        if ($currentPollCount == 0) {
            $poll->update(
                [
                    'first_voted_at' => Carbon::now(),
                ]
            );
        }

        return Response::json(
            [
                'code' => 200,
                'vote' => $vote,
                'currentPollCount' => $currentPollCount,
            ],
            200
        );
    }
}
