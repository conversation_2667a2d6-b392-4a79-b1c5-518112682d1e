<?php


namespace App\Http\Controllers\Api\V1\LetsTalk;

use Carbon\Carbon;
use App\Models\Cms;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\LetsTalk\SocialRoom;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use App\Models\LetsTalk\SocialRoomPin;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Response;
use App\Models\LetsTalk\UpcomingSchedule;
use Illuminate\Support\Facades\Validator;
use App\Models\LetsTalk\SocialRoomSchedule;
use Illuminate\Database\Eloquent\Collection;
use App\Models\LetsTalk\SocialRoomParticipant;
use App\Models\LetsTalk\CmsLibertyRepresentative;
use App\Models\LetsTalk\SocialRoomScheduleUpdate;
use App\Models\LetsTalk\SocialRoomCancelledSchedule;

class SocialThemeRoomsController extends BaseController
{
    const ROOM_KEYWORD = 'Cyber Virtual Room';

    private $business;

    protected $authUserTz;

    protected $libReps;

    protected $authUserCms;

    public function __construct(Request $request)
    {
        $this->business = ($request->has('business') && $request->get('business') === 'lmre')
            ? 'lmre'
            : 'lsm';

        $lsmLibReps = CmsLibertyRepresentative::fetchFromCacheOrCms();
        $lmreLibReps = CmsLibertyRepresentative::fetchFromCacheOrCms('lmre');

        $this->libReps = array_merge($lmreLibReps, $lsmLibReps);

        $personId = $request->get('person_id');
        $this->authUserCms = isset($this->libReps[$personId])
            ? $this->libReps[$personId]
            : [];
        $this->authUserTz = isset($this->authUserCms['office_timezone'])
            ? $this->authUserCms['office_timezone']
            : 'Europe/London';
    }

    public function index(Request $request)
    {
        $social_room = SocialRoom::where('lt_social_room_type_id', 3)
            ->where('status', 'approved')
            ->where('is_community', 0)
            ->with('socialRoomSchedule')
            ->with('deletedScheduleEntries')
            ->with('modifiedScheduleEntries');

        if ($request->get('can_access_cyber_vr')) {
            $social_room->where('description', 'like', '%' . self::ROOM_KEYWORD . '%')->with('allRoomParticipants');
        } else {
            $social_room->where('description', 'not like', '%' . self::ROOM_KEYWORD . '%');
            $social_room->with(['socialRoomParticipants' => function($query){
                    $query->whereRaw('lt_social_room_participants.person_id in (select person_id from lt_social_room_pins where lt_social_room_pins.lt_social_room_id = lt_social_room_participants.lt_social_room_id and lt_social_room_pins.deleted_at is null)');
                }
            ]);
        }

        $social_room = $social_room->get();
        $social_room = SocialRoom::setCmsData($social_room, false, 'socialRoomParticipants');

        foreach ($social_room as $schedule) {

            if (!empty($schedule->socialRoomSchedule)) {
                $currSched = Carbon::parse(
                    $schedule->socialRoomSchedule->start_date,
                    'Europe/London'
                )->timezone($this->authUserTz);
                $freq_for_ui = UpcomingSchedule::getFrequencyForUi($schedule->socialRoomSchedule, $this->authUserTz);
                $schedForUi = $freq_for_ui . ' ' . Carbon::parse($currSched->copy()->format('H:i:s'), $this->authUserTz)->format('@ G:ia');
                $schedule->socialRoomSchedule->frequency_for_ui = $schedForUi;
                $now = Carbon::now()->timezone($this->authUserTz);

                $startDate = $currSched->format('Y-m-d');
                $endTime = Carbon::parse(
                    $schedule->socialRoomSchedule->end_date,
                    $this->authUserTz
                )->format('H:i:s');
                $validUntil = Carbon::parse($startDate . ' ' . $endTime)->setTimezone($this->authUserTz)->addMinutes(30);
                $isRoomReady = (((int)$currSched->diffInMinutes($now, false) >= -5) && $validUntil->gt($now))
                    ? true
                    : false;

                $schedule->socialRoomSchedule->is_room_ready = $isRoomReady;
            }
        }


        return Response::json(
            [
                'status' => 'success',
                'data' => $social_room,
            ]
        );
    }

    public function show(Request $request, $roomCode)
    {
        $upcomingSchedules = [];
        $personId = $request->get('person_id');
        $userCode = $request->get('user_code');
        $includePolls = $request->get('include_polls');
        $includeActivePoll = true;

        // Set person id based on user code
        if (!empty($userCode) && empty($personId)) {
            $participant = SocialRoomParticipant::where('user_code', $userCode)->first();
            $personId = $participant->person_id;
        }

        $socialRooms = SocialRoom::with('socialRoomSchedule')
            ->with(
                [
                    'socialRoomParticipants' => function ($query) {
                        $query->whereRaw('lt_social_room_participants.person_id in (select person_id from lt_social_room_pins where lt_social_room_pins.lt_social_room_id = lt_social_room_participants.lt_social_room_id and lt_social_room_pins.deleted_at is null)');
                    },
                ]
            )
            ->with('deletedScheduleEntries')
            ->with('modifiedScheduleEntries')
            ->where('room_code', $roomCode);

        if (!empty($includePolls || $includeActivePoll)) {
            $socialRooms->with('polls');
        }

        $socialRooms = $socialRooms->first();

        if (!empty($socialRooms)) {
            $socialRooms = SocialRoom::setCmsData(
                new Collection([$socialRooms]),
                false,
                'socialRoomParticipants'
            )
                ->first();

            if (isset($socialRooms->socialRoomSchedule) && !empty($socialRooms->socialRoomSchedule)) {
                $currSched = Carbon::parse(
                    $socialRooms->socialRoomSchedule->start_date,
                    'Europe/London'
                )->timezone($this->authUserTz);
                $freq_for_ui = UpcomingSchedule::getFrequencyForUi($socialRooms->socialRoomSchedule, $this->authUserTz);

                // include the word Today if date is today
                $today = ($currSched->isToday())
                    ? " Today "
                    : " ";
                $schedForUi = $freq_for_ui . $today . $currSched->copy()->format('@ G:ia');
                $socialRooms->socialRoomSchedule->frequency_for_ui = $schedForUi;
                $now = Carbon::now()->timezone($this->authUserTz);
                $startDate = $currSched->format('Y-m-d');
                $endTime = Carbon::parse(
                    $socialRooms->socialRoomSchedule->end_date,
                    $this->authUserTz
                )->format('H:i:s');
                $validUntil = Carbon::parse($startDate . ' ' . $endTime)->setTimezone($this->authUserTz)->addMinutes(30);
                $isRoomReady = (((int)$currSched->diffInMinutes($now, false) >= -5) && $validUntil->gt($now))
                    ? true
                    : false;
                $socialRooms->socialRoomSchedule->is_room_ready = $isRoomReady;
            }

            if (isset($socialRooms) && $socialRooms->isCommunityThemeRoom()) {
                $socialRooms->community_editors = array_column(
                    json_decode(json_encode($socialRooms->editors()), true),
                    '_id'
                );
            }

            if (isset($socialRooms->socialRoomSchedule) && !empty($socialRooms->socialRoomSchedule) && $socialRooms->socialRoomSchedule->duration_type != 'permanent') {
                $social_room = new Collection([$socialRooms]);

                $tmpData = UpcomingSchedule::generate($social_room, $this->authUserCms);

                $upcomingSchedules = $tmpData['upcomingSchedules'];
            }

            $social_rooms = isset($tmpData['rooms'][0]) && !empty($tmpData['rooms'][0])
                ? $tmpData['rooms'][0]
                : $socialRooms;


            $joinRoomLink = '';
            if ($socialRooms->first()) {
                $participant = SocialRoomParticipant::where('lt_social_room_id', $socialRooms->first()->id)
                    ->where('person_id', $personId)->first();
                if (!(empty($participant))) //$joinRoomLink = SocialRoomParticipant::generateLink($participant->user_code);
                {
                    $joinRoomLink = SocialRoomParticipant::generateWaitingRoomCommonLink($roomCode);
                }
            }

            // Access property to load
            if (!empty($includeActivePoll)) {
                $social_rooms->active_poll = $social_rooms->active_poll;
            }

            return Response::json(
                [
                    'response' => 'success',
                    'data' => [
                        'room' => $social_rooms,
                        'upcoming_schedules' => $upcomingSchedules,
                        'join_room_link' => $joinRoomLink,
                    ],
                ]
            );
        }


        return Response::json(
            [
                'response' => 'error',
                'message' => 'Social room not found!',
            ]
        );
    }

    public function communities()
    {


        //$this->sync();

        $social_room = SocialRoom::where('lt_social_room_type_id', 3)
            ->where('status', 'approved')
            ->where('is_community', 1)
            // ->with('socialRoomParticipants')
            ->with(
                [
                    'socialRoomParticipants' => function ($query) {
                        $query->whereRaw('lt_social_room_participants.person_id in (select person_id from lt_social_room_pins where lt_social_room_pins.lt_social_room_id = lt_social_room_participants.lt_social_room_id and lt_social_room_pins.deleted_at is null)');
                    },
                ]
            )
            ->with('socialRoomSchedule')
            ->get();

        $social_room = SocialRoom::setCmsData($social_room, false, 'socialRoomParticipants');

        foreach ($social_room as $schedule) {

            if (!empty($schedule->socialRoomSchedule)) {
                $currSched = Carbon::parse(
                    $schedule->socialRoomSchedule->start_date,
                    'Europe/London'
                )->timezone($this->authUserTz);
                $freq_for_ui = UpcomingSchedule::getFrequencyForUi($schedule->socialRoomSchedule, $this->authUserTz);
                $schedForUi = $freq_for_ui . ' ' . $currSched->copy()->format('@ G:ia');
                $schedule->socialRoomSchedule->frequency_for_ui = $schedForUi;
            }
        }

        return Response::json(
            [
                'status' => 'success',
                'data' => $social_room,
            ]
        );
    }

    public function pinned(Request $request)
    {


        /*
        $data = \$request->all();
        $social_room = SocialRoomPin::where('person_id', $data['person_id'])
        ->with('socialRoom')
        ->get();
        */

        $socialRooms = SocialRoom::where('lt_social_room_type_id', 3)
            ->where('status', 'approved')
            ->with('socialRoomSchedule')
            ->with('deletedScheduleEntries')
            ->with('modifiedScheduleEntries')
            ->with(
                [
                    'socialRoomPins' => function ($query) use ($request) {
                        $query->where('person_id', $request->get('person_id'));
                    },
                ]
            )
            ->with(
                [
                    'socialRoomParticipants' => function ($query) {
                        $query->whereRaw('lt_social_room_participants.person_id in (select person_id from lt_social_room_pins where lt_social_room_pins.lt_social_room_id = lt_social_room_participants.lt_social_room_id and lt_social_room_pins.deleted_at is null)');
                    },
                ]
            )
            /*  ->with(['socialRoomParticipants' => function($query){
                  $query->whereRaw('(joined_room_at > left_room_at) OR (joined_room_at IS NOT NULL AND left_room_at IS NULL)' );
              }
              ]) */
            ->get();


        $socialRooms = SocialRoom::setCmsData($socialRooms, false, 'socialRoomParticipants');

        // $cms = CmsLibertyRepresentative::fetchAllFromCacheOrCms();

        // // Assign CMS data
        // foreach ($socialRooms as $key => $socialRoomParticipant) {
        //     $room_id = $socialRoomParticipant['id'];
        //     foreach ($socialRoomParticipant->social_room_participants as $key2 => $participant) {
        //         $socialRoomParticipants[$key]->social_room_participants[$key2]->cms = $cms[$participant['person_id']];
        //     }
        // }

        foreach ($socialRooms as $key => $room) {

            if (empty($room->socialRoomPins->toArray())) {
                unset($socialRooms[$key]);
            }
        }


        foreach ($socialRooms as $schedule) {


            if (!empty($schedule->socialRoomSchedule)) {
                $currSched = Carbon::parse(
                    $schedule->socialRoomSchedule->start_date,
                    'Europe/London'
                )->timezone($this->authUserTz);
                $freq_for_ui = UpcomingSchedule::getFrequencyForUi($schedule->socialRoomSchedule, $this->authUserTz);
                $schedForUi = $freq_for_ui . ' ' . $currSched->copy()->format('@ G:ia');
                $schedule->socialRoomSchedule->frequency_for_ui = $schedForUi;

                $now = Carbon::now()->timezone($this->authUserTz);
                $startDate = $currSched->format('Y-m-d');
                $endTime = Carbon::parse(
                    $schedule->socialRoomSchedule->end_date,
                    $this->authUserTz
                )->format('H:i:s');
                $validUntil = Carbon::parse($startDate . ' ' . $endTime)->setTimezone($this->authUserTz)->addMinutes(30);
                $isRoomReady = (((int)$currSched->diffInMinutes($now, false) >= -5) && $validUntil->gt($now))
                    ? true
                    : false;
                $schedule->socialRoomSchedule->is_room_ready = $isRoomReady;

                $joinRoomLink = '';

                $participant = SocialRoomParticipant::where('lt_social_room_id', $schedule->id)
                    ->where('person_id', $request->get('person_id'))->first();
                if (!(empty($participant))) //$joinRoomLink = SocialRoomParticipant::generateLink($participant->user_code);
                {
                    $joinRoomLink = SocialRoomParticipant::generateWaitingRoomCommonLink($socialRooms->first()->room_code);
                }

                $schedule->socialRoomSchedule->join = $joinRoomLink;
            }
        }

        return Response::json(
            [
                'status' => 'success',
                'data' => $socialRooms,
            ]
        );
    }

    public function togglepin(Request $request)
    {
        $data = $request->all();
        Log::info($data);
        $social_room = SocialRoomPin::withTrashed()->where(
            [
                'person_id' => $data['person_id'],
                'lt_social_room_id' => $data['room_id'],
            ]
        )
            ->first();

        if (empty($social_room)) {
            SocialRoomPin::create(
                [
                    'person_id' => $data['person_id'],
                    'lt_social_room_id' => $data['room_id'],
                ]
            );

            $this->createParticipant($data);

            $message = "Room was pinned to home";
        } else {
            if (empty($social_room->deleted_at)) {
                SocialRoomPin::where(
                    [
                        'person_id' => $data['person_id'],
                        'lt_social_room_id' => $data['room_id'],
                    ]
                )->delete();
                $message = "Room was unpinned from home";
            } else {
                SocialRoomPin::where(
                    [
                        'person_id' => $data['person_id'],
                        'lt_social_room_id' => $data['room_id'],
                    ]
                )->restore();
                $message = "Room was pinned to home";
                $this->createParticipant($data);
            }
        }
        return Response::json(
            [
                'status' => 'success',
                'message' => $message,
            ]
        );
    }

    private function createParticipant($data)
    {
        if (isset($this->authUserCms)) {
            $participantExists = SocialRoomParticipant::where(
                [
                    'lt_social_room_id' => $data['room_id'],
                    'person_id' => $data['person_id'],
                    'role' => 'virtual-rooms',
                ]
            )->exists();

            if ($participantExists) {
                return;
            }

            $user_code = md5(Hash::make(substr(Str::uuid()->toString(), 0, 4)));
            SocialRoomParticipant::create(
                [
                    'lt_social_room_id' => $data['room_id'],
                    'person_id' => $data['person_id'],
                    'user_code' => $user_code,
                    'role' => 'virtual-rooms',
                    'user_name' => isset($this->authUserCms['name'])
                        ? $this->authUserCms['name']
                        : "n/a",
                    'business' => isset($this->authUserCms['business'])
                        ? $this->authUserCms['business']
                        : "lsm",
                ]
            );
        }
    }

    public function store(Request $request)
    {
        $data = $request->all();

        $socialRoom = SocialRoom::where('room_code', $data['room_code'])
            ->first();

        if (empty($socialRoom)) {
            $errorResponse = [
                'response' => 'error',
                'errors' => 'No room has been found',
            ];

            return Response::json($errorResponse);
        }

        $data['lt_social_room_id'] = $socialRoom->id;


        $libReps = $this->libReps;


        $errorResponse = $this->validateStore($data, $libReps);
        if ($errorResponse instanceof JsonResponse) {
            return $errorResponse;
        }

        $libRepCreator = $libReps[$data['created_by_id']];


        if (!empty($data['start_date'])) // Convert start_date & end_date to London tz from creator tz
        {
            $data['start_date'] = Carbon::parse(
                $data['start_date'],
                $libRepCreator['office_timezone']
            )->setTimezone("Europe/London")->format('Y-m-d H:i:s');
        }
        if (!empty($data['end_date'])) {
            $data['end_date'] = Carbon::parse(
                $data['end_date'],
                $libRepCreator['office_timezone']
            )->setTimezone("Europe/London")->format('Y-m-d H:i:s');
            $data['valid_until'] = SocialRoomSchedule::getValidUntil($data);
        }

        $socialRoomSchedule = SocialRoomSchedule::create($data);

        SocialRoomScheduleUpdate::create(
            [
                'room_id' => $data['lt_social_room_id'],
                'room_type' => $socialRoom->lt_social_room_type_id,
                'room_code' => $data['room_code'],
            ]
        );

        $socialRoom->socialRoomSchedule()->save($socialRoomSchedule);


        $libRepCreator['person_id'] = $data['created_by_id'];
        $participants = [
            $libRepCreator,
        ];
        // dd($participants);
        foreach ($participants as $participant) {
            $participant['person_id']         = $participant['person_id'];
            $participant['user_name']         = $participant['name'];
            $participant['user_code']         = md5(Hash::make(substr(Str::uuid()->toString(), 0, 4)));
            $participant['role']              = $data['role'];
            $participant['lt_social_room_id'] = $data['lt_social_room_id'];

            if ($libRepCreator['person_id'] === $participant['person_id']) {
                $libRepCreator['user_code'] = $participant['user_code'];
                $libRepCreator['user_name'] = $participant['user_name'];
            }


            $socialRoomParticipant = SocialRoomParticipant::create($participant);
            $socialRoom->socialRoomParticipants()->save($socialRoomParticipant);
        }


        return Response::json(
            [
                'response' => 'success',
                'data' => [
                    'room' => $socialRoom,
                    'schedule' => $socialRoomSchedule,
                    'creator' => $libRepCreator,
                ],
                'message' => 'Room has been successfully created.',
            ]
        );
    }

    private function validateStore($data, $libReps)
    {
        $validator = $this->_validate($data, 'store');

        if ($validator->fails()) {
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        }

        if ($data['created_by_id'] && !isset($libReps[$data['created_by_id']])) {
            $validator->errors()->add('created_by_id', 'Created by id does not exists.');
            $errorResponse = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        }


        if (isset($errorResponse)) {
            return Response::json($errorResponse);
        }

        return [];
    }


    // validate store method

    private function _validate($data, $action)
    {
        $rules = [
            'room_code' => 'required',
            'duration_type' => 'required',
            'start_date' => 'required_if:duration_type,on-a-schedule|required_if:duration_type,one-off',
            'start_time' => 'required_if:duration_type,on-a-schedule|required_if:duration_type,one-off',
            'end_time' => 'required_if:duration_type,on-a-schedule|required_if:duration_type,one-off',
            'frequency' => 'required_if:duration_type,on-a-schedule',
            'end_after' => 'required_if:duration_type,on-a-schedule',
        ];

        // // On update, we don't want to validate email, cause it cannot be updated
        // if($action === 'update') {
        //     unset($rules['email']);
        // }

        // TODO: Add appropriate error messages here
        // TODO: Add appropriate error messages here
        // TODO: Add appropriate error messages here
        $messages = [
            // 'name.required' => 'Please enter Name.',
            // 'email.required' => 'Please enter Email.',
            // 'mobile_number.required' => 'Please enter Mobile Phone in a valid format.',
            // 'mobile_number.regex' => 'Please enter Mobile Phone in a valid format.',
        ];

        return Validator::make($data, $rules, $messages);
    }

    public function deleteSchedule(Request $request)
    {
        $socialRoomId = $request->get('lt_social_room_id');
        $scheduleId = $request->get('schedule_id');
        $scheduleDate = $request->get('date');

        $socialRoom = $socialRoom = SocialRoom::with(
            [
                'socialRoomParticipants',
                'socialRoomSchedule',
            ]
        )->find($socialRoomId);
        if (!$socialRoom) {
            return Response::json(['response' => 'error', 'message' => 'Unable to delete. Space not found.'], 400);
        }

        // If schedule date not provided, we will delete the whole schedule
        if (empty($scheduleDate)) {
            $socialRoomSchedule = SocialRoomSchedule::find($scheduleId);
            if (!$socialRoomSchedule) {
                return Response::json(
                    ['response' => 'error', 'message' => 'Unable to delete. Schedule not found.'],
                    400
                );
            }

            $socialRoomSchedule->delete();
            $newRoomCode = SocialRoom::roomCodeGenerator();

            $data = (object)[];
            $data->room_code = $newRoomCode;


            $updateRoomCode = SocialRoom::where('id', '=', $socialRoomId)->where(
                'is_community',
                "!=",
                1,
                'AND'
            )->update(['room_code' => $newRoomCode]);

            $deleteDeletedSchedules = SocialRoomCancelledSchedule::where(
                'lt_social_room_id',
                '=',
                $socialRoomId
            )->delete();

            return Response::json(['response' => 'success', 'message' => 'Space schedule deleted.', 'room' => $data]);
        }

        $scheduleDateCarbon = Carbon::parse($scheduleDate, $this->authUserTz)->setTimezone("Europe/London");
        $socialRoomCancelledSchedule = SocialRoomCancelledSchedule::create(
            [
                'lt_social_room_id' => $socialRoomId,
                'lt_social_room_schedule_id' => $scheduleId,
                'date' => $scheduleDateCarbon,
            ]
        );

        $data = (object)[];
        $data->room_code = $socialRoomId;

        return Response::json(['response' => 'success', 'message' => 'Space schedule deleted.', 'room' => $data]);
    }

    public function deleteSpace(Request $request, $socialRoomId)
    {
        $person_id = $request->get('person_id');
        $socialRoom = SocialRoom::with(
            [
                'socialRoomParticipants',
                'socialRoomSchedule',
                'socialRoomPins',
            ]
        )->find($socialRoomId);

        if (!$socialRoom) {
            return Response::json(['response' => 'error', 'message' => 'Unable to delete. Space not found.']);
        }

        if ($socialRoom->is_community) {
            return Response::json(['response' => 'error', 'message' => 'Unable to delete community.']);
        }
        if ($socialRoom->created_by_id != $person_id) {
            return Response::json(['response' => 'error', 'message' => 'Unable to delete! Not allowed.']);
        }

        $socialRoom->delete();

        return Response::json(['response' => 'success', 'message' => 'Space deleted.']);
    }

    public function syncCommunities()
    {
        $workspace = config('app.cms.lsm.lets_talk_workspace');
        $communities_type = config('app.cms.lsm.communities_content_type');
        $communities_description = config('app.cms.lsm.communities_description');
        $communities_image_link = config('app.cms.lsm.communities_image_link');
        $categorySourcesAll = json_decode(Cms::get('workspaces/' . $workspace . '/content-types/' . $communities_type . '/content-entries'));

        $categorySourcesCount = 0;
        $categories = [];
        $has_new_data = false;

        $social_room_count = SocialRoom::where('is_community', 1)
            ->where('lt_social_room_type_id', 3)
            ->whereNotNull('source_category_id')
            ->count();

        foreach ($categorySourcesAll->data as $categorySource) {
            if (count($categorySourcesAll->data) != $social_room_count) {
                $has_new_data = true;
                $category_exist = SocialRoom::where('source_category_id', $categorySource->_id)->first();

                if (empty($category_exist)) {
                    SocialRoom::create(
                        [
                            'source_category_id' => $categorySource->_id,
                            'lt_social_room_type_id' => 3,
                            'is_community' => 1,
                            'created_by_id' => 'SYSTEM',
                            'name' => $categorySource->name,
                            'image_link' => (isset($categorySource->{$communities_image_link}[0]->url) && !empty($categorySource->{$communities_image_link}[0]->url))
                                ? $categorySource->{$communities_image_link}[0]->url
                                : '',
                            'description' => (isset($categorySource->{$communities_description}) && !empty($categorySource->{$communities_description}))
                                ? $categorySource->{$communities_description}
                                : '',
                            'room_code' => $categorySource->_id,
                            'published_at' => date("Y-m-d H:i:s"),
                            'published_by' => 'SYSTEM',
                            'status' => ($categorySource->status === 'publish')
                                ? 'approved'
                                : 'pending',
                            'meeting_time' => '',
                        ]
                    );
                } else {
                    SocialRoom::where(
                        [
                            'source_category_id' => $categorySource->_id,
                        ]
                    )->update(
                        [
                            'name' => $categorySource->name,
                            'image_link' => (isset($categorySource->{$communities_image_link}[0]->url) && !empty($categorySource->{$communities_image_link}[0]->url))
                                ? $categorySource->{$communities_image_link}[0]->url
                                : '',
                            'description' => (isset($categorySource->{$communities_description}) && !empty($categorySource->{$communities_description}))
                                ? $categorySource->{$communities_description}
                                : '',
                            'status' => ($categorySource->status === 'publish')
                                ? 'approved'
                                : 'pending',
                        ]
                    );
                }
            }

            $categories[$categorySource->_id]['name'] = html_entity_decode($categorySource->name);
            $categories[$categorySource->_id]['description'] =
                (isset($categorySource->{$communities_description}) && !empty($categorySource->{$communities_description}))
                ? html_entity_decode($categorySource->{$communities_description})
                : '';
            $categories[$categorySource->_id]['image_link'] =
                (isset($categorySource->{$communities_image_link}) && !empty($categorySource->{$communities_image_link}))
                ? $categorySource->{$communities_image_link}[0]->url
                : '';
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => $categories,
                'has_new_data' => $has_new_data,
            ]
        );
    }

    public function updatedSchedule($room_code)
    {
        $room = SocialRoomScheduleUpdate::where('room_code', $room_code)
            ->orderBy('id', 'DESC')
            ->first();

        $room = SocialRoom::where('id', $room->room_id)->first();

        return Response::json(
            [
                'response' => 'success',
                'updated_room_code' => $room->room_code,
            ]
        );
    }

    private function sync()
    {

        $workspace = config('app.cms.lsm.lets_talk_workspace');
        $communities_type = config('app.cms.lsm.communities_content_type');

        $communitiesResponse = json_decode(Cms::get('workspaces/' . $workspace . '/content-types/' . $communities_type . '/content-entries'));


        $rooms = SocialRoom::where('lt_social_room_type_id', 3)
            ->where('status', 'approved')
            ->where('is_community', 1)
            ->with('socialRoomParticipants')
            ->with('socialRoomSchedule')
            ->get();

        $rooms = SocialRoom::setCmsData($rooms, false, 'socialRoomParticipants');

        $old_rooms = [];
        $new_rooms = [];
        $cms_ids = [];


        //Update rooms from mysql table with data from CMS


        foreach ($communitiesResponse->data as $key => $community) {

            $found = false;


            if ($community->status == 'publish') {

                $cms_ids[] = $community->_id;


                foreach ($rooms as $roomKey => $room) {

                    if ($room->name == $community->name) {
                        $room->room_code = $community->_id;
                        $room->save();

                        $old_rooms[] = json_decode(json_encode($rooms[$roomKey]), true);
                        $old_rooms[count($old_rooms) - 1]['slug'] = $community->slug;
                        $old_rooms[count($old_rooms) - 1]['community_editors'] = $community->{config('app.cms.lsm.community_editors')};
                        $found = true;
                    }
                }

                if (!$found) {

                    $room = SocialRoom::create(
                        [
                            'lt_social_room_type_id' => 1,
                            'created_by_id' => '5ef3467031239970014119d2',
                            'name' => $community->name,
                            'room_code' => $community->_id,
                            'status' => 'approved',
                            'is_community' => 1,
                        ]
                    );
                    $room->slug = $community->slug;
                    $room->community_editors = $community->{config('app.cms.lsm.community_editors')};
                    $new_rooms[] = $room;
                }
            }
        }

        $all_rooms = array_merge($old_rooms, $new_rooms);


        if (count($all_rooms) != count($communitiesResponse->data)) {
            foreach ($all_rooms as $key => $room) {
                if (array_search($room['room_code'], $cms_ids) === false) {
                    unset($all_rooms[$key]);
                }
            }
        }

        return json_decode(json_encode($all_rooms));
    }
}
