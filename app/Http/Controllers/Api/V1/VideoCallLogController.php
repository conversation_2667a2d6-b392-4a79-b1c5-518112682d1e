<?php


namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use App\Models\VideoCallParticipant;
use App\Models\VideoCallRoom;
use App\Models\VideoLog;
use App\Models\VideoLogData;
use Illuminate\Http\Request;

class VideoCallLogController extends BaseController
{
    public function log(Request $request)
    {
        $data = $request->all();

        if (isset($data['SmsSid'])) {
            $log = VideoLogData::getModel(VideoLog::TYPE_SMS, $data['SmsSid']);

            $participant = VideoCallParticipant::where('mobile_number', $data['To'])
                ->where('room_id', $log->room_id)
                ->first();

            VideoLogData::create(
                [
                'log_id' => $log->_id,
                'sms_id' => $data['SmsSid'],
                'human_readable' => 'Message has been ' . $data['MessageStatus'] . ' to ' . $participant->user_name,
                'data' => $data,
                ]
            );
        } else {
            $log = VideoLogData::getModel(VideoLog::TYPE_CALL, $data['RoomSid']);

            /**
 * @var VideoCallRoom $room 
*/
            $room = VideoCallRoom::where('name', $data['RoomName'])->first();

            if (!$log) {
                if (!$room) {
                    Log::error('Cannot create log for ' . $data['RoomName'] . ' with RoomSid="' . $data['RoomSid'] . '"');
                    Log::error($data);
                }
                $log = VideoLog::create(
                    [
                    'type' => VideoLog::TYPE_CALL,
                    'room_sid' => $data['RoomSid'],
                    'room_id' => $room->_id,
                    ]
                );
            }

            if (isset($data['ParticipantIdentity'])) {
                $participantIdentity = $data['ParticipantIdentity'];
            }
            $humanReadableLog = '';
            switch ($data['StatusCallbackEvent']) {
            case 'room-created':
                $humanReadableLog = 'Room has been created.';
                $room->roomCreated();
                break;
            case 'participant-connected':
                $humanReadableLog = "$participantIdentity has entered the room.";
                break;
            case 'participant-disconnected':
                $humanReadableLog = $participantIdentity . ' has disconnected to the call after ' . $data['ParticipantDuration'] . ' seconds.';
                break;
            case 'recording-started':
                $humanReadableLog = $participantIdentity . ' has started the recording of the call.';
                break;
            case 'recording-completed':
                $humanReadableLog = $participantIdentity . ' has finished recording the call.';
                break;
            case 'recording-failed':
                $humanReadableLog = "Recording initiated by $participantIdentity has failed.";
                break;
            case 'track-enabled':
            case 'track-added':
                $humanReadableLog = $participantIdentity . ' has enabled the ' . $data['TrackKind'] . '.';
                break;
            case 'track-disabled':
            case 'track-removed':
                $humanReadableLog = $participantIdentity . ' has disabled the ' . $data['TrackKind'] . '.';
                break;
            case 'room-ended':
                $humanReadableLog = 'All participants has left the room after ' . $data['RoomDuration'] . ' seconds.';
                $room->roomCompleted();
                break;
            }

            VideoLogData::create(
                [
                'log_id' => $log->_id,
                'room_sid' => $data['RoomSid'],
                'human_readable' => $humanReadableLog,
                'sequence_number' => $data['SequenceNumber'],
                'data' => $data,
                ]
            );
        }
    }

    public function downloadLog(Request $request)
    {
        $roomCode = $request->get('code');

        $room = VideoCallRoom::where('name', $roomCode)
            ->with(['logs', 'logs.logData'])
            ->first();
        return Response::json(
            [
            'response' => 'success',
            'data' => ['rooms' => $room],
            ]
        );
    }
}
