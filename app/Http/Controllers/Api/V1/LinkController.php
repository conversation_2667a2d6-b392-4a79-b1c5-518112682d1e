<?php
/**
 * Created by PhpStorm.
 * User: chrisvickers
 * Date: 23/04/15
 * Time: 09:42
 */

namespace App\Http\Controllers\Api\V1;

use App\Models\Links;
use App\Models\LinkSector;
use App\Http\Controllers\BaseController;
use App\Models\Documentlevels;
use App\Models\Link;
use Illuminate\Support\Facades\Validator;
use App\Models\Organisation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;

class LinkController extends BaseController
{


    public function __construct(
        Links $links,
        LinkSector $linkSector,
        Organisation $organisation,
        Documentlevels $levels
    ) {
        $this->links = $links;
        $this->linkSector = $linkSector;
        $this->organisation = $organisation;
        $this->levels = $levels;
    }

    public function organisation($id)
    {
        $organisation = $this->organisation->find($id);

        if (isset($organisation)) {
            $links = $this->links->where('organisation_id', '=', $organisation->id)->get();

            foreach ($links as $link) {
                $link->sectors = $link->sectors();
            }
            return response()->json([
                'response' => 'success',
                'data'     => $links,
            ]);
        }

        return response()->json(['response' => 'error', 'error' => 'Organisation does not exist']);
    }

    public function find($id)
    {
        $link = $this->links->find($id);

        if (isset($link)) {
            $link->sectors = $link->sectors();
            return response()->json([
                'response' => 'success',
                'data'     => $link,
            ]);
        }

        return response()->json([
            'response' => 'error',
            'error'    => 'Link does not exist',
        ]);
    }

    public function update(Request $request)
    {
        $rules = [
            'id'          => 'required',
            'link'        => 'url|required',
            'level1_type' => 'required',
            // 'level2_type'   =>  'required', NOTE: Removed for FAS Portal Feature
            // 'level3_type'   =>  'required', NOTE: Removed for FAS Portal Feature,
            'level4_type' => 'required',
            'sector'      => 'required',
        ];

        $validation = Validator::make($request->all(), $rules);

        if ($validation->fails()) {
            return response()->json([
                'response' => 'error',
                'errors'   => $validation->errors(),
            ]);
        }

        $data = $request->all();

        if (empty($data['level2_type'])) {
            $data['level2_type'] = '';
        }

        if (empty($data['level3_type'])) {
            $data['level3_type'] = '';
        }

        $link = $this->links->find($data['id']);
        if (isset($link)) {
            if (!isset($data['organisation_id'])) {
                $sectors = explode(',', $data['sector']);
                unset($data['sector']);
            } else {
                $sector = $data['sector'];
                unset($data['sector']);
            }
            unset($data['id']);

            if ($link->update($data)) {
                $this->linkSector->where('link_id', '=', $link->id)->delete();
                if (!isset($data['organisation_id'])) {
                    foreach ($sectors as $sec) {
                        $this->linkSector->create(['link_id' => $link->id, 'sector_id' => $sec]);
                    }
                } else {
                    $this->linkSector->create(['link_id' => $link->id, 'sector_id' => $sector]);
                }

                $user_id = $request->get('user_id')
                    ? $request->get('user_id')
                    : 0;
                return response()->json(
                    [
                    'response' => 'success',
                    'link' => $link,
                    ]
                );
            }
        }

        return response()->json([
            'response' => 'error',
            'error'    => 'Link does not exist',
        ]);
    }

    /**
     * Get all links without organisation
     */
    public function all()
    {
        $links = $this->links->where('organisation_id', '=', null)->get();

        foreach ($links as $link) {
            $link->sectors = $link->sectors();
        }
        return response()->json([
            'response' => 'success',
            'data'     => $links,
            'count'    => count($links),
        ]);
    }

    /**
     * Update method
     *
     * @param Request $request
     * @return json
     */
    public function delete(Request $request)
    {
        $rules = ['id' => 'required'];
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                'response' => 'error',
                'errors'   => $validator->errors(),
            ]);
        }

        $data = $request->all();
        $link = $this->links->find($data['id']);
        if (isset($link)) {
            $sectors = $this->linkSector->where('link_id', '=', $link->id)->get();
            foreach ($sectors as $sec) {
                $sec->delete();
            }

            $user_id = $request->get('user_id')
                ? $request->get('user_id')
                : 0;

            if ($link->delete()) {
                return response()->json([
                    'response' => 'success',
                    'message'  => 'Link deleted',
                ]);
            }

            return response()->json([
                'response' => 'error',
                'error'    => 'Failed to delete link',
            ]);
        }

        return response()->json([
            'response' => 'error',
            'error'    => 'Link does not exist',
        ]);
    }

    public function getFasDoLinks($organisationId, $level1, $sector)
    {
        $links = $this->links
            ->join('link_sector_pivot', 'link_sector_pivot.link_id', '=', 'links.id')
            ->where('links.level1_type', $level1)
            ->where('link_sector_pivot.sector_id', $sector)
            ->whereRaw('(links.organisation_id = ? OR links.organisation_id IS NULL)', [$organisationId])
            ->groupBy('link_sector_pivot.link_id')
            ->get();

        return Response::json([
            'response' => 'success',
            'data'     => $links,
        ]);
    }

    /**
     * Create Method for links
     *
     * @return mixed
     */
    public function create(Request $request)
    {

        $rules = [
            'name'        => 'required',
            'link'        => 'url|required',
            'level1_type' => 'required',
            'level4_type' => 'required',
            'sector'      => 'required',
        ];

        $validation = Validator::make($request->all(), $rules);
        if ($validation->fails()) {
            return response()->json([
                'response' => 'error',
                'errors'   => $validation->errors(),
            ]);
        }


        $data = $request->all();
        if (!isset($data['organisation_id'])) {
            $sectors = explode(',', $data['sector']);
            unset($data['sector']);
        }

        if ($link = $this->links->create($data)) {
            if (isset($data['organisation_id']) && ['organisation_id'] != 0) {
                $this->linkSector->create(['link_id' => $link->id, 'sector_id' => intval($data['sector'])]);
            } else {
                foreach ($sectors as $sector) {
                    $this->linkSector->create(['link_id' => $link->id, 'sector_id' => $sector]);
                }
            }

            $user_id = $request->get('user_id')
                ? $request->get('user_id')
                : 0;
            return response()->json([
                'response' => 'success',
                'link' => $link,
            ]);
        }

        return response()->json([
            'response' => 'error',
            'error' => 'Failed to create link',
        ]);
    }

    public function category($organisation_id = 0, $level1 = '', $level3 = '', $sectorId = '')
    {
        $level1 = str_replace("*slash*", "/", $level1);
        // $level2 = str_replace("*slash*","/",$level2);
        $level3 = str_replace("*slash*", "/", $level3);
        // $level4 = str_replace("*slash*","/",$level4);
        $links = [];
        if ($level3 != '') {
            $level = $this->levels->where('level_name', 'LIKE', '%' . urldecode($level3) . '%')->first();
            if (isset($level)) {
                $link = $this->links->where('level3_type', '=', $level->level_id);
                if ($level1 != '') {
                    $level = $this->levels->where('level_name', 'LIKE', '%' . urldecode($level1) . '%')->first();
                    if (isset($level)) {
                        $link->where('level1_type', '=', $level->level_id);
                    }
                }

                $link = $link->get();
                foreach ($link as $key => $l) {
                    $sector_id = false;
                    $l->sectors = $l->sectors();
                    foreach ($l->sectors as $sec) {
                        if ($sec->id == $sectorId) {
                            $sector_id = true;
                        }
                    }

                    if (!$sector_id) {
                        unset($link[$key]);
                    }
                }


                foreach ($link as $l) {
                    array_push($links, $l);
                }
            }
        } elseif ($level1 != '') {
            $level = $this->levels->where('level_name', 'LIKE', '%' . urldecode($level1) . '%')->first();
            if (isset($level)) {
                $link = $this->links->where('level1_type', '=', $level->level_id);

                if ($organisation_id != 0) {
                    $link->where('organisation_id', '=', $organisation_id);
                }
                $link = $link->get();

                if ($organisation_id != 0) {
                    $organisation = $this->organisation->find($organisation_id);
                    if (isset($organisation)) {
                        foreach ($link as $key => $l) {
                            $sector_id = false;
                            $l->sectors = $l->sectors();
                            foreach ($l->sectors as $sec) {
                                if ($sec->id == $organisation->sector) {
                                    $sector_id = true;
                                }
                            }

                            if (!$sector_id) {
                                unset($link[$key]);
                            }
                        }
                    }
                }

                foreach ($link as $l) {
                    if (!in_array($l, $links)) {
                        array_push($links, $l);
                    }
                }
            }
        } elseif ($organisation_id != 0) {
            $link = $this->links->where('organisation_id', '=', $organisation_id);
            foreach ($link as $l) {
                if ($level3 != '') {
                    $level = $this->levels->where('level_name', 'LIKE', '%' . urldecode($level3) . '%')->first();
                    if (isset($level)) {
                        $link = $this->links->where('level3_type', '=', $level->level_id);
                        if ($level1 != '') {
                            $level = $this->levels->where(
                                'level_name', 'LIKE',
                                '%' . urldecode($level1) . '%'
                            )->first();
                            if (isset($level)) {
                                $link->where('level1_type', '=', $level->level_id);
                            }
                        }

                        $link = $link->get();

                        foreach ($link as $l) {
                            array_push($links, $l);
                        }
                    }
                } elseif ($level1 != '') {
                    $level = $this->levels->where('level_name', 'LIKE', '%' . urldecode($level1) . '%')->first();
                    if (isset($level)) {
                        $link = $this->links->where('level1_type', '=', $level->level_id);

                        if ($organisation_id != 0) {
                            $link->where('organisation_id', '=', $organisation_id);
                        }
                        $link = $link->get();
                        foreach ($link as $l) {
                            if (!in_array($l, $links)) {
                                array_push($links, $l);
                            }
                        }
                    }
                } else {
                    $link->get();
                    array_push($links, $l);
                }

            }
        }

        return response()->json([
            'response' => 'success',
            'data'     => $links,
            'count'    => count($links),
        ]);
    }

    public function getAvailableLinksForOrgAndSector($organisationId, $sectorId, $level1, $level2, $level3)
    {
        $links = Link::select('links.id')
            ->join('link_sector_pivot', function ($query) use ($sectorId) {
                $query->on('link_sector_pivot.link_id', '=', 'links.id');
                $query->where('sector_id', '=', $sectorId);
                $query->whereNull('link_sector_pivot.deleted_at');
            })
            ->where('links.organisation_id', '=', $organisationId)
            ->orWhere(DB::raw('links.organisation_id'), '=', null)
            ->where('links.level1_type', '=', $level1)
            ->where('links.level2_type', '=', $level2)
            ->where('links.level3_type', '=', $level3)
            ->get();

        return Response::json([
            'response' => 'success',
            'count'    => $links->count(),
        ]);
    }
}
