<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\Organisation;
use App\Models\PolicyType;
use Illuminate\Support\Facades\Response;

class PolicyTypeController extends BaseController
{


    /**
     * Show all Types
     *
     * @param  null $limit
     * @param  null $page
     * @return mixed
     */
    public function all($limit = null, $page = null)
    {


        if (isset($page) && isset($limit)) {

            $types = PolicyType::all()->take($limit)->skip(($page * $limit) - $limit);

        } else {

            $types = PolicyType::all();

        }


        return Response::json(['response' => 'success', 'data' => $types, 'total' => count($types)]);


    }

    /**
     * Get Policy Type for a single Organisation
     *
     * @param  $organisation_id
     * @return mixed
     */
    public function organisation($organisation_id)
    {

        $organisation = Organisation::find($organisation_id);

        if ($organisation) {

            $policies = $organisation->policyNumbers();
            $types = [];
            foreach ($policies as $policy) {
                if (!in_array($policy->type, $types)) {
                    array_push($types, $policy->type);
                }
            }

            return Response::json(['response' => 'success', 'data' => $types]);


        }


        return Response::json(['response' => 'error', 'message' => 'Organisation does not exist']);
    }

    public function options($column_name = 'organisation_id')
    {

        $model = new PolicyType();

        if (in_array($column_name, $model->getFillable())) {
            return Response::json(
                PolicyType::pluck(
                    $column_name,
                    'id'
                )->all()
            );
        } else {
            return Response::json(
                [
                'error' => 'Invalid column name provided',
                ], 400
            );
        }
    }


}
