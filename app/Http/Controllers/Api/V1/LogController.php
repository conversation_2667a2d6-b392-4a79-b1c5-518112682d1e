<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\Activity;
use App\Http\Controllers\BaseController;
use Carbon\Carbon;
use App\Models\Organisation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use App\Models\User;
use Illuminate\Support\Facades\Validator;
use MongoDB\BSON\UTCDateTime;

class LogController extends BaseController
{


    public function __construct(Activity $activity, Organisation $organisation, User $user)
    {
        $this->log = $activity;
        $this->user = $user;
        $this->organisation = $organisation;
    }

    public function add(Request $request)
    {
        $rules = [
            'contentId' => 'required',
            'contentType' => 'required',
            'action' => 'required',
            'description' => 'required',
            'details' => 'required',
            'user_id' => 'required',
        ];

        $validation = Validator::make($request->all(), $rules);
        if ($validation->fails()) {
            return Response::json(
                [
                'response' => 'error',
                'errors' => $validation->errors(),
                ]
            );
        }

        $data = $request->all();

        $activity = new Activity();
        $activity->content_id = $data['contentId'];
        $activity->content_type = $data['contentType'];
        $activity->action = $data['action'];
        $activity->user_id = $data['user_id'];
        $activity->description = $data['description'];
        $activity->details = $data['details'];
        $activity->ip_address = $request->header('session_ip') != '' && $request->header('session_ip') != null
            ? $request->header('session_ip')
            : '127.0.0.1';
        $activity->user_agent = $request->header('session_agent') != '' && $request->header('session_agent') != null
            ? $request->header('session_agent')
            : 'No UserAgent';
        $activity->save();

        return Response::json(
            [
            'response' => 'success',
            ]
        );
    }


    public function overview(Request $request)
    {
        $data = $request->all();
        $date_from = Carbon::createFromFormat('d-m-Y', $data['date_from']);
        $date_from->hour(0)->minute(0)->second(0);
        $date_from = new UTCDateTime(strtotime($date_from->format('Y-m-d H:i:s')) * 1000);

        $date_to = Carbon::createFromFormat('d-m-Y', $data['date_to']);
        $date_to->hour(23)->minute(59)->second(59);
        $date_to = new UTCDateTime(strtotime($date_to->format('Y-m-d H:i:s')) * 1000);

        $filters = isset($data['filters'])
            ? $data['filters']
            : [];

        //Get users
        $users = $this->user->where('branch', '<>', 1)->pluck('id');
        //get branches
        $branches = $this->user->where('branch', '=', 1)->pluck('id');

        if (isset($filters['year']) && !in_array(trim(strtolower($filters['year'])), ['', 'all'])) {
            $this->log->whereYear('created_at', '=', $filters['year']);
        }

        if ($data['type'] == 'login') {
            $return['login'] = $this->log->where('created_at', '>=', $date_from)->where(
                'created_at', '<=',
                $date_to
            )->where('action', '=', 'Login')->where('content_type', '=', 'User')->orderBy('created_at');
        } elseif ($data['type'] == 'document+access') {
            $return['document+access'] = $this->log->where('created_at', '>=', $date_from)->where(
                'created_at', '<=',
                $date_to
            )->where('content_type', '=', 'Document')->orderBy('created_at');
        } elseif ($data['type'] == 'client') {
            $return['client_create'] = $this->log->where('created_at', '>=', $date_from)->where(
                'created_at', '<=',
                $date_to
            )->where('action', '=', 'Create')->where('content_type', '=', 'User')->orderBy('created_at');
            $return['client_delete'] = $this->log->where('created_at', '>=', $date_from)->where(
                'created_at', '<=',
                $date_to
            )->where('action', '=', 'Destroy')->where('content_type', '=', 'User')->orderBy('created_at');

            $return['branch_create'] = $this->log->where('created_at', '>=', $date_from)->where(
                'created_at', '<=',
                $date_to
            )->where('action', '=', 'Create')->where('content_type', '=', 'User')->wherein(
                'user_id',
                array_values($branches->toArray())
            )->orderBy('created_at');
            $return['branch_delete'] = $this->log->where('created_at', '>=', $date_from)->where(
                'created_at', '<=',
                $date_to
            )->where('action', '=', 'Destroy')->where('content_type', '=', 'User')->wherein(
                'user_id',
                array_values($branches->toArray())
            )->orderBy('created_at');
        } elseif ($data['type'] == 'section+usage') {
            //Page Access
            $return['section+usage'] = $this->log->where('created_at', '>=', $date_from)->where(
                'created_at', '<=',
                $date_to
            )->where('action', '=', 'Access')->orderBy('created_at');
        } elseif ($data['type'] == 'activations') {
            //Croner and Safety
            $return['croner_activation'] = $this->log->where('created_at', '>=', $date_from)->where(
                'created_at', '<=',
                $date_to
            )->where('action', '=', 'Croner Access')->where(
                'content_type', '=',
                'User'
            )->where('description', '=', '1')->orderBy('created_at');
            $return['safety_activation'] = $this->log->where('created_at', '>=', $date_from)->where(
                'created_at', '<=',
                $date_to
            )->where('action', '=', 'Safety Media Access')->where(
                'content_type', '=',
                'User'
            )->where('description', '=', '1')->orderBy('created_at');

            $return['croner_deactivation'] = $this->log->where('created_at', '>=', $date_from)->where(
                'created_at',
                '<=', $date_to
            )->where('action', '=', 'Croner Access')->where(
                'content_type', '=',
                'User'
            )->where('description', '=', 0)->orderBy('created_at');
            $return['safety_deactivation'] = $this->log->where('created_at', '>=', $date_from)->where(
                'created_at',
                '<=', $date_to
            )->where('action', '=', 'Safety Media Access')->where(
                'content_type', '=',
                'User'
            )->where('description', '=', 0)->orderBy('created_at');
        } else {
        }

        $organisation_id = $request->get('organisation');
        foreach ($return as $key => $source) {
            if (isset($organisation_id) && $organisation_id != 0 && $organisation_id != 'all') {
                $organisation = $this->organisation->find($organisation_id);
                if (isset($organisation)) {
                    $users = $organisation->users->pluck('id');
                    $return[$key] = $source->whereIn('user_id', array_map('strval', $users->toArray()));
                }
            }
        }

        $branch = $request->Get('branch');
        foreach ($return as $key => $source) {
            if (isset($branch) && $branch == 'on') {
                if (isset($organisation_id) && $organisation_id != 0 && $organisation_id != 'all') {
                    $user = $this->user->where('organisation_id', '=', $organisation_id)->where(
                        'branch', '=',
                        1
                    )->pluck('id')->all();
                } else {
                    $user = $this->user->where('branch', '=', 1)->pluck('id');
                }
                $return[$key] = $source->whereIn('user_id', array_values($user->toArray()));
            }
        }


        foreach ($return as $key => $source) {
            $return[$key] = $source->get();
        }


        foreach ($return as $key => $source) {
            //print_r($source); exit;
            foreach ($source as $k => $row) {
                if (in_array($row->user_id, $users->toArray()) || in_array($row->user_id, $branches->toArray())) {
                    if ($key == 'document') {
                        $row->level1 = $row->level(1, $row);
                        $row->level2 = $row->level(2, $row);
                        $row->level3 = $row->level(3, $row);
                    }
                    $row->organisation = $row->user->organisation;
                    $row->user = (object)$row->user;
                } else {
                    unset($source[$k]);
                }
            }
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => $return,
            ]
        );
    }


    public function page(Request $request)
    {
        $rules = [
            'date_from' => 'required',
            'date_to' => 'required',
            'organisation' => 'required',
            'page' => 'required',
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json(
                [
                'response' => 'error',
                'errors' => $validator->errors(),
                ]
            );
        }

        $data = $request->all();
        $date_from = Carbon::createFromFormat('d-m-Y', $data['date_from']);
        $date_from->hour(0)->minute(0)->second(0);
        $date_from = new UTCDateTime(strtotime($date_from->format('Y-m-d H:i:s')) * 1000);

        $date_to = Carbon::createFromFormat('d-m-Y', $data['date_to']);
        $date_to->hour(23)->minute(59)->second(59);
        $date_to = new UTCDateTime(strtotime($date_to->format('Y-m-d H:i:s')) * 1000);

        $return['page'] = $this->log->where('content_type', '=', $request->get('page'))->where(
            'created_at', '<=',
            $date_to
        )->where('created_at', '>=', $date_from)->get();

        if ($data['page'] == 'News') {
            //todo: get news papers
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => $return['page'],
            ]
        );
    }


    public function activations(Request $request)
    {
        $rules = [
            'date_from' => 'required',
            'date_to' => 'required',
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json(
                [
                'response' => 'error',
                'errros' => $validator->errors(),
                ]
            );
        }
        $data = $request->all();
        $date_from = Carbon::createFromFormat('d-m-Y', $data['date_from']);
        $date_from->hour(0)->minute(0)->second(0);
        $date_from = new UTCDateTime(strtotime($date_from->format('Y-m-d H:i:s')) * 1000);

        $date_to = Carbon::createFromFormat('d-m-Y', $data['date_to']);
        $date_to->hour(23)->minute(59)->second(59);
        $date_to = new UTCDateTime(strtotime($date_to->format('Y-m-d H:i:s')) * 1000);

        $return['croner_activation'] = $this->log->where('created_at', '>=', $date_from)->where(
            'created_at', '<=',
            $date_to
        )->where('action', '=', 'Croner Access')->where('content_type', '=', 'User')->where(
            'description',
            '=', '1'
        )->orderBy('created_at');
        $return['safety_activation'] = $this->log->where('created_at', '>=', $date_from)->where(
            'created_at', '<=',
            $date_to
        )->where('action', '=', 'Safety Media Access')->where(
            'content_type', '=',
            'User'
        )->where('description', '=', '1')->orderBy('created_at');

        $return['croner_deactivation'] = $this->log->where('created_at', '>=', $date_from)->where(
            'created_at', '<=',
            $date_to
        )->where('action', '=', 'Croner Access')->where('content_type', '=', 'User')->where(
            'description',
            '=', 0
        )->orderBy('created_at');
        $return['safety_deactivation'] = $this->log->where('created_at', '>=', $date_from)->where(
            'created_at', '<=',
            $date_to
        )->where('action', '=', 'Safety Media Access')->where(
            'content_type', '=',
            'User'
        )->where('description', '=', 0)->orderBy('created_at');

        $organisation_id = $request->Get('organisation');
        foreach ($return as $key => $source) {
            $return[$key] = $source->get();
        }

        foreach ($return as $type => $source) {
            foreach ($source as $key => $result) {
                $this_user = $this->user->find($result->user_id);
                if ($this_user) {
                    $return[$type][$key]->user = $this_user;
                }
                if ($this_user) {
                    $this_org = $this->organisation->find($result->user->organisation_id);

                    if ($this_org) {
                        $return[$type][$key]->organisation = $this_org;
                    }
                }
            }
        }

        return Response::json(['response' => 'success', 'data' => $return]);
    }


}
