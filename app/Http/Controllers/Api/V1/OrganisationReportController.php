<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\LibertyUser;
use App\Models\Mailqueue;
use App\Models\Organisation;
use App\Models\OrganisationReport;
use App\Models\ReportMention;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\Request;

class OrganisationReportController extends BaseController
{
    const MENTION_FORMAT = '@<%s id=\"%s\">%s<\/%s>';
    const MENTION_ENCODE_TAG = 'b';

    private $liberty_admins;

    public function __construct(Mailqueue $mailqueue)
    {
        $this->mail = $mailqueue;
    }

    public function show($organisation_id, $id)
    {
        $report = OrganisationReport::where('id', $id)->first();

        return Response::json(
            [
            'response' => 'success',
            'data' => $report,
            ]
        );
    }

    public function store(Request $request)
    {
        $data = $request->all();

        $report = OrganisationReport::create(
            [
            'organisation_id' => $data['organisation_id'],
            'title' => $data['title'],
            'type' => $data['type'],
            'visibility' => $data['visibility'],
            'cloudname' => $data['cloudname'],
            'filename' => $data['filename'],
            'filesize' => $data['filesize'],
            'upload_date' => $data['upload_date'],
            'content' => $data['content'],
            ]
        );

        $organisation_id = $data['organisation_id'];

        if ($report) {
            $this->saveAndNotifyMentions($report->id, $organisation_id, $data);
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => $report,
            ]
        );
    }

    public function all(Request $request, $id)
    {
        $filters = $request->get('filters', []);

        if ($request->get('client-only')) {
            $search = OrganisationReport::where('organisation_id', $id)
                ->where('visibility', 'external');
        } else {
            $search = OrganisationReport::where('organisation_id', $id);
        }

        if (isset($filters['year']) && !in_array(trim(strtolower($filters['year'])), ['', 'all'])) {
            $search->whereYear('created_at', '=', $filters['year']);
        }

        $reports = $search
            ->orderBy('id', 'desc')
            ->get();

        return Response::json(
            [
            'response' => 'success',
            'data' => $reports,
            ]
        );
    }

    private function saveAndNotifyMentions($report_id, $organisation_id, array $data)
    {
        $mentions = $this->extractMentions($data['content'], 'data-id="', '"');

        if (!empty($mentions)) {
            // skip those who were already notified
            $mentioned = ReportMention::where(
                [
                'report_id' => $report_id,
                'organisation_id' => $organisation_id,
                ]
            )->get()->toArray();

            $mentioned_users = array_column($mentioned, 'user_id');

            foreach ($mentions as $user_id) {
                $user = LibertyUser::find($user_id);
                $report = OrganisationReport::find($report_id);
                $organisation = Organisation::find($organisation_id);
                $report->author = $data['author'];

                $email_subject = $report->author . ' has updated a note you were tagged in';
                $title = '<strong>' . $report->author . '</strong> updated a note in the <strong>' . $organisation->name . '</strong> dashboard:';

                if (!in_array($user_id, $mentioned_users)) {
                    $email_subject = $report->author . ' tagged you in ' . $report->title;
                    $title = '<strong>' . $report->author . '</strong> mentioned you in the <strong>' . $organisation->name . '</strong> dashboard:';

                    //save new mentions
                    ReportMention::create(
                        [
                        'report_id' => $report_id,
                        'user_id' => $user_id,
                        'organisation_id' => $organisation_id,
                        ]
                    );
                }

                //send email
                $email_template = 'emails.risk-reduce.report-mentions';
                $this->mail->queue(
                    $user->email,
                    $user->first_name . ' ' . $user->last_name,
                    $email_subject,
                    $email_template,
                    [
                        'mentioner' => $report->author,
                        'organisation_name' => $organisation->name,
                        'report_id' => $report_id,
                        'organisation_id' => $organisation_id,
                        'subject' => $report->title,
                        'title' => $title,
                    ]
                );

            }
        }
    }

    private function extractMentions($str, $start_delimiter, $end_delimiter)
    {
        $contents = [];
        $start_delimiter_length = strlen($start_delimiter);
        $endDelimiterLength = strlen($end_delimiter);
        $start_from = $content_start = $content_end = 0;
        while (false !== ($content_start = strpos($str, $start_delimiter, $start_from))) {
            $content_start += $start_delimiter_length;
            $content_end = strpos($str, $end_delimiter, $content_start);
            if (false === $content_end) {
                break;
            }
            $contents[] = substr($str, $content_start, $content_end - $content_start);
            $start_from = $content_end + $endDelimiterLength;
        }
        return $contents;
    }

    public function update(Request $request, $organisation_id, $id)
    {
        $data = $request->all();

        $report = OrganisationReport::where('id', $id)->first();
        $report->title = $data['title'];
        $report->type = $data['type'];
        $report->visibility = $data['visibility'];
        $report->content = $data['content'];

        if (isset($data['cloudname'])) {
            $report->cloudname = $data['cloudname'];
            $report->filename = $data['filename'];
            $report->filesize = $data['filesize'];
            $report->upload_date = $data['upload_date'];
        }

        $report->save();

        if ($report) {
            $this->saveAndNotifyMentions($report->id, $organisation_id, $data);
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => $report,
            ]
        );
    }

    public function destroy($organisation_id, $id)
    {
        $report = OrganisationReport::where('id', $id)->first();

        $data['organisation_id'] = $report->organisation_id;

        $report->delete();

        return Response::json(
            [
            'response' => 'success',
            'data' => $data,
            ]
        );
    }
}
