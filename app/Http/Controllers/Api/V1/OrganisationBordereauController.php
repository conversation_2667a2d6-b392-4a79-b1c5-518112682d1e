<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\Activity;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Hash;
use App\Models\Cati;
use Exception;
use App\Models\Mailqueue;
use App\Models\Organisation;
use App\Models\OrganisationBordereauSubmission;
use App\Models\OrganisationPolicy;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Str;

class OrganisationBordereauController extends BaseController
{
    public function __construct(Mailqueue $mailqueue)
    {
        $this->mail = $mailqueue;
    }

    public function all()
    {
        $data = OrganisationBordereauSubmission::with([
            'sector',
            'broker',
            'mga',
        ])->where('status', 'pending')->get();

        if ($data) {
            return response()->json([
                'status'  => 'success',
                'message' => 'Get all data.',
                'data'    => $data,
            ]);
        } else {
            return response()->json([
                'status'  => 'error',
                'message' => 'No Available Data.',
                'data'    => null,
            ]);
        }
    }

    public function store(Request $request)
    {
        $data          = $request->except('_token');
        $failedRows    = [];
        $notifications = [];
        foreach ($data['insert'] as $key => $value) {
            $value = (object)$value;

            if ($this->failsValidation($value)) {
                $failedRows[] = $key + 2;
            } else {

                $submissionData = [
                    'name' => $value->name,
                    'attached_organisation_type' => $value->attached_organisation_type,
                    'attached_organisation_id' => $value->attached_organisation_id,
                    'sector_id' => $value->sector_id,
                    'policy_type_id' => $value->policy_type_id,
                    'policy_number' => $value->policy_number,
                    'inception_date_of_cover' => $value->inception_date_of_cover,
                    'expiry_date_of_cover' => $value->expiry_date_of_cover,
                    'premium' => $value->premium,
                    'description' => $value->description,
                    'email' => $value->email,
                    'phone' => $value->phone,
                    'address_line_1' => $value->address_line_1,
                    'address_line_2' => $value->address_line_2,
                    'postcode' => $value->postcode,
                    'country' => $value->country,
                    'admin' => [
                        'first_name' => $value->admin_first_name,
                        'last_name' => $value->admin_last_name,
                        'job_title' => $value->admin_job_title,
                        'email' => $value->admin_email,
                        'phone' => $value->admin_phone,
                    ],
                    'manager' => $value->manager,
                    'croner_access' => $value->croner_access,
                    'status' => 'pending',
                ];

                OrganisationBordereauSubmission::create($submissionData);
                $notifications[] = "Upload completed.**success";
            }
        }

        if ($failedRows) {
            // return Response::json([
            //     'status' => 'error',
            //     'message' => 'Import failed due to empty columns in rows ' . implode(', ', $failedRows) . '.',
            // ]);
            $message = 'Import failed due to empty columns in rows ' . implode(', ', $failedRows) . '.';
            $notifications[] = $message . "**error";
        }

        $notifications = array_unique($notifications);

        return response()->json([
            'status'  => 'success',
            'message' => $notifications,
        ]);
    }

    protected function failsValidation($insert)
    {
        if (
            !isset($insert->name)
            || !isset($insert->email)
            || !isset($insert->phone)
            || !isset($insert->address_line_1)
            || !isset($insert->address_line_2)
            || !isset($insert->postcode)
            || !isset($insert->country)
            || !isset($insert->admin_first_name)
            || !isset($insert->admin_last_name)
            || !isset($insert->admin_job_title)
            || !isset($insert->admin_email)
            || !isset($insert->admin_phone)
            || !isset($insert->policy_number)
        ) {
            return true;
        }

        return false;
    }

    public function show(Request $request)
    {
        $data       = $request->get('submission_id');
        $submission = OrganisationBordereauSubmission::with([
            'broker',
            'mga',
            'sector',
        ])->where('_id', $data)->first();

        return response()->json([
            'status'  => 'success',
            'data'    => $submission,
            'message' => 'Upload completed.',
        ]);
    }

    public function reject(Request $request)
    {
        $data = $request->get('submission_ids');
        foreach ($data as $submission_id) {
            OrganisationBordereauSubmission::where('_id', $submission_id)->delete();
        }

        $message         = 'Submission/s have been deleted.';
        $notifications[] = $message . "**error";
        return response()->json([
            'status' => 'success',
            'message' => $notifications,
        ]);
    }

    public function approve(Request $request)
    {
        $data                   = $request->get('submission_ids');
        $is_individual_approval = $request->get('is_individual_approval');
        $notifications          = [];
        $success                = 0;

        foreach ($data as $submission_id) {
            $submission     = OrganisationBordereauSubmission::where('_id', $submission_id)->first();
            $safety_media   = substr(Str::uuid()->toString(), 0, 4);
            $orgEmailExists = Organisation::where('email', $submission->email)->first();
            $orgUserExists  = User::where('email', $submission->admin['email'])->first();

            if ($orgEmailExists || $orgUserExists) {
                if($is_individual_approval){
                    OrganisationBordereauSubmission::where('_id', $submission_id)
                    ->update(['status' => 'approved']);

                    $message = "Submission/s have been approved.";
                    $notifications[] = $message . "**success";

                    return response()->json([
                        'status' => 'success',
                        'message' => $notifications,
                    ]);
                }

                OrganisationBordereauSubmission::where('_id', $submission_id)
                ->update(['status' => 'approved']);
            }

            $newOrganisation = Organisation::create(
                [
                    'name'                    => $submission->name,
                    'email'                   => $submission->email,
                    'phone'                   => $submission->phone,
                    'address_line_1'          => $submission->address_line_1,
                    'address_line_2'          => $submission->address_line_2,
                    'postcode'                => $submission->postcode,
                    'country'                 => $submission->country,
                    'logo'                    => '',
                    'sector'                  => $submission->sector_id,
                    'status'                  => 0,
                    'description'             => $submission->description,
                    'policy_number'           => $submission->policy_number,
                    'inception_date_of_cover' => strtotime($submission->inception_date_of_cover),
                    'expiry_date_of_cover'    => strtotime($submission->expiry_date_of_cover),
                    'safetymedia_url'         => $safety_media,
                    'broker_id'               => ($submission->attached_organisation_type === "broker_organisation")
                        ? $submission->attached_organisation_id
                        : null,
                    'mga_scheme'              => ($submission->attached_organisation_type === "mga_scheme")
                        ? $submission->attached_organisation_id
                        : null,
                ]
            );

            OrganisationPolicy::create([
                'organisation_id'         => $newOrganisation->id,
                'policy_type_id'          => $submission->policy_type_id,
                'policy_number'           => $newOrganisation->policy_number,
                'inception_date_of_cover' => date("Y-m-d 00:00:00", strtotime($submission->inception_date_of_cover)),
                'expiry_date_of_cover'    => date("Y-m-d 00:00:00", strtotime($submission->expiry_date_of_cover)),
                'premium'                 => (isset($submission->premium) && !empty($submission->premium))
                    ? $submission->premium
                    : '-',
                'loss_ratio'              => '0',
                'renewal'                 => '1970-01-01 01:00:00',
            ]);

            $user = $this->createUser($request, $submission, $newOrganisation);

            // OrganisationBordereauSubmission::where('_id', $submission_id)
            //     ->update(['status' => 'approved']);
            $success = $success + 1;
        }

        $notifications = array_unique($notifications);

        if (isset($is_individual_approval) && !empty($is_individual_approval)) {
            if ($success > 0) {
                $message = "<b>Submission status approved. View </b> &nbsp; {$newOrganisation->name}'s &nbsp; <b>organisation record by clicking</b>
                    &nbsp; <a href='/organisation/" . $newOrganisation->id . "'>here.</a>";
                $notifications[] = $message . "**success";
            }
        } else {
            if ($success > 0) {
                $message = "Submission/s have been approved.";
                $notifications[] = $message . "**success";
            }
        }

        return response()->json([
            'status' => 'success',
            'message' => $notifications,
        ]);
    }

    public function createUser(Request $request, $submission, $organisation)
    {
        $data = $submission;

        $userData = [
            'email'                   => $data->admin['email'],
            'password'                => Hash::make(uniqid() + time()),
            'first_name'              => $data->admin['first_name'],
            'last_name'               => $data->admin['last_name'],
            'address_line_1'          => '',
            'address_line_2'          => '',
            'postcode'                => '',
            'country'                 => '',
            'manager'                 => 1,
            'branch'                  => '',
            'branch_name'             => '',
            'organisation_id'         => $organisation->id,
            'activation_code'         => substr(Str::uuid()->toString(), 0, 4),
            'activation_code_expires' => time() + (14 * 24 * 60 * 60), // 14 days
            'croner_access'           => 1,
            'safetymedia_access'      => 1,
            'astutis_access'          => '',
            'safetymedia_password'    => uniqid(),
            'c_live_access'           => '',
            'triton_access'           => '',
            'branch_id'               => '',
            'file'                    => null,
            'original_filename'       => null,
            'is_submission_created'   => 0,
            'phone'                   => $data->admin['phone'],
            'jobtitle'                => $data->admin['job_title'],
            'secondary_contact'       => 0,
        ];

        if (strtolower($request->get('cati_status')) == 'active' && $request->get('manager')) {
            $organisation = Organisation::find($request->get('organisation_id'));

            if ($organisation->cati_status != 'active' || !$organisation->cati_id) {
                return response()->json([
                    'response' => 'error',
                    'message' => '<b>User Creation Failed!</b><br/><i>Cati has not been provisioned for this user\'s organisation.</i><br/>If you wish to generate a user account with access to Cati, please enable Cati for this user\'s organisation first.',
                ], 200);
            }

            $userData['cati_status'] = $request->get('cati_status');

            try {
                $cati = new Cati();
                $response = $cati->createClientUser([
                    'ClientID'     => $organisation->cati_id,
                    'Forename'     => $userData['first_name'],
                    'Surname'      => $userData['last_name'],
                    'EmailAddress' => $userData['email'],
                    'Username'     => $userData['email'],
                ]);

                if ($response && $response->StatusCode == 'CU00') {
                    $userData['cati_id'] = $response->UserID;
                } else {
                    return response()->json(
                        [
                            'response' => 'error',
                            'message' => sprintf(
                                $this->catiErrorMessage,
                                $response->StatusCode,
                                $response->StatusDescription
                            ),
                        ]
                    );
                }
            } catch (Exception $e) {
                return response()->json([
                    'response' => 'error',
                    'message'  => sprintf($this->catiErrorMessage, $e->getCode(), $e->getMessage()),
                ]);
            }
        }

        $user           = User::create($userData);
        $organisation   = Organisation::find($user->organisation_id);
        $departmentName = $organisation->name;
        $sm_disabled    = ($user->safetymedia_access == '0') ? '1' : '0';
        $admin_level    = ($user->manager == '1') ? 'clientadmin' : 'user';

        if ($organisation->tpid == '' || is_null($organisation->tpid)) {
            $sm_account = config('app.sm.org_id_prefix') . $organisation->id;
        } else {
            $sm_account = $organisation->tpid;
        }

        $this->mail->queue(
            $user->email,
            $user->fullName(),
            'Risk Reduce, Welcome to Risk Reduce',
            'emails.auth.welcome',
            $user
        );

        if ($user->id) {
            $c_access  = User::find($user->id)->croner_access;
            $sm_access = User::find($user->id)->safetymedia_access;

            $sm_disabled = ($user->safetymedia_access == '0') ? '1' : '0';
            $admin_level = ($user->manager == '1') ? 'clientadmin' : 'user';

            $this->log(
                $user,
                'Create',
                'User Created',
                $request->header('session_ip'),
                $request->header('session_agent')
            );

            // Check for croner differences and record in logs
            if (isset($user->croner_access) && $user->croner_access == $c_access) {
                $this->log(
                    $user,
                    'Croner Access',
                    $user->croner_access,
                    $request->header('session_ip'),
                    $request->header('session_agent')
                );
            }

            // Check for safety media differences
            if (isset($user->safetymedia_access) && $user->safetymedia_access == $sm_access) {
                $this->log(
                    $user,
                    'Safety Media Access',
                    $user->safetymedia_access,
                    $request->header('session_ip'),
                    $request->header('session_agent')
                );
            }

            if ($request->has('login_type') && $request->get('login_type') == 'broker-user') {
                $user->org        = $user->organisation;
                $user->request_id = $user->id;

                $this->mail->queue(
                    config('app.admin_frontend'),
                    'Client User Created',
                    'Risk Reduce, Client User Creation Notification',
                    'emails.users.created',
                    $user
                );
            }

            return response()->json([
                'response' => 'success',
                'message'  => 'User created successfully.',
                'data'     => $user->id,
            ]);
        } else {
            return response()->json([
                'response' => 'error',
                'message'  => 'Unable to create user.',
            ], 200);
        }
    }

    private function log($user, $action, $description, $session_ip = '127.0.0.1', $session_agent = 'No UserAgent')
    {
        $activity               = new Activity();
        $activity->content_id   = $user->id;
        $activity->user_id      = (string)$user->id;
        $activity->content_type = 'User';
        $activity->action       = $action;
        $activity->description  = $description;
        $activity->details      = 'Username: ' . $user->fullName();
        $activity->ip_address   = $session_ip;
        $activity->user_agent   = $session_agent;
        $activity->save();
    }
}
