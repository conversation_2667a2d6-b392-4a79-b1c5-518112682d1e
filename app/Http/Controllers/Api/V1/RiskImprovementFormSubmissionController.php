<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\Helper;
use App\Http\Controllers\BaseController;
use App\Models\BrokerUser;
use Illuminate\Support\Facades\Cache;
use App\Models\Documentlevels;
use App\Models\ExternalSurveyor;
use App\Models\Formy;
use App\Models\FormyDocuments;
use Illuminate\Database\Eloquent\Relations\Relation;
use App\Models\LegacyRiskImprovementFormySubmissions;
use App\Models\LegacySurveyFiles;
use App\Models\LibertyUser;
use App\Models\Mailqueue;
use App\Models\Messaging;
use App\Models\Organisation;
use App\Models\OrganisationContact;
use App\Models\OrganisationPolicy;
use Illuminate\Support\Facades\Response;
use App\Models\RiskGrading\RgLocationGrading;
use App\Models\RiskGrading\RgLocationGradingLog;
use App\Models\RiskGradingLog;
use App\Models\RiskGradingsFormy;
use App\Models\RiskImprovementFormy;
use App\Models\RiskImprovementFormySubmissions;
use App\Models\RiskRecommendationFormy;
use App\Models\RiskRecommendationNotification;
use App\Models\Survey;
use App\Models\RiskGrading;
use App\Models\SurveyCommentaryInfo;
use App\Models\Surveyfiles;
use App\Models\UnderwriterPolicies;
use App\Models\User;
use App\Services\QueueService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

class RiskImprovementFormSubmissionController extends BaseController
{
    public $queue;

    public function __construct(Mailqueue $queue)
    {
        $this->queue = $queue;
    }

    public function rrAll(Request $request)
    {
        $organisation_id = $request->has('organisation_id')
            ? $request->get('organisation_id')
            : null;
        $org_id = $request->has('organisation')
            ? $request->get('organisation')
            : null;
        $organisation_id = $organisation_id > $org_id
            ? $organisation_id
            : $org_id;

        if ($organisation_id != null && $organisation_id != 0) {
            $survey_ids = Survey::where('organisation_id', '=', $organisation_id)->pluck('id')->all();
            $survey_ids = array_map('strval', $survey_ids);
            $submissions = RiskImprovementFormySubmissions::whereIn('survey_id', $survey_ids)->get();
        } else {
            $survey_ids = Survey::whereNull('deleted_at')
                ->whereNotNull('resurvey_id')->groupBy('resurvey_id')->pluck('resurvey_id')->all();
            $survey_ids = array_map('strval', $survey_ids);

            $submissions = RiskImprovementFormySubmissions::where('csr_status', '=', 'submitted')
                ->whereNotIn('survey_id', $survey_ids)
                ->get();
        }

        $policies = ['1' => 'Property', '2' => 'Casualty', '3' => 'Commercial Combined'];

        $org_array = [];
        $label_array = [];
        $i = 0;

        foreach ($submissions as $ksub => $submission) {
            $s = [];
            // Cross Reference All Loss Estimates Related
            if (isset($submission->uwr_status) && !empty($submission->uwr_status) && $submission->uwr_status === "submitted") {
                $loss_estimate_form = RiskImprovementFormy::find($submission->form_id);

                $sections = explode(',', $loss_estimate_form->section_names);
                if (in_array("Loss Estimates", $sections)) {
                    foreach ($loss_estimate_form->fields as $lefKey => $lefValue) {
                        if (isset($lefValue['number']) && !empty($lefValue['number']) 
                            && isset($lefValue['number'][11]['name']) && !empty($lefValue['number'][11]['name']) 
                            && $lefValue['number'][11]['name'] === "PML_Property" 
                            && $lefValue['number'][11]['value'] === "1"
                        ) {
                            $loss_estimate_identified_fields[$submission->survey_id][$lefValue['number'][11]['name']] = $submission->{$lefValue['number'][1]['value']};
                            $submissions['loss_estimates'] = $loss_estimate_identified_fields;
                        }

                        if (isset($lefValue['number']) && !empty($lefValue['number']) 
                            && isset($lefValue['number'][12]['name']) && !empty($lefValue['number'][12]['name']) 
                            && $lefValue['number'][12]['name'] === "PML_Business_Interruption" 
                            && $lefValue['number'][12]['value'] === "1"
                        ) {
                            $loss_estimate_identified_fields[$submission->survey_id][$lefValue['number'][12]['name']] = $submission->{$lefValue['number'][1]['value']};
                            $submissions['loss_estimates'] = $loss_estimate_identified_fields;
                        }

                        if (isset($lefValue['multiply']) && !empty($lefValue['multiply']) 
                            && isset($lefValue['multiply'][13]['name']) && !empty($lefValue['multiply'][13]['name']) 
                            && $lefValue['multiply'][13]['name'] === "PML_Property" 
                            && $lefValue['multiply'][13]['value'] === "1"
                        ) {
                            $loss_estimate_identified_fields[$submission->survey_id][$lefValue['multiply'][13]['name']] = $submission->{$lefValue['multiply'][1]['value']};
                            $submissions['loss_estimates'] = $loss_estimate_identified_fields;
                        }

                        if (isset($lefValue['multiply']) && !empty($lefValue['multiply']) 
                            && isset($lefValue['multiply'][14]['name']) && !empty($lefValue['multiply'][14]['name']) 
                            && $lefValue['multiply'][14]['name'] === "PML_Business_Interruption" 
                            && $lefValue['multiply'][14]['value'] === "1"
                        ) {
                            $loss_estimate_identified_fields[$submission->survey_id][$lefValue['multiply'][14]['name']] = $submission->{$lefValue['multiply'][1]['value']};
                            $submissions['loss_estimates'] = $loss_estimate_identified_fields;
                        }
                    }
                }
            }

            //If submission is loading from cache
            Cache::forget('rrtrackerdata_' . $submission->_id);

            $cached_data = Cache::has('rrtrackerdata_' . $submission->_id)
                ? Cache::get('rrtrackerdata_' . $submission->_id)
                : (object)[];
            if (!$request->has('refresh_cache') && (isset($cached_data->_id) && isset($cached_data->updated_at) && strtotime($cached_data->updated_at) >= strtotime($submission->updated_at)) || (isset($cached_data->_id) && !isset($cached_data->updated_at))) {
                $submissions[$i] = Cache::get('rrtrackerdata_' . $submission->_id);
                $label_array[] = isset($submissions[$i]->label_array) && isset($submissions[$i]->label_array[0])
                    ? $submissions[$i]->label_array[0]
                    : [];
            } else {
                $risk_rec_data = [];
                $submission->survey = $submission->survey;
                $submission->surveyContacts = $submission->surveyContacts;
                if (!isset($submission->survey->organisation_id) || is_null($submission->survey->organisation)) {
                    continue;
                }
                $organisation_id = $submission->survey->organisation_id;
                if (array_key_exists($organisation_id, $org_array)) {
                    $submission->organisation = $org_array[(string)$organisation_id]['organisation'];
                    $submission->organisation->branch = $submission->organisation->libertyBranch();
                } else {
                    $submission->organisation = $submission->survey->organisation;
                    $submission->organisation->branch = $submission->organisation->libertyBranch();
                    $org_array[(string)$organisation_id]['organisation'] = $submission->organisation;
                    $org_array[(string)$organisation_id]['branch'] = $submission->organisation->branch;
                }

                $submission->schedule = $submission->schedule();
                $risk_rec_array = [];


                $formid = $submission->form_id;

                $form = RiskImprovementFormy::find($formid);

                if (isset($form->fields)) {
                    $fields = $form->fields;
                    $rrstatus = array_map(
                        function ($v) {
                            if (isset($v['risk_recommendation'])) {
                                return array_map(
                                    function ($vv) {
                                        return array_map(
                                            function ($vc) {
                                                $arr = [];
                                                if ($vc['name'] == 'section' || $vc['name'] == 'prefix') {
                                                    array_push($arr, $vc);
                                                }
                                                return $arr;
                                            }, $vv
                                        );
                                    }, $v
                                );
                            }
                        }, $fields
                    );


                    $codearray = [];
                    foreach ($rrstatus as $k => &$rrstatus) {
                        if (isset($rrstatus['risk_recommendation'])) {
                            $code = '';
                            $label = '';
                            foreach ($rrstatus['risk_recommendation'] as $rrrec) {
                                if (count($rrrec) > 0) {
                                    if ($rrrec[0]['name'] == 'section') {
                                        $label = $rrrec[0]['value'];
                                    }
                                    if ($rrrec[0]['name'] == 'prefix') {
                                        $code = $rrrec[0]['value'];
                                    }
                                }
                                $codearray[] = ['code' => $code, 'label' => $label];
                            }
                        }
                    }

                    $label = array_filter(
                        $codearray, function ($v) use (&$codearray) {
                            return ($v['code'] != '' && $v['label'] != '');
                        }
                    );
                    $label_array[] = array_values($label);
                    $submission->label_array = $label_array;
                }

                $rr_exists = RiskRecommendationFormy::where('form_id', '=', $submission->form_id)->first();

                if (isset($rr_exists->risk_recommendation_fields)) {
                    $submission->risk_recommendations = $rr_exists->risk_recommendation_fields;
                }

                if (!isset($submission->risk_recommendations)) {
                    $form = RiskImprovementFormy::find($submission->form_id);
                    // print_r($form->fields); exit;
                    if (isset($form->fields)) {
                        foreach ($form->fields as $field) {
                            foreach ($field as $key => $value) {
                                if ($key == 'risk_recommendation') {
                                    //print_r($value); exit;
                                    foreach ($value as $val) {
                                        if ($val['name'] == 'name') {
                                            array_push($risk_rec_array, $val['value']);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    $submission->risk_recommendations = $risk_rec_array;

                    $risk_rec_data['form_id'] = $submission->form_id;
                    $risk_rec_data['risk_recommendation_fields'] = $risk_rec_array;

                    $insertRiskRec = RiskRecommendationFormy::insert($risk_rec_data);
                }

                $risk_recs = $submission->risk_recommendations;
                $rrm_array = [];

                foreach ($risk_recs as $risk_rec) {
                    for ($i = 1; $i <= 15; ++$i) {
                        if (isset($submission->{$risk_rec . '_' . $i . '_classification'}) && $submission->{$risk_rec . '_' . $i . '_classification'} != '') {
                            $messages = Messaging::where(
                                'survey_id', '=',
                                (string)$submission->survey->id
                            )->where(
                                'rr_ref', '=',
                                $risk_rec . '_' . $i . '_message', 'AND'
                            )->get();
                            $rrm_array['' . $risk_rec . '_' . $i . '_message' . ''] = $messages;
                        }
                    }
                }

                $submission->rrm = $rrm_array;

                if (isset($submission->survey)) {
                    $policy = OrganisationPolicy::find($submission->survey->policy_id);
                    if ($policy && in_array($policy->policy_type_id, ['1', '2', '3'])) {
                        $submission->survey->policy_name = $policies[$policy->policy_type_id];
                    }
                }
                //Cache::forever('rrtrackerdata_'.$submission->_id, $submission);
            }
            ++$i;
        }

        if ($request->has('refresh_cache') && !$request->has('admin_call')) {
            return Response::json(['response' => 'success']);
        }

        $labelcode = [];

        foreach ($label_array as $labels) {
            foreach ($labels as $label) {
                $label = (object)$label;
                $labelcode[$label->code] = $label->label;
            }
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => $submissions,
            'label_array' => $labelcode,
            'total' => count($submissions),
            ]
        );
    }

    public function rrAllForTracker(Request $request)
    {
        ini_set('max_execution_time', 0);
        // Return the response is results based on query params are cached
        // and refresh_cache is not provided
        $cacheIndex = 'rrtracker_data_query_' . implode('_', [
            $request->get('organisation'),
            $request->get('keyaccount')
        ]);

        if (Cache::has($cacheIndex) && !$request->has("refresh_cache")) {
            return Response::json([
                'response' => 'success',
                'data' => Cache::get($cacheIndex),
                'total' => count(Cache::get($cacheIndex)),
            ]);
        }
        $organisation_id = $request->has('organisation') ? $request->get('organisation') : null;

        $o_id = $organisation_id;
        
        if (!in_array($organisation_id, [null, '0', 0, 'All'])) {
            $survey_ids= Survey::where('organisation_id','=',$organisation_id)->pluck('id')->toArray();
            $survey_ids=array_map('strval',$survey_ids);            
            $submissions = RiskImprovementFormySubmissions::with('survey', 'survey.organisation')
                ->whereIn('survey_id', $survey_ids)
                ->get();
        } elseif ($request->has('keyaccount') && $request->get('keyaccount') == 1) {
            $organisationIds = OrganisationContact::select('organisation_id')
                ->where('type', 'risk-engineer')
                ->pluck('organisation_id')
                ->toArray();
            $surveyIds = Survey::select('id')
                ->whereIn('organisation_id', $organisationIds)
                ->pluck('id')
                ->map(function($id) { return (string)$id; })
                ->toArray();
            $submissions = RiskImprovementFormySubmissions::with([
                    'surveyContacts',
                    'survey',
                    'survey.organisation',
                    'survey.organisation.branch',
                ])->whereIn('survey_id', $surveyIds)->get();
        } else {
            if(Cache::has("rrtracker_data_allsubmissions_0") && !$request->has("refresh_cache")) {
                $subsArr = [];
                $numberChunks = Cache::get("rrtracker_submission_chunks");
                for($g = 0; $g<=$numberChunks-1; $g++) {
                        $subsArr = $subsArr+Cache::get("rrtracker_data_allsubmissions_".$g);
                }

                return Response::json(array('response' => 'success', 'data' => $subsArr, 'total' => count($subsArr) ));
            }

            // Uncomment this when in development
            // $from = Carbon::createFromTimestamp(strtotime('2023-01-20 00:00:00'));
            $submissions = RiskImprovementFormySubmissions::with([
                'formy',
                'riskRecommendationForm',
                'survey' => [
                    'existingPolicyNumber',
                    'schedule' => [
                        'meta',
                    ],
                    'organisation' => [
                        'branch',
                    ],
                ],
            ])
                ->where('csr_status', '=', 'submitted')
                // Uncomment this when in development
                // ->where('updated_at', '>=', $from)
                ->get();
        }

        /**
         * Getting all messages related to survey ID at this
         * point instead of fetching them one by one inside the
         * `foreach` loop
         */
        $messageSurveyIds = $submissions
            ->pluck('survey.id')
            ->transform(fn($value, $key) => (string)$value)
            ->reject(fn($value) => empty($value))
            ->values();
        $surveyMessages = Messaging::whereIn('survey_id', $messageSurveyIds)->get();

        $policies = ['1' => 'Property', '2' => 'Casualty', '3' => 'Commercial Combined'];

        $org_array = [];

        $i = 0;
        $riskRecData = [];
        /** @var RiskImprovementFormySubmissions $submission */
        foreach ($submissions as $submission) {
            if (!$submission->survey) {
                continue;
            }

            $cached_data = Cache::has("rrtracker_data_".$submission->_id) ? Cache::get("rrtracker_data_".$submission->_id) : (object)[];
            if(!$request->has('refresh_cache') && (isset($cached_data->_id) && isset($cached_data->updated_at) && strtotime($cached_data->updated_at) >= strtotime($submission->updated_at)) ||  (isset($cached_data->_id) && !isset($cached_data->updated_at)) ) {
                $submissions[$i] = Cache::get("rrtracker_data_".$submission->_id);
            } else {
                $tmpRiskRecData = [];
                //$submission->survey = $submission->survey;
                //$submission->surveyContacts = $submission->surveyContacts;

                if (!isset($submission->survey->organisation_id) || is_null($submission->survey->organisation)) {
                    continue;
                }
                $organisation_id = $submission->survey->organisation_id;
                if(array_key_exists($organisation_id, $org_array)) {
                    $submission->organisation = $org_array[(string)$organisation_id]['organisation'];
                    $submission->organisation->branch = $submission->organisation->branch;
                } else {
                    $org_array[(string)$organisation_id]['organisation'] = $submission->survey->organisation;
                    $org_array[(string)$organisation_id]['branch'] = $submission->survey->organisation->branch;
                }

                $submission->schedule = $submission->formatSchedule($submission->survey->schedule);
                $risk_rec_array = [];

//                $rr_exists = RiskRecommendationFormy::select('risk_recommendation_fields')->where('form_id','=',$submission->form_id)->first();
                $rr_exists = $submission->riskRecommendationForm;

                if(isset($rr_exists->risk_recommendation_fields)) {
                    $form = $submission->formy;
                    // print_r($form->fields); exit;
                    if(isset($form->fields)) {
                      foreach ($form->fields as $field) {
                        foreach ($field as $key => $value) {
                          if($key == 'risk_recommendation') {
                            //print_r($value); exit;
                            foreach ($value as $val) {
                              if($val['name'] == 'name') {
                                $risk_rec_array[] = $val['value'];
                              }
                            }
                          }
                        }
                      }
                    }
                    $submission->risk_recommendations = $risk_rec_array;

                    $tmpRiskRecData['form_id'] = $submission->form_id;
                    $tmpRiskRecData['risk_recommendation_fields'] = $risk_rec_array;

                    $riskRecData[] = $tmpRiskRecData;
                }

                $risk_recs = $submission->risk_recommendations;
                $rrm_array = [];

                foreach ($risk_recs as $risk_rec) {
                    for($i=1; $i<=15; $i++) {
                        if(isset($submission->{$risk_rec.'_'.$i.'_classification'}) && $submission->{$risk_rec.'_'.$i.'_classification'} != '') {
                            foreach ($surveyMessages as $surveyMessage) {
                                $value = [];
                                if ($surveyMessage->rr_ref === $risk_rec . '_' . $i . '_message') {
                                    $value = $surveyMessage;
                                }
                                $rrm_array[$risk_rec . '_' . $i . '_message'] = $value;
                            }
                        }
                    }
                }

                $submission->rrm = $rrm_array;

                if(isset($submission->survey)) {
                    $policy = $submission->survey->existingPolicyNumber;
                    if($policy && in_array($policy->policy_type_id, ['1','2','3'])) {
                        $submission->survey->policy_name = $policies[$policy->policy_type_id];
                    }
                }
                Cache::put("rrtracker_data_".$submission->_id, $submission, 7200);
            }
            $i++;
        }

        // batch insert to reduce duplicated queries
        if (!empty($riskRecData)) {
            RiskRecommendationFormy::insert( $riskRecData ) ;
        }

        if($request->has('refresh_cache') && !$request->has('admin_call')) {
             if($o_id == null) {
                $submissions_array = json_decode(json_encode($submissions),true);
                \Log::info("submissions_array: ".count($submissions_array));
                $chunks = array_chunk($submissions_array,20,true);
                \Log::info("Chunks: ".count($chunks));
                Cache::forever("rrtracker_submission_chunks", count($chunks));
                for($c = 0; $c <= count($chunks)-1; $c++) {
                    Cache::forever("rrtracker_data_allsubmissions_".$c, $chunks[$c]);
                }
            }
            return Response::json(array('response' => 'success'));
        }

        // cache response if refresh_cache flag exist
        if (!Cache::has($cacheIndex) && $request->has("refresh_cache")) {
            Cache::forever($cacheIndex, $submissions);
        }

        return Response::json([
            'response' => 'success',
            'data' => $submissions,
            'total' => count($submissions),
        ]);
    }

    public function flatten(array $array)
    {
        $return = [];
        array_walk_recursive(
            $array, function ($a) use (&$return) {
                $return[] = $a;
            }
        );
        return $return;
    }

    public function rrGradingsAll(Request $request)
    {
        $finalSubmissions = [];
        $offset           = 0;
        $policies         = ['1' => 'Property', '2' => 'Casualty', '3' => 'Commercial Combined'];
        $organisation_id  = $request->has('organisation_id') ? $request->get('organisation_id') : null;
        $submissionCount  = RiskImprovementFormySubmissions::select('_id')->where('csr_status', '=', 'submitted')->get()->count();      

        if ($submissionCount) {
            while (true) {
                if ($organisation_id != null) {
                    $submissions = RiskImprovementFormySubmissions::with('survey', 'formy')->where('organisation_id', '=', $organisation_id)
                        ->where('csr_status', '=', 'submitted', 'AND')
                        ->limit(500)
                        ->offset($offset);
                } else {
                    $submissions = RiskImprovementFormySubmissions::with('survey', 'formy')->where('csr_status', '=', 'submitted')
                        ->limit(500)
                        ->offset($offset);
                }
    
                if (empty($submissions->get()->count())) {
                    // break the loop once no submissions is fetch
                    break;
                }
    
                $i = 0;
                foreach ($submissions->get() as $submission) {
                    $cachedData = Cache::has('rrgradingsdata_' . $submission->_id) ? Cache::get('rrgradingsdata_' . $submission->_id) : (object) [];
                    if (!$request->has('refresh_cache') && (isset($cachedData->_id) && isset($cachedData->updated_at) 
                        && strtotime($cachedData->updated_at) >= strtotime($submission->updated_at)) || (isset($cachedData->_id) && !isset($cachedData->updated_at))) {
                        $finalSubmissions[$i] = Cache::get('rrgradingsdata_' . $submission->_id);
                    } else {
                        $riskGradingsData = [];
                        $submission->survey = $submission->survey;
                        if (!isset($submission->survey->organisation_id)) {
                            continue;
                        }
                        
                        $riskGradingsArray = [];
                        $organisation_id   = $submission->survey->organisation_id;
                        $rrGradingsExists  = RiskGradingsFormy::where('form_id', '=', $submission->form_id)->first();
                        if (isset($rrGradingsExists->risk_gradings_fields)) {
                            $submission->risk_gradings = $rrGradingsExists->risk_gradings_fields;
                        }
    
                        if (!isset($submission->risk_gradings)) {
    
                            if (isset($submission->formy->fields)) {
                                foreach ($submission->formy->fields as $field) {
                                    foreach ($field as $key => $value) {
                                        if ($key == 'select_risk_control') {
                                            foreach ($value as $val) {
                                                if ($val['name'] == 'name') {
                                                    $riskGradingsArray[(string) $val['value']] = $value[0]['value'];
                                                }
                                            }
                                        }
                                    }
                                }
                            }
    
                            $submission->risk_gradings                = $riskGradingsArray;
                            $riskGradingsData['form_id']              = $submission->form_id;
                            //$riskGradingsData['risk_gradings_fields'] = $risk_gradings_array;
                            $insertRiskRec = RiskGradingsFormy::insert($riskGradingsData);
                        }
    
                        if (isset($submission->survey)) {
                            $policy = OrganisationPolicy::find($submission->survey->policy_id);
                            if ($policy && in_array($policy->policy_type_id, ['1', '2', '3'])) {
                                $submission->survey->policy_name = $policies[$policy->policy_type_id];
                            }
                        }
    
                        $finalSubmissions[] = $submission;
                        Cache::forever('rrgradingsdata_' . $submission->_id, $submission);
                    }
                    ++$i;
                    $offset += 500;
                }
            }
        }

        if ($request->has('refresh_cache') && !$request->has('admin_call')) {
            return Response::json(array('response' => 'success'));
        }

        return Response::json(array('response' => 'success', 'data' => $finalSubmissions, 'total' => $submissionCount));
    }

    public function savedFormsForOrg($organisation_id, $user_id, $manager = 0, $level1 = null, $level3 = null)
    {
        if (!is_null($level1) && !is_null($level3)) {
            $level1_data = Documentlevels::where('level_name', '=', urldecode($level1))->first();
            $level1 = $level1_data->level_id;

            $level3_data = Documentlevels::where('level_name', '=', urldecode($level3))->first();
            $level3 = $level3_data->level_id;
        }

        if ($manager == '1') {
            $data = RiskImprovementFormySubmissions::where('organisation_id', '=', $organisation_id)->where(
                'submitted',
                '=', '0'
            )->get();
        } else {
            $data = RiskImprovementFormySubmissions::where('user_id', '=', $user_id)->where(
                'submitted', '=',
                '0'
            )->get();
        }

        foreach ($data as $key => $submission) {
            $form = Formy::find($submission->form_id);
            if (isset($form->formType) && $form->formType == 'accident-reporting') {
                unset($data[$key]);
            } else {
                if (isset($form->name)) {
                    $submission['form_name'] = $form->name;
                }
                if (isset($form->fileUploads)) {
                    $submission['fileUploads'] = $form->fileUploads;
                }
                if (!is_null($level1) && !is_null($level3)) {
                    if (isset($form->level1_type) && isset($form->level3_type)) {
                        if ($level1 != $form->level1_type || $level3 != $form->level3_type) {
                            unset($data[$key]);
                        }
                    } else {
                        unset($data[$key]);
                    }
                }
            }
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => json_encode($data->toArray()),
            ]
        );
    }

    /*
     * get incomplete submissions from a user
     */

    public function submittedFormsForOrg($organisation_id, $user_id, $manager = 0, $level1 = null, $level3 = null)
    {
        if (!is_null($level1) && !is_null($level3)) {
            $level1_data = Documentlevels::where('level_name', '=', urldecode($level1))->first();
            $level1 = $level1_data->level_id;

            $level3_data = Documentlevels::where('level_name', '=', urldecode($level3))->first();
            $level3 = $level3_data->level_id;
        }

        if ($manager == '1') {
            $data = RiskImprovementFormySubmissions::where('organisation_id', '=', $organisation_id)->where(
                'submitted',
                '=', '1'
            )->get();
        } else {
            $data = RiskImprovementFormySubmissions::where('user_id', '=', $user_id)->where(
                'submitted', '=',
                '1'
            )->get();
        }

        foreach ($data as $key => $submission) {
            $form = Formy::find($submission->form_id);
            if (isset($form->formType) && $form->formType == 'accident-reporting') {
                unset($data[$key]);
            } else {
                if (isset($form->name)) {
                    $submission['form_name'] = $form->name;
                }
                if (isset($form->fileUploads)) {
                    $submission['fileUploads'] = $form->fileUploads;
                }
                if (!is_null($level1) && !is_null($level3)) {
                    if (isset($form->level1_type) && isset($form->level3_type)) {
                        if ($level1 != $form->level1_type || $level3 != $form->level3_type) {
                            unset($data[$key]);
                        }
                    } else {
                        unset($data[$key]);
                    }
                }
            }
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => json_encode($data->toArray()),
            ]
        );
    }

    /*
     * get submitted forms from a user
     */

    public function submissions(Request $request, $form)
    {
        if ($request->has('limit')) {
            $data = RiskImprovementFormySubmissions::where('form_id', '=', $form)->where(
                'uwr_status', '=',
                'submitted'
            );
            if (($request->has('dateFrom') && $request->get('dateFrom') != '')) {
                $from = Carbon::createFromTimestamp(strtotime($request->get('dateFrom') . ' 00:00:00'));
                $data = $data->where('updated_at', '>=', $from);
            }
            if (($request->has('dateTo') && $request->get('dateTo') != '')) {
                $to   = Carbon::createFromTimestamp(strtotime($request->get('dateTo') . ' 23:59:59'));
                $data = $data->where('updated_at', '<=', $to);
            }
            $data = $data->orderBy('updated_at', 'DESC')->limit($request->get('limit'))->get();
        } else {
            $data = RiskImprovementFormySubmissions::where('form_id', '=', $form)->where(
                'uwr_status', '=',
                'submitted'
            )->get();
        }

        foreach ($data as $submission) {
            $submission->survey = Survey::where('id', '=', (int)$submission->survey_id)->with('organisation')->get();
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => json_encode($data->toArray()),
            ]
        );
    }

    /*
     * get submissions for a form
     */

    public function updateREAction(Request $request)
    {
        $data = $request->all();
        $submission = [
            $data['attribute'] => $data['value'],
            $data['comment_by_key'] => $data['comment_by_val'],
            $data['comment_key'] => $data['comment_val'],
        ];
        $update = RiskImprovementFormySubmissions::whereIn(
            'survey_id',
            [(string)$data['survey_id'], (int)$data['survey_id']]
        )->update($submission);
        RiskRecommendationNotification::monitor($data['survey_id']);

        if ($update) {
            return Response::json(
                [
                'response' => 'success',
                'data' => '',
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'data' => '',
                ]
            );
        }
    }

    public function all(Request $request)
    {
        $organisation_id = $request->has('organisation_id')
            ? $request->get('organisation_id')
            : null;

        if ($organisation_id != null) {
            $submissions = RiskImprovementFormySubmissions::where('organisation_id', '=', $organisation_id);
        } elseif ($request->has('org_id')) {
            // for the survey export organisation colourful survey findings thingy

            $surveys = Survey::where('organisation_id', $request->get('org_id'))->pluck('id', 'id')->all();

            $surveys = array_values($surveys);

            foreach ($surveys as $key => $value) {
                $value = (string)$value;

                array_push($surveys, $value);
            }

            $submissions = RiskImprovementFormySubmissions::whereIn('survey_id', $surveys)->get();
        } else {
            if (!$request->has('export_all')) {
                $submissions = RiskImprovementFormySubmissions::all();
            } else {
                $submissions = RiskImprovementFormySubmissions::where('csr_status', 'submitted')->get();
            }
        }

        $policies = ['1' => 'Property', '2' => 'Casualty', '3' => 'Commercial Combined'];

        if (!$request->has('export_all')) {
            foreach ($submissions as $submission) {
                static::getRelations($submission);
                if (isset($submission->survey)) {
                    $policy = OrganisationPolicy::find($submission->survey->policy_id);
                    if ($policy && in_array($policy->policy_type_id, ['1', '2', '3'])) {
                        $submission->survey->policy_name = $policies[$policy->policy_type_id];
                    }
                }
            }
        }

        return Response::json(['response' => 'success', 'data' => $submissions, 'total' => count($submissions)]);
    }

    /*
    * get submissions for a form for an organisation
    */

    private function getRelations(&$item)
    {
        $relations = [
            'formy',
            'survey',
            'hasOpenRecommendations',
            // 'survey.organisation',
            'schedule',
        ];

        foreach ($relations as $relation) {
            if (is_a($item->$relation(), Relation::class)) {
                $item->$relation;
            } else {
                $item->$relation = $item->$relation();
            }

            unset($item->{sprintf('%s_id', $relation)});
        }
    }

    /*
    * get submissions for a form for an organisation
    */

    private function parse_attendees_info($data){

        $attendees = [];

        $nameCount = count($data['attendee_name'] ?? []);
        $titleCount = count($data['attendee_job_title'] ?? []);
        $orgCount = count($data['attendee_organisation'] ?? []);

        if ($nameCount === $titleCount && $nameCount === $orgCount) {
            for ($i = 0; $i < $nameCount; $i++) {
                $attendees[] = [
                    'attendee_name' => $data['attendee_name'][$i],
                    'attendee_job_title' => $data['attendee_job_title'][$i],
                    'attendee_organisation' => $data['attendee_organisation'][$i]
                ];
            }
        }

        unset($data['attendee_name']); 
        unset($data['attendee_job_title']); 
        unset($data['attendee_organisation']); 

        $data['survey_attendees_details'] = $attendees;

        return $data;

    }

    public function update(Request $request, $submission_id)
    {
        $data = $request->except('_id');
        $isAutosave = (bool)data_get($data, 'autosave', 0);
        $isFromUWR = (bool)data_get($data, 'is_uwr', 0);

        $data=$this->parse_attendees_info($data);

        if (isset($data['csr_status']) && $data['csr_status'] == 'submitted') {
            $data['csr_submission_date'] = date('d/m/Y');
        }
        if (isset($data['uwr_status']) && $data['uwr_status'] == 'submitted') {
            $data['uwr_submission_date'] = date('d/m/Y');
        }
        if (isset($data['rereview_status']) && $data['rereview_status'] == 'submitted') {
            $data['rereview_submission_date'] = date('d/m/Y');
        }

        $data['commentary_description'] = $data['commentary_description'] ?? [''];
        $data['commentary_additional_notes'] = $data['commentary_additional_notes'] ?? [''];

        $create_user    = [];
        $survey_details = Survey::with([
            'organisation',
            'schedule',
            'policyNumber.type',
            'contacts',
            'underwriter',
            'externalSurveyCompany',
            'location:id,city',
        ])->where('id', $data['survey_id'])->first();

        $surveyorId = $survey_details->surveyor_id ?? '';
        $data['surveyor_id'] = (string) $surveyorId;

        unset($data['is_uwr']); // remove this to avoid saving to ri_submissions

        if (isset($data['submitted_by']) && in_array($data['submitted_by'], ['underwriter', 'risk-control'])) {
            // $survey_details = Survey::find($data['survey_id']);
            $survey_details_submission = RiskImprovementFormySubmissions::whereIn(
                'survey_id',
                [(int)$data['survey_id'], (string)$data['survey_id']]
            )->first();
            if ($survey_details) {
                if (isset($survey_details_submission['csr_status']) && $survey_details_submission['csr_status'] == 'submitted') {
                    $data['csr_status'] = 'submitted';
                }
                if (isset($survey_details_submission['uwr_status']) && $survey_details_submission['uwr_status'] == 'submitted') {
                    $data['uwr_status'] = 'submitted';
                }
                $survey_type = $survey_details->survey_type;
                $underwriter_id = $survey_details->underwriter_id;
                if ($survey_type != 'rereview' && (($surveyorId == $underwriter_id && $data['submitted_by'] == 'underwriter') || ($data['submitted_by'] == 'risk-control'))) {
                    if (!isset($data['surveyor_id']) && isset($survey_details_submission['surveyor_id'])) {
                        $data['surveyor_id'] = $survey_details_submission['surveyor_id'];
                    }

                    if ($data['submitted_by'] != 'risk-control') {
                        $data['csr_status'] = $survey_details_submission['csr_status'];
                        $data['uwr_status'] = $survey_details_submission['uwr_status'];
                    }

                    $data = $this->addExistingSubmissionFields($data, $survey_details_submission);
                    $updateResult = DB::connection('mongodb')->collection('ri_submissions')
                    ->raw(function ($collection) use ($data) {
                        return $collection->replaceOne(
                            ['survey_id' => ['$in' => [$data['survey_id']]]],
                            $data
                        );
                    });
                    $submission = $updateResult->getModifiedCount();

                    RiskRecommendationNotification::monitor($data['survey_id']);
                } elseif ($survey_type == 'rereview' && $data['submitted_by'] == 'risk-control') {
                    if (!isset($data['surveyor_id']) && isset($survey_details_submission['surveyor_id'])) {
                        $data['surveyor_id'] = $survey_details_submission['surveyor_id'];
                    }

                    if (!isset($data['rereview_status']) && isset($survey_details_submission['rereview_status'])) {
                        $data['rereview_status'] = $survey_details_submission['rereview_status'];
                    }

                    $data = $this->addExistingSubmissionFields($data, $survey_details_submission);
                    $updateResult = DB::connection('mongodb')->collection('ri_submissions')
                    ->raw(function ($collection) use ($data) {
                        return $collection->replaceOne(
                            ['survey_id' => ['$in' => [$data['survey_id']]]],
                            $data
                        );
                    });
                    $submission = $updateResult->getModifiedCount();
                    RiskRecommendationNotification::monitor($data['survey_id']);
                }
            }
        } else {
            // $survey_details = Survey::find($data['survey_id']);
            $survey_details_submission = RiskImprovementFormySubmissions::whereIn(
                'survey_id',
                [(int)$data['survey_id'], (string)$data['survey_id']]
            )->first();
            if ($survey_details) {
                if (isset($survey_details_submission['csr_status']) && $survey_details_submission['csr_status'] == 'submitted') {
                    $data['csr_status'] = 'submitted';
                }
                if (isset($survey_details_submission['uwr_status']) && $survey_details_submission['uwr_status'] == 'submitted') {
                    $data['uwr_status'] = 'submitted';
                }
                $survey_type = $survey_details->survey_type;

                $data = $this->addExistingSubmissionFields($data, $survey_details_submission);
                $updateResult = DB::connection('mongodb')->collection('ri_submissions')
                ->raw(function ($collection) use ($data) {
                    return $collection->replaceOne(
                        ['survey_id' => ['$in' => [$data['survey_id']]]],
                        $data
                    );
                });
                $submission = $updateResult->getModifiedCount();
                
                if (!$submission) {
                    $submission = RiskImprovementFormySubmissions::where('survey_id', '=', $data['survey_id'])->update($data);
                }

                RiskRecommendationNotification::monitor($data['survey_id']);
            }
        }

        // Save to survey commentary info collection
        if (
            !empty($data['commentary_title']) ||
            !empty($data['commentary_description']) ||
            !empty($data['commentary_additional_notes'])
        ) {

            $title = $data['commentary_title'] ?? [];
            $description = $data['commentary_description'] ?? [];
            $additionalNotes = $data['commentary_additional_notes'] ?? [];
            $visible_to_all = $data['narratives_visible_to_all_users'] ?? [];
            $csrStatus = $request->get('csr_status');

            if (!$isFromUWR) {
                $this->saveCommentaryInfo(
                    $data['survey_id'],
                    $title,
                    $description,
                    $additionalNotes,
                    $visible_to_all,
                    $csrStatus
                );
            }

            unset($data['commentary_title']);
            unset($data['commentary_description']);
            unset($data['commentary_additional_notes']);
            unset($data['narratives_visible_to_all_users']);
        }

        $this->updatePhotographCaptions($data['notes'] ?? []);

        if (!$isAutosave) {
            $this->sendQueueforRecaching($data['survey_id'] ?? '');

            if (!empty($data['survey_id'])) {
                Artisan::call('survey-submission:update-photo-graph-key --survey-id=' . $data['survey_id']);
            }
        }

        if (isset($submission) && $submission) {
            if (!$isAutosave && $request->get('rereview_status') == 'submitted') {
                $submission_details = RiskImprovementFormySubmissions::find($submission_id);
                // dd($submission_details->survey_id);
                $survey_details = Survey::find($submission_details->survey_id);

                // collect primary and secondary risk control emails
                $recipients = array_filter([config('app.risk_control'), config('app.risk_control_secondary')]);
                if (!empty($recipients)) {
                    foreach ($recipients as $recipient) {
                        $rc = LibertyUser::where('email', 'LIKE', $recipient)->first();
                        if (!$rc) {
                            continue;
                        }
                        $rc['survey_id'] = $submission_details->survey_id;
                        $this->queue->queue(
                            $rc['email'],
                            $rc->fullName(),
                            'Risk Reduce - New RE Review Submitted',
                            'emails.surveys.re_review_submitted',
                            $rc
                        );
                    }
                }

                $uwr = LibertyUser::where('id', '=', $survey_details->underwriter_id)->first();
                $uwr->survey_id = $submission_details->survey_id;
                $this->queue->queue(
                    $uwr['email'], $uwr->fullName(), 'Risk Reduce - New RE Review Submitted',
                    'emails.surveys.re_review_submitted', $uwr
                );
            }

            if ($request->get('csr_status') == 'submitted') {
                $submission_details = RiskImprovementFormySubmissions::find($submission_id);
                $survey_details = Survey::with(['location:id,city', 'organisation:id,name'])->find($submission_details->survey_id);

                if ($survey_details->external_survey_company_id && $survey_details->surveyor_id) {
                    $surveyor = ExternalSurveyor::find($survey_details->surveyor_id);
                } else {
                    $surveyor = LibertyUser::find($survey_details->surveyor_id);
                }

                // collect primary and secondary risk control emails
                $recipients = array_filter([config('app.risk_control'), config('app.risk_control_secondary')]);
                if (!$isAutosave && !empty($recipients) && !$survey_details?->isDtr()) {
                    foreach ($recipients as $recipient) {
                        $rc = LibertyUser::where('email', 'LIKE', $recipient)->first();
                        if (!$rc) {
                            continue;
                        }
                        
                        $rc['survey_id']         = $submission_details->survey_id;
                        $rc['organisation_name'] = $survey_details['organisation_name'] ?? $survey_details['organisation']['name'];
                        $rc['location_name']     = $survey_details['location']['city'];
                        $rc['surveyor_name']     = $surveyor?->fullName();
                        $emailTemplate = $survey_details?->isDtr() ? 'emails.surveys.dtr_submitted' : 'emails.surveys.csr_submitted';
                        $this->queue->queue(
                            $rc['email'],
                            $rc->fullName(),
                            sprintf('Risk Reduce - New %s Submitted', $survey_details?->isDtr() ? 'Desktop Report' : 'CSR'),
                            $emailTemplate,
                            $rc
                        );
                    }
                }

                $orgRiskEngineers = OrganisationContact::where('organisation_id', $survey_details->organisation_id)
                    ->where('type', 'risk-engineer')
                    ->get();
                if (!$isAutosave && !empty($orgRiskEngineers)) {
                    foreach ($orgRiskEngineers as $re) {
                        $reUser = $re->libertyUser;
                        if (!empty($reUser)) {
                            $reUser['survey_id']         = $submission_details->survey_id;
                            $reUser['organisation_name'] = $survey_details['organisation_name'] ?? $survey_details['organisation']['name'];
                            $reUser['location_name']     = $survey_details['location']['city'];
                            $reUser['surveyor_name']     = $surveyor?->fullName();
                            $emailTemplate = $survey_details?->isDtr() ? 'emails.surveys.dtr_submitted' : 'emails.surveys.csr_submitted';
                            $this->queue->queue(
                                $reUser->email,
                                $reUser->fullName(),
                                sprintf('Risk Reduce - New %s Submitted', $survey_details?->isDtr() ? 'Desktop Report' : 'CSR'),
                                $emailTemplate,
                                $reUser
                            );
                        }
                    }
                }

                if ($survey_details->isDtr()) {
                    $orgUnderwriters = OrganisationContact::where('organisation_id', $survey_details->organisation_id)
                        ->where('type', 'underwriter')
                        ->get();
                    if (!$isAutosave && !empty($orgUnderwriters)) {
                        foreach ($orgUnderwriters as $uw) {
                            if (empty($uw->libertyUser->email)) {
                                continue;
                            }
                            $underwriter = $uw->libertyUser;
                            $underwriter['survey_id']         = $submission_details->survey_id;
                            $underwriter['organisation_name'] = $survey_details['organisation_name'] ?? $survey_details['organisation']['name'];
                            $underwriter['location_name']     = $survey_details['location']['city'];
                            $underwriter['surveyor_name']     = $surveyor?->fullName();
                            $emailTemplate = $survey_details->isDtr() ? 'emails.surveys.dtr_submitted' : 'emails.surveys.csr_submitted';
                            $this->queue->queue(
                                $underwriter->email,
                                $underwriter->fullName(),
                                sprintf('Risk Reduce - New %s Submitted', $survey_details->isDtr() ? 'Desktop Report' : 'CSR'),
                                $emailTemplate,
                                $underwriter
                            );
                        }
                    }
                }
                
                // Send Email to Underwriter if there are no assigned ORG Risk Engineers
                $underwriter = $survey_details->underwriter;
                if (!$isAutosave && $orgRiskEngineers?->isEmpty() && !empty($underwriter)) {
                    $underwriter->survey_id         = $survey_details['id'];
                    $underwriter->organisation_name = $survey_details['organisation_name'] ?? $survey_details['organisation']['name'];
                    $underwriter->location_name     = $survey_details['location']['city'];
                    $underwriter->surveyor_name     = $surveyor?->fullName();
                    $emailTemplate = $survey_details?->isDtr() ? 'emails.surveys.dtr_submitted' : 'emails.surveys.csr_submitted';
                    $this->queue->queue(
                        $underwriter->email, $underwriter->fullName(), 
                        sprintf('Risk Reduce - New %s Submitted', $survey_details?->isDtr() ? 'Desktop Report' : 'CSR'),
                        $emailTemplate,
                        $underwriter
                    );
                }

                if (!$isAutosave && !empty($request->csr_external_emails)) {
                    $this->sendCsrDtrInvitedEmail($request, $survey_details);
                }

                if (isset($data['risk_reduce_access_email']) && $data['risk_reduce_access_email'] != '') {
                    $survey = Survey::find($data['survey_id']);
                    $create_user = [
                        'organisation_id' => $survey['organisation_id'],
                        'first_name' => $data['risk_reduce_access_first_name'],
                        'last_name' => $data['risk_reduce_access_last_name'],
                        'email' => $data['risk_reduce_access_email'],
                        'safetymedia_access' => 0,
                        'croner_access' => 0,
                        'c_live_access' => 0,
                    ];

                    foreach ($data['risk_reduce_access_services'] as $service) {
                        if ($service == 'safety_media') {
                            $create_user['safetymedia_access'] = 1;
                        } elseif ($service == 'croner') {
                            $create_user['croner_access'] = 1;
                        } elseif ($service == 'cq_live') {
                            $create_user['c_live_access'] = 1;
                        }
                    }
                }
            }

            $hasLocationId = true;
            if (isset($data['csr_submission_date']) && !empty($data['csr_submission_date'])) {
                $hasLocationId = RgLocationGrading::updateLocationGradings($data);
                RgLocationGradingLog::createLocationGradingLog($data);
            }

            if (!$isAutosave && $request->get('uwr_status') == 'submitted') {
                $submission_details = RiskImprovementFormySubmissions::find($submission_id);
                $survey_details = Survey::find($submission_details->survey_id);
                if (!empty($survey_details->underwriter)) {
                    $underwriter = $survey_details->underwriter;
                    $underwriter->survey_id = $submission_details->survey_id;
                    $this->queue->queue(
                        $underwriter->email,
                        $underwriter->fullName(),
                        'Risk Reduce - New UWR Submitted',
                        'emails.surveys.uwr_submitted',
                        $underwriter
                    );
                }
            }

            try {
                if (!empty($data['csr_next_survey_due_date'])) {
                    $nextSurveyDueDate = Carbon::createFromFormat('d/m/Y', $data['csr_next_survey_due_date'])->format('Y-m-d');
                    $survey_details->next_survey_due_date = $nextSurveyDueDate;
                    $survey_details->save();
                }
            } catch (\Exception $e) {
                \Log::info('[Error] Next Survey Due Date: ' . $e->getMessage());
            }
            
            if ($survey_details_submission) {
                Artisan::call('riskrec:cards ' . $survey_details_submission->id);
                Artisan::call('riskrec:survey_cards ' . $survey_details_submission->survey_id);
            }

            // Re cache ri_submission_{survey id} every update
            $sub_cache = RiskImprovementFormySubmissions::select(
                '_id', 'csr_next_survey_due_date', 'csr_status',
                'uwr_status'
            )->whereIn('survey_id', [(int)$data['survey_id'], (string)$data['survey_id']])->first();
            Cache::forever('ri_submission_' . $data['survey_id'], $sub_cache);

            $this->updateRiskGrade($data['survey_id']);

            return Response::json(
                [
                'response' => 'success',
                'data' => $data,
                'type' => $survey_type,
                'create_user' => $create_user,
                'has_location_id' => $hasLocationId,
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to update form submission',
                ]
            );
        }
    }

    private function updateRiskGrade($submission_id)
    {
        $survey = Survey::with('location')->where('id', (int)$submission_id)->first();
        $survey_m = RiskImprovementFormySubmissions::where('survey_id', (string)$submission_id)->first();

        if ((isset($survey_m->_id) && !empty($survey_m->_id))
        ) {
            $form_submissions = RiskImprovementFormySubmissions::find($survey_m->_id);
            $data = RiskImprovementFormy::find($form_submissions->form_id);

            // ensures only with data will be imported
            if ($data) {
                $form = json_decode($data, true);

                $file_data = [];
                $rr_data = [];
                $grading_data = [];

                if (isset($form) && isset($form['fields'])) {
                    $file_data_in = [];
                    foreach ($form['fields'] as $field_types) {
                        foreach ($field_types as $field_type => $field_attr) {
                            $file_data_in_name = 'no-name';
                            $file_data_in = [];

                            if ($field_type == 'select_risk_control') {
                                $arr = [];

                                foreach ($field_attr as $element) {
                                    if ($element['name'] == 'name') {
                                        $arr['name'] = $element['value'];
                                    }
                                    if ($element['name'] == 'label') {
                                        $arr['label'] = $element['value'];
                                    }
                                }
                                array_push($grading_data, $arr);
                            }
                        }
                    }
                }

                if (isset($grading_data) && !empty($grading_data) && $survey['location']) {
                    foreach ($grading_data as $grading) {
                        $store_data['location_id'] = $survey['location']->location_id;
                        $store_data['attribute'] = $grading['label'];
                        $store_data['value'] = $form_submissions->{$grading['name']};
                        $store_data['organisation_location_id'] = $survey['location']->id;
                        $store_data['policy_id'] = $survey['policy_id'];

                        $risk_grading = RiskGrading::firstOrCreate(
                            [
                            'organisation_location_id' => $store_data['organisation_location_id'],
                            'location_id' => $store_data['location_id'],
                            'attribute' => $store_data['attribute'],
                            ]
                        );

                        $risk_grading->value = $store_data['value'];
                        $risk_grading->policy_id = $store_data['policy_id'];
                        $risk_grading->save();

                        RiskGradingLog::create(
                            [
                            'risk_gradings_id' => $risk_grading->id,
                            'location_id' => $risk_grading->location_id,
                            'policy_id' => $risk_grading->policy_id,
                            'value' => $store_data['value'],
                            ]
                        );
                    }
                }
            }
        }
    }

    public function submissionsOrg($form, $organisation_id)
    {
        $data = RiskImprovementFormySubmissions::where('form_id', '=', $form)->where(
            'submitted', '=', '1',
            'AND'
        )->where('organisation_id', '=', $organisation_id, 'AND')->get();
        $branches = User::where('organisation_id', '=', $organisation_id)->where(
            'branch', '=', '1',
            'AND'
        )->where('activated', '=', '1', 'AND')->get();
        $branch = [];
        foreach ($branches as $b) {
            $branch[$b['id']] = $b['branch_name'];
        }
        //print_r($branch); exit;
        $branch_string = 'branch-where-accident-happened';
        //print_r($data);exit;
        foreach ($data as $submission) {
            if (isset($submission[$branch_string]) && isset($branch[$submission[$branch_string]])) {
                //print_r($submission->$branch_string);exit;
                $submission[$branch_string] = $branch[$submission[$branch_string]];
            }
            $date_accident = 'date-of-accident';
            if (isset($submission[$date_accident]) && $submission[$date_accident] != '') {
                $date_of_accident = $submission[$date_accident];
                //print_r($date_accident);exit;
                $submission[$date_accident] = date('d-m-Y', $date_of_accident);
            } else {
                $submission[$date_accident] = 'N/A';
            }
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => $data,
            ]
        );
    }

    public function ChangeRRdates(Request $request)
    {
        //$organisation_id = $request->get('organisation_id');
        $survey_ids = $request->get('survey_ids');
        $survey_ids_array = explode(',', $survey_ids);
        //dd($survey_ids_array);
        $data = RiskImprovementFormySubmissions::where('csr_status', '=', 'submitted')->whereIn(
            'survey_id',
            $survey_ids_array, 'AND'
        )->get()->toArray();

        //print_r($data); exit;

        $partial_string = '_required_by';
        //print_r($data);exit;
        foreach ($data as $submission) {
            $update_dates = [];
            $survey_id = $submission['survey_id'];
            foreach ($submission as $key => $value) {
                //dd($key);
                if ($this->endswith($key, $partial_string)) {
                    if ($value != '') {
                        $update_dates[(string)$key] = '01/10/2017';
                    }
                }
            }
            RiskImprovementFormySubmissions::where('survey_id', '=', (string)$survey_id)->update($update_dates);
            RiskRecommendationNotification::monitor($survey_id);
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => $data,
            ]
        );
    }

    private function endswith($string, $test)
    {
        $strlen = strlen($string);
        $testlen = strlen($test);
        if ($testlen > $strlen) {
            return false;
        }

        return substr_compare($string, $test, $strlen - $testlen, $testlen) === 0;
    }

    /*
    * get a form submission
    */

    /**
     * Get Acciedent Reporting Forms.
     *
     * @param $organisation_id
     * @param $user_id
     * @param int $manager
     *
     * @return mixed
     */
    public function submittedAccidentFormsForOrg($organisation_id, $user_id, $manager = 0)
    {
        date_default_timezone_set('Europe/London');
        if ($manager == 1) {
            $data = RiskImprovementFormySubmissions::where('organisation_id', '=', $organisation_id)->where(
                'submitted',
                '=', '1'
            )->get();
        } else {
            $data = RiskImprovementFormySubmissions::where('user_id', '=', $user_id)->where(
                'submitted', '=',
                '1'
            )->get();
        }

        $organisations = Organisation::all();
        $org_array = [];
        foreach ($organisations as $org) {
            $org_array[$org->id] = $org->name;
        }

        if ($organisation_id == 0) {
            $branches = User::where('branch', '=', '1')->get();
        } else {
            $branches = User::where('branch', '=', '1')->where('organisation_id', '=', $organisation_id)->get();
        }

        //print_r($branches);exit;
        $branch_array = [];
        foreach ($branches as $branch) {
            $branch_array[$branch->id] = $branch->branch_name;
        }

        $submission = [];
        foreach ($data as $key => $submission) {
            if ($submission['updated_at'] != '') {
                $updated = $submission['updated_at']->timezone('Europe/London')->format('d/m/Y H:i:s');
            } else {
                $updated = 'N/A';
            }
            if (isset($submission['date-of-accident']) && $submission['date-of-accident'] != '') {
                $date_of_accident = date(
                    'd/m/Y',
                    $submission['date-of-accident']
                ); //->timezone('Europe/London')->format('d/m/Y H:i:s');
            } else {
                $date_of_accident = 'N/A';
            }
            if (isset($submission['claim_submitted_at']) && $submission['claim_submitted_at'] != '') {
                $claim_submitted_at = date(
                    'd/m/Y',
                    $submission['claim_submitted_at']
                ); //->timezone('Europe/London')->format('d/m/Y H:i:s');
            } else {
                $claim_submitted_at = 'N/A';
            }
            if (isset($submission['submission_sequence'])) {
                $submission_sequence = $submission['submission_sequence'];
            } else {
                $submission_sequence = '0';
            }
            if (isset($submission['submission_sequence'])) {
                $submission_sequence = $submission['submission_sequence'];
            } else {
                $submission_sequence = '0';
            }
            $form = Formy::find($submission->form_id);
            if (isset($form->formType) && $form->formType != 'accident-reporting') {
                unset($data[$key]);
            } else {
                if (isset($form->name)) {
                    $submission['form_name'] = $form->name;
                    $submission['updated'] = $updated;
                    $submission['submission_sequence'] = $submission_sequence;

                    $branch_str = 'branch-where-accident-happened';
                    $submission['org_name'] = $org_array[$submission->organisation_id];
                    $submission['updated'] = $updated;
                    $submission['date_of_accident'] = $date_of_accident;
                    $submission['claim_submitted_at'] = $claim_submitted_at;
                    if (isset($branch_array[$submission->$branch_str])) {
                        $submission['branch_name'] = $branch_array[$submission->$branch_str];
                    } else {
                        $submission['branch_name'] = 'N/A';
                    }
                }
            }
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => json_encode($data->toArray()),
            ]
        );
    }

    /*
    * get a form submission
    */

    /**
     * Get Acciedent Reporting Forms that resulted in claims.
     *
     * @return mixed
     */
    public function submittedAccidentFormsClaims($org_id = 0)
    {
        date_default_timezone_set('Europe/London');
        if ($org_id == 0) {
            $data = RiskImprovementFormySubmissions::where('claim', '=', '1')->where(
                'submitted', '=', '1',
                'AND'
            )->get();
        } else {
            $data = RiskImprovementFormySubmissions::where('claim', '=', '1')->where(
                'organisation_id', '=', $org_id,
                'AND'
            )->where('submitted', '=', '1', 'AND')->get();
        }

        $organisations = Organisation::all();
        $org_array = [];
        foreach ($organisations as $org) {
            $org_array[$org->id] = $org->name;
        }

        if ($org_id == 0) {
            $branches = User::where('branch', '=', '1')->get();
        } else {
            $branches = User::where('branch', '=', '1')->where('organisation_id', '=', $org_id)->get();
        }

        //print_r($branches);exit;
        $branch_array = [];
        foreach ($branches as $branch) {
            $branch_array[$branch->id] = $branch->branch_name;
        }

        $submission = [];
        foreach ($data as $key => $submission) {
            if ($submission['updated_at'] != '') {
                $updated = $submission['updated_at']->timezone('Europe/London')->format('d/m/Y H:i:s');
            } else {
                $updated = 'N/A';
            }
            if (isset($submission['date-of-accident']) && $submission['date-of-accident'] != '') {
                $date_of_accident = date(
                    'd/m/Y',
                    $submission['date-of-accident']
                ); //->timezone('Europe/London')->format('d/m/Y H:i:s');
            } else {
                $date_of_accident = 'N/A';
            }
            if (isset($submission['claim_submitted_at']) && $submission['claim_submitted_at'] != '') {
                $claim_submitted_at = date(
                    'd/m/Y',
                    $submission['claim_submitted_at']
                ); //->timezone('Europe/London')->format('d/m/Y H:i:s');
            } else {
                $claim_submitted_at = 'N/A';
            }
            if (isset($submission['submission_sequence'])) {
                $submission_sequence = $submission['submission_sequence'];
            } else {
                $submission_sequence = '0';
            }
            $form = Formy::find($submission->form_id);
            if (isset($form->formType) && $form->formType != 'accident-reporting') {
                unset($data[$key]);
            } else {
                if (isset($form->name)) {
                    $branch_str = 'branch-where-accident-happened';
                    $submission['form_name'] = $form->name;
                    $submission['org_name'] = $org_array[$submission->organisation_id];
                    $submission['updated'] = $updated;
                    $submission['submission_sequence'] = $submission_sequence;
                    $submission['date_of_accident'] = $date_of_accident;
                    $submission['claim_submitted_at'] = $claim_submitted_at;
                    if (isset($branch_array[$submission->$branch_str])) {
                        $submission['branch_name'] = $branch_array[$submission->$branch_str];
                    }
                }
            }
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => json_encode($data->toArray()),
            ]
        );
    }

    /*
    * Get survey ID
    */

    public function savedAccidentFormsForOrg($organisation_id, $user_id, $manager = 0)
    {
        if ($manager == 1) {
            $data = RiskImprovementFormySubmissions::where('user_id', '=', $user_id)->where(
                'submitted', '=',
                '0'
            )->get();
        } else {
            $data = RiskImprovementFormySubmissions::where('user_id', '=', $user_id)->where(
                'submitted', '=',
                '0'
            )->get();
        }

        foreach ($data as $key => $submission) {
            //print_r($submission['updated_at']);exit;
            if ($submission['updated_at'] != '') {
                $updated = $submission['updated_at']->timezone('Europe/London')->format('d/m/Y H:i:s');
            } else {
                $updated = 'N/A';
            }

            if (isset($submission['submission_sequence'])) {
                $submission_sequence = $submission['submission_sequence'];
            } else {
                $submission_sequence = '0';
            }

            //$updated = $submission->updated_at;
            //$updated = $updated->format('d/m/Y');
            //$updated = \Carbon\Carbon::parse($submission->updated_at)->format('d/m/Y H:i:s');
            $form = Formy::find($submission->form_id);
            if (isset($form)) {
                if ($form->formType != 'accident-reporting') {
                    unset($data[$key]);
                } else {
                    $submission['form_name'] = $form->name;
                    $submission['updated'] = $updated;
                    $submission['submission_sequence'] = $submission_sequence;
                }
            } else {
                unset($data[$key]);
            }
        }

        return Response::json(
            [
            'response' => 'success',
            'data' => json_encode($data->toArray()),
            ]
        );
    }

    /*
     * save a form
     */

    public function showLegacy($form)
    {
        $data = LegacyRiskImprovementFormySubmissions::find($form);

        // $attachments = FormyDocuments::where('submission_id', '=', $form)->get();

        if (isset($data)) {
            // $data->attachments = $attachments;
            $files = LegacySurveyFiles::where('survey_id', '=', $data->survey_id)->select('field_name')->get();
            $file_array = [];
            foreach ($files as $file) {
                array_push($file_array, $file->field_name);
            }
            $data->attached_files = $file_array;
            // exit;
            $dup_data = [];
            foreach ($data->getAttributes() as $key => $value) {
                if (!is_string($value) || (is_string($value) && $value != '')) {
                    $dup_data[(string)$key] = $value;
                } else {
                }
            }
            $dup_data['_id'] = $form;

            return Response::json(
                [
                'response' => 'success',
                'data' => json_encode($dup_data),
                ]
            );
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'Submission does not exist',
            ]
        );
    }

    public function show(Request $request, $form)
    {
        $data = RiskImprovementFormySubmissions::with('survey.location:id,city')->find($form);
        $riskGradingAttributes = Survey::riskGradingAttributes($data->survey_id);
        if (!$request->has('export')) {
            $attachments = FormyDocuments::where('submission_id', '=', $form)->get();
        }

        if (isset($data)) {
            if (isset($attachments)) {
                $data->attachments = $attachments;
                $files = Surveyfiles::where('survey_id', '=', $data->survey_id)->select('field_name')->get();
                $file_array = [];
                foreach ($files as $file) {
                    array_push($file_array, $file->field_name);
                }
                $data->attached_files = $file_array;
            }
            // foreach ($data as $key => $value) {
            //     if(is_numeric($key)) {
            //         dd($key);
            //     }
            // }
            //dd($data->updated_at);
            // echo "<pre>";
            // //print_r($data->{'-111111111'}); exit;
            // //$data = $data->toArray();
            // //print_r($data->get()); exit;
            // $dupolicate_data_obj = get_object_vars($data);
            // dd($dupolicate_data_obj);

            // foreach ($data as $key => $value) {
            //     $unserialize_obj_check = @unserialize($value);
            //     print_r($unserialize_obj_check);
            //     $a = $data->{$key};
            //     print_r($a);
            //     if($value == '') {
            //         unset($data[$key]);
            //     }
            //     // elseif($key[0] == '-') {
            //     //     $new_key = '_pre__'.$key;
            //     //     $data[$new_key] = $value;
            //     //     unset($data[$key]);
            //     // }
            // }
            //print_r((array)$data); exit;
            // foreach ((array)$data as $key => $value) {
            //     print_r($key);
            // }
            //dd($data->getAttributes());

            // exit;
            $dup_data = [];
            $srgDataFound = false;
            $legacyDataFound = false;
            foreach ($data->getAttributes() as $key => $value) {
                if (!is_string($value) || (is_string($value) && $value != '')) {
                    $dup_data[(string)$key] = $value;

                    // Search for SRG data
                    if (str_contains($key, 'sub-attribute-') 
                        && !$srgDataFound 
                        && $value != 'Not Applicable / Not Assessed'
                    ) {
                        $srgDataFound = true;
                    }

                    // Search for legacy data
                    if (str_contains($key, 'risk-control') 
                        && !$legacyDataFound 
                        && $value != 'Not Applicable / Not Assessed'
                    ) {
                        $legacyDataFound = true;
                    }
                }
            }

            $surveyId = $data->survey_id;
            $surveyCommentaries = SurveyCommentaryInfo::whereIn('survey_id', [$surveyId, (int)$surveyId])->first();

            $dup_data['commentaries'] = $surveyCommentaries;
            $dup_data['commentary_hidden'] = $data->survey->commentary_hidden;

            $dup_data['_id'] = $form;
            $dup_data['risk_grading_attributes'] = $riskGradingAttributes;
            $dup_data['is_srg_migrated'] = (isset($data->is_srg_migrated) && !empty($data->is_srg_migrated) && $data->is_srg_migrated === true)
                ? true
                : false;
            $dup_data['with_legacy_data'] = (isset($data->with_legacy_data) && !empty($data->with_legacy_data) && $data->with_legacy_data === true)
                ? true
                : false;

            // For PDF checking
            $dup_data['pdf_is_srg_migrated'] = $dup_data['is_srg_migrated'];

            // Check which grading has available legacy/srg if not included on SRG migration
            if (!$dup_data['is_srg_migrated'] && !$dup_data['with_legacy_data']) {
                $dup_data['is_srg_migrated'] = $srgDataFound;
                $dup_data['with_legacy_data'] = $legacyDataFound;
            }

            $dup_data['location']['city'] = $data->survey->location->city;
            $dup_data['organisation']['organisation_name'] = $data->survey->organisation_name;
            $dup_data['organisation']['id'] = $data->survey->organisation_id;
            return Response::json(
                [
                'response' => 'success',
                'data' => json_encode($dup_data),
                ]
            );
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'Submission does not exist',
            ]
        );
    }

    /*
    * update a form
    */

    public function getFormID($surveyor_id, $form_id, $survey_id = '0')
    {
        if ($survey_id != '0') {
            $survey_info = Survey::with('location:id,city')
                ->where('id', '=', $survey_id)
                ->first();
        } else {
            $survey_info = Survey::with('location:id,city')->where('surveyor_id', '=', $surveyor_id)
                ->where('survey_form', '=', $form_id)
                ->first();
        }

        if (!$survey_info) {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Only the assigned surveyor can submit this form.',
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'success',
                'data' => $survey_info,
                'message' => 'Only the assigned surveyor can submit this form.',
                ]
            );
        }
    }

    private function saveCommentaryInfo($surveyId, $comTitle, $comDesc, $comNotes,$comVisible, $csrStatus = '')
    {   
        $isCsrSubmitted = $csrStatus == "submitted";
        $surveyId = (int) $surveyId;
        $commentary = [];

        $existingRecord = SurveyCommentaryInfo::where('survey_id', $surveyId)
            ->orWhere('survey_id', (string) $surveyId)
            ->first();

        for ($i = 0; $i < count($comTitle); $i++) {
            if($existingRecord){
                if($isCsrSubmitted && (!isset($comDesc[$i]) || empty(trim($comDesc[$i])))){
                    //override commentary if commentary from request is empty
                    $comDesc[$i] = $existingRecord->survey_commentary[$i]['description'] ?? '';
                }
            }
         
            $commentary[] = [
                'title'            => $comTitle[$i],
                'description'      => $comDesc[$i] ?? '',
                'additional_notes' => $comNotes[$i] ?? '',
                'visible_to_all' => $comVisible[$i] ?? '',
            ];
        }

        if ($existingRecord) {
            $existingRecord->update(['survey_commentary' => $commentary]);
        } else {
            
            SurveyCommentaryInfo::insert([
                'survey_id'         => $surveyId,
                'survey_commentary' => $commentary
            ]);
        }
    }


    private function saveCommentaryInfo_old($surveyId, $comTitle, $comDesc, $comNotes)
    {
        // Check for existing commentaries and update
        $surveyId = (int)$surveyId;
        $surveyCommentaries = SurveyCommentaryInfo::select('id')
            ->where('survey_id', $surveyId)
            ->orWhere('survey_id', (string)$surveyId)
            ->get();

        $commentary = [];
        for ($i = 0; $i < count($comTitle); $i++) {
            $commentary[] = [
                'title'       => $comTitle[$i],
                'description' => isset($comDesc[$i]) ? $comDesc[$i] : '',
                'additional_notes' => isset($comNotes[$i]) ? $comNotes[$i] : '',
            ];
        }

        $surveyCommentaryCount = $surveyCommentaries->count();
        if ($surveyCommentaryCount > 0) {
            $preserveId = $surveyCommentaries[0]->_id;

            if ($surveyCommentaryCount > 1) {
                SurveyCommentaryInfo::where('survey_id', $surveyId)
                    ->orWhere('survey_id', (string)$surveyId)
                    ->where('_id', '!=', $preserveId)
                    ->delete(); // Delete all the existing data and retain 1 data
            }

            SurveyCommentaryInfo::where('_id', $preserveId)
                ->update(['survey_commentary' => $commentary]);
        } else {
            SurveyCommentaryInfo::insert([
                'survey_id'         => $surveyId,
                'survey_commentary' => $commentary
            ]);
        }
    }

    public function store(Request $request)
    {  
        $submission = json_decode($request->get('submission'), true);

        $notes = (array)($submission['notes'] ?? []);
        foreach($notes as $key=>$note){
            $file_id = substr($key, strrpos($key, '_') + 1);
            $survey_files=Surveyfiles::find($file_id);
            if($survey_files){
                $survey_files->update(['notes' => $note]);
            }
        }
        
        $submission['surveyor_id'] = $request->get('surveyor_id');
        $submission['survey_id'] = $request->get('survey_id');
        $submission['form_id'] = $request->get('form_id');
        $create_user = [];
        $isAutosave = (bool)data_get($submission, 'autosave', 0);

        $survey = Survey::with(['location:id,city', 'organisation:id,name'])->find($submission['survey_id']);

        if (isset($submission['csr_status']) && $submission['csr_status'] == 'submitted') {

            $hasLocationId = RgLocationGrading::updateLocationGradings($submission);
            RgLocationGradingLog::createLocationGradingLog($submission);

            $submission['csr_submission_date'] = date('d/m/Y');
            if (isset($submission['risk_reduce_access_email']) && $submission['risk_reduce_access_email'] != '') {
                //$survey = Survey::find($submission['survey_id']);
                $create_user = [
                    'organisation_id' => $survey['organisation_id'],
                    'first_name' => $submission['risk_reduce_access_first_name'],
                    'last_name' => $submission['risk_reduce_access_last_name'],
                    'email' => $submission['risk_reduce_access_email'],
                    'safetymedia_access' => 0,
                    'croner_access' => 0,
                    'c_live_access' => 0,
                ];

                foreach ($submission['risk_reduce_access_services'] as $service) {
                    if ($service == 'safety_media') {
                        $create_user['safetymedia_access'] = 1;
                    } elseif ($service == 'croner') {
                        $create_user['croner_access'] = 1;
                    } elseif ($service == 'cq_live') {
                        $create_user['c_live_access'] = 1;
                    }
                }
            }

            if ($survey->external_survey_company_id && $survey->surveyor_id) {
                $surveyor = ExternalSurveyor::find($survey->surveyor_id);
            } else {
                $surveyor = LibertyUser::find($survey->surveyor_id);
            }

            $recipients = array_filter([config('app.risk_control'), config('app.risk_control_secondary')]);
            if (!empty($recipients)) {
                foreach ($recipients as $recipient) {
                    $rc = LibertyUser::where('email', 'LIKE', $recipient)->first();
                    if (!$rc) {
                        continue;
                    }

                    $rc['survey_id'] = $survey->id;
                    $rc['organisation_name'] = $survey->organisation_name ?? $survey->organisation->name;
                    $rc['location_name']     = $survey->location->location_name ?? $survey->location->city;
                    $rc['surveyor_name']     = $surveyor?->fullName();
                    $emailTemplate = $survey?->isDtr() ? 'emails.surveys.dtr_submitted' : 'emails.surveys.csr_submitted';
                    $this->queue->queue(
                        $rc['email'],
                        $rc->fullName(),
                        sprintf('Risk Reduce - New %s Submitted', $survey?->isDtr() ? 'Desktop Report' : 'CSR'),
                        $emailTemplate,
                        $rc
                    );
                }
            }

            if (!$isAutosave && $survey->isDtr() && !empty($request->csr_external_emails)) {
                $surveyId = $survey->id;
                $orgName  = $survey->organisation_name ?? $survey->organisation->name;
                $city     = $survey->location->location_name ?? $survey->location->city;
                $externalEmails  = $request->csr_external_emails;
                foreach ($externalEmails as $email) {
                    $trimEmail = trim($email);
                    $rc = LibertyUser::where('email', $trimEmail)->first();
                    if (!empty($rc)) {
                        $rc['survey_id']         = $surveyId;
                        $rc['organisation_name'] = $orgName;
                        $rc['location_name']     = $city;
                        $this->queue->queue(
                            $rc['email'],
                            $rc->fullName(),
                            'Risk Reduce - New Desktop Report Submitted',
                            'emails.surveys.dtr_submitted',
                            $rc
                        );
                    }
                }
            }

            if (!$isAutosave && !empty($request->csr_external_emails)) {
                $this->sendCsrDtrInvitedEmail($request, $survey);
            }
        }

        if (isset($submission['uwr_status']) && $submission['uwr_status'] == 'submitted') {
            $submission['uwr_submission_date'] = date('d/m/Y');
        }

        if (isset($submission['rereview_status']) && $submission['rereview_status'] == 'submitted') {
            $submission['rereview_submission_date'] = date('d/m/Y');
        }

        try {
            if (!empty($submission['csr_next_survey_due_date'])) {
                $nextSurveyDueDate = Carbon::createFromFormat('d/m/Y', $submission['csr_next_survey_due_date'])->format('Y-m-d');
                $survey->next_survey_due_date = $nextSurveyDueDate;
                $survey->save();
            }
        } catch (\Exception $e) {
            \Log::info('[Error] Next Survey Due Date: ' . $e->getMessage());
        }

        $data['commentary_description'] = $data['commentary_description'] ?? [''];
        $data['commentary_additional_notes'] = $data['commentary_additional_notes'] ?? [''];

        // Save to survey commentary info collection
        if ( 
            !empty($submission['commentary_title']) ||
            !empty($submission['commentary_description']) ||
            !empty($submission['commentary_additional_notes'])
        ) {
            $this->saveCommentaryInfo(
                $survey->id, 
                $submission['commentary_title'],
                $submission['commentary_description'],
                $submission['commentary_additional_notes'],
                $submission['narratives_visible_to_all_users']??[]
            );

            unset($submission['commentary_title']);
            unset($submission['commentary_description']);
            unset($submission['narratives_visible_to_all_users']);
        }

        if (!$isAutosave) {
            $this->sendQueueforRecaching($submission['survey_id'] ?? '');
        }

        $submission = $this->parse_attendees_info($submission);

        if ($submission['survey_id'] == '') {
            $survey_info = Survey::where('surveyor_id', '=', (string)$submission['surveyor_id'])->where(
                'survey_form',
                '=', (string)$submission['form_id']
            )->first();

            if (!$survey_info) {
                return Response::json(
                    [
                    'response' => 'error',
                    'message' => 'Only the assigned surveyor can submit this form.',
                    ]
                );
            }
            $submission['survey_id'] = $survey_info['id'];
        }

        $riskImprovementSequenceerQuery = RiskImprovementFormySubmissions::orderBy(
            'submission_sequence',
            'desc'
        )->first();

        if (isset($riskImprovementSequenceerQuery->submission_sequence) && !empty($riskImprovementSequenceerQuery->submission_sequence)) {
            $sequence = RiskImprovementFormySubmissions::orderBy(
                'submission_sequence',
                'desc'
            )->first()->submission_sequence;
        } else {
            $sequence = 0;
        }

        if (!$sequence) {
            $sequence = 0;
        }

        $row = RiskImprovementFormySubmissions::where(
            'surveyor_id', '=',
            (string)$submission['surveyor_id']
        )->where('survey_id', '=', (string)$submission['survey_id'])->first();
        RiskRecommendationNotification::monitor($submission['survey_id']);

        if (!$row) {
            $submission['submission_sequence'] = $sequence + 1;
            $id = RiskImprovementFormySubmissions::insertGetId($submission);
            $this->updateKanbanData($id);
        } else {
            $submission['submission_sequence'] = $sequence;
            $update = RiskImprovementFormySubmissions::where(
                'surveyor_id', '=',
                (string)$submission['surveyor_id']
            )->where(
                'survey_id', '=',
                (string)$submission['survey_id']
            )->update($submission);

            $id = $row->_id;
            $this->updateKanbanData($id);
        }


        $this->updateRiskGrade($submission['survey_id']);

        if (!$isAutosave && !empty($submission['survey_id'])) {
            Artisan::call('survey-submission:update-photo-graph-key --survey-id=' . $submission['survey_id']);
        }

        if ($id) {
            $sub = RiskImprovementFormySubmissions::find($id);
            $sub_cache = RiskImprovementFormySubmissions::select(
                '_id', 'csr_next_survey_due_date', 'csr_status',
                'uwr_status'
            )->where('survey_id', '=', (string)$survey->id)->first();
            Cache::forever('ri_submission_' . $request->get('survey_id'), $sub_cache);

            return Response::json(
                [
                'response' => 'success',
                'data' => (string)$id,
                '_id' => (string)$id,
                'type' => isset($survey['survey_type'])
                    ? $survey['survey_type']
                    : '',
                'create_user' => $create_user,
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to create form submission',
                ]
            );
        }
    }

    /*
    * delete a submission
    */

    public function close_issue(Request $request, $submission_id)
    {
        $data = $request->all();
        $submission = RiskImprovementFormySubmissions::where('_id', '=', $submission_id)->where(
            'survey_id', '=',
            $data['survey_id']
        )->update($data);

        Artisan::call('riskrec:cards '.$submission_id);

        RiskRecommendationNotification::monitor($data['survey_id']);
        if ($submission) {
            $s = RiskImprovementFormySubmissions::find($submission_id);
            $survey = Survey::with(['broker', 'contactsBroker', 'underwriter'])->find((int)$data['survey_id']);
            $survey_broker = $survey->contacts_broker;
            $survey_underwriter = $survey->underwriter;
            $reCacheOrganisationId = $survey->organisation_id;
            //dd($survey);
            if (!is_null($survey->broker_underwriter_id)) {
                $uw = BrokerUser::find((int)$survey->broker_underwriter_id);
            } else {
                //$uw = LibertyUser::find((int) $survey->underwriter_id);
            }

            $unw = LibertyUser::where('id', $survey->underwriter_id)->first();

            $rr_id = '';
            foreach ($data as $key => $value) {
                if (substr($key, -12) == 'issue_closed') {
                    $rr_id = '/risk-recommendation/' . substr($key, 0, -12);
                }
            }

            // send the attached broker, risk engineers on organisation and survey as well
            $organisation_broker = Organisation::with(
                [
                'broker',
                ]
            )->where('id', $survey->organisation_id)
                ->first();

            if (!$s->hasOpenRecommendations()) {

                // Broker Organisation Level Notification
                $organisation_broker_email = '';
                if ($organisation_broker
                    && isset($organisation_broker->broker->email) 
                    && !empty($organisation_broker->broker->email)
                    && !$survey->isDtr()
                ) {
                    $organisation_broker_email = $organisation_broker->broker->name;
                    if (isset($organisation_broker->broker->email) && !empty($organisation_broker->broker->email)) {
                        $this->queue->queue(
                            $organisation_broker->broker->email,
                            $organisation_broker->broker->name,
                            'Risk Reduce, All Risk Recommendations closed',
                            'emails.messaging.all_closed_notification',
                            [
                                'full_name' => $organisation_broker->broker->name,
                                'survey_id' => $survey->id,
                                'survey_link' => '/surveys/report/' . $survey->id,
                                'admin_link' => true,
                            ]
                        );
                    }
                }

                // SRF Level Broker Notification
                if (isset($survey_broker[0]->email) 
                    && !empty($survey_broker[0]->email) 
                    && count($survey_broker) > 0 
                    && ($organisation_broker_email != $survey_broker[0]->email)
                    && !$survey->isDtr()
                ) {
                    $this->queue->queue(
                        $survey_broker[0]->email,
                        $survey_broker[0]->name,
                        'Risk Reduce, All Risk Recommendations closed',
                        'emails.messaging.all_closed_notification',
                        [
                            'full_name' => $survey_broker[0]->name,
                            'survey_id' => $survey->id,
                            'survey_link' => '/surveys/report/' . $survey->id,
                            'admin_link' => true,
                        ]
                    );
                }

                // Risk Engineer organisation level
                $re = OrganisationContact::with('libertyUser')
                    ->where('organisation_id', $survey->organisation_id)
                    ->where('type', 'risk-engineer')
                    ->get();

                if (isset($re) 
                    && !empty($re) 
                    && count($re) > 0
                ) {
                    foreach ($re as $user) {
                        if (isset($user->liberty_user->email) 
                            && !empty($user->liberty_user->email)
                        ) {
                            $this->queue->queue(
                                $user->liberty_user->email,
                                $user->liberty_user->fullName(),
                                'Risk Reduce, All Risk Recommendations closed',
                                'emails.messaging.all_closed_notification',
                                [
                                    'full_name' => $user->liberty_user->fullName(),
                                    'survey_id' => $survey->id,
                                    'survey_link' => '/surveys/report/' . $survey->id,
                                    'admin_link' => true,
                                ]
                            );
                        }
                    }
                }

                // Organisation Level Underwriter notification
                $underwriters = Survey::with(['organisation', 'underwriter'])
                    ->where('id', $survey->id)
                    ->first();

                $policy = OrganisationPolicy::where('id', $underwriters->policy_id)->first();

                $uwrArr = UnderwriterPolicies::where('organisation_id', $survey->organisation_id)
                    ->where('policy_id', $policy->policy_type_id)
                    ->get();

                $uwrEmails = [];

                if (isset($uwrArr) 
                    && !empty($uwrArr) 
                    && count($uwrArr) > 0
                ) {
                    foreach ($uwrArr as $user) {
                        $organisation_underwriter = LibertyUser::where('id', $user->underwriter_id)->first();
                        if (isset($organisation_underwriter->email) 
                            && !empty($organisation_underwriter->email)
                        ) {
                            $uwrEmails[] = $organisation_underwriter->email;

                            $this->queue->queue(
                                $organisation_underwriter->email,
                                $organisation_underwriter->first_name . ' ' . $organisation_underwriter->last_name,
                                'Risk Reduce, All Risk Recommendations closed',
                                'emails.messaging.all_closed_notification',
                                [
                                    'full_name' => $organisation_underwriter->first_name . ' ' . $organisation_underwriter->last_name,
                                    'survey_id' => $survey->id,
                                    'survey_link' => '/surveys/report/' . $survey->id,
                                    'admin_link' => true,
                                ]
                            );
                        }
                    }
                }

                // SRF level underwriter notification
                if (isset($survey_underwriter->email) 
                    && !empty($survey_underwriter->email) 
                    && !in_array($survey_underwriter->email, $uwrEmails)
                ) {
                    $this->queue->queue(
                        $survey_underwriter->email,
                        $survey_underwriter->first_name . ' ' . $survey_underwriter->last_name,
                        'Risk Reduce, All Risk Recommendations closed',
                        'emails.messaging.all_closed_notification',
                        [
                            'full_name' => $survey_underwriter->first_name . ' ' . $survey_underwriter->last_name,
                            'survey_id' => $survey->id,
                            'survey_link' => '/surveys/report/' . $survey->id,
                            'admin_link' => true,
                        ]
                    );
                }

            } else {

                // Risk Engineer organisation level
                $re = OrganisationContact::with('libertyUser')
                    ->where('organisation_id', $survey->organisation_id)
                    ->where('type', 'risk-engineer')
                    ->get();

                if (isset($re) 
                    && !empty($re) 
                    && count($re) > 0
                ) {
                    foreach ($re as $user) {
                        if (isset($user->liberty_user->email) 
                            && !empty($user->liberty_user->email)
                        ) {
                            $this->queue->queue(
                                $user->liberty_user->email,
                                $user->liberty_user->fullName(),
                                'Risk Reduce, Risk Recommendation closed',
                                'emails.messaging.closed_notification',
                                [
                                    'full_name' => $user->liberty_user->fullName(),
                                    'survey_id' => $survey->id,
                                    'survey_link' => '/surveys/report/' . $survey->id,
                                    'admin_link' => true,
                                ]
                            );
                        }
                    }
                }
            }
           
            return Response::json(
                [
                'response' => 'success',
                'data' => $data,
                'organisation_id' => $reCacheOrganisationId
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to update form submission',
                ]
            );
        }
    }

    /*
    * update a submission
    */

    public function destroy($submission)
    {
        if (RiskImprovementFormySubmissions::destroy($submission)) {
            return Response::json(
                [
                'response' => 'success',
                'message' => 'Submission Deleted',
                ]
            );
            $this->updateKanbanData($submission);
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to delete form',
                ]
            );
        }
    }

    /*
    * retrieve attachment
    */

    public function addDoc(Request $request)
    {
        $formData = $request->except('_token');
        $id = FormyDocuments::insertGetId($formData);
        if ($id) {
            return Response::json(
                [
                'response' => 'success',
                'data' => (string)$id,
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to create form submission',
                ]
            );
        }
    }

    public function retrieveAttachment($id)
    {
        $document = FormyDocuments::where('document_store_name', '=', $id)->first();

        if (isset($document)) {
            $submission = RiskImprovementFormySubmissions::where('_id', '=', $document->submission_id)->first();

            return Response::json(
                [
                'response' => 'success',
                'data' => $document,
                'submission' => $submission,
                ]
            );
        }

        return Response::json(
            [
            'response' => 'error',
            'error' => 'Could not find document',
            ]
        );
    }

    public function deleteDoc(Request $request)
    {
        $document_store_name = $request->get('document_store_name');
        $document = FormyDocuments::where('document_store_name', '=', $document_store_name)->delete();
        if ($document) {
            return Response::json(
                [
                'response' => 'success',
                'data' => 'document deleted',
                ]
            );
        } else {
            return Response::json(
                [
                'response' => 'error',
                'message' => 'Unable to delete document',
                ]
            );
        }
    }

    public function indexedRiskRecs($form_id)
    {
        $rr = RiskRecommendationFormy::where('form_id', '=', $form_id)->first();
        if (isset($rr->risk_recommendation_fields)) {
            return Response::json(['response' => 'success', 'data' => $rr->risk_recommendation_fields]);
        }
    }

    public function submissionBySurveyId($survey_id)
    {
        $submission = RiskImprovementFormySubmissions::where('survey_id', '=', (string)$survey_id)->first();
        if (isset($submission->_id) && !empty($submission->_id)) {
            return Response::json(['response' => 'success', 'data' => $submission]);
        }
        return Response::json([
            'response' => 'error',
            'message' => 'No submissions found',
        ]);
    }

    private function getRelationsRR(&$item)
    {
        $relations = [
            'survey',
            'hasOpenRecommendations',
            'organisation',
            'schedule',
        ];

        foreach ($relations as $relation) {
            if (is_a($item->$relation(), Relation::class)) {
                $item->$relation;
            } else {
                $item->$relation = $item->$relation();
            }

            unset($item->{sprintf('%s_id', $relation)});
        }
    }

    private function updateKanbanData($submissionId){
        Artisan::call('riskrec:cards '.$submissionId);
    }

    private function updatePhotographCaptions($surveyFiles){
        foreach($surveyFiles as $key => $val){
            $id = substr($key, strrpos($key, '_') + 1);
            if ($id !== 'undefined') {
                Surveyfiles::where('id', $id)->update(['notes' => $val]);
            }
        }
    }

    private function addExistingSubmissionFields(array $data, RiskImprovementFormySubmissions $submission)
    {
        $existingFieldsToAdd = [];
        $skippedFields = [
            '_id',
        ];

        foreach ($submission->toArray() as $field => $value) {
            if (preg_match('/(attr-|sub-attribute-)([a-z_-]+)-(\d)/m', $field) || in_array($field, $skippedFields)) {
                continue;
            }
            if (!request()->get('uwr_status') && preg_match('/([a-z_-]+)-(\d+)(_)(\d+)(_)([a-z_-]+)/m', $field)) {
                continue;
            }

            $existingFieldsToAdd[$field] = $value;
        }

        return array_merge($existingFieldsToAdd, $data);
    }

    private function sendCsrDtrInvitedEmail(Request $request, $survey)
    {
        $orgName  = $survey->organisation_name ?? $survey->organisation->name;
        $externalEmails  = $request->csr_external_emails;
        foreach ($externalEmails as $email) {
            $trimEmail = trim($email);
            if (empty($trimEmail) || LibertyUser::where('email', $trimEmail)->exists()) {
                continue;
            }

            $otp            = Helper::generateOtp($trimEmail);
            $otpToken       = encrypt($otp->token);
            $emailEnc       = encrypt($trimEmail);
            $surveyTypeExt  = $survey?->isDtr() ? ' Desktop Report' : ' Survey Report';
            $typeTitle      = $survey?->isDtr() ? ' DTR' . $survey->id : ' CSR' . $survey->id;
            $location       = !empty($survey->Location->location_name) ? ' - ' . $survey->Location->location_name : '';
            $subjectTitle   = 'Risk Reduce: ' . $orgName . $location . $typeTitle . $surveyTypeExt;

            $this->queue->queue(
                $trimEmail,
                $subjectTitle,
                $subjectTitle,
                'emails.surveys.external_csr_submitted',
                [
                    'enc_email'    => $emailEnc,
                    'orgId'        => $survey->organisation_id,
                    'orgName'      => $orgName,
                    'survey_id'    => $survey->id,
                    'city'         => $location,
                    'token'        => $otpToken,
                    'contentTitle' => $subjectTitle,
                    'isDtr'        => $survey->isDtr(),
                ]
            );
        }
    }

    private function sendQueueforRecaching($surveyId = null) 
    {
        if (empty($surveyId)) {
            return;
        }

        try {
            $queueServiceAdmin = new QueueService(config('app.aws.invalidate_cache_sqs_admin'));
            $queueServiceAdmin->sendMessages([
                [
                    'serviceClass' => "App\Services\CacheContent\GetSurveyService",
                    'params' => $surveyId ?? '',
                    'source' => 'FROM_API:RiskImprovementFormSubmissionController',
                ],
            ]);
        } catch (\Exception $e) {
            \Log::error("[RiskImprovementFormSubmissionController Error]" . $e->getMessage());
        }
    }
    
}
