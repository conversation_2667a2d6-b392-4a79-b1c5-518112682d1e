<?php

namespace App\Http\Controllers\Api\V1\REMetrics;

use App\Http\Controllers\BaseController;
use App\Models\LibertyUser;
use App\Models\Organisation;
use App\Models\OrganisationLocations;
use App\Models\OrganisationOverviewLog;
use App\Models\RiskImprovementFormySubmissions;
use App\Models\RiskRecommendationCards;
use App\Models\Schedule;
use App\Models\ScheduleMeta;
use App\Models\Survey;
use App\Models\SurveyCards;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OrganisationDetailedController extends BaseController
{
    const RISK_MAPPING = [
        'A' => 5,
        'B' => 4,
        'C' => 3,
        'D' => 2,
        'E' => 1
    ];

    const ACTIVE_ORGANISATIONS_LIMIT = 5;
    
    public function getOrganisationCountBySector(Request $request): JsonResponse
    {
        $sectorId = $request->input('sectorId', '');
        $brokerId = $request->input('brokerId', '');
        $policyTypeId = $request->input('policyTypeId', '');
        $underwriterId = $request->input('underwriterId', '');
        $accountEngineerId = $request->input('accountEngineerId', '');
        $filterValue = $request->input('filterValue', '');

        [$startDate, $endDate] = $this->getDateRangeInput($request);

        $orgModel = new Organisation();
        $orgModel::$withoutAppends = true;

        $organisations = $orgModel
        ->select('organisations.id', 'organisations.name', 'sectors.handle as category')
        ->bound()
        ->join('sectors', 'organisations.sector', '=', 'sectors.id')
        ->with([
            'contacts' => function ($query) {
                $query->where('type', 'underwriter')
                    ->whereHas('libertyUser');
            },
            'contacts.libertyUser' => function ($query) {
                $query->select('id', 'first_name', 'last_name');
            },
            'orgRiskEngineer' => function ($query) {
                $query->whereHas('libertyUser');
            },
            'orgRiskEngineer.libertyUser' => function ($query) {
                $query->select('id', 'first_name', 'last_name');
            }
        ])
        ->when($startDate, function ($query, $startDate) {
            return $query->where('organisations.created_at', '>=', $startDate);
        })
        ->when($endDate, function ($query, $endDate) {
            return $query->where('organisations.created_at', '<=', $endDate);
        })
        ->when($sectorId, function ($query, $sectorId) {
            return $query->where('organisations.sector', $sectorId);
        })
        ->when($brokerId, function ($query, $brokerId) {
            return $query->where('organisations.broker_id', $brokerId);
        })
        ->when($policyTypeId, function($query, $policyTypeId) {
            return $query->whereExists(function ($subquery) use ($policyTypeId) {
                $subquery->select(DB::raw(1))
                    ->from('organisation_policy_numbers')
                    ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                    ->where('organisation_policy_numbers.policy_type_id', $policyTypeId)
                    ->whereNull('organisation_policy_numbers.deleted_at');
            });
        })
        ->when($underwriterId, function ($query, $underwriterId) {
            return $query->whereHas('contacts', function ($subquery) use ($underwriterId) {
                $subquery->where('user_id', $underwriterId);
            });
        })
        ->when($accountEngineerId, function ($query, $accountEngineerId) {
            return $query->whereHas('contacts', function ($subquery) use ($accountEngineerId) {
                $subquery->where('user_id', $accountEngineerId);
            });
        })
        ->when($filterValue, function ($query, $filterValue) {
            return $query->where('sectors.handle', 'like', '%' . $filterValue . '%');
        })
        ->orderBy('sectors.handle')
        ->get();

        return response()->json($organisations);
    }

    public function getRiskEngineeringMeetingHeld(Request $request): JsonResponse
    {
        $sectorId = $request->input('sectorId', '');
        $brokerId = $request->input('brokerId', '');
        $policyTypeId = $request->input('policyTypeId', '');
        $underwriterId = $request->input('underwriterId', '');
        $accountEngineerId = $request->input('accountEngineerId', '');
        $filterValue = $request->input('filterValue', '');

        [$startDate, $endDate] = $this->getDateRangeInput($request);

        $reMeetings = ScheduleMeta::where('key', '=', 'event_types')
            ->where('value', '=', '1')
            ->get();

        $organisations = Organisation::withoutAppends()
            ->select('id', 'name')
            ->bound()
            ->with([
                'contacts' => function ($query) {
                    $query->where('type', 'underwriter')
                        ->whereHas('libertyUser');
                },
                'contacts.libertyUser' => function ($query) {
                    $query->select('id', 'first_name', 'last_name');
                },
                'orgRiskEngineer' => function ($query) {
                    $query->whereHas('libertyUser');
                },
                'orgRiskEngineer.libertyUser' => function ($query) {
                    $query->select('id', 'first_name', 'last_name');
                }
            ])
            ->when($sectorId, function ($query, $sectorId) {
                return $query->where('sector', $sectorId);
            })
            ->when($brokerId, function ($query, $brokerId) {
                return $query->where('broker_id', $brokerId);
            })
            ->when($policyTypeId, fn($query, $policyTypeId) =>
                $query->whereExists(function ($subquery) use ($policyTypeId) {
                    $subquery->select(DB::raw(1))
                        ->from('organisation_policy_numbers')
                        ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                        ->where('organisation_policy_numbers.policy_type_id', $policyTypeId)
                        ->whereNull('organisation_policy_numbers.deleted_at');
                })
            )
            ->when($underwriterId, function ($query, $underwriterId) {
                return $query->whereHas('contacts', function ($subquery) use ($underwriterId) {
                    $subquery->where('user_id', $underwriterId);
                });
            })
            ->when($accountEngineerId, function ($query, $accountEngineerId) {
                return $query->whereHas('contacts', function ($subquery) use ($accountEngineerId) {
                    $subquery->where('user_id', $accountEngineerId);
                });
            })
            ->get();


        $schedules = Schedule::whereHas('meta', function ($query) use ($organisations, $reMeetings) {
            $query->where('key', 'client_organisation_id')
                ->whereIn('schedule_id', $reMeetings->pluck('schedule_id'))
                ->whereIn('value', $organisations->pluck('id'));
        })
        ->when($startDate, function ($query, $startDate) {
            return $query->where('schedule.created_at', '>=', $startDate);
        })
        ->when($endDate, function ($query, $endDate) {
            return $query->where('schedule.created_at', '<=', $endDate);
        })
        ->get();

        foreach ($schedules as $item) {
            if ($item->type == "re-admin") {
                $item->getParsedMeta();
                $item->underwriter = LibertyUser::where('id', $item->underwriter_id)->first();
                $item->risk_engineer = LibertyUser::where('id', $item->risk_engineer_id)->first();
            } else {
                $surveys[] = $item->id;
            }
        }

        // Map schedules to org id
        $organisationScheduleStarts = $schedules->mapWithKeys(function ($meta) {
            return [$meta['client_organisation_id'] => $meta];
        });

        // Add schedule start to each organisation
        $organisations->each(function ($organisation) use ($organisationScheduleStarts) {
            $organisation->schedule = $organisationScheduleStarts[$organisation->id] ?? null;
        });

        $orgForResponseIds = $schedules->pluck('client_organisation_id')->unique()->map(function ($id) {
            return (int) $id;
        })->all();

        if ($filterValue === 'yes') {
            $itemsForResponse = $organisations->whereIn('id', $orgForResponseIds)->values();
        } else {
            $itemsForResponse = $organisations->whereNotIn('id', $orgForResponseIds)->values();
        }

        return response()->json($itemsForResponse);
    }

    public function getOrganisationOverviewUpdated(Request $request): JsonResponse
    {
        $sectorId = $request->input('sectorId', '');
        $brokerId = $request->input('brokerId', '');
        $policyTypeId = $request->input('policyTypeId', '');
        $underwriterId = $request->input('underwriterId', '');
        $accountEngineerId = $request->input('accountEngineerId', '');
        [$startDate, $endDate] = $this->getDateRangeInput($request);
        $filterValue = $request->input('filterValue', '');

        $organisations = Organisation::withoutAppends()
            ->select('id', 'name')
            ->bound()
            ->with([
                'contacts' => function ($query) {
                    $query->where('type', 'underwriter')
                        ->whereHas('libertyUser');
                },
                'contacts.libertyUser' => function ($query) {
                    $query->select('id', 'first_name', 'last_name');
                },
                'orgRiskEngineer' => function ($query) {
                    $query->whereHas('libertyUser');
                },
                'orgRiskEngineer.libertyUser' => function ($query) {
                    $query->select('id', 'first_name', 'last_name');
                }
            ])
            ->when($sectorId, function ($query, $sectorId) {
                return $query->where('sector', $sectorId);
            })
            ->when($brokerId, function ($query, $brokerId) {
                return $query->where('broker_id', $brokerId);
            })
            ->when($policyTypeId, function($query, $policyTypeId) {
                $query->whereExists(function ($subquery) use ($policyTypeId) {
                    $subquery->select(DB::raw(1))
                        ->from('organisation_policy_numbers')
                        ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                        ->where('organisation_policy_numbers.policy_type_id', $policyTypeId)
                        ->whereNull('organisation_policy_numbers.deleted_at');
                });
            })
            ->when($underwriterId, function ($query, $underwriterId) {
                return $query->whereHas('contacts', function ($subquery) use ($underwriterId) {
                    $subquery->where('user_id', $underwriterId);
                });
            })
            ->when($accountEngineerId, function ($query, $accountEngineerId) {
                return $query->whereHas('contacts', function ($subquery) use ($accountEngineerId) {
                    $subquery->where('user_id', $accountEngineerId);
                });
            })
            ->get();


        $organisationOverviewUpdated = OrganisationOverviewLog::whereIn('organisation_id', $organisations->pluck('id')->map(function ($id) {
                return (string) $id;
            }))
            ->when($startDate, function ($query, $startDate) {
                return $query->where('created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query, $endDate) {
                return $query->where('created_at', '<=', $endDate);
            })
            ->select('organisation_id', 'created_at')
            ->groupBy('organisation_id')
            ->get();

        $organisationOverviewUpdated = OrganisationOverviewLog::raw(function($collection) use ($organisations, $startDate, $endDate) {
                $match = [
                    'organisation_id' => ['$in' => $organisations->pluck('id')->map(fn($id) => (string) $id)->toArray()]
                ];
            
                if ($startDate) {
                    $match['created_at']['$gte'] = new \MongoDB\BSON\UTCDateTime(strtotime($startDate) * 1000);
                }
            
                if ($endDate) {
                    $match['created_at']['$lte'] = new \MongoDB\BSON\UTCDateTime(strtotime($endDate) * 1000);
                }
            
                return $collection->aggregate([
                    ['$match' => $match],
                    ['$sort' => ['created_at' => -1]],
                    ['$group' => [
                        '_id' => '$organisation_id',
                        'latest_created_at' => ['$first' => '$created_at']
                    ]],
                ]);
            });

        $organisationOverviewUpdatedIds = $organisationOverviewUpdated->pluck('_id')->unique()->all();
        

        if ($filterValue === 'yes') {
            $itemsForResponse = $organisations->whereIn('id', $organisationOverviewUpdatedIds)->values();
        } else {
            $itemsForResponse = $organisations->whereNotIn('id', $organisationOverviewUpdatedIds)->values();
        }


        $latestUpdatedMap = $organisationOverviewUpdated->keyBy('_id')->map(function ($item) {
            return $item->latest_created_at;
        });
        
        
        $itemsForResponse = $itemsForResponse->map(function ($org) use ($latestUpdatedMap) {
            $rawDate = $latestUpdatedMap[$org->id] ?? null;

            if ($rawDate instanceof \MongoDB\BSON\UTCDateTime) {
                $org->latest_overview_update = Carbon::createFromTimestampMs($rawDate->toDateTime()->getTimestamp() * 1000)->toDateTimeString();
            } elseif ($rawDate instanceof \DateTime) {
                $org->latest_overview_update = Carbon::instance($rawDate)->toDateTimeString();
            } else {
                $org->latest_overview_update = null;
            }

            return $org;
        });

        return response()->json($itemsForResponse);
    }
    
    public function getOrganisationWithSurveyProgramme(Request $request): JsonResponse
    {
        [$startDate, $endDate] = $this->getDateRangeInput($request);
        $sectorId = $request->input('sectorId', '');
        $brokerId = $request->input('brokerId', '');
        $policyTypeId = $request->input('policyTypeId', '');
        $underwriterId = $request->input('underwriterId', '');
        $accountEngineerId = $request->input('accountEngineerId', '');
        $filterValue = $request->input('filterValue', '');

        $organisationWithSurveyProgramme = Organisation::withoutAppends()
            ->select('id', 'name')
            ->bound()
            ->with([
                'contacts' => function ($query) {
                    $query->where('type', 'underwriter')
                        ->whereHas('libertyUser');
                },
                'contacts.libertyUser' => function ($query) {
                    $query->select('id', 'first_name', 'last_name');
                },
                'orgRiskEngineer' => function ($query) {
                    $query->whereHas('libertyUser');
                },
                'orgRiskEngineer.libertyUser' => function ($query) {
                    $query->select('id', 'first_name', 'last_name');
                }
            ])
            ->when($sectorId, function ($query, $sectorId) {
                return $query->where('sector', $sectorId);
            })
            ->when($brokerId, function ($query, $brokerId) {
                return $query->where('broker_id', $brokerId);
            })
            ->when($policyTypeId, fn($query, $policyTypeId) =>
                $query->whereExists(function ($subquery) use ($policyTypeId) {
                    $subquery->select(DB::raw(1))
                        ->from('organisation_policy_numbers')
                        ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                        ->where('organisation_policy_numbers.policy_type_id', $policyTypeId)
                        ->whereNull('organisation_policy_numbers.deleted_at');
                })
            )
            ->when($underwriterId, function ($query, $underwriterId) {
                return $query->whereHas('contacts', function ($subquery) use ($underwriterId) {
                    $subquery->where('user_id', $underwriterId);
                });
            })
            ->when($accountEngineerId, function ($query, $accountEngineerId) {
                return $query->whereHas('contacts', function ($subquery) use ($accountEngineerId) {
                    $subquery->where('user_id', $accountEngineerId);
                });
            });
        
        if ($filterValue === 'yes') {
            $organisationWithSurveyProgramme->whereHas('surveys', function ($query) use ($startDate, $endDate) {
                if ($startDate) {
                    $query->where('created_at', '>=', $startDate);
                }
                if ($endDate) {
                    $query->where('created_at', '<=', $endDate);
                }
            });
        } else {
            $organisationWithSurveyProgramme->whereDoesntHave('surveys', function ($query) use ($startDate, $endDate) {
                if ($startDate) {
                    $query->where('created_at', '>=', $startDate);
                }
                if ($endDate) {
                    $query->where('created_at', '<=', $endDate);
                }
            });
        }
        
        $organisationWithSurveyProgramme = $organisationWithSurveyProgramme->get();

        return response()->json($organisationWithSurveyProgramme);
    }
    
    public function getRiskRecommendationCountByStatus(Request $request): JsonResponse
    {
        $sectorId = $request->input('sectorId', '');
        $brokerId = $request->input('brokerId', '');
        $policyTypeId = $request->input('policyTypeId', '');
        $underwriterId = $request->input('underwriterId', '');
        $accountEngineerId = $request->input('accountEngineerId', '');
        $filterValue = $request->input('filterValue', '');
        [$startDate, $endDate] = $this->getDateRangeInput($request);

        $organisations = Organisation::withoutAppends()
            ->select('id', 'name')
            ->bound()
            ->when($sectorId, function ($query, $sectorId) {
                return $query->where('sector', $sectorId);
            })
            ->when($brokerId, function ($query, $brokerId) {
                return $query->where('broker_id', $brokerId);
            })
            ->when($policyTypeId, fn($query, $policyTypeId) =>
                $query->whereExists(function ($subquery) use ($policyTypeId) {
                    $subquery->select(DB::raw(1))
                        ->from('organisation_policy_numbers')
                        ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                        ->where('organisation_policy_numbers.policy_type_id', $policyTypeId)
                        ->whereNull('organisation_policy_numbers.deleted_at');
                })
            )
            ->when($underwriterId, function ($query, $underwriterId) {
                return $query->whereHas('contacts', function ($subquery) use ($underwriterId) {
                    $subquery->where('user_id', $underwriterId);
                });
            })
            ->when($accountEngineerId, function ($query, $accountEngineerId) {
                return $query->whereHas('contacts', function ($subquery) use ($accountEngineerId) {
                    $subquery->where('user_id', $accountEngineerId);
                });
            })
            ->get();

        $riskRecommendationCards = RiskRecommendationCards::raw(function ($collection) use ($organisations, $startDate, $endDate, $filterValue) {
            $matchConditions = [
                'organisation_id' => ['$in' => $organisations->pluck('id')->toArray()]
            ];
        
            if ($filterValue) {
                $matchConditions['column'] = $filterValue;
            }
        
            if ($startDate) {
                $matchConditions['created_at'] = ['$gte' => new \MongoDB\BSON\UTCDateTime(strtotime($startDate) * 1000)];
            }
        
            if ($endDate) {
                if (isset($matchConditions['created_at'])) {
                    $matchConditions['created_at']['$lte'] = new \MongoDB\BSON\UTCDateTime(strtotime($endDate) * 1000);
                } else {
                    $matchConditions['created_at'] = ['$lte' => new \MongoDB\BSON\UTCDateTime(strtotime($endDate) * 1000)];
                }
            }
        
            return $collection->aggregate([
                [
                    '$match' => $matchConditions
                ],
                [
                    '$project' => [
                        'organisation_id' => 1,
                        'survey_id' => 1,
                        'underwriter_id' => 1,
                        'url' => 1,
                        'export_properties.title' => 1,
                        'export_properties.organisation' => 1,
                        'export_properties.classification' => 1,
                        'export_properties.postcode' => 1,
                        'column' => 1,
                        'created_at' => 1,
                        'title' => 1,
                    ]
                ]
            ]);
        });

        foreach ($riskRecommendationCards as $riskCard) {
            $location = OrganisationLocations::where('postcode', '=', $riskCard->export_properties->postcode)->first();
            $riskCard->location_name = $location ? $location->location_name : '';
        }

        return response()->json($riskRecommendationCards);
    }

    // TODO: field engineer ?? surveyor
    public function getSrfs(Request $request)
    {
        $sectorId = $request->input('sectorId', '');
        $brokerId = $request->input('brokerId', '');
        $policyTypeId = $request->input('policyTypeId', '');
        $underwriterId = $request->input('underwriterId', '');
        $accountEngineerId = $request->input('accountEngineerId', '');
        $filterValue = $request->input('filterValue', '');

        [$startDate, $endDate] = $this->getDateRangeInput($request);
        // actual_submission_deadline
        $organisations = Organisation::withoutAppends()
            ->select('id', 'name')
            ->bound()
            ->when($sectorId, function ($query, $sectorId) {
                return $query->where('sector', $sectorId);
            })
            ->when($brokerId, function ($query, $brokerId) {
                return $query->where('broker_id', $brokerId);
            })
            ->when($policyTypeId, fn($query, $policyTypeId) =>
                $query->whereExists(function ($subquery) use ($policyTypeId) {
                    $subquery->select(DB::raw(1))
                        ->from('organisation_policy_numbers')
                        ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                        ->where('organisation_policy_numbers.policy_type_id', $policyTypeId)
                        ->whereNull('organisation_policy_numbers.deleted_at');
                })
            )
            ->when($underwriterId, function ($query, $underwriterId) {
                return $query->whereHas('contacts', function ($subquery) use ($underwriterId) {
                    $subquery->where('user_id', $underwriterId);
                });
            })
            ->when($accountEngineerId, function ($query, $accountEngineerId) {
                return $query->whereHas('contacts', function ($subquery) use ($accountEngineerId) {
                    $subquery->where('user_id', $accountEngineerId);
                });
            })
            ->get();

        $srfs = Survey::with('scheduleMeta')
            ->whereIn('organisation_id', $organisations->pluck('id')->toArray())
            ->where('survey_type', '=', 'survey')
            ->when($startDate, function ($query, $startDate) {
                return $query->where('created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query, $endDate) {
                return $query->where('created_at', '<=', $endDate);
            })
            ->with('location', 'surveyor', 'externalSurveyor', 'organisation.orgRiskEngineer.libertyUser')
            ->get();

        // Get the submissions from MongoDB
        $submissions = RiskImprovementFormySubmissions::raw(function ($collection) use ($srfs) {
            $surveyIds = $srfs->pluck('id')
                ->map(function ($id) {
                    return (string) $id;
                })
                ->toArray();
                

            return $collection->aggregate([
                [
                    '$match' => [
                        'survey_id' => ['$in' => $surveyIds]
                    ]
                ],
                [
                    '$project' => [
                        'survey_id' => 1,
                        'csr_submission_date' => 1,
                        'uwr_status' => 1,
                        'csr_status' => 1,
                        '_id' => 0
                    ]
                ]
            ]);
        });

        $results = [];
        foreach ($srfs as $survey) {
            $submission = collect($submissions)
                ->where('survey_id', $survey->id)
                ->first();
            
            $organisation = $organisations->where('id', $survey->organisation_id)->first();
            
            $status = '';
            if (!empty($submission['csr_submission_date']) && !empty($survey->scheduleMeta->value)) {
                $submissionDate = Carbon::createFromFormat('d/m/Y', $submission['csr_submission_date']);
                $deadline = Carbon::parse($survey->scheduleMeta->value);
                $graceDeadline = $deadline->copy()->addDays(14);

                $status = $submissionDate->isAfter($graceDeadline) ? 'failed' : 'passed';
            } elseif (
                !empty($submission['csr_status']) && 
                !empty($submission['uwr_status']) && 
                $submission['csr_status'] !== 'submitted' && 
                $submission['uwr_status'] !== 'submitted'
            ) {
                $status = 'inProgress';
            } else {
                $status = 'notStarted';
            }


            if ($status === $filterValue) {
                $surveyor = '';
                if ($survey->externalSurveyor) {
                    $surveyor = $survey->externalSurveyor->first_name . ' ' . $survey->externalSurveyor->last_name;
                } elseif ($survey->surveyor) {
                    $surveyor = $survey->surveyor->first_name . ' ' . $survey->surveyor->last_name;
                }

                $results[] = [
                    'organisation_id' => $survey->organisation_id,
                    'organisation_name' => $organisation->name,
                    'survey_id' => $survey->id,
                    'csr_submission_date' => $submission['csr_submission_date'] ?? null,
                    'status' => $status,
                    'is_outsourced' => !empty($survey->external_survey_company_id),
                    'location_name' => $survey->location?->location_name ?? '',
                    'risk_engineer' =>  $survey->organisation?->orgRiskEngineer && count($survey->organisation->orgRiskEngineer) > 0 ? $survey->organisation->orgRiskEngineer[0] : null,
                    'surveyor' => $surveyor,
                ];
            }
        }

        return response()->json($results);
    }

    public function getDtrs(Request $request)
    {
        $sectorId = $request->input('sectorId', '');
        $brokerId = $request->input('brokerId', '');
        $policyTypeId = $request->input('policyTypeId', '');
        $underwriterId = $request->input('underwriterId', '');
        $accountEngineerId = $request->input('accountEngineerId', '');
        $filterValue = $request->input('filterValue', '');
        [$startDate, $endDate] = $this->getDateRangeInput($request);

        $organisations = Organisation::withoutAppends()
            ->select('id', 'name')
            ->bound()
            ->when($sectorId, function ($query, $sectorId) {
                return $query->where('sector', $sectorId);
            })
            ->when($brokerId, function ($query, $brokerId) {
                return $query->where('broker_id', $brokerId);
            })
            ->when($policyTypeId, fn($query, $policyTypeId) =>
                $query->whereExists(function ($subquery) use ($policyTypeId) {
                    $subquery->select(DB::raw(1))
                        ->from('organisation_policy_numbers')
                        ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                        ->where('organisation_policy_numbers.policy_type_id', $policyTypeId)
                        ->whereNull('organisation_policy_numbers.deleted_at');
                })
            )
            ->when($underwriterId, function ($query, $underwriterId) {
                return $query->whereHas('contacts', function ($subquery) use ($underwriterId) {
                    $subquery->where('user_id', $underwriterId);
                });
            })
            ->when($accountEngineerId, function ($query, $accountEngineerId) {
                return $query->whereHas('contacts', function ($subquery) use ($accountEngineerId) {
                    $subquery->where('user_id', $accountEngineerId);
                });
            })
            ->get();

        $srfs = Survey::with('schedule')
            ->whereIn('organisation_id', $organisations->pluck('id')->toArray())
            ->where('survey_type', '=', 'dtr')
            ->when($startDate, function ($query, $startDate) {
                return $query->where('created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query, $endDate) {
                return $query->where('created_at', '<=', $endDate);
            })
            ->with('location', 'surveyor', 'externalSurveyor')
            ->get();

        $submissions = RiskImprovementFormySubmissions::raw(function ($collection) use ($srfs) {
            $surveyIds = $srfs->pluck('id')
                ->map(function ($id) {
                    return (string) $id;
                })
                ->toArray();

            return $collection->aggregate([
                [
                    '$match' => [
                        'survey_id' => ['$in' => $surveyIds]
                    ]
                ],
                [
                    '$project' => [
                        'survey_id' => 1,
                        'csr_submission_date' => 1,
                        'uwr_status' => 1,
                        'csr_status' => 1,
                        '_id' => 0
                    ]
                ]
            ]);
        });

        $results = [];
        foreach ($srfs as $survey) {
            $submission = collect($submissions)
                ->where('survey_id', $survey->id)
                ->first();
            
            $organisation = $organisations->where('id', $survey->organisation_id)->first();
            
            $status = '';
            if (!empty($submission['csr_submission_date']) && !empty($survey->schedule->start)) {
                $submissionDate = Carbon::createFromFormat('d/m/Y', $submission['csr_submission_date'])->startOfDay();
                $deadline = Carbon::parse($survey->schedule->start)->startOfDay();
                $graceDeadline = $deadline->copy()->addDays(14);
    
                $status = $submissionDate->isAfter($graceDeadline) ? 'failed' : 'passed';
            } elseif (
                !empty($survey->external_survey_company_id)
                || !empty($survey->surveyor_id)
                || (!empty($submission['csr_status']) && $submission['csr_status'] == 'completed')
            ) {
                $status = 'inProgress';
            } else {
                $status = 'notStarted';
            }
    
            if ($status === $filterValue) {
                $surveyor = '';
                if ($survey->externalSurveyor) {
                    $surveyor = $survey->externalSurveyor->first_name . ' ' . $survey->externalSurveyor->last_name;
                } elseif ($survey->surveyor) {
                    $surveyor = $survey->surveyor->first_name . ' ' . $survey->surveyor->last_name;
                }

                $results[] = [
                    'organisation_id' => $survey->organisation_id,
                    'organisation_name' => $organisation->name,
                    'survey_id' => $survey->id,
                    'csr_submission_date' => $submission['csr_submission_date'] ?? null,
                    'status' => $status,
                    'is_outsourced' => !empty($survey->external_survey_company_id),
                    'location_name' => $survey->location?->location_name ?? '',
                    'surveyor' => $surveyor,
                ];
            }
        }
    
        return response()->json($results);
    }

    public function getBoundData(Request $request)
    {
        $sectorId = $request->input('sectorId', '');
        $brokerId = $request->input('brokerId', '');
        $policyTypeId = $request->input('policyTypeId', '');
        $underwriterId = $request->input('underwriterId', '');
        $accountEngineerId = $request->input('accountEngineerId', '');
        [$startDate, $endDate] = $this->getDateRangeInput($request);
        $filterValue = $request->input('filterValue', '');
        
        $organisations = Organisation::withoutAppends()
            ->select('organisations.id', 'organisations.name')
            ->bound()
            ->with([
                'contacts' => function ($query) {
                    $query->whereIn('type', ['underwriter', 'risk-engineer']);
                },
                'contacts.libertyUser' => function ($query) {
                    $query->select('id', 'first_name', 'last_name', 'role_override');
                }
            ])
            ->when($sectorId, function ($query, $sectorId) {
                return $query->where('sector', $sectorId);
            })
            ->when($brokerId, function ($query, $brokerId) {
                return $query->where('broker_id', $brokerId);
            })
            ->when($policyTypeId, fn($query, $policyTypeId) =>
                $query->whereExists(function ($subquery) use ($policyTypeId) {
                    $subquery->select(DB::raw(1))
                        ->from('organisation_policy_numbers')
                        ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                        ->where('organisation_policy_numbers.policy_type_id', $policyTypeId)
                        ->whereNull('organisation_policy_numbers.deleted_at');
                })
            )
            ->when($underwriterId, function ($query, $underwriterId) {
                return $query->whereHas('contacts', function ($subquery) use ($underwriterId) {
                    $subquery->where('user_id', $underwriterId);
                });
            })
            ->when($accountEngineerId, function ($query, $accountEngineerId) {
                return $query->whereHas('contacts', function ($subquery) use ($accountEngineerId) {
                    $subquery->where('user_id', $accountEngineerId);
                });
            })
            ->when($filterValue === 'bound_lead', function ($query) {
                return $query->whereExists(function ($subquery) {
                    $subquery->select(DB::raw(1))
                        ->from('organisation_policy_numbers')
                        ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                        ->where('bound_lead', 1);
                });
            })
            ->when($filterValue === 'bound_follow', function ($query) {
                return $query->whereExists(function ($subquery) {
                    $subquery->select(DB::raw(1))
                        ->from('organisation_policy_numbers')
                        ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                        ->where('bound_follow', 1);
                });
            })
            ->when($filterValue === 'didnt_bind', function ($query) {
                return $query->whereExists(function ($subquery) {
                    $subquery->select(DB::raw(1))
                        ->from('organisation_policy_numbers')
                        ->whereColumn('organisation_policy_numbers.organisation_id', 'organisations.id')
                        ->whereNull('bound_lead')
                        ->whereNull('bound_follow');
                });
            })
            ->when($startDate, function ($query, $startDate) {
                return $query->where('organisations.created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query, $endDate) {
                return $query->where('organisations.created_at', '<=', $endDate);
            })
            ->get();
    
        return response()->json($organisations);
    }

    private function getDateRangeInput(Request $request): array
    {
        $startDate = $request->input('startDate') ? Carbon::parse($request->input('startDate'))->endOfDay() : null;
        $endDate = $request->input('endDate') ? Carbon::parse($request->input('endDate'))->endOfDay() : null;
        return [$startDate, $endDate];
    }

    public function showOrganisations(Request $request)
    {
        $organisations = Organisation::withoutAppends()
            ->select('id', 'name')
            ->bound()
            ->with([
                'contacts' => function ($query) {
                    $query->whereIn('type', ['underwriter', 'risk-engineer'])
                          ->whereHas('libertyUser');
                },
                'contacts.libertyUser' => function ($query) {
                    $query->select('id', 'first_name', 'last_name', 'role_override');
                }
            ])
            ->get();

        return view('your-view-file', compact('organisations'));
    }
}
