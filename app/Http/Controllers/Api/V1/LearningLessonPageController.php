<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\Lesson;
use App\Models\Pages;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class LearningLessonPageController extends BaseController
{
    /**
     * Show Page
     *
     * @param  $lesson_id
     * @param  $page_id
     * @return mixed
     */
    public function show($lesson_id, $page_id)
    {

        $lesson = Lesson::find($lesson_id);

        if (!$lesson) {
            return response()->json([
                'response' => 'error',
                'message' => 'Lesson does not exist',
            ]);
        }


        $page = Pages::where('_id', '=', $page_id)
            ->whereIn('lesson_id', [(int)$lesson_id, (string)$lesson_id])
            ->orderby('sort_order', 'ASC')
            ->first();


        if ($page) {
            $this->getRelations($page);

            return response()->json([
                'response' => 'success',
                'data'     => $page,
            ]);
        }

        return response()->json([
            'response' => 'error',
            'message' => 'Page does not exist',
        ]);
    }

    /**
     * Get Relations for object
     *
     * @param $item
     */
    private function getRelations(&$item)
    {
        $relations = [
            'lesson',
        ];

        foreach ($relations as $relation) {
            unset($item->{sprintf('%s_id', $relation)});
        }
    }

    /**
     * Delete Page
     *
     * @param  $lesson_id
     * @param  $page_id
     * @return mixed
     */
    public function delete($lesson_id, $page_id)
    {
        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json([
                'response' => 'error',
                'message' => 'Lesson does not exist',
            ]);
        }

        $page = Pages::whereIn('lesson_id', [(int)$lesson_id, (string)$lesson_id])
            ->where('_id', '=', $page_id)
            ->delete();

        // if(count($page) < 1){

        //     return Response::json(array(
        //         'response' => 'error',
        //         'message'    =>    'Page does not exist'
        //     ));

        // }


        // $page->delete();

        return response()->json([
            'response' => 'success',
            'message' => 'Page deleted',
        ]);
    }

    /**
     * Store page
     *
     * @param  $lesson_id
     * @return mixed
     */
    public function store(Request $request, $lesson_id)
    {

        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json([
                'response' => 'error',
                'message' => 'Lesson does not exist',
            ]);
        }

        //Find previous lessons
        $pages = Pages::whereIn('lesson_id', [(int)$lesson_id, (string)$lesson_id])
            ->orderby('sort_order', 'DESC')
            ->get();

        if ($pages) {
            //Get first item
            if (isset($pages->first()->sort_order)) {
                $sort_order_no = $pages->first()->sort_order + 1;
            } else {
                $sort_order_no = 1;
            }

        } else {
            $sort_order_no = 1;
        }

        $page = Pages::create([
            'lesson_id' => $lesson->id,
            'title'     => $request->has('title')
                ? $request->get('title')
                : null,
            'sort_order' => $sort_order_no,
        ]);

        $this->getRelations($page);
        return response()->json([
            'response' => 'success',
            'data'     => $page,
            'message'  => 'Page created successfully',
        ]);
    }

    /**
     * Store Section
     *
     * @param  $lesson_id
     * @param  $page_id
     * @return mixed
     */
    public function storeSection(Request $request, $lesson_id, $page_id)
    {
        $rules = [
            'type' => 'required',
        ];

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json(['response' => 'errors', 'error' => $validator->errors()]);
        }

        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json(['response' => 'error', 'message' => 'Lesson Does not exist']);
        }


        $data = $request->except('_token');
        $data['id'] = uniqid($page_id);

        // $page_object_id = new MongoId($page_id);


        $page = Pages::whereIn('lesson_id', [(int)$lesson_id, (string)$lesson_id])
            ->where('_id', '=', $page_id)
            ->orderby('sort_order', 'ASC')
            ->first();


        if ($page) {
            return response()->json([
                'response' => 'error',
                'message' => 'Page does not exist',
            ]);
        }

        if (isset($page->sections)) {
            $sections = $page->sections;
            array_push($sections, $data);
            $page->sections = $sections;
            $page->save();
        } else {
            //Add section
            $page->sections = [$data];
            $page->save();
        }

        return response()->json(['response' => 'success', 'data' => $data, 'message' => 'Section added successfully']);

    }

    /**
     * Delete Section
     *
     * @param  $lesson_id
     * @param  $page_id
     * @param  $section_id
     * @return mixed
     */
    public function deleteSection($lesson_id, $page_id, $section_id)
    {

        $lesson = Lesson::where('id', '=', $lesson_id)
            ->first();

        if (!$lesson) {
            return response()->json(['response' => 'error', 'message' => 'Lesson does not exist']);
        }

        $page = Pages::whereIn('lesson_id', [(int)$lesson_id, (string)$lesson_id])
            ->where('_id', '=', $page_id)
            ->first();

        if ($page) {
            return response()->json(['response' => 'error', 'message' => 'Page does not exist']);
        }

        if (isset($page->sections) && is_array($page->sections)) {
            $sections = $page->sections;
            foreach ($sections as $key => $section) {
                if ($section['id'] == $section_id) {
                    unset($sections[$key]);
                }
            }

            $page->sections = $sections;
            $page->save();
        }

        return response()->json(['response' => 'success', 'message' => 'Section deleted']);
    }

    /**
     * Update Section
     *
     * @param  $lesson_id
     * @param  $page_id
     * @param  $section_id
     * @return mixed
     */
    public function updateSection(Request $request, $lesson_id, $page_id, $section_id)
    {
        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json(['response' => 'error', 'message' => 'Lesson does not exist']);
        }

        $page = Pages::where('_id', '=', $page_id)
            ->whereIn('lesson_id', [(int)$lesson_id, (string)$lesson_id])
            ->first();

        if (!$page) {
            return response()->json(['response' => 'error', 'message' => 'Page does not exist']);
        }

        $data = $request->except('_token');
        if (isset($page->sections)) {
            $sections = $page->sections;
            foreach ($sections as $key => $section) {
                if ($section['id'] == $section_id) {
                    if ($section['type'] == 'video-embed') {
                        $this->clearEmbeddedCode($data['url']);
                    }

                    foreach ($data as $name => $input) {
                        $sections[$key][$name] = $input;
                    }
                }

            }

            $page->sections = $sections;
            $page->save();
            return response()->json(['response' => 'success', 'data' => $page]);
        }

        return response()->json([
            'response' => 'error', 
            'message'  => 'There was an error updating. Please try again'
        ]);
    }

    private function clearEmbeddedCode(&$url)
    {
        $line = $url;
        if (strpos($line, 'embed')) {
            if (strpos($line, 'src=')) {
                $line = substr($line, strpos($line, 'src=') + 5);
            }
            if (strpos($line, '"')) {
                $line = substr($line, 0, strpos($line, '"'));
            }
        } elseif (strpos($line, 'youtu.be')) {
            $line = substr($line, strpos($line, 'youtu.be'));
            $line = 'https://www.youtube.com/embed' . substr($line, strpos($line, '/'));
        } elseif (strpos($line, 'v=')) {
            $line = 'https://www.youtube.com/embed/' . substr($line, strpos($line, '=') + 1);
        }
        if (strpos($line, '&')) {
            $line = substr($line, 0, strpos($line, '&'));
        }
        $url = $line;
    }

    public function duplicate(Request $request, $lesson_id, $page_id)
    {

        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json(['response' => 'error', 'message' => 'Lesson does not exist']);
        }

        $page = Pages::where('lesson_id', '=', (int)$lesson_id)
            ->where('_id', '=', $page_id)
            ->first();

        if (!$page) {
            return response()->json(['response' => 'error', 'message' => 'Page does not exist']);
        }

        $new_page = Pages::find($page_id)->replicate();

        //Change Title
        if ($request->has('title')) {
            $new_page->title = $request->Get('title');
        }

        //Change Lesson
        if ($request->has('lesson_id')) {
            $new_page->lesson_id = (int)$request->get('lesson_id');
        }

        $new_page->save();
        return response()->json([
            'response' => 'success',
            'data'     => $new_page,
        ]);
    }

    /**
     * Sort Page Order
     *
     * @param  $lesson_id
     * @return mixed
     */
    public function sortPages(Request $request, $lesson_id)
    {
        $rules = [
            'page_ids' => 'required|array',
        ];

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json(['response' => 'error', 'errors' => $validator->errors()]);
        }

        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json(['response' => 'error', 'message' => 'Lesson does not exist']);
        }

        $pages = Pages::where('lesson_id', '=', (int)$lesson_id)
            ->get();

        if ($request->has('page_ids') && is_array($request->get('page_ids'))) {
            $page_ids   = $request->get('page_ids');
            $sort_order = 1;
            foreach ($page_ids as $id) {
                foreach ($pages as $page) {
                    if ($page->_id == $id) {
                        $page->sort_order = $sort_order;
                        $page->save();
                        $sort_order++;
                    }
                }
            }
            return response()->json(['response' => 'success', 'data' => $pages]);
        }

        return response()->json(['response' => 'success', 'data' => $pages]);
    }

    /**
     * Reorder Sections
     *
     * @param  $lesson_id
     * @param  $page_id
     * @return mixed
     */
    public function sortSections(Request $request, $lesson_id, $page_id)
    {
        $rules = [
            'section_ids' => 'required|array',
        ];

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json(['response' => 'error', 'errors' => $validator->errors()]);
        }

        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json(['response' => 'error', 'message' => 'Lesson does not exist']);
        }

        $page = Pages::where('lesson_id', '=', (int)$lesson_id)
            ->where('_id', '=', $page_id)
            ->first();

        if (!$page) {
            return response()->json(['response' => 'error', 'message' => 'Page does not exist']);
        }

        $new_ordered_sections = [];
        $section_ids = $request->get('section_ids');

        if (isset($page->sections)) {
            $sections = $page->sections;
            foreach ($section_ids as $id) {
                foreach ($sections as $key => $section) {
                    if ($section['id'] == $id) {
                        array_push($new_ordered_sections, $section);
                    }
                }
            }

            $page->sections = $new_ordered_sections;
            $page->save();
        }

        return response()->json(['response' => 'success', 'data' => $page]);
    }

    public function duplicateSection($lesson_id, $page_id, $section_id)
    {
        $lesson = Lesson::find($lesson_id);
        if (!$lesson) {
            return response()->json(['response' => 'error', 'message' => 'Lesson does not exist']);
        }

        $page = Pages::where('lesson_id', '=', (int)$lesson_id)
            ->where('_id', '=', $page_id)
            ->first();

        if (!$page) {
            return response()->json(['response' => 'error', 'message' => 'Page does not exist']);
        }

        if (isset($page->sections)) {
            $sections = $page->sections;
            foreach ($sections as $section) {
                if ($section['id'] == $section_id) {
                    //Duplicate this section and return
                    $new_id        = uniqid($page->_id);
                    $section['id'] = $new_id;
                    $new_section   = $section;
                    array_push($sections, $section);
                }
            }

            $page->sections = $sections;
            $page->save();

            return response()->json([
                'response' => 'success',
                'data'     => isset($new_section)
                    ? $new_section
                    : null,
                ]
            );
        }

        return response()->json(['response' => 'error', 'message' => 'Section does not exist']);
    }
}
