<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\BrokerUser;
use App\Models\ExternalSurveyor;
use App\Models\LibertyUser;
use App\Models\PreviousPassword;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Hash;

class PreviousPasswordController extends Controller
{
    const GET_LATEST_PASSWORD_COUNT = 4;

    public function check(Request $request)
    {
        $password = $request->get('password');
        $code = $request->get('code');

        $decryptedPassword = Crypt::decrypt($password);

        $userModel = match($request->get('table')) {
            'users' => User::class,
            'liberty_users' => LibertyUser::class,
            'broker_users' => BrokerUser::class,
            'external_surveyors' => ExternalSurveyor::class,
            default => null,
        };
        if(empty($userModel)) {
            return response()->json(['is_previously_used' => false]);
        }

        $user = $userModel::where('reset_password_code', '=', $code)
            ->orWhere('activation_code', $code)
            ->select('id')
            ->first();


        $isPreviouslyUsed = $this->isPasswordPreviouslyUsed($decryptedPassword, $user);

        $data = [
            'is_previously_used' => $isPreviouslyUsed
        ];

        return response()->json($data);
    }

    private function isPasswordPreviouslyUsed($password, $user)
    {
        $matchedPassword = null;
        if ($user) {
            $previousPasswords = PreviousPassword::where('user_id', $user->id)
                ->latest()
                ->limit(self::GET_LATEST_PASSWORD_COUNT)
                ->get();

            $matchedPassword = $previousPasswords->first(function ($item) use ($password) {
                return Hash::check($password, $item->password);
            });
        }

        return !empty($matchedPassword);
    }
}
