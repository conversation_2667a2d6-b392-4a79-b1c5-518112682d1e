<?php
/**
 * Created by PhpStorm.
 * User: chrisvickers
 * Date: 19/01/15
 * Time: 10:00
 */

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\Cover;
use Illuminate\Support\Facades\Response;


class CoverController extends BaseController
{


    /**
     * Get: All
     */

    public function all()
    {
        $cover = Cover::all();

        return Response::json(
            [
            'response' => 'success',
            'data' => $cover->toArray(),
            ]
        );
    }

    public function options()
    {
        return Response::json(
            Cover::pluck(
                'handle',
                'id'
            )->all()
        );
    }

}
