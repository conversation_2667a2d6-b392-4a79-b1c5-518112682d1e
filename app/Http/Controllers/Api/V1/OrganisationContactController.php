<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\OrganisationContact;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\Request;

class OrganisationContactController extends BaseController
{
    public function all($organisation_id)
    {
        $notes = OrganisationContact::where('organisation_id', $organisation_id)->get();

        return Response::json(
            [
            'response' => 'success',
            'data' => $notes,
            ]
        );
    }

    public function store(Request $request, $organisation_id)
    {
        $data = $request->except('_token');

        OrganisationContact::create(
            $data + [
                'organisation_id' => $organisation_id,
            ]
        );

        return Response::json(
            [
            'response' => 'success',
            'message' => 'Organisation contact has been created successfully',
            ]
        );
    }

    public function show(Request $request, $organisation_id, $contact_id)
    {
        $data = $request->except('_token');

        $contact = OrganisationContact::where('organisation_id', $organisation_id)
            ->where('_id', $contact_id)
            ->first();

        return Response::json(
            [
            'response' => 'success',
            'data' => $contact,
            ]
        );
    }

    public function update(Request $request, $organisation_id, $contact_id)
    {
        $data = $request->except('_token');

        OrganisationContact::where('organisation_id', $organisation_id)
            ->where('_id', $contact_id)
            ->update($data);

        return Response::json(
            [
            'response' => 'success',
            'message' => 'Organisation contact has been updated successfully',
            ]
        );
    }

    public function getAllRiskEngineer()
    {
        return response()->json([
            'response' => 'success',
            'data'  => OrganisationContact::getAllRiskEngineer(),
        ]);
    }

    public function getAllRiskEngineerForOptions()
    {
        return response()->json([
            'response' => 'success',
            'data'  => OrganisationContact::getAllREorUWForOptions('risk-engineer'),
        ]);
    }

    public function getAllUnderwriterForOptions()
    {
        return response()->json([
            'response' => 'success',
            'data'  => OrganisationContact::getAllREorUWForOptions('underwriter'),
        ]);
    }
}
