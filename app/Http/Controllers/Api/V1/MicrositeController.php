<?php

namespace App\Http\Controllers\Api\V1;

use Illuminate\Http\Request;

use App\Http\Controllers\BaseController;
use App\Models\CsrMicrosite\MicrositeRgAttributeSetting;
use App\Models\CsrMicrosite\MicrositeRgSubAttributeSetting;
use App\Models\CsrMicrosite\Otp as ModelOtp;
use App\Models\RGAttribute;
use App\Models\RGSubAttribute;
use App\Models\Mailqueue;
use App\Models\Survey;
use App\Models\RiskImprovementFormySubmissions;
use App\Models\RgGradingsTooltip;
use App\Models\RgGradingsNarratives;
use Ichtrojan\Otp\Otp;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use stdClass;
class MicrositeController extends BaseController
{
    // onCreate
    public function riskGradingSettings($survey_id, $is_ordered_by_sub_attr_id=null)
    {
        try {

            $submissions = RiskImprovementFormySubmissions::where('survey_id', (string)$survey_id)->first();

            $policy = $this->generateRiskGradingSettings($submissions, $survey_id);

            $orderSubAttributeBy = 'subAttributes';
            $orderAttributeBy = 'order';

            if ($is_ordered_by_sub_attr_id) {
                $orderSubAttributeBy = 'orderedByIdSubAttributes';
                $orderAttributeBy = 'attribute_id';
            }

            $settings = MicrositeRgAttributeSetting::with([$orderSubAttributeBy, 'getRgAttribute', 'getRgAttribute.policyType:id,name'])
                ->where('survey_id', $survey_id)
                ->orderBy($orderAttributeBy, 'ASC')
                ->get();

            return response()->json([
                'status' => 200,
                'data' => compact(['settings']),
                'has_property_policy' => $policy['has_property'],
                'has_casualty_policy' => $policy['has_casualty'],
                'has_construction_policy' => $policy['has_construction'],
            ]);
        } catch (\Exception $e) {

            return response()->json([
                'status' => 500,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function toggleColumn(Request $request)
    {
        try {
            $data     = $request->all();
            $surveyId = (int)$data['survey_id'];
            $field    = $data['field'];
            $value    = (int)$data['value'];
            $key      = (int)$data['key'] ?? null;
            $attrId   = $data['attrId'] ?? null;

            if ($field == "risk_grading_attribute_is_hidden") {

                $attrSetting = MicrositeRgAttributeSetting::where('attribute_id', $key)
                    ->where('survey_id' , $surveyId)
                    ->first();

                if ($attrSetting) {
                    $attrSetting->is_hidden = $value;
                    $attrSetting->save();
                } else {
                    $attrSetting = $this->createAttrSetting($surveyId, $key, $value);
                }
            }

            if ($field == "risk_grading_sub_attribute_is_hidden") {

                if (empty($attrSetting) && !empty($attrId)) {
                    $attrSetting = $this->createAttrSetting($surveyId, $attrId);
                }

                $subAttrSetting = MicrositeRgSubAttributeSetting::where('sub_attribute_id', $key)
                    ->where('attribute_settings_id', $attrSetting->id)
                    ->first();

                if ($subAttrSetting) {
                    $subAttrSetting->is_hidden = $value;
                    $subAttrSetting->save();
                } else {
                    MicrositeRgSubAttributeSetting::create([
                        'attribute_settings_id' => $attrSetting->id,
                        'is_hidden'             => $value,
                        'sub_attribute_id'      => $key,
                    ]);
                }
            }

            return response()->json([
                'status' => 200
            ], 200);

            Survey::where('id', $surveyId)
                ->update([$field => $value]);

            return response()->json([
                'status' => 200
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    private function createAttrSetting($surveyId, $attrId, $isHidden = 0)
    {
        $attrSetting = MicrositeRgAttributeSetting::where('attribute_id', $attrId)
            ->where('survey_id' , $surveyId)
            ->first();

        if ($attrSetting) {
            return $attrSetting;
        }

        return MicrositeRgAttributeSetting::create([
            'survey_id'    => $surveyId,
            'is_hidden'    => $isHidden,
            'attribute_id' => $attrId,
            'created_at'   => now(),
            'updated_at'   => now(),
        ]);
    }

    public function toggleCommentary(Request $request)
    {
        try {
            $data = $request->all();

            $response = Survey::where('id', $data['survey_id'])
                ->update(['commentary_hidden' => $data['value']]);

            return response()->json([
                'status' => 200,
                'data' => $response,
            ]);
        } catch(\Exception $e) {
            return response()->json([
                'status' => 500,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function riskGradingReOrder(Request $request)
    {
        try {
            $data = $request->all();
            $surveyId = $data['survey_id'];
            $riskgradingReOrderValues = $data['risk_grading_attribute_order'] ?? null;
            $riskgradingSubReOrderValues = $data['risk_grading_sub_attribute_order'] ?? null;

            foreach ($riskgradingReOrderValues as $key => $riskgradingReOrderValue) {

                MicrositeRgAttributeSetting::where('id', $key)
                    ->where('survey_id', $surveyId)
                    ->update([
                        'order' => $riskgradingReOrderValue
                    ]);
            }

            foreach ($riskgradingSubReOrderValues as $subKey => $riskgradingSubReOrderValue) {

                MicrositeRgSubAttributeSetting::where('id', $subKey)
                    ->update([
                        'order' => $riskgradingSubReOrderValue
                    ]);
            }

            return response()->json([
                'status' => 200
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function generateOtp($email)
    {

        $otp = new Otp();
        $generatedOtp = $otp->generate($email, 6, 1440);
        return $generatedOtp;
    }

    public function sendToClient(Request $request)
    {
        $data = $request->all();
        $emails = $data['emails'];
        $surveyId = $data['survey_id'];
        $organisationName = $data['organisation_name'] ?? '';
        $createdByAdminUserId = $data['created_by_admin_user_id'];
        $city   = !empty($data['city']) ? $data['city'] : '';
        $orgId  = $data['org_id'];
        $sector = !empty($data['sector']) ? $data['sector'] : '';
        $survey = Survey::find($surveyId);
        $isDtr  = $survey?->isDtr();

        $mail = new Mailqueue();
        foreach ($emails as $email) {

            $otp = $this->generateOtp($email);
            $externalOtp = ModelOtp::where('identifier', $email)->latest()->first();
            $surveyUuid = Str::uuid()->toString();
            ModelOtp::where('id', $externalOtp->id)
                ->update([
                    'survey_id' => $surveyId,
                    'created_by_admin_user_id' => $createdByAdminUserId
                ]);

            $otpToken = encrypt($otp->token);
            $emailEnc = encrypt($email);
            $appUrl   = config('app.client_frontend');
            $loginUrl = $appUrl . '/microsite/login/' . $otpToken . '/' . $emailEnc . '/' . $surveyId . '/' . $orgId . '/' . $sector;
            $surveyTypeExt = $isDtr ? ' Desktop Report' : ' Survey Report';
            $typeTitle     = $isDtr ? ' DTR' . $surveyId : ' CSR' . $surveyId;
            $location      = !empty($city) ? ' - ' . $city : '';
            $subjectTitle  = 'Risk Reduce: ' . $organisationName . $location . $typeTitle . $surveyTypeExt;
            $mail->notify(
                $email,
                $subjectTitle,
                $subjectTitle,
                'emails.csr-microsite.notification_invited',
                [
                    'survey_id'         => $surveyId,
                    'organisation_name' => $organisationName,
                    'city'              => $location,
                    'contentTitle'      => $subjectTitle,
                    'typeTitle'         => $typeTitle,
                    'isDtr'             => $isDtr,
                    'loginUrl'          => $loginUrl,
                ]
            );
        }
    }

    public function auth(Request $request)
    {
        try {
            $data = $request->all();

            $submittedToken = $data['submitted_token'];
            $decryptedEmail = decrypt($data['email_enc']);
            $decryptedToken = decrypt($data['token']);

            $otp = new Otp();
            $current = $otp->validate(trim($decryptedEmail), trim($decryptedToken));
            $submittedLatest = $otp->validate(trim($decryptedEmail), trim($submittedToken));
            
            if ($current->status === true || $submittedLatest->status === true) {

                return response()->json([
                    'redirect_to_microsite_page' => true,
                    'message' => '',
                    'status' => 200
                ], 200);
            }

            return response()->json([
                'redirect_to_microsite_page' => false,
                'message' => 'OTP is incorrect. Please try again.',
                'status' => 200
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function sendOtpToClient(Request $request)
    {
        try {
            $data = $request->all();
            $email = $data['email'];
            $decryptedEmail = decrypt($data['email_enc']);
            $decryptedToken = decrypt($data['token']);
            $isDtr = $data['is_dtr'];

            if ($email == $decryptedEmail) {

                $otp = ModelOtp::where('identifier', $email)
                    ->where('survey_id', $data['survey_id'])
                    ->where('valid', 1)
                    ->first();

                if (!$otp) {
                    $newOtp = $this->generateOtp($email);
                    $newModelOtp = ModelOtp::where('identifier', $email)
                        ->latest()
                        ->first();
                    $newModelOtp->token = $newOtp->token;
                    $newModelOtp->survey_id = $data['survey_id'];
                    $newModelOtp->save();
                    $decryptedToken = $newOtp->token;
                } else {
                    if ($decryptedToken !== $otp->token) {
                        $decryptedToken = $otp->token;
                    }
                }

                $typeTitle    = $isDtr ? 'DTR' : 'CSR Survey Report';
                $subjectTitle = 'Risk Reduce - ' . $typeTitle . ' OTP';

                $mail = new Mailqueue();
                $mail->notify(
                    $email,
                    $subjectTitle,
                    $subjectTitle,
                    'emails.csr-microsite.notification_otp_token',
                    [
                        'token'        => $decryptedToken,
                        'contentTitle' => $subjectTitle,
                    ]
                );

                return response()->json([
                    'response' => 'success',
                    'redirect_to_otp_page' => true,
                    'status' => 200
                ], 200);
            }

            return response()->json([
                'response' => 'error',
                'redirect_to_otp_page' => false,
                'message' => 'Email is incorrect. Please try again.',
                'status' => 200
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'response' => 'error',
                'status' => 500,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    private function generateRiskGradingSettings($submissions, $survey_id)
    {
        $data = json_decode($submissions);
        $subAttributes = [];
        $subAttributeDescription = [];
        $policyTypeIds = [];

        if (!empty($data)) {
            foreach ($data as $attribute => $value) {

                $attributeType = explode("-", $attribute)[0];
                $attributeInfo = explode("-", $attribute);

                if ($attributeType == "attr") {
                    $policyTypeIds[] = end($attributeInfo);
                }

                if ($attributeType == "sub") {
                    $subAttributes[$attribute] = $value;
                }

                if ($attributeType == "description") {
                    $subAttributeDescription[$attribute] = $value;
                }
            }
        }

        $policyTypeIds = array_unique($policyTypeIds);

        $attributeIds = RGAttribute::select('id')
            ->with('subAttributes')
            ->whereIn('rg_policy_type_id', $policyTypeIds)
            ->get()
            ->toArray();

        foreach ($attributeIds as $id => $attributeId) {
            $msRgAttributeId = MicrositeRgAttributeSetting::updateOrCreate([
                'survey_id'    => $survey_id,
                'attribute_id' => $attributeId['id'],
            ]);

            foreach ($attributeId['sub_attributes'] as $idx => $subAttribute) {

                $subAttributeId = $subAttribute['id'];
                MicrositeRgSubAttributeSetting::updateOrCreate([
                    'attribute_settings_id' => $msRgAttributeId->id,
                    'sub_attribute_id'      => $subAttributeId,
                ]);
            }
        }

        $hasProperty = in_array(1, $policyTypeIds);
        $hasCasualty = in_array(2, $policyTypeIds);
        $hasConstruction = in_array(5, $policyTypeIds);

        return [
            'has_property' => $hasProperty,
            'has_casualty' => $hasCasualty,
            'has_construction' => $hasConstruction,
        ];
    }

    public function reportExist($survey_id, $email) {
        $reportExist = ModelOtp::where('survey_id', $survey_id)
            ->where('identifier', $email)
            ->exists();

        if ($reportExist) {
            return response()->json([
                'report_exist' => true,
                'status' => 200
            ], 200);
        }

        return response()->json([
            'report_exist' => false,
            'status' => 200
        ], 200);
    }

    private function searchArrayByName($array, $searchedName)
    {
        $key = array_search($searchedName, array_column($array, 'name'));

        if ($key !== false) {
            return $array[$key];
        } else {
            return null;
        }
    }

    private function disectLocationGradingValueSubAttrFindAttr($attribute)
    {
        $updatedSubAttribute = explode('_', explode("sub-attribute-", $attribute)[1]);
        array_shift($updatedSubAttribute);
        return trim(explode("-", implode(' ', ($updatedSubAttribute)))[0]);
    }

    private function encrypt($data, $key, $iv)
    {
        $cipher = "aes-256-cbc";
        $options = 0;
        $encrypted = openssl_encrypt($data, $cipher, $key, $options, $iv);
        return base64_encode($encrypted);
    }

    private function decrypt($data, $key, $iv)
    {
        $cipher = "aes-256-cbc";
        $options = 0;
        $decrypted = openssl_decrypt(base64_decode($data), $cipher, $key, $options, $iv);
        return $decrypted;
    }

        public function getRiskGradingTooltip() {
            $tooltipData = RgGradingsTooltip::first();
            return response()->json([
                'status' => 200,
                'data' => $tooltipData,
            ], 200);
        }

        public function getRiskGradingNarratives() {
            $narrativesData = RgGradingsNarratives::first();
            return response()->json([
                'status' => 200,
                'data' => $narrativesData,
            ], 200);
        }
}