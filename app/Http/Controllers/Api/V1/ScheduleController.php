<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\ExternalSurveyor;
use App\Models\LibertyUser;
use Illuminate\Http\Request;
use App\Models\Schedule;
use App\Models\ScheduleFileAttachments;
use App\Models\ScheduleMeta;
use App\Models\Survey;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ScheduleController extends BaseController
{
    protected static
        $types = [
        'holiday'  => 'Holiday',
        're-admin' => 'RE Admin Task',
    ];

    public static $event_types = [
        'Claims Meeting',
        'Risk Engineering Meeting',
        'Account Meeting',
        'Risk Engineering Plan Review',
        'Other Site Visit',
        'Risk reduce demo',
        'On-boarding meeting',
        'Pre-renewal meeting (internal)',
        'Pre-renewal meeting (external)',
    ];

    /**
     * Store new Schedule
     *
     * @param Request $request
     * @return json
     */
    public function store(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            static::getValidationRules('store')
        );

        if ($validator->fails()) {
            $response = [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ];
        } else {
            $schedule = Schedule::create([
                'type'  => $request->get('type'),
                'title' => $request->get('title', null),
                'start' => $request->get('start'),
                'end'   => $request->get('end', null),
            ]);

            $schedule->saveMeta(
                array_diff_key(
                    $request->all(),
                    array_fill_keys($schedule->getFillable(), null)
                )
            );

            if ($schedule->id) {
                $response = [
                    'response'    => 'success',
                    'message'     => sprintf('%s scheduled successfully', static::$types[$schedule->type]),
                    'schedule_id' => $schedule->id,
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message'  => sprintf('%s could not be scheduled', static::$types[$schedule->type]),
                ];
            }
        }

        return response()->json($response);
    }

    private static function getValidationRules($method)
    {
        return [
            'type'  => 'required',
            'start' => 'required|date_format:Y-m-d H:i:s',
        ];
    }

    public function add_schedule_attachment_empty(Request $request)
    {
        $id = ScheduleFileAttachments::insertGetId($request->all());

        if ($id) {
            $response = [
                'id' => $id,
            ];

            return response()->json($response);
        }
    }

    public function update_schedule_attachment_empty(Request $request)
    {
        if ($request->get('sessions')) {
            $sessions = $request->get('sessions');

            for ($i = 0; $i < count($sessions); $i++) {
                ScheduleFileAttachments::where(
                    'id',
                    $sessions[$i]
                )->update(['schedule_id' => $request->get('schedule_id')]);

                //Session::forget('sessions.'.$i);
            }

            return response()->json(['success' => false]);
        }
    }

    /**
     * Update Schedule
     *
     * @param Request $request
     * @param int $id
     * @return json
     */
    public function update(Request $request, $id)
    {
        if ($id && is_numeric($id)) {
            $validator = Validator::make(
                $request->all(),
                static::getValidationRules('update')
            );

            if ($validator->fails()) {
                $response = [
                    'response' => 'error',
                    'errors'   => $validator->messages()->toArray(),
                ];
            } else {
                $schedule = Schedule::find($id);
                if ($schedule->id) {
                    $schedule->type  = $request->get('type');
                    $schedule->start = $request->get('start');
                    $schedule->end   = $request->get('end', null);
                    $schedule->save();

                    $schedule->saveMeta(
                        array_diff_key(
                            $request->all(),
                            array_fill_keys($schedule->getFillable(), null)
                        )
                    );

                    $response = [
                        'response' => 'success',
                        'message'  => sprintf('%s schedule updated successfully', static::$types[$schedule->type]),
                    ];
                } else {
                    $response = [
                        'response' => 'error',
                        'message'  => sprintf('Invalid %s schedule', static::$types[$schedule->type]),
                    ];
                }
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid schedule ID',
            ];
        }

        return response()->json($response);
    }

    /**
     * Find Schedule
     */
    public function show($id)
    {
        if (is_numeric($id)) {
            $schedule = Schedule::where('id', '=', $id)->get();
            $response = [
                'response' => ($schedule) ? 'success' : 'error',
            ];

            if ($schedule) {
                foreach ($schedule as $item) {
                    $item->getParsedMeta();
                }

                $response['data'] = $schedule->first();
                $files = ScheduleFileAttachments::where('schedule_id', $id)->get();
                $response['files'] = $files;
            } else {
                $response['message'] = sprintf('%s schedule could not be found', static::$types[$schedule->type]);
            }
        } else {
            $response = [
                'response' => 'error',
                'message' => 'Invalid schedule ID',
            ];
        }

        return response()->json($response);
    }

    /**
     * Delete Schedule
     *
     * @param $id
     *
     * @return mixed
     */
    public function destroy(Request $request)
    {
        $id = $request->get('id');
        if ($schedule = Schedule::find($id)) {
            if (Schedule::destroy($id)) {
                ScheduleMeta::where('schedule_id', $schedule->id)->delete();
                $response = [
                    'response' => 'success',
                    'message'  => sprintf('%s schedule deleted', static::$types[$schedule->type]),
                ];
            } else {
                $response = [
                    'response' => 'error',
                    'message'  => sprintf('%s schedule could not be deleted', static::$types[$schedule->type]),
                ];
            }
        } else {
            $response = [
                'response' => 'error',
                'message'  => sprintf('%s schedule could not be found', static::$types[$schedule->type]),
            ];
        }

        return response()->json($response);
    }

    private function getParsedMetaCalendar($item)
    {
        // copy the timestamps
        $timestamps = [
            'created_at' => $item->created_at,
            'updated_at' => $item->updated_at,
        ];

        // remove them temporarily
        unset(
            $item->created_at,
            $item->updated_at
        );

        // parse the schedule meta into properties
        foreach ($item->meta as $meta) {
            $key = explode("_",$meta['key'])[1] ?? null;
            if (!isset($item->$meta['key']) && isset($key) && $key == 'id') {
                $item->{$meta['key']} = (int)$meta['value'];
            } else {
                $item->{$meta['key']} = $meta['value'];
            }
        }

        if (isset($item->actual_submission_deadline)) {
            $item->start = $item->actual_submission_deadline;
            $item->end = date('Y-m-d H:i:s', strtotime("+1 day", strtotime($item->actual_submission_deadline)));
            $item->actual = "set";
        }

        // re-add the timestamps at the end for consistency
        foreach ($timestamps as $key => $value) {
            $item->$key = $value;
        }

        return $item;
    }

    /**
     * Get Schedule Range
     */
    public function range($start, $end)
    {


        $schedule = Schedule::whereDate(
            'start', '>=', $start
        )->whereDate('end', '<=', $end, 'AND');

        $schedule = $schedule->get();



        $schedule_array = [];
        foreach ($schedule as $item) {
            $itemx = $this->getParsedMetaCalendar($item);
            if ((isset($item->type) && ($item->type == "re-admin" || $item->type == "holiday"))) {
                array_push($schedule_array, $itemx);
            }
        }

        $scheduleMeta = ScheduleMeta::where('key', '=', 'actual_submission_deadline')->whereDate(
            'value', '>=', $start,
            'AND'
        );

        $scheduleMeta = $scheduleMeta->get();

        foreach ($scheduleMeta as $item) {
            $item->actual_submission_deadline = $item->value;
            $item->actual = 'set';
            $sch          = Schedule::find($item->schedule_id);
            $item->type   = $sch->type;
            $item->id     = $sch->id;
            $item->start  = $item->value;
            $item->end    = $item->value;
            $survey       = Survey::where('schedule_id', $item->schedule_id)->with(['externalSurveyCompany','organisation'=> function ($query) {
                $query->select('id', 'name');
            }])->get();
            foreach ($survey as $s) {
                $item->survey_id = $s->id;
                if (is_null($s->external_survey_company_id)) {
                    $item->surveyor_details = LibertyUser::where('id', '=', $s->surveyor_id)->withTrashed()->first();
                } else {
                    $item->surveyor_details = ExternalSurveyor::where(
                        'id', '=',
                        $s->surveyor_id
                    )->withTrashed()->first();
                }
                // print_r($s); exit;
                if (isset($item->surveyor_details)) {

                    unset($item->surveyor_details->phone);
                    unset($item->surveyor_details->claims_notification);
                    unset($item->surveyor_details->activation_code);
                    unset($item->surveyor_details->activation_code_expires);
                    unset($item->surveyor_details->activated);
                    unset($item->surveyor_details->secret);
                    unset($item->surveyor_details->valid);
                    unset($item->surveyor_details->established);
                    unset($item->surveyor_details->reset_password_code);
                    unset($item->surveyor_details->reset_password_code_expires);
                    unset($item->surveyor_details->created_at);
                    unset($item->surveyor_details->updated_at);
                    unset($item->surveyor_details->deleted_at);
                    unset($item->surveyor_details->attempts);
                }
                $item->survey = $s;
            }

            if (isset($item->survey_id)) {
                array_push($schedule_array, $item);
            }
        }

        return response()->json([
            'schedule' => $schedule_array,
        ]);
    }

    public function getCategoryTypesByOrganisation($orgid)
    {
        //using raw sql as data table is entity based
        $sql = 'select sm1.value as event_types, count(sm1.value) as total from schedule_meta sm1
        join schedule_meta sm2 on sm1.schedule_id = sm2.schedule_id
            where sm1.`key`=\'event_types\' and sm2.`key`=\'client_organisation_id\' and sm2.value=' . (int)$orgid . ' and sm2.deleted_at is null Group By sm1.value';

        $category_types = DB::select($sql);
        $array_mapped = array_map(
            function ($type) {
                return $type->event_types = static::$event_types[(int)$type->event_types];
            }, $category_types
        );

        return response()->json($category_types);
    }

    public function getSchedulesForOrganisation($orgid, $lastlogin)
    {
        $types      = ['re-admin', 'survey'];
        $schedule   = Schedule::whereIn('type', $types);
        $schedule   = $schedule->get();
        $eventsData = [];
        $surveys    = [];
        $reTasks    = [];
        $today      = date("Y-m-d H:i:s");
        $lastlogin  = isset($lastlogin) ? $lastlogin : date("Y-m-d H:i:s");

        foreach ($schedule as $item) {
            if ($item->type == "re-admin") {
                $item->getParsedMeta();
                if ($item->client_organisation_id == $orgid && $item->end >= $today) {
                    $reTasks[] = $item;
                }
            } else {
                $surveys[] = $item->id;
            }
        }

        foreach ($reTasks as $reTask) {
            $data = [
                'id'               => $reTask->id,
                'date'             => $reTask->end,
                'created_at'       => $reTask->created_at,
                'updated_at'       => $reTask->updated_at,
                'type'             => 're-admin',
                'event_type'       => static::$event_types[$reTask->event_types],
                'risk_engineer_id' => $reTask->risk_engineer_id,
            ];
            $eventsData[] = (object)$data;
        }


        $orgSurveys = Survey::where('organisation_id', $orgid)->whereIn('schedule_id', $surveys)->get();
        foreach ($orgSurveys as $survey) {
            $schedule = $survey->scheduleMeta()->get();
            if (isset($schedule) && isset($schedule[0]) && isset($schedule[0]->value) && $schedule[0]->value >= $today) {
                $address = [];
                $contacts = $survey->contacts()->get();
                foreach ($contacts as $contact) {
                    if ($contact->type == 'client') {
                        $address['city'] = $contact->city;
                        $address['postcode'] = $contact->postcode;
                        break;
                    }
                }
                $data = [
                    'id'         => $survey->id,
                    'date'       => $schedule[0]->value,
                    'created_at' => $survey->created_at,
                    'updated_at' => $survey->updated_at,
                    'address'    => (object)$address,
                    'type'       => 'survey',

                ];
                $eventsData[] = (object)$data;
            }
        }
        return response()->json($eventsData);
    }

    public function getSchedulesForClient($schedule_id)
    {
        $schedule = Schedule::where('id', $schedule_id)->first();
        $schedule->getParsedMeta();
        $schedule->file = ScheduleFileAttachments::where('schedule_id', $schedule_id)->get();
        $schedule->event_type = static::$event_types[$schedule->event_types];
        return response()->json($schedule);
    }
}
