<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\ClientEnquiries;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\Request;

class ClientEnquiriesController extends BaseController
{
    public function getAllClientEnquiries(Request $request)
    {
        $search = urldecode($request->get('search', ''));
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        $data = ClientEnquiries::orderBy('id', 'desc');
        if (isset($search)) {
            $data = $data->where('organisation_name', 'LIKE', '%' . $search . '%')
                ->orWhere('user_name', 'LIKE', '%' . $search . '%')
                ->orWhere('product_name', 'LIKE', '%' . $search . '%');
        }
        $total = $data->count();
        $data = $data->take($limit)->skip(($page * $limit) - $limit)->get();

        if (isset($data)) {
            return Response::json(
                [
                'response' => 'success',
                'data' => $data->toarray(),
                'total' => $total,
                ]
            );
        }

        return Response::json(
            [
            'response' => 'error',
            'message' => 'Could not return enquiries',
            ]
        );
    }
}
