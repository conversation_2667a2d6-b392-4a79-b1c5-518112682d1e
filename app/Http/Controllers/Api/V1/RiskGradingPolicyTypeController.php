<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\RGPolicyType;

class RiskGradingPolicyTypeController extends BaseController
{
    /**
     * Get all the Risk Grading Policy Types
     *
     * @return json
     */
    public function index()
    {
        return response()->json(RGPolicyType::all());
    }

    /**
     * Get certain policy type
     *
     * @param  int $policyTypeId
     * @return json
     */
    public function getPolicyType($policyTypeId)
    {
        return response()->json(RGPolicyType::find($policyTypeId));
    }
}
