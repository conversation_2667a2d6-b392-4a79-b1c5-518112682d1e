<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\Formy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class FormController extends BaseController
{

    /*
     * get all forms for an organisation
     */
    public function formsfororganisation($organisation_id, $page = 1, $limit = 10)
    {
        $page = (int)$page;
        $limit = (int)$limit;
        $count = 0;
        $formids = $this->getFormIdsForSelectedSector();

        $forms = Formy::where('organisation', '=', $organisation_id)
            ->orwhere('organisation', '=', '0')
            ->orwhereIn('_id', $formids)
            ->take($limit)->skip(($page * $limit) - $limit)->get();
        foreach ($forms as $index => $form) {
            if (isset($form->exclude_ids)) {
                $excluded_org_array = explode(',', $form->exclude_ids);
                if (in_array($organisation_id, $excluded_org_array)) {
                    $count = $count + 1;
                    $forms->offsetUnset($index);
                }
            }
        }

        return Response::json(
            [
                'response' => 'success',
                'data' => $forms->toArray(),
                'total' => Formy::where('organisation', '=', $organisation_id)->orwhere(
                    'organisation',
                    '=',
                    '0'
                )->get()->count(),
            ]
        );
    }

    public function getFormIdsForSelectedSector()
    {
        $sectorid = request()->has('sector')
            ? (int)request()->get('sector')
            : 0;
        $formids = [];
        $forms = Formy::where('selected_sectors', 'exists', true)->get(['selected_sectors', '_id']);
        foreach ($forms as $form) {
            if (in_array($sectorid, $form->selected_sectors)) {
                $formids[] = $form->_id;
            }
        }
        return $formids;
    }


    /**
     * @param  Request  $request
     * @param  int  $organisation_id
     * @param  int  $page
     * @param  int  $limit
     * @return JsonResponse
     */
    public function accidentReportingFormsForOrganisation(
        Request $request,
        int $organisation_id,
        int $page = 1,
        int $limit = 10,
        bool $fewFields=false
    ): JsonResponse {
        $request->merge([
            'page' => $page,
            'limit' => $limit,
        ]);

        $formIds = $this->getFormIdsForSelectedSector();
        $forms = Formy::where(function ($query) use ($organisation_id, $formIds) {
            $query->where('organisation', $organisation_id)
                ->orWhere('organisation', '0')
                ->orwhereIn('_id', $formIds);
        })
        ->where('formType', 'accident-reporting');

        if($fewFields){
            $forms=$forms->get(['name','exclude_ids']);
        }else{
            $forms=$forms->orderBy('updated_at', 'asc')
            ->paginate($limit)->items();
        }

        foreach ($forms as $key => $form) {
            if (isset($form['exclude_ids'])) {
                $excluded_org_array = explode(',', $form['exclude_ids']);
                if (in_array($organisation_id, $excluded_org_array)) {
                    unset($forms[$key]);
                }
            }
        }

        return Response::json([
            'response' => 'success',
            'data' => $forms,
            'total' => count($forms),
        ]);
    }


    /*
     * get all forms
     */
    public function index(Request $request, $page = 1, $limit = 10)
    {
        $page = (int)$page;
        $limit = (int)$limit;
        $search = $request->get('search');

        $request->merge(['page' => $page]);

        if (!empty($search)) {
            $forms = Formy::where('name', 'LIKE', '%' . $search . '%')->paginate($limit);
        } else {
            $forms = Formy::paginate($limit);
        }

        return Response::json([
            'response' => 'success',
            'data' => $forms->items(),
            'total' => $forms->total(),
        ]);
    }

    /*
     * get a form
     */
    public function show($form)
    {
        $data = Formy::find($form);
        if (isset($data)) {
            return Response::json([
                'response' => 'success',
                'data' => json_encode($data->toArray()),
            ]);
        }
        return Response::json([
            'response' => 'error',
            'data' => json_encode(['message' => 'Form not found']),
        ]);
    }

    /*
     * save a form
     */
    public function store(Request $request)
    {
        $form = json_decode($request->get('form'), true);
        $form['created_at'] = date('Y-m-d');
        $id = Formy::insertGetId($form);
        if ($id) {
            $form = Formy::find($id);
            return Response::json(
                [
                    'response' => 'success',
                    'data' => (string)$id,
                ]
            );
        } else {
            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'Unable to create form',
                ]
            );
        }
    }

    /*
     * update a form
     */
    public function update(Request $request, $formId)
    {

        $formData = json_decode($request->get('form'), true);
        $form = Formy::find($formId);
        $form->fill($formData);
        if ($form->save()) {
            return Response::json(
                [
                    'response' => 'success',
                    'data' => (string)$formId,
                ]
            );
        } else {
            return Response::json([
                'response' => 'error',
                'message' => 'Unable to update form',
            ]);
        }
    }

    /*
     * delete a form
     */
    public function destroy($form)
    {
        $form_object = Formy::find($form);
        if (Formy::destroy($form)) {
            return Response::json(
                [
                    'response' => 'success',
                    'message' => 'Form Deleted',
                ]
            );
        } else {
            return Response::json(
                [
                    'response' => 'error',
                    'message' => 'Unable to delete form',
                ]
            );
        }
    }

    public function getAllPrivateForms()
    {
        $forms = Formy::all(['_id','name']);
        return Response::json([
            'response' => 'success',
            'data' => $forms,
        ]);
    }

    


}
