<?php

namespace App\Http\Controllers\Api\V1\RRAppetite;

use App\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use App\Models\LibertyUser;
use Illuminate\Support\Facades\Response;
use App\Models\RRAppetite\Product;
use App\Models\RRAppetite\ProductRelationship;
use App\Models\RRAppetite\ProductUnderwriter;
use App\Models\RRAppetite\Region;
use App\Models\RRAppetite\Sector;
use App\Models\RRAppetite\Subsector;

class RegionController extends BaseController
{

    public function index(Request $request)
    {
        if ($request->has('list')) {
            $regions = Region::pluck('name', 'id')->all();
        } else {
            $regions = Region::all();
        }

        $response = [
            'status' => 'success',
            'data' => $regions,
        ];

        return Response::json($response);
    }

    public function regionBySlug($slug)
    {
        $region = Region::where('slug', $slug)->first();

        if ($region) {
            $response = [
                'status' => 'success',
                'data' => $region,
            ];
        } else {
            $response = [
                'status' => 'error',
                'message' => 'no regions found',
            ];
        }
        return Response::json($response);
    }

    public function show(Region $region)
    {
        $response = [
            'status' => 'success',
            'data' => $region,
        ];

        return Response::json($response);
    }

    public function getRegionSectors($id)
    {
        $region = Region::find($id);

        $response = [
            'status' => 'success',
            'data' => [],
        ];

        if ($region) {
            $region->regionProducts = $region->regionInfo('include_all_regions')
                ->whereIn('ra_product_relationships.region_id', ['0', $id])
                ->orderBy('sector_id')->orderBy('subsector_id')->orderBy('product_id')
                ->with('sector', 'subsector', 'product')->get();

            //print_r($region->regionProducts->toArray()); exit;


            $region_items = [];

            $sector_count = 0;
            $subsector_count = 0;

            foreach ($region->regionProducts as $region_product) {

                $sectors = [];
                if ($region_product->sector_id == '0') {
                    //$region_items['sectors'] = [];
                    $sectors = $region_product->allSectors()->toArray();
                } elseif ($region_product->sector_id == '') {
                    $sectors = [];
                } else {
                    $sectors[] = $region_product->sector->toArray();
                }

                $allSubsectors = Subsector::all()->toArray();

                $product = $region_product->product;


                foreach ($sectors as $sector) {

                    if (!isset($region_items['sectors'][$sector['id']])) {

                        $region_items['sectors'][$sector['id']] = $sector;

                        if (!is_null($product) && is_null($region_product->subsector_id)) {
                            $region_items['sectors'][$sector['id']]['products'][] = $this->generateObject($product);
                        }
                    }

                    $subsectors = [];

                    if ($region_product->subsector_id == '0') {
                        $subsectors = $allSubsectors;
                    } elseif ($region_product->subsector_id == '') {
                        $subsectors = [];
                    } else {
                        $subsectors[] = $region_product->subsector->toArray();
                    }


                    foreach ($subsectors as $subsector) {
                        if (!isset($region_items['sectors'][$sector['id']]['subsectors'][$subsector['id']])) {
                            $region_items['sectors'][$sector['id']]['subsectors'][$subsector['id']] = $subsector;
                            if (!is_null($product) && !is_null($region_product->subsector_id)) {
                                $region_items['sectors'][$sector['id']]['subsectors'][$subsector['id']]['products'][] = $this->generateObject($product);
                            }
                        }
                    }


                }

            }

            $response = [
                'status' => 'success',
                'data' => $region_items,
            ];
        }

        return Response::json($response);
    }

    private function generateObject($object)
    {
        return [
            'id' => $object->id,
            'name' => $object->name,
            'slug' => $object->slug,
        ];
    }

    public function getRegionSubsectors(Request $request, $id)
    {
        $region = Region::find($id);

        $response = [
            'status' => 'success',
            'data' => [],
        ];

        $subsector_list = [];

        if ($region) {
            $sector_id = $request->get('sector_id');


            $sector_list = $region->regionInfo('include_all_regions')
                ->whereIn('ra_product_relationships.region_id', ['0', $id])
                ->whereIn('ra_product_relationships.sector_id', ['0', $sector_id], 'AND')
                ->orderBy('sector_id')->orderBy('subsector_id')
                ->with('sector', 'subsector')->get();
            // echo "<pre>";
            // print_r($sector_list->toArray()); exit;

            $subsector_list = [];

            $subsector = [];
            foreach ($sector_list as $sector) {

                if (in_array($sector->sector_id, [$sector_id, 0]) && $sector->subsector_id == '0') {
                    $subsector = Subsector::all()->toArray();
                } elseif ($sector->sector_id == $sector_id && isset($sector->subsector->id)) {
                    $subsector[$sector->subsector->id] = $sector->subsector->toArray();
                }

                $subsector_list = $subsector;
            }

            //$subsector_list = array_unique($subsector_list, 0);
            $response['data']['subsectors'] = $subsector_list;
        }

        return Response::json($response);
    }

    public function getRegionProducts(Request $request, $id)
    {
        $region = Region::find($id);

        $response = [
            'status' => 'success',
            'data' => [],
        ];

        if ($region) {
            $sector_id = $request->get('sector_id');
            $subsector_id = $request->get('subsector_id') != ''
                ? $request->get('subsector_id')
                : null;

            $sector_list = $region->regionInfo('include_all_regions')
                ->whereIn('sector_id', [0, $sector_id])
                ->orderBy('sector_id')->orderBy('subsector_id')
                ->with('sector', 'subsector', 'product');

            if (is_null($subsector_id)) {

                $sector_list = $sector_list->whereNull('ra_product_relationships.subsector_id');
            } else {
                $sector_list = $sector_list->whereIn('ra_product_relationships.subsector_id', [0, $subsector_id]);
            }


            $sector_list = $sector_list->get();

            // echo "<pre>";
            // print_r($sector_list->toArray()); exit;

            $product_list = [];

            foreach ($sector_list as $sector) {
                // echo "<pre>";
                // print_r($sector->toArray()); exit;
                // $product = ProductRelationship::all();
                if (in_array($sector->sector_id, [0, $sector_id]) && in_array(
                    $sector->subsector_id,
                    [0, $subsector_id]
                )
                ) {
                    $product = $sector->product;
                    $product_list[] = $product;
                }

            }
            // echo "<pre>";
            // print_r($product_list); exit;


            $product_list = array_unique($product_list, 0);
            $response['data']['products'] = $product_list;
        }

        return Response::json($response);
    }

    public function getRegionProductsInfo(Request $request, $id)
    {
        $region = Region::find($id);

        $response = [
            'status' => 'success',
            'data' => [],
        ];

        if ($region) {
            $sector_slug = $request->has('product_slug')
                ? $request->get('sector_slug')
                : null;
            $subsector_slug = $request->has('subsector_slug')
                ? $request->get('subsector_slug')
                : null;
            $product_slug = $request->get('product_slug');

            // get product sector and subsector
            $product = Product::where('slug', $product_slug)->first();
            $sector = null;
            $subsector = null;

            if ($sector_slug) {
                $sector = Sector::where('slug', $sector_slug)->first();
            }

            if ($subsector_slug) {
                $subsector = Subsector::where('slug', $subsector_slug)->first();
            }

            $product_info = $region->regionInfo('include_all_regions');

            if ($product) {
                $product_info = $product_info->whereIn('product_id', [0, $product->id]);
            } else {
                $product_info = $product_info->whereNull('product_id');
            }

            if ($sector) {
                $product_info = $product_info->whereIn('sector_id', [0, $sector->id]);
            } else {
                $product_info = $product_info->whereNull('sector_id');
            }

            if ($subsector) {
                $product_info = $product_info->whereIn('subsector_id', [0, $subsector->id]);
            } else {
                $product_info = $product_info->whereNull('subsector_id');
            }

            $product_info = $product_info->orderBy('sector_id')
                ->orderBy('subsector_id')
                ->with('sector', 'subsector', 'product')
                ->first();

            if ($product_info) {
                // get the underwriter info
                $product_underwriter = $this->getProductUnderwriter($product_info, $product);

                $product_info = $product_info->toArray();

                if (!empty($product_underwriter)) {
                    $product_info['underwriter'] = $product_underwriter;
                }

                if (is_null($product_info['sector'])) {
                    $product_info['sector'] = $sector;
                }

                if (is_null($product_info['subsector'])) {
                    $product_info['subsector'] = $subsector;
                }

                $response = [
                    'status' => 'success',
                    'data' => $product_info,
                ];
            }
        }

        return Response::json($response);
    }

    private function getProductUnderwriter($product_relationship, $product)
    {
        $underwriter = false;
        $underwriter_object = [];

        if (!is_null($product_relationship->underwriter_id)) {
            $underwriter = LibertyUser::find($product_relationship->underwriter_id);
        } else {
            $underwriter = ProductUnderwriter::where('region_id', $product_relationship->region_id)
                ->where('product_id', $product_relationship->product_id)
                ->first();

            if (!$underwriter) {
                $underwriter = ProductUnderwriter::whereNull('region_id')
                    ->where('product_id', $product_relationship->product_id)
                    ->first();
            }

            $underwriter = LibertyUser::find($underwriter->underwriter_id);
        }

        if ($underwriter) {
            $underwriter_object = [
                'first_name' => $underwriter->first_name,
                'last_name' => $underwriter->last_name,
                'email' => $underwriter->email,
                'phone' => $underwriter->phone,
                'role' => $underwriter->role,
                'image' => $underwriter->image,
            ];
        }

        return $underwriter_object;
    }

    public function getOrphanProduct(Request $request)
    {
        $product_slug = $request->get('product_slug');

        // get product sector and subsector
        $product = Product::where('slug', $product_slug)->first();

        $product_info = ProductRelationship::whereNull('region_id')
            ->whereNull('sector_id')
            ->whereNull('subsector_id')
            ->where('product_id', $product->id)->with('product', 'productIntro');

        $product_info = $product_info->first();

        if (!$product_info) {
            return Response::json(
                [
                'status' => 'success',
                'data' => [],
                ]
            );
        }

        // get the underwriter info
        $product_underwriter = $this->getProductUnderwriter($product_info, $product);

        $product_info = $product_info->toArray();

        if (!empty($product_underwriter)) {
            $product_info['underwriter'] = $product_underwriter;
        }

        // check to see if we should pull through product sectors/subsectors
        $sector_ids = ProductRelationship::whereNotNull('region_id')
            ->where('product_id', $product->id)
            ->pluck('sector_id');

        $product_info['sectors'] = Sector::whereIn('id', $sector_ids)->get();

        return Response::json(
            [
            'status' => 'success',
            'data' => $product_info,
            ]
        );
    }
}
