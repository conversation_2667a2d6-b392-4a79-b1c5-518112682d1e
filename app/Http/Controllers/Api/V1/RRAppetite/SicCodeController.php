<?php

namespace App\Http\Controllers\Api\V1\RRAppetite;

use App\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use App\Models\RRAppetite\SicCode;
use App\Models\RRAppetite\SicCodeSectorRelationship;
use App\Models\RRAppetite\SicCodeSubSectorRelationship;

class SicCodeController extends BaseController
{
    public function index(Request $request)
    {
        if ($request->has('list')) {
            $siccodes = SicCode::all()->pluck('code', 'id');
        } else {
            $siccodes = SicCode::all();
        }
        $response = [
            'status' => 'success',
            'data' => $siccodes,
        ];

        return response()->json($response);
    }

    public function siccodesBysectorId($id)
    {
        $siccodes = SicCodeSectorRelationship::where('sector_id', $id)
            ->select('sic_code_id')
            ->get();

        $response = [
            'status' => 'success',
            'data' => $siccodes,
        ];

        return response()->json($response);
    }

    public function siccodesBysubsectorId($id)
    {
        $siccodes = SicCodeSubSectorRelationship::where('subsector_id', $id)
            ->select('sic_code_id')
            ->get();

        $response = [
            'status' => 'success',
            'data' => $siccodes,
        ];

        return response()->json($response);
    }
}
