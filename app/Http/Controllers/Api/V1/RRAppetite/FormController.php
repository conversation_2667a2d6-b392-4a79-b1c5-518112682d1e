<?php

namespace App\Http\Controllers\Api\V1\RRAppetite;

use App\Http\Controllers\BaseController;
use App\Models\Broker;
use App\Models\FileUpload;
use App\Models\LibertyUser;
use App\Models\Mailqueue;
use App\Models\RRAppetite\FormSubmission;
use App\Models\RRAppetite\FormSubmissionProduct;
use App\Models\RRAppetite\Product;
use App\Models\RRAppetite\ProductUnderwriter;
use App\Models\RRAppetite\Region;
use App\Models\RRAppetite\Sector;
use App\Models\RRAppetite\Subsector;
use Carbon\Carbon;
use DateTime;
use DateTimeZone;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;

class FormController extends BaseController
{
    public function __construct(Mailqueue $mailqueue, FileUpload $fileupload)
    {
        $this->mail = $mailqueue;
        $this->files = $fileupload;
    }

    public function all()
    {
        $submissions = FormSubmission::orderByDesc('id')->get();

        $data = [
            'submissions' => $submissions,
        ];

        return Response::json(
            [
                'status' => 'success',
                'data' => $data,
            ]
        );
    }

    public function activeForms()
    {
        $submissions = FormSubmission::where('archived', 0)
            ->orderbyDesc('created_at')
            ->get();

        foreach ($submissions as &$submission) {
            if ($submission->verified_broker) {
                $email_domain = substr(strrchr($submission['email'], "@"), 1);

                $broker = Broker::where('domain', 'LIKE', '%' . $email_domain . '%')->first();

                if ($broker) {
                    $submission->broker = $broker->name;
                }
            }

            $submission->products = $submission->linkedProducts()->get();
            foreach ($submission->products as &$product) {
                $product_info = Product::withTrashed()->where('id', $product->product_id)->first();
                if ($product_info) {
                    $product->name = $product_info->name;
                }
                if ($product->underwriter_id) {
                    $primary_product = Product::find($product->product_id); // what be this?
                    $sector = Sector::find($product->sector_id);
                    if ($sector) {
                        $submission->sector = $sector->name;
                    }

                    $subsector = Subsector::find($product->subsector_id);
                    if ($subsector) {
                        $submission->subsector = $subsector->name;
                    }

                    $submission->underwriter = LibertyUser::where('id', $product->underwriter_id)->first();
                }
            }
        }

        $data = [
            'submissions' => $submissions,
        ];

        return Response::json(
            [
                'status' => 'success',
                'data' => $data,
            ]
        );
    }

    public function archivedForms()
    {
        $submissions = FormSubmission::where('archived', 1)
            ->orderbyDesc('created_at')
            ->get();

        foreach ($submissions as &$submission) {
            if ($submission->verified_broker) {
                $email_domain = substr(strrchr($submission['email'], "@"), 1);

                $broker = Broker::where('domain', 'LIKE', '%' . $email_domain . '%')->first();

                if ($broker) {
                    $submission->broker = $broker->name;
                }
            }

            $submission->products = $submission->linkedProducts()->get();
            foreach ($submission->products as &$product) {
                $product_info = Product::withTrashed()->where('id', $product->product_id)->first();
                if ($product_info) {
                    $product->name = $product_info->name;
                }
                if ($product->underwriter_id) {
                    $primary_product = Product::find($product->product_id);
                    $sector = Sector::find($product->sector_id);
                    $subsector = Subsector::find($product->subsector_id);

                    if ($sector) {
                        $product->sector = $sector->name;
                    }

                    if ($subsector) {
                        $product->subsector = $subsector->name;
                    }

                    $product->underwriter = LibertyUser::where('id', $product->underwriter_id)->first();
                }
            }
        }

        $data = [
            'submissions' => $submissions,
        ];

        return Response::json(
            [
                'status' => 'success',
                'data' => $data,
            ]
        );
    }

    public function show(FormSubmission $formSubmission)
    {
        $submission = $formSubmission;
        $products = $submission->linkedProducts()->get();

        if ($submission->verified_broker) {
            $email_domain = substr(strrchr($submission['email'], "@"), 1);

            $broker = Broker::where('domain', 'LIKE', '%' . $email_domain . '%')->first();

            // if ($broker) {
            //     $data['broker'] = $broker;
            // }
            // print_r($submission); exit;
        }

        foreach ($products as &$product) {
            $product_info = Product::withTrashed()->where('id', $product->product_id)->first();
            if ($product_info) {
                $product->name = $product_info->name;
            }

            //if ($product->underwriter_id) {
            $primary_product = Product::find($product->product_id);
            $sector = Sector::find($product->sector_id);
            $subsector = Subsector::find($product->subsector_id);

            if ($sector) {
                $product->sector = $sector->name;
            }

            if ($subsector) {
                $product->subsector = $subsector->name;
            }

            //dd($product->product_id);

            $productUnderwriter = ProductUnderwriter::where('region_id', $product->region_id)->where(
                'product_id',
                $product->product_id
            )->first();

            if (!$productUnderwriter) {
                $productUnderwriter = ProductUnderwriter::whereNull('region_id')->where(
                    'product_id',
                    $product->product_id
                )->first();

            }

            if ($productUnderwriter) {
                $underwriter = LibertyUser::find($productUnderwriter->underwriter_id);
            }

            $product->uwr = $underwriter;
            $product->uwr->product_video_image = $productUnderwriter->product_video_image;
            $product->uwr->product_video_link = $productUnderwriter->product_video_link;
            //}
        }

        $region = Region::find($submission->region_id);

        $data = [
            'submission' => $submission,
            'products' => $products,
            'underwriter' => $underwriter,
            'region' => $region,
        ];
        if (isset($broker)) {
            $data['broker'] = $broker;
        }

        return Response::json(
            [
                'status' => 'success',
                'data' => $data,
            ]
        );
    }

    public function changeFormArchiveStatus(Request $request, $id)
    {
        $form = FormSubmission::find($id);
        $archive = $request->get('archive');

        if ($archive) {
            $form->archived = 1;
            $form->save();
            $data = [
                'submission' => $form,
            ];
        } else {
            $form->archived = 0;
            $form->save();
            $data = [
                'submission' => $form,
            ];
        }

        return Response::json(
            [
                'status' => 'success',
                'data' => $data,
            ]
        );
    }

    public function changeFormEnquiryStatus(Request $request, $id)
    {
        $form = FormSubmission::find($id);
        $enquiry = $request->get('enquiry');

        if ($enquiry) {
            $form->enquiry_status = 1;
            $form->save();
            $data = [
                'submission' => $form,
            ];
        } else {
            $form->enquiry_status = 0;
            $form->save();
            $data = [
                'submission' => $form,
            ];
        }

        return Response::json(
            [
                'status' => 'success',
                'data' => $data,
            ]
        );
    }

    public function icsString(FormSubmission $formSubmission)
    {
        $submission = $formSubmission;
        return Response::json(
            [
                'status' => 'success',
                'ics' => $this->compileICSFile($submission),
            ], 403
        );

    }

    private function compileICSFile($submission)
    {
        $admin_link = config('app.admin_frontend');
        $url = $admin_link . '/rr-appetite/enquiries/' . $submission['id'];
        $description = "Please view business enquiry at this url - " . $url;
        if (is_null($submission['timezone']) || $submission['timezone'] == "+00:00") {
            $submission['timezone'] = 'Europe/London';
        } else {
            $dateTime = new DateTime();
            $dateTime->setTimeZone(new DateTimeZone($submission['timezone']));
        }
        $sla_date = $this->checkSLADate($submission['preferred_datetime'], $submission['created_at']);
        //print_r($sla_date->startOfDay()->addHours(9)->format("Ymd\THis\Z")); exit;
        $sla_date_at_9_am = $sla_date->startOfDay()->addHours(9);
        $datetime_start = $sla_date_at_9_am->format("Ymd\THis\Z");

        $datetime_end = $sla_date_at_9_am->addMinutes(30)->format("Ymd\THis\Z");

        $datetime_stamp = Carbon::now()->subMinutes(15)->format('Ymd\THis\Z');

        $ics_file = "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Risk Appetite//EN\r\nMETHOD:REQUEST\r\nBEGIN:VEVENT\r\nSUMMARY:" . $description . "\r\nDTSTART;VALUE=DATE-TIME:" . $datetime_start . "\r\nDTEND;VALUE=DATE-TIME:" . $datetime_end . "\r\nDTSTAMP;VALUE=DATE-TIME:" . $datetime_stamp . "\r\nUID:" . Str::random(40) . "\r\nSEQUENCE:1\r\nATTENDEE:" . $submission['email'] . "\r\nCREATED;VALUE=DATE-TIME:" . $datetime_stamp . "\r\nDESCRIPTION:" . $description . "\r\nLOCATION:" . $url . "\r\nORGANIZER:" . $submission['email'] . "\r\nPRIORITY:5\r\nSTATUS:CONFIRMED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-PT1H\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR";

        return $ics_file;
    }

    private function checkSLADate($rtd_date, $created_date)
    {
        // to check
        $rtd_date = Carbon::createFromTimestamp(strtotime($rtd_date));
        $created_date = Carbon::createFromTimestamp(strtotime($created_date));

        if ($rtd_date >= $created_date->addWeekdays(3)) {
            return $created_date;
        } else {
            return $rtd_date;
        }
    }

    public function handleSubmission(Request $request)
    {
        $submission = $request->get('submission');
        // validate everything here
        // get ids for the products
        if (!is_array($submission)) {
            $submission = get_object_vars(json_decode($submission));
        }

        foreach ($submission['product_binder'] as $key => $product) {
            $submission['product_binder'][$key] = $this->prepareProduct(
                $product,
                $submission['selected_underwriter_product'], $submission['selected_underwriter_email']
            );
        }

        // separate out product binders
        $product_binder = $submission['product_binder'];
        // check if email to send back to is on broker user table
        $email_domain = substr(strrchr($submission['email'], "@"), 1);

        $verified = $this->verifyEmail($email_domain);
        $submission = $this->createSubmissionObject($submission);

        $submission['verified_broker'] = $verified
            ? 1
            : 0;
        // store main submission
        $submission = FormSubmission::create($submission);
        // get id back from main submission
        if ($submission) {
            $form_id = $submission->id;
            $form_submission_ids = [];
            // store products for submission
            foreach ($product_binder as $product) {
                $product['form_id'] = $form_id;
                $submission_product = FormSubmissionProduct::create($product);
                $form_submission_ids[] = $submission_product->id;
            }

            //send email to broker if verified
            if ($verified) {
                $this->sendBrokerEmail($submission);
                $this->sendUnderwriterEmail($submission, $verified, $email_domain);
            } else {
                $this->sendBrokerEmail($submission, 'unverified');
                $this->sendUnderwriterEmail($submission, $verified, $email_domain);
            }


            $data = [
                'form_id' => $form_id,
                'form_product_ids' => $form_submission_ids,
                'important_enquiry' => $submission['req_price'] == 1,
                'verified' => (bool)$verified,
            ];

            return Response::json(
                [
                    'status' => 'success',
                    'data' => $data,
                ]
            );
        }

        return Response::json(
            [
                'status' => 'error',
                'data' => 'Form failed to save',
            ], 403
        );
    }

    private function prepareProduct($product, $selected_product, $selected_uw_email)
    {
        if (!is_array($product)) {
            $product = (array)$product;
        }

        $product['product_id'] = Product::where('slug', $product['product_slug'])->first()->id;

        if ($product['sector_slug']) {
            $product['sector_id'] = Sector::where('slug', $product['sector_slug'])->first()->id;
        }

        if ($product['subsector_slug']) {
            $product['subsector_id'] = Subsector::where('slug', $product['subsector_slug'])->first()->id;
        }

        if ($product['product_slug'] == $selected_product) {
            $product['underwriter_id'] = LibertyUser::where('email', $selected_uw_email)->first()->id;
        }
        // remove product name and timestamp elsewhere
        unset($product['product_name']);
        unset($product['product_slug']);
        unset($product['sector_slug']);
        unset($product['subsector_slug']);
        unset($product['timestamp']);

        return $product;
    }

    private function verifyEmail($broker_email)
    {
        $check_domain = Broker::where('domain', 'LIKE', '%' . $broker_email . '%')->count();

        if ($check_domain > 0) {
            return true;
        }

        return false;
    }

    private function createSubmissionObject($submission)
    {
        $submission['req_price'] = $submission['price'];
        // this should be done on rr end
        unset($submission['price']);
        unset($submission['date']);
        unset($submission['time']);
        unset($submission['selected_underwriter_email']);
        unset($submission['selected_underwriter_product']);
        unset($submission['product_binder']);

        return $submission;
    }

    private function sendBrokerEmail($submission, $verified = 'verified', $resend = '')
    {
        $attachments = [];
        $submission = $submission->toArray();

        // region name
        $region = Region::find($submission['region_id']);
        $submission['region'] = $region->name;
        $broker_name = $submission['fname'] . ' ' . $submission['surname'];
        // products
        $products = FormSubmissionProduct::where('form_id', $submission['id'])->get();

        $primary_product = null;
        foreach ($products as $key => &$product) {
            // get the underwriter for the product based on sector info
            $product->product_name = Product::where('id', $product->product_id)->first()->name;
            if (isset($product->underwriter_id)) {
                $primary_product = $product;
                unset($products[$key]);
            } else {
                // get the underwriter from the product or from the relationship
                $product_relationship = $region->regionInfo()
                    ->whereIn('region_id', [0, $submission['region_id']])
                    ->whereIn('product_id', [0, $product->product_id]);

                if (isset($product->sector_id)) {
                    $product_relationship = $product_relationship->whereIn('sector_id', [$product->sector_id, 0]);
                }

                if (isset($product->subsector_id)) {
                    $product_relationship = $product_relationship->whereIn('subsector_id', [$product->subsector_id, 0]);
                }

                $product_relationship->with('sector', 'subsector', 'product')->first();

                $product->underwriter = $this->getProductUnderwriter($product->product_id, $submission['region_id']);
            }
        }

        if (is_null($submission['timezone']) || $submission['timezone'] == "+00:00") {
            $submission['timezone'] = 'GMT';
        } else {
            //$dateTime = new DateTime();
            //$dateTime->setTimeZone(new DateTimeZone($submission['timezone']));
            $submission['timezone'] = 'GMT';
        }

        $products = $products->toArray();

        $primary_product = $primary_product->toArray();

        if ($primary_product['sector_id']) {
            $primary_product['sector_name'] = Sector::find($primary_product['sector_id'])->name;
        }

        if ($primary_product['subsector_id']) {
            $primary_product['subsector_name'] = Subsector::find($primary_product['subsector_id'])->name;
        }

        $primary_product['underwriter'] = $this->getProductUnderwriter(
            $primary_product['product_id'],
            $submission['region_id']
        );

        // get pdf download link
        // expiry days (from pdf link presumably)
        $s3_link = 'pdf/' . $submission['id'] . '/liberty-presentation.pdf';
        $download_link = null;

        try {
            //$file_exists = $this->files->exists('pdf/'.$submission['id'].'/liberty-presentation.pdf');
            //if ($file_exists) {
            $download_link = $this->files->link('pdf/' . $submission['id'] . '/liberty-presentation.pdf', '7 days');
            //}
        } catch (\Exception $e) {
            $download_link = null;
        }

        $links = [
            'download_pdf' => $download_link,
            'expiry_time' => '7',
        ];

        $tmpl_type = intval($submission['region_id']) == config('app.usa_region_id')
            ? 'lmi'
            : 'lsm';

        if ($verified == 'verified') {
            $this->mail->queue(
                $submission['email'],
                $broker_name,
                'Your PDF is ready to download',
                'emails.risk-appetite.' . $tmpl_type . '.broker',
                [
                    'links' => $links,
                    'submission' => $submission,
                    'products' => $products,
                    'primary_product' => $primary_product,
                ]
            );
        } else {
            if ($resend == 'resend') {
                $email_template = 'emails.risk-appetite.' . $tmpl_type . '.broker_unverified_resend';
                $subject = "Your PDF is ready to download";
            } else {
                $email_template = 'emails.risk-appetite.' . $tmpl_type . '.broker_unverified';
                $subject = 'Thank you for your submission';
            }
            $this->mail->queue(
                $submission['email'],
                $broker_name,
                $subject,
                $email_template,
                [
                    'links' => $links,
                    'submission' => $submission,
                    'products' => $products,
                    'primary_product' => $primary_product,
                ]
            );
        }

        return true;
    }

    private function getProductUnderwriter($product_id, $region_id)
    {
        $underwriter = false;
        $underwriter_object = [];


        $underwriter = ProductUnderwriter::where('region_id', $region_id)->where('product_id', $product_id)->first();
        if (!$underwriter) {
            $underwriter = ProductUnderwriter::whereNull('region_id')->where('product_id', $product_id)->first();
        }
        $underwriter = LibertyUser::find($underwriter->underwriter_id);

        if ($underwriter) {
            $underwriter_object = [
                'first_name' => $underwriter->first_name,
                'last_name' => $underwriter->last_name,
                'email' => $underwriter->email,
                'phone' => $underwriter->phone,
                'role' => $underwriter->role,
                'image' => $underwriter->image,
            ];
        }

        return $underwriter_object;
    }

    private function sendUnderwriterEmail($submission, $verified, $email_domain, $resend = '')
    {
        $attachments = [];

        $admin_link = config('app.admin_frontend');
        $submission = $submission->toArray();

        $submission['region'] = $submission['region_name'];

        $broker_name = $submission['fname'] . ' ' . $submission['surname'];

        // products
        $selected_product = FormSubmissionProduct::where(
            'form_id',
            $submission['id']
        )->whereNotNull('underwriter_id')->first();
        $selected_product = $selected_product->toArray();

        $underwriter = $this->getProductUnderwriter($selected_product['product_id'], $selected_product['region_id']);
        $uw_name = $underwriter['first_name'] . ' ' . $underwriter['last_name'];

        $ics_file = $this->compileICSFile($submission);

        if ($selected_product['sector_id']) {
            $selected_product['sector_name'] = Sector::find($selected_product['sector_id'])->name;
        }

        if ($selected_product['subsector_id']) {
            $selected_product['subsector_name'] = Subsector::find($selected_product['subsector_id'])->name;
        }

        $selected_product['underwriter'] = LibertyUser::find($selected_product['underwriter_id']);

        $products = FormSubmissionProduct::where('form_id', $submission['id'])->get();
        $product_names = [];

        if (is_null($submission['timezone']) || $submission['timezone'] == "+00:00") {
            $submission['timezone'] = 'UTC';
        } else {
            $dateTime = new DateTime();
            $dateTime->setTimeZone(new DateTimeZone($submission['timezone']));
            $submission['timezone'] = $dateTime->format('T');
        }

        foreach ($products as $product) {
            $product_info = Product::where('id', $product->product_id)->first();
            $product_names[] = $product_info->name;
        }

        $product_names = implode(", ", $product_names);

        $s3_link = 'pdf/' . $submission['id'] . '/liberty-presentation.pdf';
        $download_link = null;

        try {
            $download_link = $this->files->link('pdf/' . $submission['id'] . '/liberty-presentation.pdf', '7 days');
        } catch (\Exception $e) {
            $download_link = null;
        }

        // attachment, pdf link
        $links = [
            'dealt_with' => $admin_link . '/rr-appetite/enquiries/' . $submission['id'] . '/status?enquiry=1',
            'not_dealt_with' => $admin_link . '/rr-appetite/enquiries/' . $submission['id'] . '/status?enquiry=0',
            'download_pdf' => $download_link,
            'expiry_time' => '7',
        ];

        $attachments = [
            'ics' => [
                'StringValue' => $ics_file,
                'DataType' => 'string',
            ],
        ];
        // to set up

        // $cc_email = '<EMAIL>';
        $cc_email = '<EMAIL>';
        // $cc_email = '<EMAIL>';

        if ($ics_file) {
            $attachments = [
                'ics' => [
                    'StringValue' => $ics_file,
                    'DataType' => 'String',
                ],
            ];
        }

        $tmpl_type = intval($submission['region_id']) == config('app.usa_region_id')
            ? 'lmi'
            : 'lsm';

        $email_date = $this->checkSLADate($submission['preferred_datetime'], $submission['created_at']);
        $submission['preferred_datetime'] = $email_date;

        if ($submission['verified_broker']) {
            // get broker info
            $broker = Broker::where('domain', 'LIKE', '%' . $email_domain . '%')->first();
            if ($submission['req_price']) {
                $subject = 'Important enquiry submission from ' . $broker->name;
                $this->mail->queue(
                    $underwriter['email'],
                    $uw_name,
                    $subject,
                    'emails.risk-appetite.' . $tmpl_type . '.important-enquiry',
                    [
                        'underwriter' => $underwriter,
                        'submission' => $submission,
                        'broker_company' => $broker->name,
                        'product_names' => $product_names,
                        'primary_product' => $selected_product,
                        'verified' => true,
                        'links' => $links,
                    ],
                    $attachments,
                    $cc_email
                );
            } else {
                $subject = 'New enquiry submission from ' . $broker->name;
                $this->mail->queue(
                    $underwriter['email'],
                    $uw_name,
                    $subject,
                    'emails.risk-appetite.' . $tmpl_type . '.new-enquiry',
                    [
                        'underwriter' => $underwriter,
                        'submission' => $submission,
                        'broker_company' => $broker->name,
                        'product_names' => $product_names,
                        'primary_product' => $selected_product,
                        'verified' => true,
                        'links' => $links,
                    ],
                    $attachments,
                    $cc_email
                );
            }
        } else {
            if ($resend == 'resend') {
                $to_email = $underwriter['email'];
                if ($submission['req_price']) {
                    $email_template = 'emails.risk-appetite.' . $tmpl_type . '.uw-unverified-resend-important-enquiry';
                } else {
                    $email_template = 'emails.risk-appetite.' . $tmpl_type . '.uw-unverified-resend-new-enquiry';
                }

                if ($submission['req_price']) {
                    $this->mail->queue(
                        $to_email,
                        $uw_name,
                        'Important enquiry submission from an unrecognised broker',
                        $email_template,
                        [
                            'underwriter' => $underwriter,
                            'submission' => $submission,
                            'product_names' => $product_names,
                            'primary_product' => $selected_product,
                            'verified' => false,
                            'links' => $links,
                        ],
                        $attachments
                    );
                } else {
                    $this->mail->queue(
                        $to_email,
                        $uw_name,
                        'New enquiry submission from an unrecognised broker',
                        $email_template,
                        [
                            'underwriter' => $underwriter,
                            'submission' => $submission,
                            'product_names' => $product_names,
                            'primary_product' => $selected_product,
                            'verified' => false,
                            'links' => $links,
                        ],
                        $attachments
                    );
                }

            } else {
                $to_email = $cc_email;


                if ($submission['req_price']) {
                    $email_template = 'emails.risk-appetite.' . $tmpl_type . '.bd-important-enquiry';
                    $this->mail->queue(
                        $to_email,
                        $uw_name,
                        'Important enquiry submission from an unrecognised broker',
                        $email_template,
                        [
                            'underwriter' => $underwriter,
                            'submission' => $submission,
                            'product_names' => $product_names,
                            'primary_product' => $selected_product,
                            'verified' => false,
                            'links' => $links,
                        ]
                    );
                } else {
                    $email_template = 'emails.risk-appetite.' . $tmpl_type . '.bd-new-enquiry';
                    $this->mail->queue(
                        $to_email,
                        $uw_name,
                        'New enquiry submission from an unrecognised broker',
                        $email_template,
                        [
                            'underwriter' => $underwriter,
                            'submission' => $submission,
                            'product_names' => $product_names,
                            'primary_product' => $selected_product,
                            'verified' => false,
                            'links' => $links,
                        ]
                    );
                }
            }


        }
    }

    public function resendBrokerEmail($id, $send_to = 'broker')
    {
        $submission = FormSubmission::find($id);
        $email_domain = substr(strrchr($submission['email'], "@"), 1);
        $verified = $this->verifyEmail($email_domain);
        if ($send_to == 'broker') {
            $email = $this->sendBrokerEmail($submission);
        } else {
            $email = $this->sendBrokerEmail($submission, $verified, 'resend');
        }

        if ($email) {
            return Response::json(
                [
                    'status' => 'success',
                    'message' => 'Success',
                ]
            );
        }

        return Response::json(
            [
                'status' => 'error',
                'message' => 'error has occurred',
            ]
        );
    }
}
