<?php

namespace App\Http\Controllers\Api\V1\RRAppetite;

use App\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use App\Models\RRAppetite\Sector;
use App\Models\RRAppetite\SicCodeSectorRelationship;
use Illuminate\Support\Facades\Validator;

class SectorController extends BaseController
{
    public function index(Request $request)
    {
        if ($request->has('list')) {
            $sectors = Sector::all()->pluck('name', 'id');
        } else {
            $sectors = Sector::all();
        }

        $response = [
            'status' => 'success',
            'data' => $sectors,
        ];

        return Response::json($response);
    }

    public function all()
    {
        $sectors = Sector::get();

        $response = [
            'status' => 'success',
            'data' => $sectors,
        ];

        return Response::json($response);
    }

    /**
     * Find Broker
     */
    public function show($id)
    {
        $sector = Sector::find($id);

        $response = [
            'status' => 'success',
            'data' => $sector,
        ];

        return Response::json($response);
    }

    public function getBySlug(Request $request)
    {
        $slug = $request->get('slug');
        $sector = Sector::where('slug', $slug)->first();

        $response = [
            'status' => 'success',
            'data' => $sector,
        ];

        return Response::json($response);
    }

    public function store(Request $request)
    {
        $data = $request->all();

        $validator = Validator::make(
            $data, [
            'description' => 'max:500',
            ]
        );

        if ($validator->fails()) {
            return Response::json(
                [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
                ]
            );
        }

        $sector = Sector::firstOrCreate(
            [
            'name' => $data['name'],
            'slug' => $this->slugify($data['name']),
            'description' => $data['description'],
            'image' => $data['image'],
            ]
        );

        $sectorid = $sector->id;

        $sector_relation = [];
        if (isset($data['states']) && count($data['states']) > 0) {
            foreach ($data['states'] as $siccode) {
                array_push($sector_relation, ['sic_code_id' => $siccode, 'sector_id' => $sectorid]);
            }
        }

        SicCodeSectorRelationship::insert($sector_relation);

        $response = [
            'status' => 'success',
            'data' => $sector,
        ];

        return Response::json($response);
    }

    public function update(Request $request, $sector_id)
    {
        $data = $request->all();

        $validator = Validator::make(
            $data, [
            'description' => 'max:500',
            ]
        );

        if ($validator->fails()) {
            return Response::json(
                [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
                ]
            );
        }

        $sector = Sector::find($sector_id);

        $sector->name = $data['name'];
        $sector->slug = $this->slugify($data['name']);
        $sector->description = $data['description'];
        $sector->image = $data['image'];
        $sector->save();

        if (isset($data['states'])) {
            SicCodeSectorRelationship::where('sector_id', $sector->id)
                ->whereNotIn('sic_code_id', $data['states'])
                ->delete();

            SicCodeSectorRelationship::where('sector_id', $sector->id)
                ->whereIn('sic_code_id', $data['states'])
                ->restore();

            foreach ($data['states'] as $siccode) {
                SicCodeSectorRelationship::updateOrCreate(['sector_id' => $sector->id, 'sic_code_id' => (int)$siccode]);
            }
        } else {
            SicCodeSectorRelationship::where('sector_id', $sector->id)
                ->delete();
        }

        $response = [
            'status' => 'success',
            'data' => $sector,
        ];

        return Response::json($response);
    }

    public function delete($sector_id)
    {
        $sector = Sector::find($sector_id);
        $sector->delete();

        SicCodeSectorRelationship::withTrashed()
            ->where('sector_id', $sector->id)
            ->forceDelete();

        $response = [
            'status' => 'success',
            'message' => 'Deleted',
        ];

        return Response::json($response);
    }

}
