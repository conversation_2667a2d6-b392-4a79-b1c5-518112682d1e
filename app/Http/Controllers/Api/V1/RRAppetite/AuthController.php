<?php

namespace App\Http\Controllers\Api\V1\RRAppetite;

use App\Http\Controllers\BaseController;
use App\Models\Activity;
use App\Models\ApiKey;
use App\Models\LibertyUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;

class AuthController extends BaseController
{
    /**
     * Authenticate User
     *
     * @return mixed
     */
    public function auth(Request $request)
    {
        if ($request->has('id')) {
            //$id = $request->get('id');

            $user = LibertyUser::where('role', '=', 'admin')->where('id', '=', $request->get('id'))->firstOrFail();

            //Audit
            $this->log(
                $user,
                'Login',
                'User Login to RR Inspire',
                $request->header('session_ip'),
                $request->header('session_agent')
            );
            $user_id = $user->id;
            $key_data = [
                'user_id' => $user_id,
                'api_key' => Str::uuid()->toString(),
                'user_type' => 'liberty-user',
            ];
            ApiKey::create($key_data);
            return Response::json([
                'response' => 'success',
                'login_key' => $key_data['api_key'],
            ]);

        }

        return Response::json(
            [
                'response' => 'error',
                'message' => 'Could not log in.',
            ],
            200
        );

    }

    private function log($user, $action, $description, $session_ip = '127.0.0.1', $session_agent = 'No UserAgent')
    {
        $activity = new Activity();
        $activity->content_id = $user->id;
        $activity->user_id = (string)$user->id;
        $activity->content_type = 'User';
        $activity->action = $action;
        $activity->description = $description;
        $activity->details = 'Username: ' . $user->fullName();
        $activity->ip_address = $session_ip;
        $activity->user_agent = $session_agent;
        $activity->save();
    }


}
