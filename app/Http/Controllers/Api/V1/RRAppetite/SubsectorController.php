<?php

namespace App\Http\Controllers\Api\V1\RRAppetite;

use App\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use App\Models\RRAppetite\SicCodeSubSectorRelationship;
use App\Models\RRAppetite\Subsector;
use App\Models\RRAppetite\SubsectorTags;


class SubsectorController extends BaseController
{

    public function index(Request $request)
    {
        if ($request->has('list')) {
            $subsectors = Subsector::pluck('name', 'id')->all();
        } else {
            $subsectors = Subsector::all();
        }

        $response = [
            'status' => 'success',
            'data' => $subsectors,
        ];

        return response()->json($response);
    }

    public function all()
    {
        $subsectors = Subsector::get();

        $response = [
            'status' => 'success',
            'data' => $subsectors,
        ];

        return response()->json($response);
    }

    /**
     * Find Broker
     */
    public function show($id)
    {
        $subsector = Subsector::find($id);

        $response = [
            'status' => 'success',
            'data' => $subsector,
        ];

        return response()->json($response);
    }

    public function getSubsectorTagsBySubsectorID($id)
    {
        $tags = SubsectorTags::where('subsector_id', $id)->get();

        $response = [
            'status' => 'success',
            'data' => $tags,
        ];

        return response()->json($response);
    }

    public function getBySlug(Request $request)
    {
        $slug = $request->get('slug');
        $subsector = Subsector::where('slug', $slug)->first();

        $response = [
            'status' => 'success',
            'data' => $subsector,
        ];

        return response()->json($response);
    }

    public function store(Request $request)
    {
        $data = $request->all();

        $subsector = Subsector::firstOrCreate(
            [
            'name' => $data['name'],
            'slug' => $this->slugify($data['name']),
            ]
        );

        $subsectorid = $subsector->id;

        //subsector sic code
        $subsector_relation = [];
        if (isset($data['states']) && count($data['states']) > 0) {
            foreach ($data['states'] as $siccode) {
                $subsector_relation[] = ['sic_code_id' => $siccode, 'subsector_id' => $subsectorid];
            }
        }

        SicCodeSubSectorRelationship::insert($subsector_relation);

        //subsector Tags

        $tags = $data['tags'];

        $tags_array = explode(';', $tags);
        $tag_array_data = [];
        foreach ($tags_array as $tag) {
            $tag_array_data[] = ['subsector_id' => $subsectorid, 'tag' => $tag];
        }
        SubsectorTags::insert($tag_array_data);

        $response = [
            'status' => 'success',
            'data' => $subsector,
        ];

        return response()->json($response);
    }

    public function update(Request $request, $subsector_id)
    {
        $data = $request->all();

        $subsector = Subsector::find($subsector_id);

        $subsector->name = $data['name'];
        $subsector->slug = $this->slugify($data['name']);
        $subsector->save();

        //sic codes
        if (isset($data['states'])) {
            SicCodeSubSectorRelationship::where('subsector_id', $subsector->id)
                ->whereNotIn('sic_code_id', $data['states'])
                ->delete();

            SicCodeSubSectorRelationship::where('subsector_id', $subsector->id)
                ->whereIn('sic_code_id', $data['states'])
                ->restore();

            foreach ($data['states'] as $siccode) {
                SicCodeSubSectorRelationship::updateOrCreate(
                    [
                    'subsector_id' => $subsector->id,
                    'sic_code_id' => (int)$siccode,
                    ]
                );
            }
        } else {
            SicCodeSubSectorRelationship::where('subsector_id', $subsector->id)
                ->delete();
        }

        //Tags

        if (isset($data['tags'])) {
            $tags = explode(';', $data['tags']);

            SubsectorTags::where('subsector_id', $subsector->id)
                ->whereNotIn('tag', $tags)
                ->delete();

            SubsectorTags::where('subsector_id', $subsector->id)
                ->whereIn('tag', $tags)
                ->restore();

            foreach ($tags as $tag) {
                SubsectorTags::updateOrCreate(['subsector_id' => $subsector->id, 'tag' => $tag]);
            }
        } else {
            SubsectorTags::where('subsector_id', $subsector->id)
                ->delete();
        }

        $response = [
            'status' => 'success',
            'data' => $subsector,
        ];

        return response()->json($response);
    }

    public function delete($subsector_id)
    {
        $subsector = Subsector::find($subsector_id);

        $subsector->delete();

        SicCodeSubSectorRelationship::withTrashed()
            ->where('subsector_id', $subsector->id)
            ->forceDelete();

        SubsectorTags::withTrashed()
            ->where('subsector_id', $subsector->id)
            ->forceDelete();

        $response = [
            'status' => 'success',
            'message' => 'Deleted',
        ];

        return response()->json($response);
    }
}
