<?php

namespace App\Http\Controllers\Api\V1\RRAppetite;

use App\Http\Controllers\BaseController;
use Illuminate\Http\Request;

class IndexController extends BaseController
{
    /**
     * Store new Broker
     */

    public function index()
    {
        $response = [
            'status' => 'success',
            'message' => 'Index',
        ];

        return response()->json($response);
    }

    /**
     * Update Broker
     */
    public function update($id)
    {
        $response = [
            'status' => 'success',
            'message' => 'Update' . $id,
        ];

        return response()->json($response);
    }

    /**
     * Find Broker
     */
    public function show($id)
    {
        $response = [
            'status' => 'success',
            'message' => 'Show' . $id,
        ];

        return response()->json($response);
    }

    /**
     * Delete Broker
     *
     * @param $id
     *
     * @return mixed
     */
    public function destroy($id)
    {
        $response = [
            'status' => 'success',
            'message' => 'Destroy' . $id,
        ];

        return response()->json($response);
    }


    /**
     * Get All Brokers
     */
    public function all($page = 1, $limit = 10)
    {
        $response = [
            'status' => 'success',
            'message' => 'All',
        ];

        return response()->json($response);
    }

    public function options()
    {
        // return response()->json(Broker::orderBy('name', 'ASC')->pluck(
        //     'name',
        //     'id'
        // ));

        $response = [
            'status' => 'success',
            'message' => 'Options',
        ];

        return response()->json($response);
    }
}
