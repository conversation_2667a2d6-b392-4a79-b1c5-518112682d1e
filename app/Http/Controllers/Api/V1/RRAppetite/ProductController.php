<?php

namespace App\Http\Controllers\Api\V1\RRAppetite;

use App\Http\Controllers\BaseController;
use App\Models\LibertyUser;
use App\Models\RRAppetite\AppetiteClarificationsIntro;
use App\Models\RRAppetite\CoverWarrantiesIntro;
use App\Models\RRAppetite\Product;
use App\Models\RRAppetite\ProductIntro;
use App\Models\RRAppetite\ProductRelationship;
use App\Models\RRAppetite\ProductTag;
use App\Models\RRAppetite\ProductUnderwriter;
use App\Models\RRAppetite\Region;
use App\Models\RRAppetite\Sector;
use App\Models\RRAppetite\SectorExpertiseIntro;
use App\Models\RRAppetite\Subsector;
use App\Models\RRAppetite\TerritorySpecificInfoIntro;
use App\Models\RRAppetite\ValueAddOneIntro;
use App\Models\RRAppetite\ValueAddTwoIntro;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;

class ProductController extends BaseController
{

    public function store(Request $request)
    {
        $data = $request->all();

        $product = Product::firstOrCreate(
            [
                'name' => $data['name'],
                'slug' => $this->slugify($data['name']),
                'image' => $data['image'],
                'pdf_image' => $data['pdf_image'],
                'lm_pdf_image' => $data['lm_pdf_image'],
            ]
        );

        $product_id = $product->id;

        $product_underwriter = ProductUnderwriter::insert(
            [
                'product_id' => $product_id,
                'underwriter_id' => $data['underwriter_id'],
                'product_video_image' => $data['product_video_image'] ?? null,
                'product_video_link' => $data['product_video_link'] ?? null,
            ]
        );


        if (isset($data['region_product_uwr'])) {
            foreach ($data['region_product_uwr']['region'] as $key => $value) {

                ProductUnderwriter::insert(
                    [
                        'product_id' => $product_id,
                        'region_id' => $data['region_product_uwr']['region'][$key],
                        'underwriter_id' => $data['region_product_uwr']['underwriter'][$key],
                        'product_video_image' => $data['region_product_uwr']['product_video_image'][$key] ?? null,
                        'product_video_link' => $data['region_product_uwr']['product_video_link'][$key] ?? null,
                    ]
                );

            }
        }


        $tags = $data['tags'];

        $tags_array = explode(';', $tags);

        $tag_array_data = [];

        foreach ($tags_array as $tag) {
            array_push($tag_array_data, ['product_id' => $product_id, 'tag' => $tag]);
        }

        ProductTag::insert($tag_array_data);

        // Product Relationship
        $pr = ProductRelationship::firstOrCreate(
            [
                'product_id' => $product_id,
            ]
        );

        $pr_id = $pr->id;

        // Product Intro
        $pi = ProductIntro::firstOrCreate(
            [
                'description' => $data['product_intro'],
                'relationship_id' => $pr_id,
            ]
        );

        // sector_expertise Intro
        $se = SectorExpertiseIntro::firstOrCreate(
            [
                'description' => $data['sector_expertise'],
                'relationship_id' => $pr_id,
            ]
        );

        // cover_warranties Intro

        $se = CoverWarrantiesIntro::firstOrCreate(
            [
                'description_col_1' => $data['cover_warranties_col_1'],
                'description_col_2' => $data['cover_warranties_col_2'],
                'relationship_id' => $pr_id,
            ]
        );

        // territory_specific_info Intro

        $se = TerritorySpecificInfoIntro::firstOrCreate(
            [
                'description' => $data['territory_specific_info'],
                'relationship_id' => $pr_id,
            ]
        );

        // value_add_one Intro

        $se = ValueAddOneIntro::firstOrCreate(
            [
                'description' => $data['value_add_one'],
                'relationship_id' => $pr_id,
            ]
        );

        // value_add_two Intro

        if (isset($data['value_add_two'])) {
            $se = ValueAddTwoIntro::firstOrCreate(
                [
                    'description' => $data['value_add_two'],
                    'relationship_id' => $pr_id,
                ]
            );
        }

        $se = AppetiteClarificationsIntro::firstOrCreate(
            [
                'description' => $data['appetite_clarifications'],
                'relationship_id' => $pr_id,
            ]
        );

        // Add Combinations
        if (isset($data['combination'])) {
            $combinations = $data['combination'];

            $sections = [
                'intro',
                'sector',
                'cover_warranties',
                'territory_specific_info',
                'value_add_one',
                'value_add_two',
                'appetite_clarifications',
            ];

            foreach ($sections as $section) {

                if (isset($combinations[$section])) {
                    $combination_count = count($combinations[$section]['region']);
                    for ($i = 0; $i < $combination_count; $i++) {

                        if (isset($combinations[$section]['underwriter'][$i])) {
                            $uwr_id = $combinations[$section]['underwriter'][$i];
                        } else {
                            $uwr_id = $data['underwriter_id'];
                        }

                        $pr = ProductRelationship::firstOrCreate(
                            [
                                'product_id' => $product_id,
                                'region_id' => ($combinations[$section]['region'][$i] != '')
                                    ? $combinations[$section]['region'][$i]
                                    : null,
                                'sector_id' => ($combinations[$section]['sector'][$i] != '')
                                    ? $combinations[$section]['sector'][$i]
                                    : null,
                                'subsector_id' => ($combinations[$section]['subsector'][$i] != '')
                                    ? $combinations[$section]['subsector'][$i]
                                    : null,
                                'underwriter_id' => $uwr_id,
                            ]
                        );

                        $relationship_id = $pr->id;

                        if ($section == 'intro') {

                            $intro = ProductIntro::create(
                                [
                                    'relationship_id' => $relationship_id,
                                    'description' => $combinations[$section]['introtext'][$i],
                                ]
                            );

                        } elseif ($section == 'sector') {

                            $intro = SectorExpertiseIntro::create(
                                [
                                    'relationship_id' => $relationship_id,
                                    'description' => $combinations[$section]['introtext'][$i],
                                ]
                            );

                        } elseif ($section == 'value_add_one') {

                            $intro = ValueAddOneIntro::create(
                                [
                                    'relationship_id' => $relationship_id,
                                    'description' => $combinations[$section]['introtext'][$i],
                                ]
                            );

                        } elseif ($section == 'value_add_two') {

                            $intro = ValueAddTwoIntro::create(
                                [
                                    'relationship_id' => $relationship_id,
                                    'description' => $combinations[$section]['introtext'][$i],
                                ]
                            );

                        } elseif ($section == 'territory_specific_info') {

                            $intro = TerritorySpecificInfoIntro::create(
                                [
                                    'relationship_id' => $relationship_id,
                                    'description' => $combinations[$section]['introtext'][$i],
                                ]
                            );

                        } elseif ($section == 'appetite_clarifications') {

                            $intro = AppetiteClarificationsIntro::create(
                                [
                                    'relationship_id' => $relationship_id,
                                    'description' => $combinations[$section]['introtext'][$i],
                                ]
                            );

                        } else {

                            $intro = CoverWarrantiesIntro::create(
                                [
                                    'relationship_id' => $relationship_id,
                                    'description_col_1' => $combinations[$section]['introtext'][$i]['col_1'],
                                    'description_col_2' => $combinations[$section]['introtext'][$i]['col_2'],
                                ]
                            );

                        }


                    }

                }

            }
        }


        $response = [
            'status' => 'success',
            'data' => $product,
        ];

        return response()->json($response);
    }

    public function all()
    {
        $products = Product::all();

        $response = [
            'status' => 'success',
            'data' => $products,
        ];

        return response()->json($response);
    }

    public function searchProduct($product_slug, $sector_slug)
    {
        $product = Product::where('slug', $product_slug)->first();
        $sector = Sector::where('slug', $sector_slug)->first();

        if (!$product || !$sector) {
            return response()->json(
                [
                    'status' => 'error',
                    'message' => 'Invalid form submission',
                ]
            );
        }

        $subsector_ids = ProductRelationship::whereNotNull('region_id')
            ->where('product_id', $product->id)
            ->where('sector_id', $sector->id)
            ->pluck('subsector_id');

        $subsectors = Subsector::whereIn('id', $subsector_ids)->get();

        return response()->json(
            [
                'status' => 'success',
                'data' => [
                    'subsectors' => $subsectors,
                ],
            ]
        );
    }

    public function getProductAssociations($id)
    {
        $product = Product::find($id);
        $product_underwriter_default = $product->underwriter()->first();

        if ($product_underwriter_default) {
            $product_underwriter = LibertyUser::find($product_underwriter_default->underwriter_id);
            $product->underwriter = $product_underwriter;
            $product->product_video_image = $product_underwriter_default->product_video_image;
            $product->product_video_link = $product_underwriter_default->product_video_link;
        }

        $product_relationship_default = ProductRelationship::where('product_id', '=', $id)
            ->where('region_id', null)
            ->where('sector_id', null)
            ->where('subsector_id', null)
            ->with('regionRelationship')
            ->with('sector')
            ->with('subsector')
            ->with('underwriter')
            ->with('productIntro')
            ->with('sectorExpertiseIntro')
            ->with('coverWarrantiesIntro')
            ->with('territorySpecificInfoIntro')
            ->with('valueAddOneIntro')
            ->with('valueAddTwoIntro')
            ->with('appetiteClarificationsIntro')
            ->get();

        $product_relationship_combinations = ProductRelationship::where(
            function ($query) use ($id) {
                $query->whereNotNull('region_id')
                    ->orWhereNotNull('sector_id')
                    ->orWhereNotNull('subsector_id')
                    ->groupBy('region_id', 'sector_id', 'subsector_id');
            }
        )
            ->where('product_id', $id)
            ->with('regionRelationship')
            ->with('sector')
            ->with('subsector')
            ->with('underwriter')
            ->with('productIntro')
            ->with('sectorExpertiseIntro')
            ->with('coverWarrantiesIntro')
            ->with('territorySpecificInfoIntro')
            ->with('valueAddOneIntro')
            ->with('valueAddTwoIntro')
            ->with('appetiteClarificationsIntro')
            ->get();

        $product_underwriter_combinations = ProductUnderwriter::where(
            'product_id',
            $id
        )->whereNotNull('region_id')->get();

        $product_tags = ProductTag::where('product_id', '=', $id)->pluck('tag');

        $tags = implode(';', $product_tags);

        $data = [
            'product' => $product,
            'tags' => $tags,
            'relationships' => [
                'default' => $product_relationship_default,
                'combinations' => $product_relationship_combinations,
                'uwr_combinations' => $product_underwriter_combinations,
            ],
        ];
        $response = [
            'status' => 'success',
            'data' => $data,
        ];

        return response()->json($response);
    }

    public function destroyProductCombination(Request $request)
    {
        $relationship_id = $request->get('relationship_id');
        $partial_type = $request->get('partial_type');
        $partial_id = $request->get('partial_id');

        switch ($partial_type) {
            case 'intro':
                ProductIntro::where('relationship_id', '=', $relationship_id)->delete();
                break;

            case 'sector':
                SectorExpertiseIntro::where('relationship_id', '=', $relationship_id)->delete();
                break;

            case 'cover_warranties':
                CoverWarrantiesIntro::where('relationship_id', '=', $relationship_id)->delete();
                break;

            case 'value_add_one':
                ValueAddOneIntro::where('relationship_id', '=', $relationship_id)->delete();
                break;

            case 'value_add_two':
                ValueAddTwoIntro::where('relationship_id', '=', $relationship_id)->delete();
                break;

            case 'territory_specific_info':
                TerritorySpecificInfoIntro::where('relationship_id', '=', $relationship_id)->delete();
                break;

            case 'appetite_clarifications':
                AppetiteClarificationsIntro::where('relationship_id', '=', $relationship_id)->delete();
                break;

            default:
                break;
        }

        $data = [
            'relationship_id' => $relationship_id,
            'partial_type' => $partial_type,
            'partial_id' => $partial_id,
        ];

        $response = [
            'status' => 'success',
            'data' => $data,
        ];

        return response()->json($response);
    }

    /**
     * delete a product
     *
     * @param  int  $product_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($product_id)
    {
        $product = Product::find($product_id);
        $product->delete();

        ProductRelationship::where('product_id', $product_id)->delete();

        $response = [
            'status' => 'success',
            'message' => 'Deleted',
        ];

        return response()->json($response);
    }

    /**
     * search products
     */
    public function search(Request $request)
    {
        $term = urldecode($request->get('search'));
        //dd($term);
        $region = $request->get('region');
        Session::put('region', $region);

        if ($request->has('page')) {
            $query = "select * from `ra_products` inner join `ra_product_relationships` on `ra_product_relationships`.`product_id` = `ra_products`.`id` where `ra_products`.`deleted_at` is null and `ra_product_relationships`.`deleted_at` is null and `ra_product_relationships`.`sector_id` is null and `ra_product_relationships`.`subsector_id` is null and (MATCH (name) AGAINST ('+" . $term . "' IN BOOLEAN MODE) or (select count(*) from `ra_product_intro` inner join `ra_product_relationships` on `ra_product_relationships`.`id` = `ra_product_intro`.`relationship_id` where `ra_products`.`id` = `ra_product_relationships`.`id` and MATCH (description) AGAINST ('+" . $term . "' IN BOOLEAN MODE) and `ra_product_intro`.`deleted_at` is null) >= 1 or (select count(*) from `ra_sector_expertise_intro` inner join `ra_product_relationships` on `ra_product_relationships`.`id` = `ra_sector_expertise_intro`.`relationship_id` where `ra_products`.`id` = `ra_product_relationships`.`id` and MATCH (description) AGAINST ('+" . $term . "' IN BOOLEAN MODE) and `ra_sector_expertise_intro`.`deleted_at` is null) >= 1 or (select count(*) from `ra_cover_warranties_intro` inner join `ra_product_relationships` on `ra_product_relationships`.`id` = `ra_cover_warranties_intro`.`relationship_id` where `ra_products`.`id` = `ra_product_relationships`.`id` and MATCH (description_col_1,description_col_2) AGAINST ('+" . $term . "' IN BOOLEAN MODE) and `ra_cover_warranties_intro`.`deleted_at` is null) >= 1 or (select count(*) from `ra_territory_specific_info_intro` inner join `ra_product_relationships` on `ra_product_relationships`.`id` = `ra_territory_specific_info_intro`.`relationship_id` where `ra_products`.`id` = `ra_product_relationships`.`id` and MATCH (description) AGAINST ('+" . $term . "' IN BOOLEAN MODE) and `ra_territory_specific_info_intro`.`deleted_at` is null) >= 1 or (select count(*) from `ra_value_add_one_intro` inner join `ra_product_relationships` on `ra_product_relationships`.`id` = `ra_value_add_one_intro`.`relationship_id` where `ra_products`.`id` = `ra_product_relationships`.`id` and MATCH (description) AGAINST ('+" . $term . "' IN BOOLEAN MODE) and `ra_value_add_one_intro`.`deleted_at` is null) >= 1 or (select count(*) from `ra_value_add_two_intro` inner join `ra_product_relationships` on `ra_product_relationships`.`id` = `ra_value_add_two_intro`.`relationship_id` where `ra_products`.`id` = `ra_product_relationships`.`id` and MATCH (description) AGAINST ('+" . $term . "' IN BOOLEAN MODE) and `ra_value_add_two_intro`.`deleted_at` is null) >= 1 or (select count(*) from `ra_product_tags` where `ra_product_tags`.`product_id` = `ra_products`.`id` and MATCH (tag) AGAINST ('+" . $term . "' IN BOOLEAN MODE) and `ra_product_tags`.`deleted_at` is null) >= 1) LIMIT 10 OFFSET " . ($request->get('page') - 1) * 10;
        } else {
            $query = "select * from `ra_products` inner join `ra_product_relationships` on `ra_product_relationships`.`product_id` = `ra_products`.`id` where `ra_products`.`deleted_at` is null and `ra_product_relationships`.`deleted_at` is null and `ra_product_relationships`.`sector_id` is null and `ra_product_relationships`.`subsector_id` is null and (MATCH (name) AGAINST ('+" . $term . "' IN BOOLEAN MODE) or (select count(*) from `ra_product_intro` inner join `ra_product_relationships` on `ra_product_relationships`.`id` = `ra_product_intro`.`relationship_id` where `ra_products`.`id` = `ra_product_relationships`.`id` and MATCH (description) AGAINST ('+" . $term . "' IN BOOLEAN MODE) and `ra_product_intro`.`deleted_at` is null) >= 1 or (select count(*) from `ra_sector_expertise_intro` inner join `ra_product_relationships` on `ra_product_relationships`.`id` = `ra_sector_expertise_intro`.`relationship_id` where `ra_products`.`id` = `ra_product_relationships`.`id` and MATCH (description) AGAINST ('+" . $term . "' IN BOOLEAN MODE) and `ra_sector_expertise_intro`.`deleted_at` is null) >= 1 or (select count(*) from `ra_cover_warranties_intro` inner join `ra_product_relationships` on `ra_product_relationships`.`id` = `ra_cover_warranties_intro`.`relationship_id` where `ra_products`.`id` = `ra_product_relationships`.`id` and MATCH (description_col_1,description_col_2) AGAINST ('+" . $term . "' IN BOOLEAN MODE) and `ra_cover_warranties_intro`.`deleted_at` is null) >= 1 or (select count(*) from `ra_territory_specific_info_intro` inner join `ra_product_relationships` on `ra_product_relationships`.`id` = `ra_territory_specific_info_intro`.`relationship_id` where `ra_products`.`id` = `ra_product_relationships`.`id` and MATCH (description) AGAINST ('+" . $term . "' IN BOOLEAN MODE) and `ra_territory_specific_info_intro`.`deleted_at` is null) >= 1 or (select count(*) from `ra_value_add_one_intro` inner join `ra_product_relationships` on `ra_product_relationships`.`id` = `ra_value_add_one_intro`.`relationship_id` where `ra_products`.`id` = `ra_product_relationships`.`id` and MATCH (description) AGAINST ('+" . $term . "' IN BOOLEAN MODE) and `ra_value_add_one_intro`.`deleted_at` is null) >= 1 or (select count(*) from `ra_value_add_two_intro` inner join `ra_product_relationships` on `ra_product_relationships`.`id` = `ra_value_add_two_intro`.`relationship_id` where `ra_products`.`id` = `ra_product_relationships`.`id` and MATCH (description) AGAINST ('+" . $term . "' IN BOOLEAN MODE) and `ra_value_add_two_intro`.`deleted_at` is null) >= 1 or (select count(*) from `ra_product_tags` where `ra_product_tags`.`product_id` = `ra_products`.`id` and MATCH (tag) AGAINST ('+" . $term . "' IN BOOLEAN MODE) and `ra_product_tags`.`deleted_at` is null) >= 1) LIMIT 10";
        }
        $products = DB::select(DB::raw($query));

        $response = [
            'status' => 'success',
            'data' => [
                'data' => $products,
                'total' => count($products),
                'per_page' => 10,
                'current_page' => $request->has('page')
                    ? $request->get('page')
                    : 1,
                'last_page' => $request->has('page')
                    ? $request->get('page') - 1
                    : 1,
                'from' => $request->has('page')
                    ? (($request->get('page') - 1) * 10) + 1
                    : 1,
                'to' => $request->has('page')
                    ? (($request->get('page') - 1) * 10) + count($products)
                    : count($products),
            ],
        ];

        return response()->json($response);
    }

    public function searchAll(Request $request)
    {
        $keyword = urldecode($request->get('keyword'));
        $region = $request->get('region');

        $sql = "select distinct
            s.name as sector_name,
            s.slug as sector_slug,
            ss.name as sub_sector_name,
            ss.slug as sub_sector_slug,
            p.name as product_name,
            p.slug as product_slug,
            p.updated_at
        from ra_products p
            left join ra_product_relationships pr on p.id=pr.product_id
            left join ra_regions r on pr.region_id=r.id
            left join ra_sectors s on pr.sector_id=s.id
            left join ra_subsectors ss on pr.subsector_id=ss.id
            left join ra_product_tags pt on p.id=pt.product_id
            left join ra_sic_sector_relationships scsr on s.id=scsr.sector_id
            left join ra_sic_codes sc on scsr.sic_code_id=sc.id
            left join ra_sic_subsector_relationships scssr on ss.id=scssr.subsector_id
            left join ra_sic_codes ssc on scssr.sic_code_id=ssc.id
            left join ra_subsector_tags st on ss.id=st.subsector_id
        WHERE
            (r.id=" . $region . " or r.id is NULL) AND
            CONCAT_WS ('', p.deleted_at, s.deleted_at, ss.deleted_at, r.deleted_at,sc.deleted_at,ssc.deleted_at,st.deleted_at) ='' AND
            CONCAT_WS (' ', p.name, s.name, ss.name, r.name,sc.code,ssc.code,st.tag)
            LIKE CONCAT('%', '$keyword' , '%')
            order BY p.updated_at desc, s.name, ss.name, p.name
            LIMIT 10 ";

        $products = DB::select(DB::raw($sql));

        $response = [
            'status' => 'success',
            'data' => $products,
        ];

        return response()->json($response);
    }

    /**
     * Find Product
     */
    public function show($id)
    {
        $product = Product::find($id);

        $response = [
            'status' => 'success',
            'data' => $product,
        ];

        return response()->json($response);
    }

    public function getBySlug(Request $request)
    {
        $slug = $request->get('slug');
        $product = Product::where('slug', $slug)->first();
        if ($product) {
            $response = [
                'status' => 'success',
                'data' => $product,
            ];
            return response()->json($response);
        }

        $response = [
            'status' => 'error',
            'message' => 'Product not found',
        ];
        return response()->json($response);

    }

    public function getUnderwriterForProduct(Request $request)
    {
        $slug = $request->get('product_slug');
        $sector_slug = $request->get('sector_slug');
        $subsector_slug = $request->get('subsector_slug');
        $region_id = $request->get('region_id');

        // verify that this is the correct product inside correct sector/subsector/region
        $product = Product::where('slug', $slug)->first();
        $sector = Sector::where('slug', $sector_slug)->first();
        $subsector = !is_null($subsector_slug)
            ? Subsector::where('slug', $subsector_slug)->first()
            : null;
        $region = Region::find($region_id);

        $product_uwr = ProductUnderwriter::where('region_id', $region_id)->where('product_id', $product->id);

        // $product_relationship = $region->regionInfo()
        //         ->whereIn('region_id', [0, $region_id])
        //         ->whereIn('product_id', [0, $product->id]);

        // if ($sector) {
        //     $product_relationship = $product_relationship->whereIn('sector_id', [$sector->id, 0]);
        // }

        // if ($subsector) {
        //     $product_relationship = $product_relationship->whereIn('subsector_id', [$subsector->id,0]);
        // }

        $product_uwr = $product_uwr->first();

        if (!$product_uwr) {

            $product_uwr = ProductUnderwriter::whereNull('region_id')->where('product_id', $product->id);
            $product_uwr = $product_uwr->first();
        }

        // echo "<pre>";
        // print_r($product_relationship); exit;

        if ($product_uwr) {
            // if underwriter id is not null in product relationship
            // get the specific underwriter for this product in this region
            $underwriter = false;
            $underwriter_object = [];

            if (!is_null($product_uwr->underwriter_id)) {
                $underwriter = LibertyUser::find($product_uwr->underwriter_id);
            } else {
                $underwriter = $product->underwriter()->first();
                if ($underwriter) {
                    $underwriter = LibertyUser::find($underwriter->underwriter_id);
                }
            }
            if ($underwriter) {
                $underwriter_object = [
                    'first_name' => $underwriter->first_name,
                    'last_name' => $underwriter->last_name,
                    'email' => $underwriter->email,
                    'role' => $underwriter->role,
                    'image' => $underwriter->image,
                ];
            }

            return response()->json(
                [
                    'status' => 'success',
                    'data' => $underwriter_object,
                ]
            );
        }

        return response()->json(
            [
                'status' => 'error',
                'message' => 'Product not found',
            ]
        );
    }

    public function deleteVideoFrame($product_id)
    {
        ProductUnderwriter::where('product_id', $product_id)->update(
            [
                'product_video_image' => null,
            ]
        );

        return response()->json(
            [
                'status' => 'success',
                'message' => 'Video frame successfully deleted.',
            ]
        );
    }

    public function update(Request $request, $product_id)
    {
        $data = $request->all();

        $product = Product::find($product_id);

        $product->name = $data['name'];

        $product->slug = $this->slugify($data['name']);

        $product->image = $data['image'];

        $product->pdf_image = $data['pdf_image'];
        $product->lm_pdf_image = $data['lm_pdf_image'];

        $product->save();

        // Product Relationship
        $pr = ProductRelationship::where('product_id', '=', $product_id)->pluck('id');

        $tags = $data['tags'];

        $tags_array = explode(';', $tags);

        $tag_array_data = [];

        foreach ($tags_array as $tag) {
            array_push($tag_array_data, ['product_id' => $product_id, 'tag' => $tag]);
        }

        ProductTag::where('product_id', '=', $product_id)->delete();

        ProductTag::insert($tag_array_data);

        $default_underwriter = ProductUnderwriter::where('product_id', $product_id)->delete();

        ProductUnderwriter::insert(
            [
                'product_id' => $product_id,
                'underwriter_id' => $data['underwriter_id'],
                'product_video_image' => $data['product_video_image'],
                'product_video_link' => $data['product_video_link'],
            ]
        );

        if (isset($data['region_product_uwr'])) {
            foreach ($data['region_product_uwr']['region'] as $key => $value) {

                ProductUnderwriter::insert(
                    [
                        'product_id' => $product_id,
                        'region_id' => $data['region_product_uwr']['region'][$key],
                        'underwriter_id' => $data['region_product_uwr']['underwriter'][$key],
                        'product_video_image' => $data['region_product_uwr']['product_video_image'][$key] ?? null,
                        'product_video_link' => $data['region_product_uwr']['product_video_link'][$key] ?? null,
                    ]
                );

            }
        }

        foreach ($pr as $relationship_id) {
            ProductIntro::where('relationship_id', '=', $relationship_id)->delete();
            SectorExpertiseIntro::where('relationship_id', '=', $relationship_id)->delete();
            CoverWarrantiesIntro::where('relationship_id', '=', $relationship_id)->delete();
            ValueAddOneIntro::where('relationship_id', '=', $relationship_id)->delete();
            ValueAddTwoIntro::where('relationship_id', '=', $relationship_id)->delete();
            TerritorySpecificInfoIntro::where('relationship_id', '=', $relationship_id)->delete();
            AppetiteClarificationsIntro::where('relationship_id', '=', $relationship_id)->delete();
        }

        ProductRelationship::where('product_id', '=', $product_id)->delete();

        $pr = ProductRelationship::firstOrCreate(
            [
                'product_id' => $product_id,
            ]
        );

        $pr_id = $pr->id;

        // Product Intro

        $pi = ProductIntro::firstOrCreate(
            [
                'description' => $data['product_intro'],
                'relationship_id' => $pr_id,
            ]
        );


        // sector_expertise Intro

        $se = SectorExpertiseIntro::firstOrCreate(
            [
                'description' => $data['sector_expertise'],
                'relationship_id' => $pr_id,
            ]
        );

        // cover_warranties Intro

        $se = CoverWarrantiesIntro::firstOrCreate(
            [
                'description_col_1' => $data['cover_warranties_col_1'],
                'description_col_2' => $data['cover_warranties_col_2'],
                'relationship_id' => $pr_id,
            ]
        );

        // territory_specific_info Intro

        $se = TerritorySpecificInfoIntro::firstOrCreate(
            [
                'description' => $data['territory_specific_info'],
                'relationship_id' => $pr_id,
            ]
        );

        // value_add_one Intro

        $se = ValueAddOneIntro::firstOrCreate(
            [
                'description' => $data['value_add_one'],
                'relationship_id' => $pr_id,
            ]
        );

        // value_add_two Intro
        if (isset($data['value_add_two'])) {
            $se = ValueAddTwoIntro::firstOrCreate(
                [
                    'description' => $data['value_add_two'],
                    'relationship_id' => $pr_id,
                ]
            );
        }

        $se = AppetiteClarificationsIntro::firstOrCreate(
            [
                'description' => $data['appetite_clarifications'],
                'relationship_id' => $pr_id,
            ]
        );

        // Add Combinations
        if (isset($data['combination'])) {
            $combinations = $data['combination'];

            $sections = [
                'intro',
                'sector',
                'cover_warranties',
                'territory_specific_info',
                'value_add_one',
                'value_add_two',
                'appetite_clarifications',
            ];

            foreach ($sections as $section) {

                if (isset($combinations[$section])) {
                    $combination_count = count($combinations[$section]['region']);

                    // reset index of keys inside each array so the for count will increment properly
                    $combinations[$section]['region'] = array_values($combinations[$section]['region']);
                    $combinations[$section]['sector'] = array_values($combinations[$section]['sector']);
                    $combinations[$section]['subsector'] = array_values($combinations[$section]['subsector']);
                    $combinations[$section]['introtext'] = array_values($combinations[$section]['introtext']);


                    for ($i = 0; $i < $combination_count; $i++) {

                        // dd($combinations[$section]['subsector'][$i]);

                        $pr = ProductRelationship::firstOrCreate(
                            [
                                'product_id' => $product_id,
                                'region_id' => ($combinations[$section]['region'][$i] != '')
                                    ? $combinations[$section]['region'][$i]
                                    : null,
                                'sector_id' => ($combinations[$section]['sector'][$i] != '')
                                    ? $combinations[$section]['sector'][$i]
                                    : null,
                                'subsector_id' => ($combinations[$section]['subsector'][$i] != '')
                                    ? $combinations[$section]['subsector'][$i]
                                    : null,
                            ]
                        );

                        $relationship_id = $pr->id;

                        if ($section == 'intro') {

                            $intro = ProductIntro::create(
                                [
                                    'relationship_id' => $relationship_id,
                                    'description' => $combinations[$section]['introtext'][$i],
                                ]
                            );

                        } elseif ($section == 'sector') {

                            $intro = SectorExpertiseIntro::create(
                                [
                                    'relationship_id' => $relationship_id,
                                    'description' => $combinations[$section]['introtext'][$i],
                                ]
                            );

                        } elseif ($section == 'value_add_one') {

                            $intro = ValueAddOneIntro::create(
                                [
                                    'relationship_id' => $relationship_id,
                                    'description' => $combinations[$section]['introtext'][$i],
                                ]
                            );

                        } elseif ($section == 'value_add_two') {

                            $intro = ValueAddTwoIntro::create(
                                [
                                    'relationship_id' => $relationship_id,
                                    'description' => $combinations[$section]['introtext'][$i],
                                ]
                            );

                        } elseif ($section == 'territory_specific_info') {

                            $intro = TerritorySpecificInfoIntro::create(
                                [
                                    'relationship_id' => $relationship_id,
                                    'description' => $combinations[$section]['introtext'][$i],
                                ]
                            );

                        } elseif ($section == 'appetite_clarifications') {

                            $intro = AppetiteClarificationsIntro::create(
                                [
                                    'relationship_id' => $relationship_id,
                                    'description' => $combinations[$section]['introtext'][$i],
                                ]
                            );

                        } else {

                            $intro = CoverWarrantiesIntro::create(
                                [
                                    'relationship_id' => $relationship_id,
                                    'description_col_1' => $combinations[$section]['introtext'][$i]['col_1'],
                                    'description_col_2' => $combinations[$section]['introtext'][$i]['col_2'],
                                ]
                            );

                        }


                    }

                }

            }
        }


        $response = [
            'status' => 'success',
            'data' => $product,
        ];

        return response()->json($response);
    }

}
