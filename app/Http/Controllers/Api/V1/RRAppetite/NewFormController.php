<?php

namespace App\Http\Controllers\Api\V1\RRAppetite;

use App\Http\Controllers\BaseController;
use App\Models\Broker;
use App\Models\FileUpload;
use App\Models\Mailqueue;
use App\Models\RRAppetite\FormSubmission;
use App\Models\RRAppetite\FormSubmissionProduct;
use Carbon\Carbon;
use DateTime;
use DateTimeZone;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;

class NewFormController extends BaseController
{
    public function __construct(Mailqueue $mailqueue, FileUpload $fileupload)
    {
        $this->mail = $mailqueue;
        $this->files = $fileupload;
    }

    public function activeForms()
    {
        $submissions = FormSubmission::where('archived', 0)
            ->orderby('created_at', 'desc')
            ->get();

        foreach ($submissions as &$submission) {
            $submission->products = [];

            if ($submission->verified_broker) {
                $email_domain = substr(strrchr($submission['email'], "@"), 1);

                $broker = Broker::where('domain', 'LIKE', '%' . $email_domain . '%')->first();

                if ($broker) {
                    $submission->broker = $broker->name;
                }
            }

            if (isset($submission->data_cloudname)) {
                $file = $this->getFileData($submission);

                $submission->products = $file->product_binder ?? '';
            }
        }

        $data = [
            'submissions' => $submissions,
        ];

        return Response::json(
            [
                'status' => 'success',
                'data' => $data,
            ]
        );
    }

    private function getFileData($submission)
    {
        if (!$this->files->exists('json/' . $submission['data_cloudname'])) {
            return;
        }

        $file = $this->files->download('json/' . $submission['data_cloudname'], $submission['data_cloudname']);

        $data = file_get_contents(storage_path() . '/file_to_download/' . $submission['data_cloudname']);

        return json_decode($data);
    }

    public function archivedForms()
    {
        $submissions = FormSubmission::where('archived', 1)
            ->orderby('created_at', 'desc')
            ->get();

        foreach ($submissions as &$submission) {
            $submission->products = [];

            if ($submission->verified_broker) {
                $email_domain = substr(strrchr($submission['email'], "@"), 1);

                $broker = Broker::where('domain', 'LIKE', '%' . $email_domain . '%')->first();

                if ($broker) {
                    $submission->broker = $broker->name;
                }
            }

            if (isset($submission->data_cloudname)) {
                $file = $this->getFileData($submission);

                $submission->products = $file->product_binder ?? '';
            }
        }

        $data = [
            'submissions' => $submissions,
        ];

        return Response::json(
            [
                'status' => 'success',
                'data' => $data,
            ]
        );
    }

    public function show(FormSubmission $formSubmission)
    {
        $submission = $formSubmission;

        if ($submission->verified_broker) {
            $email_domain = substr(strrchr($submission['email'], "@"), 1);

            $broker = Broker::where('domain', 'LIKE', '%' . $email_domain . '%')->first();
        }

        $file = $this->getFileData($submission);
        // print_r($file);exit;
        $data = [
            'submission' => $submission,
            'products' => $file->product_binder ?? '',
            'underwriter' => $file->underwriter_underwriter ?? '',
            'region' => $submission->region_slug ?? '',
            'region_name' => $file->region_name ?? '',
            'primary_product' => isset($file->broker_primary_product) && isset($file->broker_primary_product->product_name_1564400540191)
                ? $file->broker_primary_product->product_name_1564400540191
                : "N/A",
            'primary_sector' => isset($file->broker_primary_product) && isset($file->broker_primary_product->sector_1564412648774) && isset($file->broker_primary_product->sector_1564412648774[0]) && isset($file->broker_primary_product->sector_1564412648774[0]->name)
                ? $file->broker_primary_product->sector_1564412648774[0]->name
                : "",
            'primary_subsector' => isset($file->broker_primary_product) && isset($file->broker_primary_product->subsector_1564412664986) && isset($file->broker_primary_product->subsector_1564412664986[0]) && isset($file->broker_primary_product->subsector_1564412664986[0]->name)
                ? $file->broker_primary_product->subsector_1564412664986[0]->name
                : "",
        ];
        if (isset($broker)) {
            $data['broker'] = $broker;
        }

        return Response::json(
            [
                'status' => 'success',
                'data' => $data,
            ]
        );
    }

    public function handleSubmission(Request $request)
    {
        $form_submission = $request->get('submission');

        // validate everything here, get ids for the products
        if (!is_array($form_submission)) {
            $form_submission = get_object_vars(json_decode($form_submission));
        }

        // separate out product binders
        $product_binder = $form_submission['product_binder'];

        // check if email to send back to is on broker user table
        $email_domain = substr(strrchr($form_submission['email'], "@"), 1);

        $verified = $this->verifyEmail($email_domain);
        $form_submission = $this->createSubmissionObject($form_submission);

        // verified broker verified status
        $form_submission['verified_broker'] = $verified
            ? 1
            : 0;

        // store main submission
        $submission = FormSubmission::create($form_submission);

        // get id back from main submission
        if ($submission) {
            $form_id = $submission->id;

            $form_submission_ids = [];

            // store products for submission
            foreach ($product_binder as $product) {
                $submission['region_slug'] = $product->region_slug;

                $submission_product = FormSubmissionProduct::create(
                    [
                        'form_id' => $form_id,
                        'product_slug' => $product->product_slug,
                        'region_slug' => $product->region_slug,
                        'sector_slug' => $product->sector_slug,
                        'subsector_slug' => $product->subsector_slug,
                        'underwriter_slug' => $product->underwriter_slug,
                    ]
                );

                array_push($form_submission_ids, $submission_product->id);
            }

            // send email to broker if verified
            if ($verified) {
                $this->sendBrokerEmail($submission, $form_submission);
                $this->sendUnderwriterEmail($submission, $form_submission, $verified, $email_domain);
            } else {
                $this->sendBrokerEmail($submission, $form_submission, 'unverified');
                $this->sendUnderwriterEmail($submission, $form_submission, $verified, $email_domain);
            }

            $data = [
                'form_id' => $form_id,
                'form_product_ids' => $form_submission_ids,
                'important_enquiry' => $submission['req_price'] == 1,
                'verified' => (bool)$verified,
            ];

            return Response::json(
                [
                    'status' => 'success',
                    'data' => $data,
                ]
            );
        }

        return Response::json(
            [
                'status' => 'error',
                'data' => 'Form failed to save',
            ], 403
        );
    }

    private function verifyEmail($broker_email)
    {
        $check_domain = Broker::where('domain', 'LIKE', '%' . $broker_email . '%')->count();

        if ($check_domain > 0) {
            return true;
        }

        return false;
    }

    private function createSubmissionObject($submission)
    {
        $submission['req_price'] = $submission['price'];

        // this should be done on rr end
        unset($submission['price']);
        unset($submission['date']);
        unset($submission['time']);
        unset($submission['selected_underwriter_email']);
        unset($submission['selected_underwriter_product']);
        unset($submission['product_binder']);

        return $submission;
    }

    private function sendBrokerEmail($submission, $form_submission, $verified = 'verified', $resend = '')
    {
        $file = $this->getFileData($submission);

        $submission['region'] = $submission['region_name'];
        $submission['region_name'] = isset($file->region_name)?$file->region_name:null;

        $products = $submission['products'];

        // get pdf download link, expiry days (from pdf link presumably)
        $s3_link = 'pdf/' . $submission['id'] . '/liberty-presentation.pdf';
        $download_link = null;

        try {
            $download_link = $this->files->link('pdf/' . $submission['id'] . '/liberty-presentation.pdf', '7 days');
        } catch (\Exception $e) {
            $download_link = null;
        }

        $links = [
            'download_pdf' => $download_link,
            'expiry_time' => '7',
        ];

        $tmpl_type = 'lsm';

        if (isset($file->region_slug) && $file->region_slug == 'us') {
            $tmpl_type = 'lmi';
        }

        $products = isset($file->broker_products)?$file->broker_products:null;
        $primary_product = isset($file->broker_primary_product) && is_array($file->broker_primary_product)
            ? $file->broker_primary_product[0]
            : $file->broker_primary_product;

        if ($verified == 'verified') {
            $this->mail->queue(
                $submission['email'],
                $submission['fname'] . ' ' . $submission['surname'],
                'Your PDF is ready to download',
                'emails.risk-appetite.' . $tmpl_type . '.broker',
                [
                    'links' => $links,
                    'submission' => $submission,
                    'products' => $products,
                    'primary_product' => $primary_product,
                ]
            );
        } else {
            if ($resend == 'resend') {
                $email_template = 'emails.risk-appetite.' . $tmpl_type . '.broker_unverified_resend';
                $subject = 'Your PDF is ready to download';
            } else {
                $email_template = 'emails.risk-appetite.' . $tmpl_type . '.broker_unverified';
                $subject = 'Thank you for your submission';
            }

            $this->mail->queue(
                $submission['email'],
                $submission['fname'] . ' ' . $submission['surname'],
                $subject,
                $email_template,
                [
                    'links' => $links,
                    'submission' => $submission,
                    'products' => $products,
                    'primary_product' => $primary_product,
                ]
            );
        }

        return true;
    }

    private function sendUnderwriterEmail($submission, $form_submission, $verified, $email_domain, $resend = '')
    {
        $submission = $submission->toArray();

        $file = $this->getFileData($submission);

        $submission['region'] = (isset($form_submission) && !is_null($form_submission))
            ? $form_submission['region_name']
            : '';
        $submission['region_name'] = $file->region_name;

        $ics_file = $this->compileICSFile($submission);

        $product_names = implode(", ", $file->underwriter_product_names);

        $s3_link = 'pdf/' . $submission['id'] . '/liberty-presentation.pdf';
        $download_link = null;

        try {
            $download_link = $this->files->link('pdf/' . $submission['id'] . '/liberty-presentation.pdf', '7 days');
        } catch (\Exception $e) {
            $download_link = null;
        }

        // attachment, pdf link
        $links = [
            'dealt_with' => config('app.admin_frontend') . '/rr-appetite/enquiries/' . $submission['id'] . '/status?enquiry=1',
            'not_dealt_with' => config('app.admin_frontend') . '/rr-appetite/enquiries/' . $submission['id'] . '/status?enquiry=0',
            'download_pdf' => $download_link,
            'expiry_time' => '7',
        ];

        $attachments = [
            'ics' => [
                'StringValue' => $ics_file,
                'DataType' => 'string',
            ],
        ];

        if ($ics_file) {
            $attachments = [
                'ics' => [
                    'StringValue' => $ics_file,
                    'DataType' => 'String',
                ],
            ];
        }

        // $cc_email = '<EMAIL>';
        $cc_email = '<EMAIL>';
        // $cc_email = '<EMAIL>';

        $tmpl_type = 'lsm';

        if ($file->region_slug == 'us') {
            $tmpl_type = 'lmi';
        }

        $email_date = $this->checkSLADate($submission['preferred_datetime'], $submission['created_at']);
        $submission['preferred_datetime'] = $email_date;

        $selected_product = $file->underwriter_selected_product;
        $underwriter = $file->underwriter_underwriter;

        if ($submission['verified_broker']) {
            // get broker info
            $broker = Broker::where('domain', 'LIKE', '%' . $email_domain . '%')->first();

            $to_email = $underwriter->email_1564407408748;

            if ($submission['req_price']) {
                $subject = 'Important enquiry submission from ' . $broker->name;
                $email_template = 'emails.risk-appetite.' . $tmpl_type . '.important-enquiry';
            } else {
                $subject = 'New enquiry submission from ' . $broker->name;
                $email_template = 'emails.risk-appetite.' . $tmpl_type . '.new-enquiry';
            }

            $this->mail->queue(
                $to_email, $underwriter->name, $subject, $email_template, [
                'underwriter' => $underwriter,
                'submission' => $submission,
                'broker_company' => $broker->name,
                'product_names' => $product_names,
                'primary_product' => $selected_product,
                'verified' => true,
                'links' => $links,
            ], $attachments, $cc_email
            );
        } else {
            if ($resend == 'resend') {
                $to_email = $underwriter->email_1564407408748;

                if ($submission['req_price']) {
                    $email_template = 'emails.risk-appetite.' . $tmpl_type . '.uw-unverified-resend-important-enquiry';
                    $subject = 'Important enquiry submission from an unrecognised broker';
                } else {
                    $email_template = 'emails.risk-appetite.' . $tmpl_type . '.uw-unverified-resend-new-enquiry';
                    $subject = 'New enquiry submission from an unrecognised broker';
                }

                $this->mail->queue(
                    $to_email, $underwriter->name, $subject, $email_template, [
                    'underwriter' => $underwriter,
                    'submission' => $submission,
                    'product_names' => $product_names,
                    'primary_product' => $selected_product,
                    'verified' => false,
                    'links' => $links,
                ], $attachments
                );
            } else {
                $to_email = $cc_email;

                if ($submission['req_price']) {
                    $email_template = 'emails.risk-appetite.' . $tmpl_type . '.bd-important-enquiry';
                    $subject = 'Important enquiry submission from an unrecognised broker';
                } else {
                    $email_template = 'emails.risk-appetite.' . $tmpl_type . '.bd-new-enquiry';
                    $subject = 'New enquiry submission from an unrecognised broker';
                }

                $this->mail->queue(
                    $to_email, $underwriter->name, $subject, $email_template, [
                        'underwriter' => $underwriter,
                        'submission' => $submission,
                        'product_names' => $product_names,
                        'primary_product' => $selected_product,
                        'verified' => false,
                        'links' => $links,
                    ]
                );
            }
        }
    }

    private function compileICSFile($submission)
    {
        $admin_link = config('app.admin_frontend');
        $name = 'Business enquiry from -  ' . $submission['fname'] . ' ' . $submission['surname'];
        $url = $admin_link . '/rr-appetite/enquiries/' . $submission['id'];
        $description = "Please view business enquiry at this url - " . $url;

        if (is_null($submission['timezone']) || $submission['timezone'] == "+00:00") {
            $submission['timezone'] = 'Europe/London';
            $timezone = "GMT";
        } else {
            $dateTime = new DateTime();
            $dateTime->setTimeZone(new DateTimeZone($submission['timezone']));
            $timezone = "GMT";
        }

        $sla_date = $this->checkSLADate($submission['preferred_datetime'], $submission['created_at']);

        $sla_date_at_9_am = $sla_date->startOfDay()->addHours(9);
        $datetime_start = $sla_date_at_9_am->format("Ymd\THis\Z");

        $datetime_end = $sla_date_at_9_am->addMinutes(30)->format("Ymd\THis\Z");

        $datetime_stamp = Carbon::now()->subMinutes(15)->format('Ymd\THis\Z');

        $ics_file = "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:-//Risk Appetite//EN\r\nMETHOD:REQUEST\r\nBEGIN:VEVENT\r\nSUMMARY:" . $description . "\r\nDTSTART;VALUE=DATE-TIME:" . $datetime_start . "\r\nDTEND;VALUE=DATE-TIME:" . $datetime_end . "\r\nDTSTAMP;VALUE=DATE-TIME:" . $datetime_stamp . "\r\nUID:" . Str::random(40) . "\r\nSEQUENCE:1\r\nATTENDEE:" . $submission['email'] . "\r\nCREATED;VALUE=DATE-TIME:" . $datetime_stamp . "\r\nDESCRIPTION:" . $description . "\r\nLOCATION:" . $url . "\r\nORGANIZER:" . $submission['email'] . "\r\nPRIORITY:5\r\nSTATUS:CONFIRMED\r\nBEGIN:VALARM\r\nACTION:DISPLAY\r\nDESCRIPTION:Reminder\r\nTRIGGER;RELATED=START:-PT1H\r\nEND:VALARM\r\nEND:VEVENT\r\nEND:VCALENDAR";

        return $ics_file;
    }

    private function checkSLADate($rtd_date, $created_date)
    {
        // to check
        $rtd_date = Carbon::createFromTimestamp(strtotime($rtd_date));
        $created_date = Carbon::createFromTimestamp(strtotime($created_date));

        if ($rtd_date >= $created_date->addWeekdays(3)) {
            return $created_date;
        } else {
            return $rtd_date;
        }
    }

    public function resendBrokerEmail($id, $send_to = 'broker')
    {
        $submission = FormSubmission::find($id);
        $email_domain = substr(strrchr($submission['email'], "@"), 1);
        $verified = $this->verifyEmail($email_domain);

        if ($send_to == 'broker') {
            $email = $this->sendBrokerEmail($submission, null);
        } else {
            $email = $this->sendBrokerEmail($submission, null, $verified, 'resend');
        }

        if ($email) {
            return Response::json(
                [
                    'status' => 'success',
                    'message' => 'Success',
                ]
            );
        }

        return Response::json(
            [
                'status' => 'error',
                'message' => 'error has occurred',
            ]
        );
    }

    public function icsString(FormSubmission $formSubmission)
    {
        $submission = $formSubmission;

        return Response::json(
            [
                'status' => 'success',
                'ics' => $this->compileICSFile($submission),
            ], 403
        );
    }
}
