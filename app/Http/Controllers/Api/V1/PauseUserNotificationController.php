<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\LibertyUser;
use App\Models\BrokerUser;
use App\Models\User;
use Illuminate\Http\Request;

class PauseUserNotificationController extends Controller
{
    public function pauseUserNotification(Request $request)
    {
        $userId = $request->user_id;
        $userType = $request->user_type;
        $pause = $request->pause ?? false;

        if (!$userId || !$userType) {
            return response()->json(['message' => 'User ID and user type are required'], 400);
        }

        if (!in_array($userType, ['liberty', 'broker', 'user'])) {
            return response()->json(['message' => 'Invalid user type'], 400);
        }
        
        $user = match($userType) {
            'liberty' => LibertyUser::find($userId),
            'broker' => BrokerUser::find($userId),
            'user' => User::find($userId),
        };

        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        $user->notification_paused_at = $pause ? now() : null;
        $user->save();  

        return response()->json(['message' => "Notification ". ($pause ? 'paused' : 'resumed') ." successfully"]);
    }
    
    
}