<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\OrganisationOverview;
use App\Models\OrganisationOverviewLog;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\Request;

class OrganisationOverviewController extends BaseController
{
    public function show($organisation_id)
    {
        $overviews = OrganisationOverview::where('organisation_id', $organisation_id)->first();

        return Response::json(
            [
            'response' => 'success',
            'data' => $overviews,
            ]
        );
    }

    public function save(Request $request, $organisation_id)
    {
        $data = $request->except('_token');
        
        // Get existing record
        $existing = OrganisationOverview::where('organisation_id', $organisation_id)->first();
        
        // Track changed fields
        $changedFields = $this->getChangedFields($data, $existing);

        \Log::info($changedFields);
        
        OrganisationOverview::updateOrCreate(
            [
            'organisation_id' => $organisation_id,
            ], $data
        );

        if (count($changedFields) > 0) {
            OrganisationOverviewLog::create([
                'organisation_id' => $organisation_id,
                'changed_fields' => $changedFields,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }   

        return Response::json([
            'response' => 'success',
            'message' => 'Organisation overview has been saved successfully',
        ]);
    }

        /**
     * Get list of fields that have changed values
     *
     * @param array $newData
     * @param ?OrganisationOverview $existing
     * @return array
     */
    private function getChangedFields(array $newData, ?OrganisationOverview $existing): array
    {
        if (!$existing) {
            return array_keys($newData); // All fields are new
        }

        $changedFields = [];
        foreach ($newData as $field => $value) {
            if (is_array($value)) {
                // Handle nested arrays recursively
                $existingValue = $existing->$field ?? [];
                $nestedChanges = $this->compareArrays($value, $existingValue, $field);
                $changedFields = array_merge($changedFields, $nestedChanges);
            } else {
                if ($existing->$field != $value) {
                    $changedFields[] = $field;
                }
            }
        }

        return $changedFields;
    }

    /**
     * Compare two arrays recursively and return changed fields using dot notation
     *
     * @param array $newArray
     * @param array $existingArray
     * @param string $prefix
     * @return array
     */
    private function compareArrays(array $newArray, array $existingArray, string $prefix): array
    {
        $changedFields = [];
        
        foreach ($newArray as $key => $value) {
            $fieldPath = $prefix . '.' . $key;
            
            if (is_array($value)) {
                // If value is an array, recurse deeper
                $existingValue = $existingArray[$key] ?? [];
                if (is_array($existingValue)) {
                    $nestedChanges = $this->compareArrays($value, $existingValue, $fieldPath);
                    $changedFields = array_merge($changedFields, $nestedChanges);
                } else {
                    $changedFields[] = $fieldPath;
                }
            } else {
                // Compare scalar values
                if (!isset($existingArray[$key]) || $existingArray[$key] !== $value) {
                    $changedFields[] = $fieldPath;
                }
            }
        }

        return $changedFields;
    }
}
