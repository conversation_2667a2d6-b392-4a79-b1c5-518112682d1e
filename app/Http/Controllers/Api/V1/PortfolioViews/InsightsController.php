<?php

namespace App\Http\Controllers\Api\V1\PortfolioViews;

use App\Http\Controllers\BaseController;
use App\Jobs\PortfolioViews\GenerateInsightsDataJob;
use App\Models\PortfolioViews\PortfolioViewsInsights;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;

class InsightsController extends BaseController
{
    public function index(Request $request)
    {
        $template = $request->get('template', null);
        $allColumns = empty($template);
        $insights = PortfolioViewsInsights::template($template)->allColumns($allColumns)->get();
        
        return Response::json($insights);
    }

    public function show($id)
    {
        $insight = PortfolioViewsInsights::find($id)->append('latest_data');

        if (empty($insight)) {
            return $this->notFoundResponse();
        }

        if (request()->get('include_other_insights')) {
            $others = PortfolioViewsInsights::template($insight->template)->get();
            return Response::json(['insight' => $insight, 'other_insights' => $others]);
        }

        return Response::json(['insight' => $insight]);
    }

    public function showByTemplate($template)
    {
        $insights = PortfolioViewsInsights::where('template', $template)->get();
        return Response::json($insights);
    }

    public function store()
    {
        $data = request()->only([
            'name',
            'template',
            'variation',
        ]);

        // Check if name is used
        $exists = PortfolioViewsInsights::where('name', $data['name'])
            ->first();

        if (!empty($exists)) {
            return Response::json(
                [
                    'response' => 'error',
                    'message'  => 'A report with the same name already exists.',
                ],
                200
            );
        }

        $insight = PortfolioViewsInsights::create($data);
        $this->generateInsight($insight);
        return Response::json([
            'message' => 'Insight created successfully.',
            'id' => $insight->id
        ]);
    }

    public function update($id)
    {
        $insight = PortfolioViewsInsights::find($id);
        if (empty($insight)) {
            return $this->notFoundResponse();
        }
        $data = request()->only([
            'name',
            'template',
            'variation',
        ]);
        $insight->fill($data);
        $insight->save();

        $this->generateInsight($insight);

        return Response::json(['message' => 'Insight updated successfully.']);
    }

    public function delete($id)
    {
        $insight = PortfolioViewsInsights::find($id);
        if (empty($insight)) {
            return $this->notFoundResponse();
        }

        $insight->delete();

        return Response::json(['message' => 'Insight deleted successfully.']);
    }

    private function generateInsight($insight = null)
    {
        $job = new GenerateInsightsDataJob($insight);
        $job->run();
    }

    private function notFoundResponse()
    {
        return Response::json(
            [
                'status'  => 404, // Not found
                'message' => 'Insight does not exists.'
            ],
            404
        );
    }
}
