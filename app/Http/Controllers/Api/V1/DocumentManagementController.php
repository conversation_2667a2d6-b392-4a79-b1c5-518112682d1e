<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\Organisation;
use Illuminate\Support\Facades\Response;

class DocumentManagementController extends BaseController
{

    public function __construct()
    {

    }


    //ALL
    public function GetOrganisationDetailsForDMS($organisation_id)
    {
        $organisation = Organisation::find($organisation_id, ['id', 'name', 'email', 'logo', 'loss_ratio', 'bursary']);
        $organisation->policy_numbers = $organisation->policyNumbers();

        return Response::json(
            [
            'response' => 'success',
            'data' => $organisation,
            ]
        );
    }

    public function GetAllOrganisations()
    {
        $all_organisations = Organisation::orderBy('name')->get(['id', 'name']);
        return Response::json(
            [
            'response' => 'success',
            'data' => $all_organisations,
            ]
        );
    }
}
