<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\BaseController;
use App\Models\ClientDashboardComponent;
use App\Models\ClientDashboardComponentOrder;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use App\Services\RiskGradingService;

class ClientDashboardController extends BaseController
{
    public function storeClientComponentOrder(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make(
            $input, [
            'user_id' => 'required',
            'order' => 'required',
            'type' => 'required|in:' . ClientDashboardComponent::TYPE_RISK_ENGINEERING . ',' . ClientDashboardComponent::TYPE_CLAIMS,
            ]
        );
        if ($validator->fails()) {
            return Response::json(
                [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
                ]
            );
        }

        $orderArr = explode(',', $input['order']);
        $componentsCount = ClientDashboardComponent::whereIn('id', $orderArr)->type($input['type'])->count();
        // Make sure all ids exists in client_dashboard_components table
        if ($componentsCount < count($orderArr)) {
            return Response::json(
                [
                'response' => 'error',
                'errors' => [
                    'order' => 'There is an invalid component order id.',
                ],
                ]
            );
        }

        $componentOrder = ClientDashboardComponentOrder::updateOrCreate(
            [
            'user_id' => $input['user_id'],
            'type' => $input['type'],
            ]
        );
        $componentOrder->update(['order' => $input['order']]);

        return Response::json(
            [
            'response' => 'success',
            'data' => $componentOrder,
            ]
        );
    }

    public function storeComponentCollapseSettings(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make(
            $input, 
            [
                'user_id' => 'required',
                'component_id' => 'required',
                'hidden' => 'required',
                'type' => 'required|in:' . ClientDashboardComponent::TYPE_RISK_ENGINEERING . ',' . ClientDashboardComponent::TYPE_CLAIMS,
            ]
        );
        if ($validator->fails()) {
            return Response::json([
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
            ]);
        }

        $componentExists = ClientDashboardComponent::where('id', $input['component_id'])->type($input['type'])->exists();
        if (!$componentExists) {
            return Response::json(
                [
                    'response' => 'error',
                    'errors' => [
                        'component_id' => 'There is an invalid component order id.',
                    ],
                ]
            );
        }

        $componentOrder = ClientDashboardComponentOrder::getOrCreate($input['user_id'], $input['type']);
        $componentOrder->updateHiddenComponent($input);
        return Response::json(
            [
                'response' => 'success',
                'message' => 'Hidden components have updated'
            ]
        );
    }

    public function updateLastSeenAt(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make(
            $input, [
            'user_id' => 'required',
            'column' => 'required',
            ]
        );
        if ($validator->fails()) {
            return Response::json(
                [
                'response' => 'error',
                'errors' => $validator->messages()->toArray(),
                ]
            );
        }

        $userId = $request->get('user_id');
        $column = $request->get('column', 'seen_last_updated_survey_at');

        $componentOrder = ClientDashboardComponentOrder::getOrCreate($userId);
        $componentOrder->updateDateColumnToNow($column);

        return Response::json(
            [
            'response' => 'success',
            'data' => $componentOrder,
            ]
        );
    }

    public function updateLastLoginAt(Request $request)
    {
        $userId = $request->get('user_id');
        if (!$userId) {
            return Response::json(
                [
                'response' => 'success',
                'errors' => [
                    'user_id' => 'User id is required.',
                ],
                ]
            );
        }

        $componentOrder = ClientDashboardComponentOrder::getOrCreate($userId);
        $componentOrder->updateDateColumnToNow('last_login_at');

        return Response::json(
            [
            'response' => 'success',
            'data' => $componentOrder,
            ]
        );
    }

    public function getStatus(Request $request)
    {
        $rgService=new RiskGradingService(); 
        $input = $request->all();
        $organisation_id= $request->get('org_id');
        $data=$rgService->getRiStatus($organisation_id);
        
        return Response::json(
            [
            'response' => 'success',
            'data' => $data,
            ]
        );
    }
}
