<?php
namespace app\Decorators;

use App\Models\ClientClaimsDashboard\ClientClaim;

class ClaimLifecycleDecorator
{
    protected $claims;
    protected $libertyId;
    protected $organisationId;

    public function __construct($organisationId, $claims, $libertyId)
    {
        $this->organisationId = $organisationId;

        $this->claims = $claims
            ->where('status', ClientClaim::$STATUS_CLOSED)
            ->orderBy('claim_created_date', 'desc')
            ->get();

        $this->libertyId = $libertyId;
    }

    public function getClaimLifecycle($months)
    {
        if ($this->claims->isEmpty()) {
            return [];
        }

        $latestYear = $this->getLatestClaimYear($this->libertyId);

        $response   = [];
        $rows       = [$latestYear => $months];
        $average    = [$latestYear => $months];
        $values     = [$latestYear => $months];
        foreach ($this->claims as $claim) {
            $year   = date('Y', strtotime($claim->claim_created_date));
            $month  = date('M', strtotime($claim->claim_created_date));

            if (!is_numeric($claim->claim_lifecycle_in_days)) continue;

            if (empty($rows[$year])) {
                $rows[$year] = $months;
                $average[$year] = $months;
                $values[$year] = $months;
            }

            $average[$year][$month] += 1;
            $values[$year][$month] += $claim->claim_lifecycle_in_days;
            $rows[$year][$month] = ($values[$year][$month] / $average[$year][$month]);
        }

        if (empty($rows)) {
            return [];
        }

        $currentYear    = array_slice($rows, 0, 1, true);
        $currentYearKey = array_keys($currentYear)[0];
        $response['current'] = [
            'year' => $currentYearKey,
            'data' => $currentYear[$currentYearKey]
        ];

        // Get the previous year value
        $previousYearKey = $currentYearKey - 1;
        $response['previous'] = [
            'year' => $previousYearKey,
            'data' => isset($rows[$previousYearKey]) ? $rows[$previousYearKey] : []
        ];

        // Get the last 3 following years
        $lastYearsKey1  = $previousYearKey - 1;
        $lastYearsKey2  = $previousYearKey - 2;
        $lastYearsKey3  = $previousYearKey - 3;
        $last3Years     = [
            $lastYearsKey1 => isset($rows[$lastYearsKey1]) ? $rows[$lastYearsKey1] : [],
            $lastYearsKey2 => isset($rows[$lastYearsKey2]) ? $rows[$lastYearsKey2] : [],
            $lastYearsKey3 => isset($rows[$lastYearsKey3]) ? $rows[$lastYearsKey3] : []
        ];

        $response['first_three_years'] = [];

        $firstYears = [];
        $yearKeys = [];
        $totalCount = 0;
        $totalValue = 0;
        foreach ($months as $month => $value) {
            $monthCount = 0;
            $monthValue = 0;
            foreach ($last3Years as $year => $last3YearMonths) {
                $yearKeys[] = substr($year, -2);
                if (!isset($last3YearMonths[$month]) || $last3YearMonths[$month] < 1) continue;

                $monthCount++;
                $monthValue += $last3YearMonths[$month];
            }

            $totalCount += $monthCount;
            $totalValue += $monthValue;
            $firstYears[$month] = ($monthValue > 0) ? round($monthValue / $monthCount) : 0;
        }

        $firstYearKey = end($yearKeys);
        $response['first_three_years']['year'] = $firstYearKey . '-' . $yearKeys[0];
        $response['first_three_years']['data'] = $firstYears;
        $response['baseline'] = ($totalCount > 0) ? round($totalValue / $totalCount) : 0;

        return [
            'chart' => $response,
//            'data' => $this->exportClaimLifecycle($months)
        ];
    }

    public function exportClaimLifecycle($months)
    {
        $response = $this->claims->map(function ($claim) {
            return [
                'policy_number'             => $claim->policy_number,
                'claim_number'              => $claim->claim_number,
                'product'                   => $claim->product,
                'trading_name'              => $claim->trading_name,
                'class_of_business'         => $claim->class_of_business,
                'loss_type'                 => $claim->loss_type,
                'status'                    => $claim->status,
                'claim_created_date'        => $claim->claim_created_date,
                'claim_lifecycle_in_days'   => $claim->claim_lifecycle_in_days
            ];
        });

        return $response;
    }

    protected function getLatestClaimYear($libertyId)
    {
        $clientClaimClass = ClientClaim::getClientClaimModelForDemo($this->organisationId);
        $latestClaim = $clientClaimClass::select('claim_created_date')
            ->where('liberty_id', $libertyId)
            ->where('status', ClientClaim::$STATUS_CLOSED)
            ->orderBy('claim_created_date', 'desc')
            ->first();

        if (!$latestClaim) {
            return [];
        }

        return date('Y', strtotime($latestClaim->claim_created_date));
    }
}
