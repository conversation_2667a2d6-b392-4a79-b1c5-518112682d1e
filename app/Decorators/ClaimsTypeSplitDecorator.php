<?php
namespace app\Decorators;

use App\Decorators\FactoryDecorator;

class ClaimsTypeSplitDecorator extends FactoryDecorator
{
    protected $claims;
    protected $colors = [];

    public function __construct($organisationId, $colors, $claims)
    {
        parent::__construct($organisationId);

        $this->colors = $colors;
        $this->claims = $claims
            ->orderBy('loss_date', 'desc')
            ->get();
    }

    public function getClaimsTypeSplit()
    {
        if ($this->claims->isEmpty()) {
            return [];
        }

        $response = [];
        $latestLossYear = date("Y", strtotime($this->claims[0]->loss_date));
        $latestYearClaims = $this->filterClaimsByLossDate($this->claims, $latestLossYear);
        if (!$latestYearClaims->isEmpty()) {
            $response['current_year_claims'] = [
                'year' => $latestLossYear,
                'class_of_business' => $this->makeClaimsByPercentage($this->colors, $latestYearClaims),
                'total' => $latestYearClaims->count()
            ];
        }

        $previousLossYear = strval($latestLossYear - 1);
        $previousYearClaims = $this->filterClaimsByLossDate($this->claims, $previousLossYear);
        if (!$previousYearClaims->isEmpty()) {
            $response['previous_year_claims'] = [
                'year' => $previousLossYear,
                'class_of_business' => $this->makeClaimsByPercentage($this->colors, $previousYearClaims),
                'total' => $previousYearClaims->count()
            ];
        }

        return [
            'chart' => $response,
//            'data' => $this->exportClaimsTypeSplit()
        ];
    }

    public function exportClaimsTypeSplit()
    {
        $response = $this->claims->map(function ($claim) {
            return [
                'trading_name'              => $claim->trading_name,
                'status'                    => $claim->status,
                'class_of_business'         => $claim->class_of_business,
                'loss_date'                 => $claim->loss_date,
                'claim_number'              => $claim->claim_number
            ];
        });

        return $response;
    }
}