<?php

namespace app\Decorators;

use App\Models\ClientClaimsDashboard\ClientClaimId;
use App\Models\ClientClaimsDashboard\ClientClaim;
use Illuminate\Database\Eloquent\Builder;

class ClientClaimsFilterDataDecorator
{
    protected $organisationId;
    protected $query;

    private $STATUS_TITLE_MAP = [
        "Reopened" => "Re-opened"
    ];

    public function __construct($organisationId, $query = null)
    {
        $this->organisationId = $organisationId;
        $this->query = $query;
    }

    public function get()
    {
        $claimId = ClientClaimId::where('organisation_id', $this->organisationId)->first();
        if (empty($claimId)) {
            return [];
        }

        $clientClaimClass = ClientClaim::getClientClaimModelForDemo($this->organisationId);
        $query = $clientClaimClass::where('liberty_id', $claimId->liberty_id);
        if ($this->query) {
            $query = $this->query;
        }

        $productQuery = clone $query;
        $classOfBusinessQuery = clone $query;
        $lossTypeQuery = clone $query;
        $policyNumberQuery = clone $query;

        $status = array_map(array($this, 'mapStatus'), $this->getDistinct($query, 'status'));
        $value = array_column($status, 'value');
        array_multisort($value, SORT_ASC, $status);
        $product = $this->getDistinct($productQuery, 'product');
        sort($product);
        $classOfBusiness = $this->getDistinct($classOfBusinessQuery, 'class_of_business');
        sort($classOfBusiness);
        $lossType = $this->getDistinct($lossTypeQuery, 'loss_type');
        sort($lossType);
        $policyNumber = $this->getDistinct($policyNumberQuery, 'policy_number');
        sort($policyNumber);

        return [
            'status' => $status,
            'product' => $product,
            'class_of_business' => $classOfBusiness,
            'loss_type' => $lossType,
            'policy_number' => $policyNumber,
        ];
    }

    private function mapStatus($status)
    {
        $mappedStatus = [
            'value' => $status,
        ];

        if (array_key_exists($status, $this->STATUS_TITLE_MAP)) {
            $mappedStatus['title'] =  $this->STATUS_TITLE_MAP[$status];
        }

        return $mappedStatus;
    }

    private function getDistinct(Builder $query, string $columnName): array
    {
        return $query->groupBy($columnName)
            ->select($columnName)
            ->get($columnName)
            ->pluck($columnName)
            ->all();
    }
}
