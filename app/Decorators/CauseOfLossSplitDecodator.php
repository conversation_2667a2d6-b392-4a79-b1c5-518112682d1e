<?php
namespace app\Decorators;

use App\Decorators\FactoryDecorator;

class CauseOfLossSplitDecodator extends FactoryDecorator
{
    protected $claims;

    public function __construct($claims)
    {
        $this->claims = $claims
            ->whereNotNull('loss_date')
            ->get();
    }

    public function get()
    {
        $lossTypes = $this->claims->toArray();
        $lossTypes = array_values(
            array_unique(
                array_column($lossTypes, 'loss_type')
                , SORT_REGULAR
            )
        );
        sort($lossTypes);

        $mappedLossTypes = [];
        $total = $this->claims->count();
        foreach ($lossTypes as $lossType) {
            $count = count($this->claims->filter(function ($value) use ($lossType) {
                return $value->loss_type == $lossType;
            }));
            $mappedLossTypes[] = [
                'title' => $lossType,
                'percentage' => ($total > 0) ? round(($count / $total) * 100, 2) : 0,
                'value' => $count
            ];
        }

        return $mappedLossTypes;
    }
}
