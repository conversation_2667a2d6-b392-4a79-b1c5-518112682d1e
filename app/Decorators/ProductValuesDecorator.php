<?php
namespace app\Decorators;

use App\Decorators\FactoryDecorator;

class ProductValuesDecorator extends FactoryDecorator
{
    protected $claims;
    protected $libertyId;

    public function __construct($organisationId, $claims, $libertyId)
    {
        parent::__construct($organisationId);

        $this->claims = $claims
            ->whereNotNull('loss_date')
            ->orderBy('loss_date', 'desc')
            ->get();
        
        $this->libertyId = $libertyId;
    }

    public function getProductValues()
    {
        if ($this->claims->isEmpty()) {
            return [];
        }

        $response = [];
        $currentYear = $this->getLatestClaimYear($this->libertyId);

        foreach ($this->businessClasses as $class) {
            $months = array_filter(array_values($this->makeMonthsWithIncurred(
                $this->claims,
                [
                    strval($currentYear - 2),
                    strval($currentYear - 3),
                    strval($currentYear - 4)
                ],
                $class
            )));

            $baseline = 0;
            if (!empty($months)) {
                $baseline = ceil(array_sum($months) / count($months));
            }

            $response[$class] = [
                'current_year' => [
                    'year' => $currentYear,
                    'months' => $this->makeMonthsWithIncurred($this->claims, [$currentYear], $class)
                ],
                'previous_year' => [
                    'year' => strval($currentYear - 1),
                    'months' => $this->makeMonthsWithIncurred($this->claims, [strval($currentYear - 1)], $class)
                ],
                'previous_years' => array(
                    'years' => substr(strval($currentYear - 4), 2).'-'.substr(strval($currentYear - 2), 2),
                    'months' => $this->makeMonthsWithIncurred(
                        $this->claims,
                        [strval($currentYear - 2), strval($currentYear - 3), strval($currentYear - 4)],
                        $class
                    )
                ),
                'baseline' => $baseline
            ];
        }

        return [
            'chart' => $response,
//            'data' => $this->exportProductValues()
        ];
    }

    public function exportProductValues()
    {
        $response = $this->claims->map(function ($claim) {
            return [
                'policy_number'             => $claim->policy_number,
                'product'                   => $claim->product,
                'trading_name'              => $claim->trading_name,
                'loss_type'                 => $claim->loss_type,
                'status'                    => $claim->status,
                'class_of_business'         => $claim->class_of_business,
                'loss_date'                 => $claim->loss_date,
                'claim_number'              => $claim->claim_number
            ];
        });

        return $response;
    }
}