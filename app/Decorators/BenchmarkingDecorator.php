<?php
namespace app\Decorators;

use App\Decorators\FactoryDecorator;
use App\Models\ClientClaimsDashboard\ClientClaim;
use App\Models\Organisation;
use Illuminate\Support\Facades\DB;

class BenchmarkingDecorator extends FactoryDecorator
{
    protected $claim;
    protected $peers;
    protected $libertyId;
    protected $product;
    protected $tradingName;
    protected $status;
    protected $lossType;
    protected $classOfBusiness;
    protected $organisationId;

    public function __construct($organisationId, $libertyId, $product, $tradingName, $status, $classOfBusiness, $lossType)
    {
        parent::__construct($organisationId);

        $this->libertyId = $libertyId;
        $this->product = $product;
        $this->tradingName = $tradingName;
        $this->status = $status;
        $this->lossType = $lossType;
        $this->classOfBusiness = $classOfBusiness;
        $this->organisationId = $organisationId;

        $clientClaimClass = ClientClaim::getClientClaimModelForDemo($organisationId);
        $search = $clientClaimClass::where('liberty_id', $this->libertyId)
            ->select(
                '*',
                DB::raw("COUNT(claim_number) as total_claim_number"),
                DB::raw("SUM(incurred) as total_incurred")
            );

        $this->claim = $this->applyFilters($search)
            ->groupBy('type_1')
            ->groupBy('type_2')
            ->first();

        if (!empty($this->claim)) {
            $peersSearch = $clientClaimClass::where('type_1', $this->claim->type_1)
                ->where('type_2', $this->claim->type_2)
                ->groupBy('liberty_id');

            $this->peers = $this->applyFilters($peersSearch);
        }
    }

    public function getClaimVolumesPedestal()
    {
        if (empty($this->peers)) return [];

        $search = clone $this->peers;

        $peers = $search
            ->select(
                DB::raw("COUNT(claim_number) as total"),
                'product',
                'trading_name',
                'status',
                'type_1',
                'type_2',
                'claim_number'
            )
            ->orderBy('total', 'desc')
            ->get();

        if ($peers->isEmpty()) return [];

        $hasComparison = true;
        if (count($peers) <= 2) {
            $hasComparison = false;
        }

        $currentTotal = number_format($this->claim->total_claim_number, 2);
        $highest = $peers[0];
        $lowest  = $peers[$peers->count() - 1];
        $average = empty($peers) ? 0 : round(array_sum(array_column($peers->toArray(), 'total')) / $peers->count());

        return [
            'chart' => [
                'current' => [
                    'name' => $this->claim->trading_name,
                    'total' => $currentTotal,
                    'difference' => $this->getBenchmarkDifference($average, $currentTotal)
                ],
                'highest' => number_format($highest['total'], 2),
                'lowest' => number_format($lowest['total'], 2),
                'average' => $average,
                'has_comparison' => $hasComparison
            ],
//            'data' => $peers
        ];
    }

    public function getClaimValuesPedestal()
    {
        if (empty($this->peers)) return [];

        $search = clone $this->peers;

        $peers = $search
            ->select(
                DB::raw("SUM(incurred) as total"),
                'product',
                'trading_name',
                'status',
                'type_1',
                'type_2',
                'claim_number',
                'incurred'
            )
            ->orderBy('total', 'desc')
            ->get();

        if ($peers->isEmpty()) return [];

        $highest = $peers[0];
        $lowest  = $peers[$peers->count() - 1];
        $average = ($peers->count() > 0) ? round(array_sum(array_column($peers->toArray(), 'total')) / $peers->count()) : 0;

        return [
            'chart' => [
                'current' => [
                    'name' => $this->claim->trading_name,
                    'total' => number_format($this->claim->total_incurred, 2)
                ],
                'highest' => number_format($highest['total'], 2),
                'lowest' => number_format($lowest['total'], 2),
                'average' => ceil($average)
            ],
//            'data' => $peers
        ];
    }

    public function getRepudiationPedestal($lossType)
    {
        $clientClaimClass = ClientClaim::getClientClaimModelForDemo($this->organisationId);
        // Get the claim record of the organisation
        $orgClaim = $clientClaimClass::where('liberty_id', $this->libertyId)->first();
        if (!$orgClaim) {
            return [];
        }

        // Get the claims of the repudiations based on type_1 and type_2
        $search = $clientClaimClass::select('*', DB::raw("COUNT(id) as total"))
            ->where('type_1', $orgClaim->type_1)
            ->where('type_2', $orgClaim->type_2);

        $classOfBusiness = [];
        if (!empty($this->classOfBusiness)) {
            $classOfBusiness = explode(',', $this->classOfBusiness);
            $cobKey = array_search('Property', $classOfBusiness);
            if (is_numeric($cobKey)) {
                unset($classOfBusiness[$cobKey]);
            }
        }

        if (empty($classOfBusiness)) {
            $factoryDecorator = new FactoryDecorator($this->organisationId);
            $classOfBusiness = $factoryDecorator->getRepudiationCOB();
        }

        $this->classOfBusiness = implode(',', $classOfBusiness);
        $statuses = explode(',', $this->status);
        $this->status = implode(',', array_filter($statuses, function ($status) {
            return (in_array($status, [ClientClaim::$STATUS_CLOSED, ClientClaim::$STATUS_RECLOSED]));
        }));

        if (empty($this->status)) {
            $this->status = implode(',', [ClientClaim::$STATUS_CLOSED, ClientClaim::$STATUS_RECLOSED]);
        }

        $search = $this->applyFilters($search, true)
            ->groupBy('trading_name')
            ->orderBy('total', 'desc');

        $claims = clone $search;
        $repudiated = clone $search;

        $claims = $claims
            ->where('detailed_status', '<>', FactoryDecorator::DETAILED_STATUS_WITHDRAWN)
            ->get();

        $repudiated = $repudiated
            ->where('detailed_status', FactoryDecorator::DETAILED_STATUS_REPUDIATED)
            ->get();

        $currentAvg = 0;
        $average = [];
        if (!$claims->isEmpty()) {
            foreach ($claims as $claim) {
                foreach ($repudiated as $rep) {
                    if ($claim->trading_name == $rep->trading_name) {
                        $avg = round(($rep->total / $claim->total) * 100);
                        $average[] = $avg;

                        if ($rep->trading_name == $orgClaim->trading_name) {
                            $currentAvg = $avg;
                        }
                    }
                }
            }
        }

        if (!empty($average)) {
            rsort($average);
        }

        $highest    = (isset($average[0]) ? $average[0] : 0);
        $lowest     = (isset($average[count($average)-1]) ? $average[count($average)-1] : 0);
        $baseline   = ((count($average) > 0) ? (round((array_sum($average)) / count($average))) : 0);

        return [
            'chart' => [
                'current' => [
                    'name' => $orgClaim->trading_name,
                    'total' => number_format($currentAvg, 2)
                ],
                'highest' => number_format($highest, 2),
                'lowest' => number_format($lowest, 2),
                'average' => $baseline
            ]
        ];
    }

    public function getBanner()
    {
        $organisation = Organisation::find($this->organisationId);
        if (!isset($organisation)) {
            return [];
        }

        return [
            'organisation_logo_url' => $organisation->link,
            'organisation_name' => $organisation->name,
            'type_1' => isset($this->claim->type_1) ? $this->claim->type_1 : '',
            'type_2' => isset($this->claim->type_2) ? $this->claim->type_2 : ''
        ];
    }

    private function applyFilters($search, $filterClassOfBusiness = true)
    {
        if (!empty($this->product)) {
            $search->whereIn('product', explode(',', $this->product));
        }

        if (!empty($this->status)) {
            $search->whereIn('status', explode(',', $this->status));
        }

        if (!empty($this->lossType)) {
            $search->whereIn('loss_type', explode(',', $this->lossType));
        }

        if (!empty($this->classOfBusiness) && $filterClassOfBusiness) {
            $search->whereIn('class_of_business', explode(',', $this->classOfBusiness));
        }

        return $search;
    }

    private function getBenchmarkDifference($average, $current)
    {
        if ($average == 0) {
            return 'Relevant insights are unavailable for benchmarking.';
        }

        $percentage = ceil((($current - $average) / $average) * 100);
        $summary = 'You are %s%s %s to the average volume of claims in comparison to your peers.';

        if ($current > $average) {
            return sprintf($summary, abs($percentage), '%', 'above');
        } else if ($current < $average) {
            return sprintf($summary, abs($percentage), '%', 'below');
        } else {
            return sprintf($summary, 'currently', null, 'equal');
        }
    }
}
