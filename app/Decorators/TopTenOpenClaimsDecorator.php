<?php
namespace app\Decorators;

use App\Decorators\FactoryDecorator;
use Illuminate\Support\Facades\DB;
use App\Models\ClientClaimsDashboard\ClientClaim;

class TopTenOpenClaimsDecorator extends FactoryDecorator
{
    protected $claims;

    public function __construct($organisationId, $claims)
    {
        $clientClaimClass = ClientClaim::getClientClaimModelForDemo($organisationId);
        $firstClaim = $clientClaimClass::first();
        $fileDate = $firstClaim->file_date;
        $latestImportDate = date('Y-m-d', strtotime($fileDate));

        $this->claims = $claims
            ->select(
                DB::raw("DATEDIFF('$latestImportDate', claim_created_date) as number_of_days_open"),
                'policy_number',
                'product',
                'trading_name',
                'loss_type',
                'status',
                'claim_number',
                'class_of_business',
                'claimant_name'
            )
            ->whereNotNull('claim_created_date')
            ->where('status', ClientClaim::$STATUS_OPEN)
            ->orderBy('number_of_days_open', 'desc')
            ->limit(10)
            ->get();
    }
    
    public function getTopTenOpenClaims()
    {
        if ($this->claims->isEmpty()) {
            return [];
        }

        $response = $this->claims->map(function ($claim) {
            return [
                'claim_number'              => $claim->claim_number,
                'class_of_business'         => $claim->class_of_business,
                'claimant_name'             => $claim->claimant_name,
                'loss_type'                 => $claim->loss_type,
                'number_of_days_open'       => $claim->number_of_days_open
            ];
        });

        return [
            'chart' => $response,
//            'data' => $this->claims
        ];
    }
}