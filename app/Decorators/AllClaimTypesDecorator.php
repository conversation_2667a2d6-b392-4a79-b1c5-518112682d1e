<?php
namespace app\Decorators;

use App\Decorators\FactoryDecorator;

class AllClaimTypesDecorator extends FactoryDecorator
{
    protected $claims;
    protected $libertyId;
    protected $colors = [];

    public function __construct($organisationId, $colors, $claims, $libertyId)
    {
        parent::__construct($organisationId);

        $this->colors = $colors;
        $this->claims = $claims
            ->orderBy('loss_date', 'desc')
            ->get();
        
        $this->libertyId = $libertyId;
    }

    public function getAllClaimTypes()
    {
        if ($this->claims->isEmpty()) {
            return [];
        }

        $currentYear = $this->getLatestClaimYear($this->libertyId);
        $rows = [];
        $colors = [];
        foreach ($this->businessClasses as $businessClass) {
            for ($i=0; $i<5; $i++) {
                $year = $currentYear-$i;
                $rows[$currentYear-$i][$businessClass] = $this->claims->filter(function ($value) use ($year, $businessClass){
                    return date("Y", strtotime($value->loss_date)) == $year
                        && $value->class_of_business == $businessClass;
                })->count();
            }
            $colors[] = $this->colors[$businessClass];
        }

        return [
            'colors' => $colors,
            'chart' => $rows
        ];
    }
    
    public function exportAllClaimTypes()
    {
        $response = $this->claims->map(function ($claim) {
            return [
                'policy_number'             => $claim->policy_number,
                'product'                   => $claim->product,
                'trading_name'              => $claim->trading_name,
                'loss_type'                 => $claim->loss_type,
                'status'                    => $claim->status,
                'class_of_business'         => $claim->class_of_business,
                'loss_date'                 => $claim->loss_date,
                'claim_number'              => $claim->claim_number
            ];
        });

        return $response;
    }
}