<?php
namespace app\Decorators;

use App\Decorators\FactoryDecorator;

class ProductSnapshotDecorator extends FactoryDecorator
{
    protected $claims;

    public function __construct($organisationId, $claims)
    {
        parent::__construct($organisationId);

        $this->claims = $claims
            ->orderBy('loss_date', 'desc')
            ->get();
    }

    public function getProductSnapshot()
    {
        if ($this->claims->isEmpty()) {
            return [];
        }

        $response = [];

        foreach ($this->businessClasses as $class) {
            $firstYear = date("Y", strtotime($this->claims[0]->loss_date)) - 4;
            $lastYear = date("Y", strtotime($this->claims[0]->loss_date));
            for ($year = $firstYear; $year <= $lastYear; $year++) {
                $response[$class][$year] = $this->generateMonths($year, $class);
            }
        }

        return [
            'chart' => $response,
//            'data' => $this->exportProductSnapshot()
        ];
    }

    public function exportProductSnapshot()
    {
        $response = $this->claims->map(function ($claim) {
            return [
                'policy_number'             => $claim->policy_number,
                'product'                   => $claim->product,
                'trading_name'              => $claim->trading_name,
                'loss_type'                 => $claim->loss_type,
                'status'                    => $claim->status,
                'class_of_business'         => $claim->class_of_business,
                'loss_date'                 => $claim->loss_date,
                'claim_number'              => $claim->claim_number
            ];
        });

        return $response;
    }

    private function generateMonths($year, $class)
    {
        $months = [];
        for ($month = 1; $month <= 12; $month++) {
            $monthName = date('M', mktime(0, 0, 0, $month, 1, date('Y')));
            $months[$monthName] = count($this->claims->filter(function ($value) use($month, $year, $class) {
                return date("Y", strtotime($value->loss_date)) == $year
                    && date("n", strtotime($value->loss_date)) == $month
                    && $value->class_of_business == $class;
            }));
        }
        return $months;
    }
}
