<?php
namespace app\Decorators;

use App\Models\ClientClaimsDashboard\ClientClaim;

class FactoryDecorator
{
    const DETAILED_STATUS_WITHDRAWN = 'Withdrawn';
    const DETAILED_STATUS_REPUDIATED = 'Repudiated';

    protected $businessClasses = [];
    protected $organisationId = '';

    protected $colors = [
        0x7CC7AD,
        0x7CAFC7,
        0x7C82C7,
        0xA37CC7,
        0xD16799,
        0xA58C6E,
        0x00A0AD,
        0x4BF2D1,
        0x8CAAD2,
        0xFFCB57,
        0xC20A20,
        0x8B2252,
        0xE69138,
        0x797EE1,
        0x918403
    ];

    protected $defaultColor = 0x696969;

    public function __construct($organisationId)
    {
        $this->organisationId = $organisationId;
        $clientClaimClass = ClientClaim::getClientClaimModelForDemo($organisationId);
        $this->businessClasses = $clientClaimClass::distinct()->get(['class_of_business'])->map(function ($cob) {
            return $cob['class_of_business'];
        })->toArray();
    }

    protected function filterClaimsByLossDate($claims, $year)
    {
        return $claims->filter(function ($value) use ($year) {
            return date("Y", strtotime($value->loss_date)) == $year;
        });
    }

    protected function makeClaimsByPercentage($colors, $claims)
    {
        $result = [];
        foreach ($this->businessClasses as $class) {
            $businessClassPercentage = $this->calcClaimsPercentageForClass($colors, $claims, $class);
            if ($businessClassPercentage == 0) continue;

            $result[$class] = $businessClassPercentage;
        }
        return $result;
    }

    public function getClassOfBusinessColor($cobs)
    {
        $colors = [];
        for ($i = 0; $i < count($cobs); $i++) {
            if (isset($this->colors[$i])) {
                $color = $this->colors[$i];
            } else {
                $color = $this->defaultColor;
            }

            $key = $cobs[$i]['class_of_business'];
            $colors[$key] = $color;
        }

        return $colors;
    }

    protected function calcClaimsPercentageForClass($colors, $claims, $class)
    {
        $filteredClaims = $claims->filter(function ($value) use ($class) {
            return $value->class_of_business == $class;
        });

        if ($filteredClaims->isEmpty()) return 0;

        return [
            'percent' => (count($filteredClaims) / count($claims)) * 100,
            'count' => count($filteredClaims),
            'color' => $colors[$class]
        ];
    }

    protected function makeMonthsWithVolume($claims, $years, $class)
    {
        $months = [];
        for ($month = 1; $month <= 12; $month++) {
            $average = 0;
            $values = $this->getAverageValues($month, $claims, $years, $class);

            if (!empty($values)) {
                $average = array_sum($values) / count($values);
            }

            $monthName = date('M', mktime(0, 0, 0, $month, 1, date('Y')));
            $months[$monthName] = round($average, 2);
        }

        return $months;
    }

    protected function makeMonthsWithIncurred($claims, $years, $class)
    {
        $months = [];
        for ($month = 1; $month <= 12; $month++) {
            $average = 0;
            $incurred = $this->getAverageIncurred($month, $claims, $years, $class);

            if (!empty($incurred)) {
                $average = array_sum($incurred) / count($incurred);
            }

            $monthName = date('M', mktime(0, 0, 0, $month, 1, date('Y')));
            $months[$monthName] = round($average, 2);
        }

        return $months;
    }

    protected function getAverageValues($month, $claims, $years, $class)
    {
        $values = [];
        foreach ($claims as $claim) {
            $lossYear = date("Y", strtotime($claim->loss_date));
            if (in_array($lossYear, $years)
                && date("n", strtotime($claim->loss_date)) == $month
                && $claim->class_of_business == $class) {
                if (!isset($values[$lossYear])) {
                    $values[$lossYear] = 0;
                }

                $values[$lossYear]++;
            }
        }

        return $values;
    }

    protected function getAverageIncurred($month, $claims, $years, $class)
    {
        $incurred = [];
        foreach ($claims as $claim) {
            $lossYear = date("Y", strtotime($claim->loss_date));
            if (in_array($lossYear, $years)
            && date("n", strtotime($claim->loss_date)) == $month
            && $claim->class_of_business == $class) {
                if (!isset($incurred[$lossYear])) {
                    $incurred[$lossYear] = 0;
                }

                $incurred[$lossYear] += $claim->incurred;
            }
        }

        return $incurred;
    }

    protected function getLatestClaimYear($libertyId)
    {
        $clientClaimClass = ClientClaim::getClientClaimModelForDemo($this->organisationId);
        $latestClaim = $clientClaimClass::select('loss_date')->where('liberty_id', $libertyId)
            ->whereNotNull('loss_date')
            ->orderBy('loss_date', 'desc')
            ->first();
        
        if (!$latestClaim) {
            return [];
        }

        return date('Y', strtotime($latestClaim->loss_date));
    }

    public function getCreatedDate($claims)
    {
        $claimsCollection = $claims->get();
        if ($claimsCollection->isEmpty()) {
            return null;
        }

        return date('d/m/Y', strtotime($claimsCollection[0]->file_date));
    }

    public function getRepudiationCOB()
    {
        $classOfBusiness = $this->businessClasses;
        $cobKey = array_search('Property', $classOfBusiness);
        if (!empty($cobKey)) {
            unset($classOfBusiness[$cobKey]);
        }

        return $classOfBusiness;
    }
}