<?php
namespace app\Decorators;

use App\Decorators\FactoryDecorator;
use ClientClaimsDashboard\ClientClaim;

class TopTenIncurredClaimsDecorator extends FactoryDecorator
{
    protected $claims;

    public function __construct($claims)
    {
        $this->claims = $claims
            ->select(
                'policy_number',
                'product',
                'trading_name',
                'loss_type',
                'status',
                'claim_number',
                'class_of_business',
                'claimant_name',
                'incurred'
            )
            ->orderBy('incurred', 'desc')
            ->limit(10)
            ->get();
    }

    public function getTopTenIncurredClaims()
    {
        if ($this->claims->isEmpty()) {
            return [];
        }

        $response = $this->claims->map(function ($claim) {
            return [
                'claim_number'      => $claim->claim_number,
                'class_of_business' => $claim->class_of_business,
                'claimant_name'     => $claim->claimant_name,
                'loss_type'         => $claim->loss_type,
                'incurred'          => number_format($claim->incurred)
            ];
        });

        return [
            'chart' => $response,
//            'data' => $this->claims
        ];
    }
}