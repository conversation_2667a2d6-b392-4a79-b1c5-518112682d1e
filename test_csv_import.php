<?php

/**
 * Simple test script to validate CSV import logic
 * Run this with: php test_csv_import.php
 */

// Test CSV parsing logic
function testCsvParsing() {
    $csvPath = 'public/risk-insights-csv/organisations.csv';
    
    if (!file_exists($csvPath)) {
        echo "❌ CSV file not found at: {$csvPath}\n";
        return false;
    }
    
    $handle = fopen($csvPath, 'r');
    if ($handle === false) {
        echo "❌ Unable to open CSV file\n";
        return false;
    }
    
    // Read headers
    $headers = fgetcsv($handle);
    if ($headers === false) {
        echo "❌ Unable to read CSV headers\n";
        return false;
    }
    
    echo "✅ CSV file found and readable\n";
    echo "📋 Headers: " . implode(', ', $headers) . "\n\n";
    
    // Test parsing first data row
    $data = fgetcsv($handle);
    if ($data !== false) {
        $mappedData = array_combine($headers, $data);
        echo "📊 Sample data row:\n";
        foreach ($mappedData as $key => $value) {
            echo "   {$key}: {$value}\n";
        }
        echo "\n";
        
        // Test date parsing
        if (!empty($mappedData['Renewal Date'])) {
            echo "📅 Testing date parsing for: " . $mappedData['Renewal Date'] . "\n";
            try {
                $date = DateTime::createFromFormat('d/m/Y', $mappedData['Renewal Date']);
                if ($date) {
                    echo "✅ Date parsed successfully: " . $date->format('Y-m-d') . "\n";
                } else {
                    echo "❌ Date parsing failed\n";
                }
            } catch (Exception $e) {
                echo "❌ Date parsing error: " . $e->getMessage() . "\n";
            }
        }
        
        // Test field mapping
        echo "\n🔄 Testing field mapping:\n";
        $organisationData = [
            'name' => trim($mappedData['client_name']),
            'sector' => !empty($mappedData['sector']) ? trim($mappedData['sector']) : null,
            'product_subsector' => !empty($mappedData['subsector']) ? trim($mappedData['subsector']) : null,
            'broker_name' => !empty($mappedData['Broker']) ? trim($mappedData['Broker']) : null,
            'risk_grading' => !empty($mappedData['risk_score']) && is_numeric($mappedData['risk_score']) ? (int) $mappedData['risk_score'] : null,
        ];
        
        foreach ($organisationData as $key => $value) {
            echo "   {$key}: " . ($value ?? 'NULL') . "\n";
        }
    }
    
    fclose($handle);
    return true;
}

// Test required fields validation
function testRequiredFields() {
    echo "\n🔍 Testing required fields validation:\n";
    
    $testData = [
        'client_name' => 'Test Company',
        'sector' => 'Technology',
        'Broker' => 'Test Broker',
        'risk_score' => '75'
    ];
    
    $requiredFields = ['client_name'];
    
    foreach ($requiredFields as $field) {
        if (empty($testData[$field])) {
            echo "❌ Missing required field: {$field}\n";
            return false;
        }
    }
    
    echo "✅ All required fields present\n";
    return true;
}

// Run tests
echo "🧪 CSV Import Validation Tests\n";
echo str_repeat('=', 40) . "\n\n";

$csvTest = testCsvParsing();
$fieldsTest = testRequiredFields();

echo "\n" . str_repeat('=', 40) . "\n";
echo "📊 Test Results:\n";
echo "CSV Parsing: " . ($csvTest ? "✅ PASS" : "❌ FAIL") . "\n";
echo "Field Validation: " . ($fieldsTest ? "✅ PASS" : "❌ FAIL") . "\n";

if ($csvTest && $fieldsTest) {
    echo "\n🎉 All tests passed! The import command should work correctly.\n";
    echo "\n📝 To run the actual import:\n";
    echo "   php artisan insights:import\n";
} else {
    echo "\n⚠️  Some tests failed. Please check the issues above.\n";
}
