#!/bin/bash

# Build and start the containers
docker-compose build
docker-compose up -d

# Wait for the MySQL container to start
echo "Waiting for MySQL to start..."
while ! docker exec rr_mysql mysqladmin ping --silent; do
  sleep 1
done

# Import the SQL dump into the MySQL container
echo "Checking if the database is empty..."
docker exec -i rr_mysql bash -l -c "mysql -uroot -proot docker -e 'SHOW TABLES;'" | grep -q '^Tables_in_docker$'

if [ $? -eq 0 ]; then
  echo "The database is empty, proceeding with the import..."
  docker exec -i rr_mysql bash -l -c "mysql -uroot -proot docker < /var/lib/sql_dump.sql"
else
  echo "The database is not empty, skipping the import..."
fi

# Wait for the MongoDB container to start
echo "Waiting for MongoDB to start..."
while ! docker exec rr_mongodb mongo --eval "quit()" &>/dev/null; do
  sleep 1
done

# Import the MongoDB dump into the MongoDB container
echo "Importing the MongoDB dump into MongoDB..."
docker exec -i rr_mongodb mongorestore /var/mongodb_dump

echo "All database dumps have been imported successfully!"

# Check if the rr_web container is up and running
status=$(docker inspect -f '{{.State.Status}}' rr_webserver)
if [ "$status" == "running" ]; then
  echo "rr_webserver container is up and running, proceeding with the exec command..."
  docker exec -it rr_webserver bash -c "cd /var/www/html/Risk-Reduce-API && composer install && cd ../Risk-Reduce-Client && composer install && cd ../Risk-Reduce-Admin && composer install"
else
  echo "rr_webserver container is not running, cannot proceed with the exec command..."
fi