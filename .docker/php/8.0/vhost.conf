<VirtualHost *:80>
	ServerName dev.rradmin.com
    DocumentRoot /var/www/html/management-web-portal/public/

    <Directory "/var/www/html/management-web-portal/public/">
        AllowOverride all
        Require all granted
    </Directory>

    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined
</VirtualHost>

<VirtualHost *:80>
	ServerName dev.rrclient.com
    DocumentRoot /var/www/html/client-web-portal/public

    <Directory "/var/www/html/client-web-portal/public">
        AllowOverride all
        Require all granted
    </Directory>

    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined
</VirtualHost>

<VirtualHost *:80>
	ServerName dev.rrapi.com
    DocumentRoot /var/www/html/business-application/public

    <Directory "/var/www/html/business-application/public">
        AllowOverride all
        Require all granted
    </Directory>

    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined
</VirtualHost>
