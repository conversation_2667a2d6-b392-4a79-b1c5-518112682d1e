FROM php:5.6.38-apache

RUN a2enmod rewrite headers

RUN docker-php-ext-install pdo pdo_mysql


RUN apt-get update \
    && apt-get install -y wget \
    && apt-get install -y apt-transport-https \
    && apt-get install -y libfreetype6 \
    && apt-get install -y libfontconfig \
	&& apt-get install -y git \
	&& apt-get install -y zip \
	&& apt-get install -y libssl-dev \
    && apt-get install -y \
        libmcrypt-dev \
    && rm -rf /var/lib/apt/lists/* \
    && docker-php-ext-install mcrypt \
    && apt-get remove -y \
        libmcrypt-dev \
    && apt-get install -y \
        libmcrypt4 \
    && apt-get autoremove -y

# PHANTOM JS INSTALLATION
RUN wget -q https://bitbucket.org/ariya/phantomjs/downloads/phantomjs-1.9.7-linux-x86_64.tar.bz2
RUN tar xjf phantomjs-1.9.7-linux-x86_64.tar.bz2
RUN install -t /usr/local/bin phantomjs-1.9.7-linux-x86_64/bin/phantomjs
RUN rm -rf phantomjs-1.9.7-linux-x86_64
RUN rm phantomjs-1.9.7-linux-x86_64.tar.bz2

RUN pecl install mongo && docker-php-ext-enable mongo

RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

RUN cd /usr/local/etc/php/conf.d/ && \
	echo 'memory_limit = -1' >> /usr/local/etc/php/conf.d/docker-php-memlimit.ini

RUN docker-php-ext-install pcntl

COPY ./vhost.conf /etc/apache2/sites-available/000-default.conf

