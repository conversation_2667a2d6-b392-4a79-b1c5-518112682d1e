<VirtualHost *:80>
	ServerName dev.rradmin.com
    DocumentRoot /var/www/html/Risk-Reduce-Admin/public/

    <Directory "/var/www/html/Risk-Reduce-Admin/public/">
        AllowOverride all
        Require all granted
    </Directory>

    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined
</VirtualHost>

<VirtualHost *:80>
	ServerName dev.rrclient.com
    DocumentRoot /var/www/html/Risk-Reduce-Client/public

    <Directory "/var/www/html/Risk-Reduce-Client/public">
        AllowOverride all
        Require all granted
    </Directory>

    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined
</VirtualHost>

<VirtualHost *:80>
	ServerName dev.rrapi.com
    DocumentRoot /var/www/html/Risk-Reduce-API/public

    <Directory "/var/www/html/Risk-Reduce-API/public">
        AllowOverride all
        Require all granted
    </Directory>

    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined
</VirtualHost>
