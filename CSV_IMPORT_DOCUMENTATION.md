# Organisation CSV Import Documentation

## Overview

The `ImportCsvForDemo` Laravel console command has been enhanced to import organisation data from a CSV file located at `public/risk-insights-csv/organisations.csv` into the database using the `Organisation` model.

## Features

### ✅ Implemented Features

1. **CSV File Reading**: Reads from `public/risk-insights-csv/organisations.csv`
2. **Field Mapping**: Maps CSV columns to Organisation model fields
3. **Broker Handling**: Automatically creates brokers if they don't exist
4. **Date Parsing**: Handles dates in d/m/Y format (e.g., "14/3/2026")
5. **Duplicate Handling**: Uses `updateOrCreate` based on organisation name
6. **Error Handling**: Graceful error handling with detailed reporting
7. **Progress Tracking**: Progress bar and detailed statistics
8. **User Confirmation**: Asks for confirmation before proceeding
9. **Comprehensive Logging**: Detailed error reporting and success statistics

### 📊 CSV Field Mappings

| CSV Column | Organisation Field | Notes |
|------------|-------------------|-------|
| `client_name` | `name` | Required field |
| `sector` | `sector` | Looks up/creates sector by name (integer ID) |
| `subsector` | `product_subsector` | Optional string field |
| `Broker` | `broker_id` | Looks up/creates broker by name (integer ID) |
| `Renewal Date` | `expiry_date_of_cover` | Parsed from d/m/Y format, stored as Unix timestamp |
| `risk_score` | `risk_grading` | Numeric value |

### 🔧 Default Values

The following default values are set for required Organisation fields:
- `bound`: 0 (boolean: not bound)
- `status`: 0 (integer: active status)
- `country`: 'GB' (default to UK)
- `address_line_1`: '' (empty string)
- `postcode`: '' (empty string)
- `phone`: '' (empty string)
- `email`: '' (empty string)

## Usage

### Running the Import

```bash
php artisan insights:import
```

The command will:
1. Display the CSV file path
2. Ask for confirmation before proceeding
3. Show a progress bar during import
4. Display comprehensive results at the end

### Sample Output

```
Starting organisation CSV import...
CSV file path: /path/to/public/risk-insights-csv/organisations.csv

Do you want to proceed with the import? This will create/update organisation records. (yes/no) [no]:
> yes

CSV headers found: client_name,Premium,sector,subsector,lead_insurer,Broker,position,line_size,Surveys (No. of unique locations that has been surveyed),Last Updated (the date at which the most recent RAFA output was generated/ edited by a user),policy_segment,Renewal Date,Underwriter,risk_score
Found 5 data rows to process

 5/5 [▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓] 100%

==================================================
IMPORT COMPLETED
==================================================
+----------------------------------+-------+
| Metric                           | Count |
+----------------------------------+-------+
| Total Rows Processed             | 5     |
| New Organisations Imported       | 3     |
| Existing Organisations Updated   | 2     |
| Errors Encountered               | 0     |
+----------------------------------+-------+

Import process completed successfully!
```

## Error Handling

### Common Error Scenarios

1. **Missing CSV File**: Clear error message with file path
2. **Invalid CSV Format**: Handles malformed CSV gracefully
3. **Missing Required Fields**: Validates `client_name` is present
4. **Invalid Dates**: Logs warning and continues without setting date
5. **Database Errors**: Catches and reports database constraint violations

### Error Reporting

- Individual row errors are collected and displayed at the end
- Progress continues even if individual rows fail
- Detailed error messages include row numbers and specific issues

## Testing

### Validation Script

A test script `test_csv_import.php` is provided to validate the CSV structure and parsing logic:

```bash
php test_csv_import.php
```

This script will:
- Verify CSV file exists and is readable
- Test header parsing
- Validate sample data row parsing
- Test date parsing logic
- Verify field mapping

### Manual Testing Steps

1. **Backup Database**: Always backup before running import
2. **Test with Sample Data**: Start with a small CSV file
3. **Verify Results**: Check created/updated organisations in database
4. **Check Broker Creation**: Verify brokers were created correctly
5. **Validate Dates**: Ensure dates were parsed correctly

## CSV File Requirements

### File Location
- Must be located at: `public/risk-insights-csv/organisations.csv`
- File must be readable by the web server

### Required Headers
The CSV must contain these exact headers (case-sensitive):
- `client_name` (required)
- `sector` (optional)
- `subsector` (optional)
- `Broker` (optional)
- `Renewal Date` (optional, format: d/m/Y)
- `risk_score` (optional, numeric)

### Sample CSV Format
```csv
client_name,Premium,sector,subsector,lead_insurer,Broker,position,line_size,Surveys (No. of unique locations that has been surveyed),Last Updated (the date at which the most recent RAFA output was generated/ edited by a user),policy_segment,Renewal Date,Underwriter,risk_score
Tesco Plc,"£94,021.00",Retail,Food & Beverage,Chubb,Aon,Follow,21.5,6,30/4/2025,Risk Managed,14/3/2026,Martin Vallins,89
```

## Database Impact

### Tables Affected
- `organisations`: Main target table
- `brokers`: May create new broker records

### Duplicate Handling
- Uses organisation `name` as the unique identifier
- Existing organisations are updated, not duplicated
- Broker lookup is case-insensitive with partial matching

## Security Considerations

- Command requires confirmation before execution
- No direct user input validation (CSV is trusted source)
- Database transactions ensure data integrity
- Error handling prevents partial imports

## Troubleshooting

### Common Issues

#### 1. "Incorrect integer value" for Status Field
**Error**: `SQLSTATE[HY000]: General error: 1366 Incorrect integer value: 'active' for column 'status'`

**Solution**: The `status` field expects an integer, not a string. This has been fixed to use `0` (active status).

#### 2. "Incorrect integer value" for Sector Field
**Error**: `SQLSTATE[HY000]: General error: 1366 Incorrect integer value: 'Retail' for column 'sector'`

**Solution**: The `sector` field expects a sector ID (integer), not the sector name. The import now automatically:
- Looks up existing sectors by name (handle or type fields)
- Creates new sectors if they don't exist
- Uses the sector ID in the organisation record

#### 3. "Data truncated for column 'expiry_date_of_cover'" Warning
**Error**: `SQLSTATE[01000]: Warning: 1265 Data truncated for column 'expiry_date_of_cover'`

**Solution**: The `expiry_date_of_cover` field expects a Unix timestamp (integer), not a date string. The import now:
- Parses dates from d/m/Y format
- Converts them to Unix timestamps using `getTimestamp()`
- Stores the integer timestamp in the database

#### 4. "CSV file not found" Error
**Error**: `CSV file not found at: /path/to/public/risk-insights-csv/organisations.csv`

**Solutions**:
- Verify the CSV file exists at the correct path
- Check file permissions (must be readable by web server)
- Ensure the file is not empty

#### 5. Date Parsing Warnings
**Warning**: `Unable to parse date: 32/13/2025`

**Solutions**:
- Ensure dates are in d/m/Y format (e.g., "14/3/2026")
- Check for invalid dates (month > 12, day > 31)
- Empty date fields are handled gracefully

#### 6. Broker Creation Issues
**Issue**: Multiple brokers created with similar names

**Solutions**:
- The system uses partial matching (`LIKE '%name%'`)
- Clean up duplicate brokers manually if needed
- Consider exact matching for production use

#### 7. Sector Creation Issues
**Issue**: Multiple sectors created with similar names

**Solutions**:
- The system searches both `handle` and `type` fields for existing sectors
- New sectors are created with both `handle` and `type` set to the CSV sector name
- Review created sectors in the `sectors` table after import

### Database Constraints

#### Status Field Values
The `status` field in the organisations table accepts integer values:
- `0`: Active (default)
- `1`: Inactive
- `2`: Archived

#### Required Fields
These fields cannot be null in the database:
- `name` (validated in CSV import)
- `address_line_1` (set to empty string if not provided)
- `postcode` (set to empty string if not provided)
- `country` (defaults to 'GB')

## Maintenance

### Adding New Fields
To map additional CSV columns:
1. Update the `mapCsvToOrganisation()` method
2. Add field validation if needed
3. Update documentation

### Modifying Date Formats
Update the `parseDate()` method to handle different date formats.

### Changing Duplicate Logic
Modify the lookup logic in `processOrganisationRow()` to use different unique identifiers.
