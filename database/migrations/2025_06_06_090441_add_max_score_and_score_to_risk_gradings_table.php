<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('risk_gradings', function (Blueprint $table) {
            $table->integer('max_score')->nullable()->after('value');
            $table->integer('score')->nullable()->after('max_score');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('risk_gradings', function (Blueprint $table) {
            $table->dropColumn('max_score, score');
        });
    }
};
